﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.DataAccess;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.Response;
using RevCord.Util;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.VoiceRecEntities;
using System.Threading.Tasks;
using System.Data.SqlClient;
using RevCord.DataAccess.Util;
using System.Web.Script.Serialization;

namespace RevCord.BusinessLogic
{
    public class EvaluationManager
    {
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress; //"127.0.0.1";
        public EvaluationResponse GetPaged(EvaluationRequest evalReq)
        {
            try
            {
                int totalPages = 0;
                int totalRecords = 0;
                //out totalPages, out totalRecords,
                List<CallEvaluationDTO> CallEvaluationDTOs = null;
                string whereClause = getCriteria(evalReq);

                switch (evalReq.ActionToPerform)
                {
                    case EvalActionType.None:
                        CallEvaluationDTOs = new EvaluationDAL(evalReq.TenantId).GetCallEvaluationDTOPaged(whereClause, evalReq.UserId, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords, evalReq.SharedRequired, evalReq.SearchCriteria.IsEvaluatorSearch);
                        break;
                    case EvalActionType.MarkAsComplete:
                        CallEvaluationDTOs = new EvaluationDAL(evalReq.TenantId).PerformActionAndGetCallEvaluationDTOPaged(evalReq.CallEvaluationIds, evalReq.RevSyncCallEvaluationIds, whereClause,
                                                                                                           (int)EvalActionColumn.StatusID,
                                                                                                           Convert.ToInt32(EnumHelper.GetDescription(evalReq.ActionToPerform)),
                                                                                                           evalReq.UserId, evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords);
                        break;
                    case EvalActionType.MarkAsShareUnshare:
                        CallEvaluationDTOs = new EvaluationDAL(evalReq.TenantId).PerformActionAndGetCallEvaluationDTOPaged(evalReq.CallEvaluationIds, evalReq.RevSyncCallEvaluationIds, whereClause,
                                                                                                           (int)EvalActionColumn.IsShared,
                                                                                                           Convert.ToInt32(EnumHelper.GetDescription(evalReq.ActionToPerform)),
                                                                                                           evalReq.UserId, evalReq.ShareWith, evalReq.IsSharedEvaluatorRetains, evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords);
                        break;
                    case EvalActionType.MarkAsDelete:
                        CallEvaluationDTOs = new EvaluationDAL(evalReq.TenantId).PerformActionAndGetCallEvaluationDTOPaged(evalReq.CallEvaluationIds, evalReq.RevSyncCallEvaluationIds, whereClause,
                                                                                                           (int)EvalActionColumn.IsDeleted,
                                                                                                           Convert.ToInt32(EnumHelper.GetDescription(evalReq.ActionToPerform)),
                                                                                                           evalReq.UserId, evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords);
                        break;
                    case EvalActionType.MarkAsRejected:
                        CallEvaluationDTOs = new EvaluationDAL(evalReq.TenantId).PerformActionAndGetCallEvaluationDTOPaged(evalReq.CallEvaluationIds, evalReq.RevSyncCallEvaluationIds, whereClause,
                                                                                                           (int)EvalActionColumn.StatusID,
                                                                                                           (int)EvaluationStatus.Rejected,
                                                                                                           evalReq.UserId, evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords);
                        break;
                    default:
                        CallEvaluationDTOs = new EvaluationDAL(evalReq.TenantId).GetCallEvaluationDTOPaged(whereClause, evalReq.UserId, evalReq.SharedRequired);
                        break;
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetPaged", evalReq.TenantId, "GetPaged function has been called successfully."));

                return new EvaluationResponse { CallEvaluationDTOs = CallEvaluationDTOs, TotalPages = totalPages, TotalRecords = totalRecords };

            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetPaged", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetPaged", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        /*Used in Call to be Evaluated*/
        public EvaluationResponse UpdateCallEvaluationAndGetPaged(EvaluationRequest evalReq)
        {
            try
            {
                List<CallEvaluationDTO> callEvaluationDTOs = null;
                string whereClause = getCriteria(evalReq);

                callEvaluationDTOs = new EvaluationDAL(evalReq.TenantId).UpdateCallEvaluationAndGetPaged(whereClause, evalReq.CallEvaluationIds,
                                                                                         evalReq.SurveyId, (int)evalReq.EvalStatus,
                                                                                          evalReq.ActionPerformedDate,
                                                                                         evalReq.IsDeleted, evalReq.UserId);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "UpdateCallEvaluationAndGetPaged", evalReq.TenantId, "UpdateCallEvaluationAndGetPaged function has been called successfully."));
                return new EvaluationResponse { CallEvaluationDTOs = callEvaluationDTOs };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "UpdateCallEvaluationAndGetPaged", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "UpdateCallEvaluationAndGetPaged", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public EvaluationResponse GetClosedEvaluationsByAgentId(int agentId, int tenantId)
        {
            try
            {
                List<CallEvaluationDTO> callEvaluationDTOs = null;
                callEvaluationDTOs = new EvaluationDAL(tenantId).GetClosedEvaluationsByAgentId(agentId);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetClosedEvaluationsByAgentId", tenantId, "GetClosedEvaluationsByAgentId function has been called successfully."));
                return new EvaluationResponse { CallEvaluationDTOs = callEvaluationDTOs };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetClosedEvaluationsByAgentId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetClosedEvaluationsByAgentId", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool DeleteEvaluation(long evaluationId, int tenantId)
        {
            try
            {
                var bDeleted = new EvaluationDAL(tenantId).DeleteEvaluation(evaluationId);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "DeleteEvaluation", tenantId, "DeleteEvaluation function has been called successfully. bDeleted = " + bDeleted));
                return bDeleted;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "DeleteEvaluation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "DeleteEvaluation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public EvaluationResponse CallEvaluationDetailsById(long id, int userId, int tenantId)
        {
            try
            {
                EvaluationResponse evalResponse = new EvaluationResponse { CallEvaluation = new EvaluationDAL(tenantId).CallCallEvaluationDetailsById(id, userId) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "CallEvaluationDetailsById", tenantId, "CallEvaluationDetailsById function has been called successfully. CallEvaluation = " + new JavaScriptSerializer().Serialize(evalResponse.CallEvaluation)));
                return evalResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "CallEvaluationDetailsById", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "CallEvaluationDetailsById", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CallEvaluation UpdateCallEvaluation(CallEvaluation callEval, int tenantId)
        {
            try
            {
                callEval.ModifiedDate = DateTime.Now;
                if (callEval.Survey.NoOfQuestions == callEval.Answers.Count)
                    callEval.Status = EvaluationStatus.Completed;
                else
                    callEval.Status = EvaluationStatus.InProgress;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "UpdateCallEvaluation", tenantId, "UpdateCallEvaluation function has been called successfully. callEval.Status = " + callEval.Status + " Id = " + callEval.Id));
                return new EvaluationDAL(tenantId).UpdateCallEvaluation(callEval);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "UpdateCallEvaluation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "UpdateCallEvaluation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public User UpdateAndGetAssociatedUser(int evalId, int userNum, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "UpdateAndGetAssociatedUser", tenantId, "UpdateAndGetAssociatedUser function has been called successfully. evalId = " + evalId + " userNum = " + userNum));
                return new EvaluationDAL(tenantId).UpdateAndGetAssociatedUser(evalId, userNum);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "UpdateAndGetAssociatedUser", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "UpdateAndGetAssociatedUser", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public short InsertCallEvaluation(EvaluationRequest evalReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "InsertCallEvaluation", evalReq.TenantId, "InsertCallEvaluation function has been called successfully. evalReq.CallId = " + evalReq.CallId + " evalReq.UserId = " + evalReq.UserId + " evalReq.SurveyId = " + evalReq.SurveyId + " evalReq.IsSegmented = " + evalReq.IsSegmented));
                if (evalReq.IsSegmented)
                    return new EvaluationDAL(evalReq.TenantId).InsertSegmentedCallEvaluation(evalReq.CallId, evalReq.UserId, evalReq.SurveyId, evalReq.SegmentCallIds, evalReq.EvaluationType);
                else
                    return new EvaluationDAL(evalReq.TenantId).InsertCallEvaluation(evalReq.SurveyId, evalReq.CallId, evalReq.AgentId, evalReq.EvalStatus, evalReq.UserId, evalReq.EvaluationType);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "InsertCallEvaluation", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "InsertCallEvaluation", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool InsertEvaluations(EvaluationRequest evaluationRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "InsertEvaluations", evaluationRequest.TenantId, "InsertEvaluations function has been called successfully. evalReq.CallId = " + evaluationRequest.CallId + " evalReq.UserId = " + evaluationRequest.UserId + " evaluationRequest.CallEvaluationIds = " + evaluationRequest.CallEvaluationIds + " evaluationRequest.SurveyId = " + evaluationRequest.SurveyId));
                return new EvaluationDAL(evaluationRequest.TenantId).InsertEvaluations(evaluationRequest.CallIds, evaluationRequest.UserId, evaluationRequest.SurveyId, evaluationRequest.EvaluationType, evaluationRequest.GroupId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "InsertEvaluations", evaluationRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "InsertEvaluations", evaluationRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool InsertEvaluationFeedback(EvaluationRequest evaluationRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "InsertEvaluationFeedback", evaluationRequest.TenantId, "InsertEvaluationFeedback function has been called successfully. CallEvaluationId = " + evaluationRequest.CallEvaluationId + " AgentId = " + evaluationRequest.AgentId + "Comments = " + evaluationRequest.Comments));
                return new EvaluationDAL(evaluationRequest.TenantId).InsertEvaluationFeedback(evaluationRequest);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "InsertEvaluationFeedback", evaluationRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "InsertEvaluationFeedback", evaluationRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool DeleteEvaluationFeedback(EvaluationRequest evaluationRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "DeleteEvaluationFeedback", evaluationRequest.TenantId, "DeleteEvaluationFeedback function has been called successfully. CallEvaluationId = " + evaluationRequest.CallEvaluationId));
                return new EvaluationDAL(evaluationRequest.TenantId).DeleteEvaluationFeedback(evaluationRequest);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "DeleteEvaluationFeedback", evaluationRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "DeleteEvaluationFeedback", evaluationRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public EvaluationResponse GetCallEvaluationFeedback(EvaluationRequest evaluationRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetCallEvaluationFeedback", evaluationRequest.TenantId, "GetCallEvaluationFeedback function has been called successfully. CallEvaluationId = " + evaluationRequest.CallEvaluationId));
                return new EvaluationResponse { CallEvaluationFeedbacks = new EvaluationDAL(evaluationRequest.TenantId).GetCallEvaluationFeedback(evaluationRequest) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetCallEvaluationFeedback", evaluationRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetCallEvaluationFeedback", evaluationRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IList<CallInfo> GetSegmentedCalls(int tenantId, long evaluationId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetSegmentedCalls", tenantId, "GetSegmentedCalls function has been called successfully. evaluationId = " + evaluationId));
                return new EvaluationDAL(tenantId).GetSegmentedCalls(evaluationId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetSegmentedCalls", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetSegmentedCalls", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region Private Methods

        private static string getCriteria(EvaluationRequest evalReq)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();//" StatusId IN (" + 2 + ")";
                sbWhereClause.Append(" 1 = 1  AND ce.EvaluationType =   " + evalReq.SearchCriteria.EvaluationType);
                if (evalReq.EvalStatuses != null && evalReq.EvalStatuses.Count > 0)
                {
                    bool isMultipleStatus = false;
                    sbWhereClause.Append(" AND StatusId IN (");
                    foreach (var status in evalReq.EvalStatuses)
                    {
                        if (isMultipleStatus)
                            sbWhereClause.Append("," + (int)status);
                        else
                            sbWhereClause.Append((int)status);
                        isMultipleStatus = true;
                    }
                    sbWhereClause.Append(")");
                }
                else
                    sbWhereClause.Append(" AND StatusId IN (0)");

                if (evalReq.SearchCriteria != null && evalReq.SearchCriteria.IsSearchActive)
                {
                    if (evalReq.SearchCriteria.StartDate.HasValue && evalReq.SearchCriteria.EndDate.HasValue)
                    {
                        sbWhereClause.Append(" AND CAST(ce.CreatedDate AS date) BETWEEN " + "'" + evalReq.SearchCriteria.StartDate.Value.Date.ToString("yyyy-MM-dd") + "' AND '" + evalReq.SearchCriteria.EndDate.Value.Date.ToString("yyyy-MM-dd") + "'");
                    }
                    if (evalReq.SearchCriteria.SharedValue == SharedValue.Unshared || evalReq.SearchCriteria.SharedValue == SharedValue.Shared)
                    {
                        switch (evalReq.SearchCriteria.SharedValue)
                        {
                            case SharedValue.All:
                                break;
                            case SharedValue.Shared:
                                sbWhereClause.Append(" AND IsShared = " + Convert.ToInt16(evalReq.SearchCriteria.SharedValue) + " AND SharedWith = " + Convert.ToInt32(evalReq.UserId));
                                break;
                            case SharedValue.Unshared:
                                sbWhereClause.Append(" AND IsShared = " + Convert.ToInt16(evalReq.SearchCriteria.SharedValue));
                                break;
                            default:
                                break;
                        }
                    }
                    if (evalReq.SearchCriteria.MinScore != null)
                    {
                        if (evalReq.SearchCriteria.MaxScore != null)
                        {
                            sbWhereClause.Append(" AND EvaluatedScore BETWEEN " + evalReq.SearchCriteria.MinScore + " AND " + evalReq.SearchCriteria.MaxScore);
                        }
                        else
                        {
                            sbWhereClause.Append(" AND EvaluatedScore >= " + evalReq.SearchCriteria.MinScore);
                        }
                    }
                    else if (evalReq.SearchCriteria.MinScore == null && evalReq.SearchCriteria.MaxScore != null)
                        sbWhereClause.Append(" AND EvaluatedScore <= " + evalReq.SearchCriteria.MaxScore);
                    //if (evalReq.EvalStatuses.FirstOrDefault(s=> s
                    if (!String.IsNullOrEmpty(evalReq.SearchCriteria.CampaignName))
                    {
                        sbWhereClause.Append(" AND (s.Title LIKE N'%" + evalReq.SearchCriteria.CampaignName + "%' OR s.Title IS NULL)");
                    }
                    if (!String.IsNullOrEmpty(evalReq.SearchCriteria.AgentName))
                    {
                        sbWhereClause.Append(" AND (ce.AppUserId = " + evalReq.SearchCriteria.AgentNum + ")");
                        //sbWhereClause.Append(" AND (Acc.UserName LIKE N'%" + evalReq.SearchCriteria.AgentName + "%' OR Ag.Name LIKE N'%" + evalReq.SearchCriteria.AgentName + "%')");
                        //sbWhereClause.Append(" AND ((Acc.UserName LIKE N'%" + evalReq.SearchCriteria.AgentName + "%' ) OR (ce.UserId=(select TOP 1 UserNum from t_Account where UserName LIKE N'%" + evalReq.SearchCriteria.AgentName + "%')))");
                    }
                    if (!String.IsNullOrEmpty(evalReq.SearchCriteria.EvaluatorName) && evalReq.SearchCriteria.IsEvaluatorSearch)
                    {
                        sbWhereClause.Append(" AND (ce.UserId=(select TOP 1 UserNum from t_Account where UserName LIKE N'%" + evalReq.SearchCriteria.EvaluatorName + "%'))");
                    }
                    if (evalReq.SearchCriteria.IsEnterpriseAssociatedEvalSearch)
                    {
                        sbWhereClause.Append(" AND entEval.CallEvaluationId = ce.Id AND ce.IsEnterpriseAssociated = 1 AND entEval.EvaluatorId = " + Convert.ToInt32(evalReq.UserId));
                    }
                    //if (evalReq.IsEnterpriseEvaluator)
                    //{
                    //    sbWhereClause.Append(" AND entEval.CallEvaluationId = ce.Id AND ce.IsEnterpriseAssociated = 1 ");
                    //}
                }
                else if (evalReq.SearchCriteria != null && evalReq.SearchCriteria.IsSearchActive == false)
                {
                    if (evalReq.SearchCriteria.SharedValue == SharedValue.Shared || evalReq.SearchCriteria.SharedValue == SharedValue.Unshared)
                        sbWhereClause.Append(" AND IsShared = " + Convert.ToInt16(evalReq.SearchCriteria.SharedValue));
                }
                System.Diagnostics.Debug.WriteLine("Where Clause = " + sbWhereClause.ToString());
                return sbWhereClause.ToString();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "getCriteria", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "getCriteria", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        private static string getMultiCallEvaluationCriteria(EvaluationRequest evalReq)
        {
            try
            {
                StringBuilder sbWhereClause = new StringBuilder();//" StatusId IN (" + 2 + ")";
                sbWhereClause.Append(" 1 = 1 ");
                if (evalReq.SearchCriteria != null)
                {
                    if (evalReq.SearchCriteria.StartDate.HasValue && evalReq.SearchCriteria.EndDate.HasValue)
                    {
                        sbWhereClause.Append(" AND CAST(m.CreatedDate AS date) BETWEEN " + "'" + evalReq.SearchCriteria.StartDate.Value.Date.ToString("yyyy-MM-dd") + "' AND '" + evalReq.SearchCriteria.EndDate.Value.Date.ToString("yyyy-MM-dd") + "'");
                    }
                }
                if (evalReq.MultiCallEvaluationId > 0) {
                    sbWhereClause.Append(" AND m.Id = " + evalReq.MultiCallEvaluationId);
                }
                System.Diagnostics.Debug.WriteLine("Where Clause = " + sbWhereClause.ToString());
                return sbWhereClause.ToString();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "getMultiCallEvaluationCriteria", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "getMultiCallEvaluationCriteria", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion


        #region Charts

        public List<Tuple<int, string, int>> ChartGetEvaluationsByStatus(int uID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "ChartGetEvaluationsByStatus", tenantId, "ChartGetEvaluationsByStatus function has been called successfully. uID = " + uID));
                return new EvaluationDAL(tenantId).GetEvaluationsByStatus(uID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "ChartGetEvaluationsByStatus", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "ChartGetEvaluationsByStatus", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tuple<int, string, int>> ChartGetEvaluationsByCampaign(int uID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "ChartGetEvaluationsByCampaign", tenantId, "ChartGetEvaluationsByCampaign function has been called successfully. uID = " + uID));
                return new EvaluationDAL(tenantId).ChartGetEvaluationsByCampaign(uID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "ChartGetEvaluationsByCampaign", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "ChartGetEvaluationsByCampaign", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tuple<int, string, int>> ChartGetEvaluationsByEvaluator(int uID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "ChartGetEvaluationsByEvaluator", tenantId, "ChartGetEvaluationsByEvaluator function has been called successfully. uID = " + uID));
                return new EvaluationDAL(tenantId).ChartGetEvaluationsByEvaluator(uID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "ChartGetEvaluationsByEvaluator", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "ChartGetEvaluationsByEvaluator", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tuple<int, string, int>> ChartGetEvaluationsByTopScorers(int uID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "ChartGetEvaluationsByTopScorers", tenantId, "ChartGetEvaluationsByTopScorers function has been called successfully. uID = " + uID));
                return new EvaluationDAL(tenantId).ChartGetEvaluationsByTopScorers(uID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "ChartGetEvaluationsByTopScorers", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "ChartGetEvaluationsByTopScorers", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tuple<int, string, int, string, string>> ChartGetDrilldownEvaluationsByStatus(int uID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "ChartGetDrilldownEvaluationsByStatus", tenantId, "ChartGetDrilldownEvaluationsByStatus function has been called successfully. uID = " + uID));
                return new EvaluationDAL(tenantId).ChartGetDrilldownEvaluationsByStatus(uID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "ChartGetDrilldownEvaluationsByStatus", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "ChartGetDrilldownEvaluationsByStatus", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tuple<int, string, int, string, string>> ChartGetDrilldownEvaluationsByCampaign(int uID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "ChartGetDrilldownEvaluationsByCampaign", tenantId, "ChartGetDrilldownEvaluationsByCampaign function has been called successfully. uID = " + uID));
                return new EvaluationDAL(tenantId).ChartGetDrilldownEvaluationsByCampaign(uID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "ChartGetDrilldownEvaluationsByCampaign", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "ChartGetDrilldownEvaluationsByCampaign", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tuple<int, string, int, string, string>> ChartGetDrilldownEvaluationsByEvaluator(int uID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "ChartGetDrilldownEvaluationsByEvaluator", tenantId, "ChartGetDrilldownEvaluationsByEvaluator function has been called successfully. uID = " + uID));
                return new EvaluationDAL(tenantId).ChartGetDrilldownEvaluationsByEvaluator(uID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "ChartGetDrilldownEvaluationsByEvaluator", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "ChartGetDrilldownEvaluationsByEvaluator", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tuple<int, string, int, string, string>> ChartGetDrilldownEvaluationsByTopScorers(int uID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "ChartGetDrilldownEvaluationsByTopScorers", tenantId, "ChartGetDrilldownEvaluationsByTopScorers function has been called successfully. uID = " + uID));
                return new EvaluationDAL(tenantId).ChartGetDrilldownEvaluationsByTopScorers(uID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "ChartGetDrilldownEvaluationsByTopScorers", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "ChartGetDrilldownEvaluationsByTopScorers", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tuple<int, string, int>> GetEvaluationsByStatus(int userId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetEvaluationsByStatus", tenantId, "GetEvaluationsByStatus function has been called successfully. uID = " + userId));
                return new EvaluationDAL(tenantId).ChartGetEvaluationsByStatus(userId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetEvaluationsByStatus", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetEvaluationsByStatus", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<Tuple<int, string, int, string, string>> GetMultiCallEvaluationByStatus(int uID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetMultiCallEvaluationByStatus", tenantId, "GetMultiCallEvaluationByStatus function has been called successfully. uID = " + uID));
                return new EvaluationDAL(tenantId).GetMultiCallEvaluationByStatus(uID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetMultiCallEvaluationByStatus", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetMultiCallEvaluationByStatus", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion


        #region User Evaluation

        public EvaluationResponse SaveUserEvaluation(EvaluationRequest evalReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "SaveUserEvaluation", evalReq.TenantId, "SaveUserEvaluation function has been called successfully. uID = " + evalReq.UserId + " evalReq.SurveyId = " + evalReq.SurveyId));
                return new EvaluationResponse { LastSavedIds = new EvaluationDAL(evalReq.TenantId).SaveUserEvaluations(evalReq.UserIds, evalReq.SurveyId, (int)evalReq.EvalStatus, evalReq.EvaluatorId, evalReq.ControlState), StatusFromDB = 1 };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "SaveUserEvaluation", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "SaveUserEvaluation", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public EvaluationResponse UserEvaluationDetailsById(long id, int userId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "UserEvaluationDetailsById", tenantId, "UserEvaluationDetailsById function has been called successfully. id = " + id + " userId = " + userId));
                return new EvaluationResponse { UserEvaluation = new EvaluationDAL(tenantId).UserEvaluationDetailsById(id, userId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "UserEvaluationDetailsById", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "UserEvaluationDetailsById", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserEvaluation UpdateUserEvaluation(UserEvaluation userEval, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "UpdateUserEvaluation", tenantId, "UpdateUserEvaluation function has been called successfully. userEval.Id = " + userEval.Id));
                userEval.ModifiedDate = DateTime.Now;
                if (userEval.Survey.NoOfQuestions == userEval.Answers.Count)
                    userEval.Status = EvaluationStatus.Completed;
                else
                    userEval.Status = EvaluationStatus.InProgress;
                return new EvaluationDAL(tenantId).UpdateUserEvaluation(userEval);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "UpdateUserEvaluation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "UpdateUserEvaluation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public EvaluationResponse GetUserEvaluations(EvaluationRequest evalReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetUserEvaluations", evalReq.TenantId, "GetUserEvaluations function has been called successfully."));
                string whereClause = " 1=1 ";// getCriteria(evalReq);
                return new EvaluationResponse { UserEvaluations = new EvaluationDAL(evalReq.TenantId).GetUserEvaluationPaged(whereClause, evalReq.UserId, evalReq.SharedRequired) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetUserEvaluations", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetUserEvaluations", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public EvaluationResponse UpdateUserEvaluationStatus(EvaluationRequest evalRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "UpdateUserEvaluationStatus", evalRequest.TenantId, "UpdateUserEvaluationStatus function has been called successfully."));
                return new EvaluationResponse { StatusFromDB = new EvaluationDAL(evalRequest.TenantId).UpdateUserEvaluationStatus(evalRequest.EvaluationIds, (int)evalRequest.EvalStatus, evalRequest.ActionPerformedDate, evalRequest.IsDeleted) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "UpdateUserEvaluationStatus", evalRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "UpdateUserEvaluationStatus", evalRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #endregion

        #region Evaluation EC
        public EvaluationResponse GetAllDrilldownChartsFromAllRecorders(EvaluationRequest evaluationRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetAllDrilldownChartsFromAllRecorders", evaluationRequest.TenantId, "GetAllDrilldownChartsFromAllRecorders function has been called successfully."));

                List<RecorderEvaluation> recorderEvaluations = new List<RecorderEvaluation>();
                foreach (var recorder in evaluationRequest.Recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        if (recorder.IsPrimary)
                            recorderEvaluations.Add(EvaluationDALEC.GetAllDrilldownChartsFromAllRecorders(recorder));
                        else
                        {
                            // Alternative Approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                            recorderEvaluations.Add(entClient.GetAllDrilldownChartsFromRecorder(recorder));
                        }
                        Task.Run(() => RevAuditLogger.WriteInformation(Originator.Evaluation, "GetAllDrilldownChartsFromAllRecorders", evaluationRequest.TenantId, "Drilldown charts data fetched successfully from Recorderid = " + recorder.Id + " Recorder Name" + recorder.Name));
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetAllDrilldownChartsFromAllRecorders", evaluationRequest.TenantId, "An error has occurred while fetching drill down charts from Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new EvaluationResponse { RecorderEvaluations = recorderEvaluations };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetAllDrilldownChartsFromAllRecorders", evaluationRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetAllDrilldownChartsFromAllRecorders", evaluationRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public EvaluationResponse GetEvaluationsFromAllRecorders(EvaluationRequest evalReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetEvaluationsFromAllRecorders", evalReq.TenantId, "GetEvaluationsFromAllRecorders function has been called successfully."));
                int totalPages = 0;
                int totalRecords = 0;
                List<CallEvaluationDTO> CallEvaluationDTOs = null;
                string whereClause = getCriteria(evalReq);

                List<Recorder> recorders = evalReq.Recorders;
                var recEvals = new List<RecorderEvaluation>();
                List<CallEvaluationDTO> callEvaluationDTO = new List<CallEvaluationDTO>();

                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        switch (evalReq.ActionToPerform)
                        {
                            case EvalActionType.None:

                                if (rec.IsPrimary)
                                {
                                    if (!evalReq.SearchCriteria.IsEnterpriseAssociatedEvalSearch)
                                        callEvaluationDTO = new EvaluationDALEC(evalReq.TenantId).GetEvaluationsFromAllRecorders(rec, whereClause, evalReq.UserId, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords, evalReq.SharedRequired, evalReq.SearchCriteria.IsEvaluatorSearch);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string entWhereClause = string.Empty;
                                    entWhereClause = whereClause + " AND entEval.CallEvaluationId = ce.Id AND ce.IsEnterpriseAssociated = 1 AND entEval.EvaluatorId = " + Convert.ToInt32(evalReq.UserId) + " ";
                                    callEvaluationDTO = entClient.GetEnterpriseAssociatedEvaluations(rec, entWhereClause, evalReq.UserId, evalReq.PageNumber, evalReq.PageSize, evalReq.SharedRequired, true, out totalPages, out totalRecords).ToList();
                                }
                                callEvaluationDTO.ForEach(ce => ce.RecIP = rec.IP);
                                recEvals.Add(new RecorderEvaluation
                                {
                                    RecorderId = rec.Id,
                                    RecorderName = rec.Name,
                                    CallEvaluationDTO = callEvaluationDTO,
                                    TotalPages = totalPages,
                                    TotalRecords = totalRecords
                                });
                                break;
                            case EvalActionType.MarkAsComplete:
                                if (rec.IsPrimary)
                                {
                                    callEvaluationDTO = new EvaluationDALEC(evalReq.TenantId).PerformActionAndGetEvaluationDTOFromAllRecorders(rec, evalReq.ActionToPerformOnRecorder, evalReq.CallEvaluationIds, whereClause,
                                                        (int)EvalActionColumn.StatusID,
                                                        Convert.ToInt32(EnumHelper.GetDescription(evalReq.ActionToPerform)),
                                                        evalReq.UserId, evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string entWhereClause = string.Empty;
                                    entWhereClause = whereClause + " AND entEval.CallEvaluationId = ce.Id AND ce.IsEnterpriseAssociated = 1 AND entEval.EvaluatorId = " + Convert.ToInt32(evalReq.UserId) + " ";
                                    callEvaluationDTO = entClient.PerformActionAndGetEnterpriseEvaluationDTO(rec, evalReq.ActionToPerformOnRecorder, evalReq.CallEvaluationIds, entWhereClause, false, true,
                                                        (int)EvalActionColumn.StatusID,
                                                        Convert.ToInt32(EnumHelper.GetDescription(evalReq.ActionToPerform)),
                                                        evalReq.UserId, evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords).ToList();
                                }
                                callEvaluationDTO.ForEach(ce => ce.RecIP = rec.IP);
                                recEvals.Add(new RecorderEvaluation
                                {
                                    RecorderId = rec.Id,
                                    RecorderName = rec.Name,
                                    CallEvaluationDTO = callEvaluationDTO,
                                    TotalPages = totalPages,
                                    TotalRecords = totalRecords
                                });
                                break;
                            case EvalActionType.MarkAsShareUnshare:
                                if (rec.IsPrimary)
                                {
                                    callEvaluationDTO = new EvaluationDALEC(evalReq.TenantId).shareUnshareAndGetEvaluationDTOFromAllRecorders(rec, evalReq.ActionToPerformOnRecorder, evalReq.CallEvaluationIds, whereClause,
                                                                            (int)EvalActionColumn.IsShared,
                                                                            Convert.ToInt32(EnumHelper.GetDescription(evalReq.ActionToPerform)),
                                                                            evalReq.UserId, evalReq.ShareWith, evalReq.IsSharedEvaluatorRetains,
                                                                            evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize,
                                                                            out totalPages, out totalRecords);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string entWhereClause = string.Empty;
                                    entWhereClause = whereClause + " AND entEval.CallEvaluationId = ce.Id AND ce.IsEnterpriseAssociated = 1 AND entEval.EvaluatorId = " + Convert.ToInt32(evalReq.UserId) + " ";
                                    callEvaluationDTO = entClient.ShareUnshareAndGetEvaluationDTO(rec, evalReq.ActionToPerformOnRecorder, evalReq.CallEvaluationIds, entWhereClause,
                                                                            (int)EvalActionColumn.IsShared,
                                                                            Convert.ToInt32(EnumHelper.GetDescription(evalReq.ActionToPerform)),
                                                                            evalReq.UserId, evalReq.ShareWith, evalReq.IsSharedEvaluatorRetains,
                                                                            evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize,
                                                                            out totalPages, out totalRecords).ToList();
                                }
                                callEvaluationDTO.ForEach(ce => ce.RecIP = rec.IP);
                                recEvals.Add(new RecorderEvaluation
                                {
                                    RecorderId = rec.Id,
                                    RecorderName = rec.Name,
                                    CallEvaluationDTO = callEvaluationDTO,
                                    TotalPages = totalPages,
                                    TotalRecords = totalRecords
                                });
                                break;
                            case EvalActionType.MarkAsDelete:
                                if (rec.IsPrimary)
                                {
                                    callEvaluationDTO = new EvaluationDALEC(evalReq.TenantId).PerformActionAndGetEvaluationDTOFromAllRecorders(rec, evalReq.ActionToPerformOnRecorder, evalReq.CallEvaluationIds, whereClause,
                                                        (int)EvalActionColumn.IsDeleted,
                                                        Convert.ToInt32(EnumHelper.GetDescription(evalReq.ActionToPerform)),
                                                        evalReq.UserId, evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string entWhereClause = string.Empty;
                                    entWhereClause = whereClause + " AND entEval.CallEvaluationId = ce.Id AND ce.IsEnterpriseAssociated = 1 AND entEval.EvaluatorId = " + Convert.ToInt32(evalReq.UserId) + " ";
                                    callEvaluationDTO = entClient.PerformActionAndGetEnterpriseEvaluationDTO(rec, evalReq.ActionToPerformOnRecorder, evalReq.CallEvaluationIds, entWhereClause, false, true,
                                                        (int)EvalActionColumn.StatusID,
                                                        Convert.ToInt32(EnumHelper.GetDescription(evalReq.ActionToPerform)),
                                                        evalReq.UserId, evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords).ToList();
                                }
                                callEvaluationDTO.ForEach(ce => ce.RecIP = rec.IP);
                                recEvals.Add(new RecorderEvaluation
                                {
                                    RecorderId = rec.Id,
                                    RecorderName = rec.Name,
                                    CallEvaluationDTO = callEvaluationDTO,
                                    TotalPages = totalPages,
                                    TotalRecords = totalRecords
                                });
                                break;
                            case EvalActionType.MarkAsRejected:
                                if (rec.IsPrimary)
                                {
                                    callEvaluationDTO = new EvaluationDALEC(evalReq.TenantId).PerformActionAndGetEvaluationDTOFromAllRecorders(rec, evalReq.ActionToPerformOnRecorder, evalReq.CallEvaluationIds, whereClause,
                                                        (int)EvalActionColumn.StatusID,
                                                        (int)EvaluationStatus.Rejected,
                                                        evalReq.UserId, evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string entWhereClause = string.Empty;
                                    entWhereClause = whereClause + " AND entEval.CallEvaluationId = ce.Id AND ce.IsEnterpriseAssociated = 1 AND entEval.EvaluatorId = " + Convert.ToInt32(evalReq.UserId) + " ";
                                    callEvaluationDTO = entClient.PerformActionAndGetEnterpriseEvaluationDTO(rec, evalReq.ActionToPerformOnRecorder, evalReq.CallEvaluationIds, entWhereClause, false, true,
                                                        (int)EvalActionColumn.StatusID,
                                                        Convert.ToInt32(EnumHelper.GetDescription(evalReq.ActionToPerform)),
                                                        evalReq.UserId, evalReq.ActionPerformedDate, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords).ToList();
                                }
                                callEvaluationDTO.ForEach(ce => ce.RecIP = rec.IP);
                                recEvals.Add(new RecorderEvaluation
                                {
                                    RecorderId = rec.Id,
                                    RecorderName = rec.Name,
                                    CallEvaluationDTO = callEvaluationDTO,
                                    TotalPages = totalPages,
                                    TotalRecords = totalRecords
                                });
                                break;
                        }
                        Task.Run(() => RevAuditLogger.WriteInformation(Originator.Evaluation, "GetAllDrilldownChartsFromAllRecorders", evalReq.TenantId, "Evaluations fetched successfully from Recorder Id = " + rec.Id + " Recorder Name = " + rec.Name));
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetEvaluationsFromAllRecorders", evalReq.TenantId, "An error has occurred while fetching evaluations from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name + " evalReq.ActionToPerform = " + evalReq.ActionToPerform.ToString() +  ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }

                return new EvaluationResponse { CallEvaluationDTOs = CallEvaluationDTOs, TotalPages = totalPages, TotalRecords = totalRecords, RecorderEvaluations = recEvals };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetEvaluationsFromAllRecorders", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetEvaluationsFromAllRecorders", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public EvaluationResponse GetRecorderCallEvaluationDetailsById(EvaluationRequest evalReq)
        {
            CallEvaluation callEvaluation = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetRecorderCallEvaluationDetailsById", evalReq.TenantId, "GetRecorderCallEvaluationDetailsById function has been called successfully."));
                if (evalReq.Recorder.IsPrimary)
                    callEvaluation = new EvaluationDALEC(evalReq.TenantId).GetRecorderCallEvaluationDetailsById(evalReq.Recorder, evalReq.CallEvaluationId, evalReq.UserId);
                else
                {
                    // Alternative Approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + evalReq.Recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    callEvaluation = entClient.GetCallEvaluationDetailsById((int)evalReq.CallEvaluationId, evalReq.UserId);
                }
                if (callEvaluation != null)
                {
                    callEvaluation.RecId = evalReq.Recorder.Id;
                    callEvaluation.RecIP = evalReq.Recorder.IP;
                    callEvaluation.RecName = evalReq.Recorder.Name;
                }
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Evaluation, "GetRecorderCallEvaluationDetailsById", evalReq.TenantId, "Call evaluation details by id have been fetched successfully. CallEvaluationId = " + evalReq.CallEvaluationId + " Recorder Id = " + evalReq.Recorder.Id + " Recorder Name = " + evalReq.Recorder.Name));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetRecorderCallEvaluationDetailsById", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetRecorderCallEvaluationDetailsById", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return new EvaluationResponse { CallEvaluation = callEvaluation };
        }
        public CallEvaluation RecorderUpdateCallEvaluation(Recorder recorder, CallEvaluation callEval, int tenantId)
        {
            CallEvaluation callEvaluation = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "RecorderUpdateCallEvaluation", tenantId, "RecorderUpdateCallEvaluation function has been called successfully."));
                callEval.ModifiedDate = DateTime.Now;
                if (callEval.Survey.NoOfQuestions == callEval.Answers.Count)
                    callEval.Status = EvaluationStatus.Completed;
                else
                    callEval.Status = EvaluationStatus.InProgress;
                if (recorder.IsPrimary)
                    callEvaluation = new EvaluationDALEC(tenantId).RecorderUpdateCallEvaluation(recorder, callEval);
                else
                {
                    // Alternative Approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    callEvaluation = entClient.UpdateCallEvaluation(callEval);
                }
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Evaluation, "RecorderUpdateCallEvaluation", tenantId, "Call evaluation has been updated successfully. CallEvaluationId = " + callEval.Id + " Recorder Id = " + recorder.Id + " Recorder Name = " + recorder.Name));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "RecorderUpdateCallEvaluation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "RecorderUpdateCallEvaluation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return callEvaluation;
        }
        public User UpdateAndGetAssociatedUserFromRecorder(Recorder recorder, int evalId, int agentId, int tenantId)
        {
            User user = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "UpdateAndGetAssociatedUserFromRecorder", tenantId, "UpdateAndGetAssociatedUserFromRecorder function has been called successfully."));
                if (recorder.IsPrimary)
                    user = new EvaluationDALEC(tenantId).UpdateAndGetAssociatedUserFromRecorder(recorder, evalId, agentId);
                else
                {
                    // Alternative Approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    user = entClient.UpdateAndGetAssociatedUser(evalId, agentId);
                }
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Evaluation, "UpdateAndGetAssociatedUserFromRecorder", tenantId, "Evaluation successfully associated with Agent. Agent Id = " + agentId + " callEvaluationId = " + evalId + " Recorder Id = " + recorder.Id + " Recorder Name = " + recorder.Name));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "UpdateAndGetAssociatedUserFromRecorder", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "UpdateAndGetAssociatedUserFromRecorder", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return user;
        }
        public short InsertCallEvaluationOnRecorder(Recorder recorder, EvaluationRequest evalReq)
        {
            short returnVal = -1;
            try
            {
                returnVal = new EvaluationDALEC(evalReq.TenantId).InsertCallEvaluationOnRecorder(recorder, evalReq.SurveyId, evalReq.CallId, evalReq.AgentId, evalReq.EvalStatus, evalReq.UserId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Evaluation, "InsertCallEvaluationOnRecorder", evalReq.TenantId, "Evaluation successfully inserted/updated. SurveyId = " + evalReq.SurveyId + "  EvaluationStatus = " + evalReq.EvalStatus + "  Recorder Id = " + recorder.Id + "  Recorder Name = " + recorder.Name));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "InsertCallEvaluationOnRecorder", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "InsertCallEvaluationOnRecorder", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return returnVal;
        }
        public short AddEnterpriseEvaluations(Recorder recorder, EvaluationRequest evalReq, int evaluatorId, string evaluatorName, string EvaluatorEmail)
        {
            short returnVal = -1;
            try
            {
                if (recorder.IsPrimary)
                    returnVal = new EvaluationDALEC(evalReq.TenantId).InsertCallEvaluationOnRecorder(recorder, evalReq.SurveyId, evalReq.CallId, evalReq.AgentId, evalReq.EvalStatus, evalReq.UserId);
                else
                {
                    // Alternative Approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    returnVal = entClient.AddEnterpriseEvaluations(evalReq.CallId, evalReq.UserId, evalReq.SurveyId, evaluatorId, evaluatorName, EvaluatorEmail);
                }
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Evaluation, "AddEnterpriseEvaluations", evalReq.TenantId, "Evaluation successfully inserted/updated. SurveyId = " + evalReq.SurveyId + "  EvaluationStatus = " + evalReq.EvalStatus + "  Recorder Id = " + recorder.Id + "  Recorder Name = " + recorder.Name));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "AddEnterpriseEvaluations", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "AddEnterpriseEvaluations", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return returnVal;
        }
        public bool InsertEnterpriseEvaluations(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Evaluation, "InsertEnterpriseEvaluations", 0, "Calls added successfully for evaluation on recorder. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name));
                // Alternative approach
                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                // Instead of passing userId, pass here user num of administrator.
                int adminUserNum = 1000;
                if (entClient.InsertEnterpriseEvaluations(callIds, adminUserNum, surveyId, evaluatorId, evaluatorName, evaluatorEmail))
                    return true;
                return false;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "InsertEnterpriseEvaluations", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "InsertEnterpriseEvaluations", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public EvaluationResponse GetEvaluationIdFromRecorder(EvaluationRequest evalReq, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetEvaluationIdFromRecorder", evalReq.TenantId, "GetEvaluationIdFromRecorder function has been called successfully."));
                if (recorder.IsPrimary)
                    return new EvaluationResponse { UserEvaluations = new EvaluationDAL(evalReq.TenantId).GetEvaluationId(evalReq.SurveyId, evalReq.CallId, evalReq.UserId) };
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return new EvaluationResponse { UserEvaluations = entClient.GetEvaluationId(evalReq.SurveyId, evalReq.CallId, evalReq.UserId).ToList() };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetEvaluationIdFromRecorder", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetEvaluationIdFromRecorder", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool PerformActionOnRecorder(Recorder recorder, string callSurveyIds, int action, int actionValue, int userId, DateTime? ActionDate, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "PerformActionOnRecorder", tenantId, "PerformActionOnRecorder function has been called successfully."));
                if (recorder.IsPrimary)
                {
                    return new EvaluationDALEC(tenantId).PerformActionOnRecorder(recorder, callSurveyIds, action, actionValue, userId, ActionDate);
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return entClient.PerformActionOnRecorder(recorder, callSurveyIds, action, actionValue, userId, ActionDate);
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "PerformActionOnRecorder", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "PerformActionOnRecorder", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion
        #region Evaluation from Search Tab - Arivu
        public EvaluationResponse GetEvaluationId(EvaluationRequest evalReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetEvaluationId", evalReq.TenantId, "GetEvaluationId function has been called successfully."));
                return new EvaluationResponse { UserEvaluations = new EvaluationDAL(evalReq.TenantId).GetEvaluationId(evalReq.SurveyId, evalReq.CallId, evalReq.UserId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetEvaluationId", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetEvaluationId", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion Evaluation from Search Tab 

        #region Multi Call Evaluation
        public EvaluationResponse GetMultiCallEvaluations(EvaluationRequest evalReq)
        {
            try
            {
                int totalPages = 0;
                int totalRecords = 0;
                List<MultiCallEvaluationGroup> multiCallEvaluationGroups = null;
                string whereClause = getMultiCallEvaluationCriteria(evalReq);
                multiCallEvaluationGroups = new EvaluationDAL(evalReq.TenantId).GetMultiCallEvaluations(whereClause, evalReq.EvaluatorId, evalReq.PageNumber, evalReq.PageSize, out totalPages, out totalRecords);

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetMultiCallEvaluations", evalReq.TenantId, "GetMultiCallEvaluations function has been called successfully."));

                return new EvaluationResponse { MultiCallEvaluationGroups = multiCallEvaluationGroups, TotalPages = totalPages, TotalRecords = totalRecords };

            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetMultiCallEvaluations", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetMultiCallEvaluations", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public EvaluationResponse DeleteMultiCallEvaluation(EvaluationRequest evalReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "DeleteMultiCallEvaluation", evalReq.TenantId, "DeleteMultiCallEvaluation function has been called successfully."));
                var result = new EvaluationDAL(evalReq.TenantId).DeleteMultiCallEvaluation(evalReq.MultiCallEvaluationId);
                return new EvaluationResponse { Status = result };

            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "DeleteMultiCallEvaluation", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "DeleteMultiCallEvaluation", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public EvaluationResponse CloseMultiCallEvaluation(EvaluationRequest evalReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "CloseMultiCallEvaluation", evalReq.TenantId, "CloseMultiCallEvaluation function has been called successfully."));
                var result = new EvaluationDAL(evalReq.TenantId).CloseMultiCallEvaluation(evalReq.MultiCallEvaluationId);
                return new EvaluationResponse { Status = result };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "CloseMultiCallEvaluation", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "CloseMultiCallEvaluation", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public EvaluationResponse GetMultiCallEvaluationDetails(EvaluationRequest evalReq)
        {
            try
            {
                MultiCallEvaluationGroup multiCallEvaluationGroup = null;
                multiCallEvaluationGroup = new EvaluationDAL(evalReq.TenantId).GetMultiCallEvaluationDetails(evalReq.EvaluatorId, evalReq.MultiCallEvaluationId);

                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Evaluation, "GetMultiCallEvaluationDetails", evalReq.TenantId, "GetMultiCallEvaluationDetails function has been called successfully."));
                return new EvaluationResponse { MultiCallEvaluationGroup = multiCallEvaluationGroup };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Evaluation, "GetMultiCallEvaluationDetails", evalReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Evaluation, "GetMultiCallEvaluationDetails", evalReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion
    }
}