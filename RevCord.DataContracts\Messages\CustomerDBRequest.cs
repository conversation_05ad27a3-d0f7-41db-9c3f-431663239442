﻿using RevCord.DataContracts.CustomerDBEntities;
using RevCord.DataContracts.MessageBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class CustomerDBRequest : RequestBase
    {
        public List<IQ3AssetModel> IQ3AssetsModel { get; set; }
        public IQ3AssetModel IQ3AssetModel { get; set; }
        public IQ3AssetAdd IQ3AssetAdd { get; set; }
        public IQ3AssetUpdate IQ3AssetUpdate { get; set; }
        public IQ3AssetStatus Status { get; set; }
        public int AssetId { get; set; }
        public int IQ3AssetModelId { get; set; }
        public string IQ3AssetModelIds { get; set; }
        public int UserNum { get; set; }
        public string SearchText { get; set; }

        public IQ3AssetStatus AssetStatus { get; set; }

        public IQ3AssetHistory IQ3AssetHistory { get; set; }

        public int Id { get; set; }
        public bool VisibleStatus { get; set; }
        public string AssetPhoto { get; set; }
    }
}
