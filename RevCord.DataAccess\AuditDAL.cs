﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.AuditEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class AuditDAL
    {
        private int _tenantId;
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);

        public AuditDAL(int tenantId)
        {
            _tenantId = tenantId;
        }

        public List<Audit> GetAllAudits(DateTime startDate, DateTime endDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords)
        {
            List<Audit> audits = new List<Audit>();
            Audit audit = null;
            try
            {
                using (var conn = DALHelper.GetRevLogDBConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevLog.REV_LOG_AUDITLOG_SEARCH;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Audit, "GetAllAudits", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            audits = new List<Audit>();
                            while (dr.Read())
                            {
                                audit = new Audit();
                                audit.RowNo = Convert.ToInt32(dr["RowNo"]);
                                audit.Id = Convert.ToInt32(dr["Id"]);
                                audit.AppId = Convert.ToInt32(dr["AppId"]);
                                audit.LogCategoryId = Convert.ToInt32(dr["LogCategoryId"]);
                                audit.LogCategory = Enum.GetName(typeof(AuditLogCategory), Convert.ToInt32(dr["LogCategoryId"])); //((AuditLogCategory)Convert.ToInt32(dr["LogCategoryId"])).GetEnumDescription();
                                audit.UserId = (int)dr["UserId"];
                                audit.Message = Convert.ToString(dr["Message"]);
                                audit.Originator = Convert.ToString(dr["Originator"]);
                                audit.FunctionName = Convert.ToString(dr["FunctionName"]);
                                audit.Query = Convert.ToString(dr["Query"]);
                                audit.StackTrace = Convert.ToString(dr["StackTrace"]);
                                audit.ExceptionCode = Convert.ToInt32(dr["ExceptionCode"]);
                                audit.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                audit.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                                audits.Add(audit);
                            }
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return audits;
        }

        public List<Audit> GetAuditsByCategory(int categoryId, DateTime startDate, DateTime endDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords)
        {
            List<Audit> audits = new List<Audit>();
            Audit audit = null;
            try
            {
                using (var conn = DALHelper.GetRevLogDBConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevLog.REV_LOG_AUDITLOG_SEARCH_BY_CATEGORY;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@CategoryId", categoryId);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Audit, "GetAuditsByCategory", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            audits = new List<Audit>();
                            while (dr.Read())
                            {
                                audit = new Audit();
                                audit.RowNo = Convert.ToInt32(dr["RowNo"]);
                                audit.Id = Convert.ToInt32(dr["Id"]);
                                audit.AppId = Convert.ToInt32(dr["AppId"]);
                                audit.LogCategoryId = Convert.ToInt32(dr["LogCategoryId"]);
                                audit.LogCategory = Enum.GetName(typeof(AuditLogCategory), Convert.ToInt32(dr["LogCategoryId"])); //((AuditLogCategory)Convert.ToInt32(dr["LogCategoryId"])).GetEnumDescription();
                                audit.UserId = (int)dr["UserId"];
                                audit.Message = Convert.ToString(dr["Message"]);
                                audit.Originator = Convert.ToString(dr["Originator"]);
                                audit.FunctionName = Convert.ToString(dr["FunctionName"]);
                                audit.Query = Convert.ToString(dr["Query"]);
                                audit.StackTrace = Convert.ToString(dr["StackTrace"]);
                                audit.ExceptionCode = Convert.ToInt32(dr["ExceptionCode"]);
                                audit.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                audit.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                                audits.Add(audit);
                            }
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return audits;
        }

        public int GetAuditLogsCount(int categoryId, DateTime startDate, DateTime endDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords)
        {
            List<Audit> audits = new List<Audit>();
            try
            {
                using (var conn = DALHelper.GetRevLogDBConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevLog.REV_LOG_AUDITLOG_SEARCH_BY_CATEGORY;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@CategoryId", categoryId);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Audit, "GetAuditLogsCount", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return totalRecords;
        }

        public Audit GetAuditDetails(int auditId)
        {
            Audit audit = new Audit();
            try
            {
                using (var conn = DALHelper.GetRevLogDBConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM rlApplicationLog WHERE Id=@Id";
                    cmd.Parameters.AddWithValue("@Id", auditId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Audit, "GetAuditDetails", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                audit = new Audit();
                                audit.Id = Convert.ToInt32(dr["Id"]);
                                audit.AppId = Convert.ToInt32(dr["AppId"]);
                                audit.LogCategoryId = Convert.ToInt32(dr["LogCategoryId"]);
                                audit.LogCategory = Enum.GetName(typeof(AuditLogCategory), Convert.ToInt32(dr["LogCategoryId"])); //((AuditLogCategory)Convert.ToInt32(dr["LogCategoryId"])).GetEnumDescription();
                                audit.UserId = (int)dr["UserId"];
                                audit.Message = Convert.ToString(dr["Message"]);
                                audit.Originator = Convert.ToString(dr["Originator"]);
                                audit.FunctionName = Convert.ToString(dr["FunctionName"]);
                                audit.Query = Convert.ToString(dr["Query"]);
                                audit.StackTrace = Convert.ToString(dr["StackTrace"]);
                                audit.ExceptionCode = Convert.ToInt32(dr["ExceptionCode"]);
                                audit.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                audit.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return audit;
        }
    }
}
