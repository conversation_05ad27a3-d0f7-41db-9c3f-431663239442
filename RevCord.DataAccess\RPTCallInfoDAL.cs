﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data.SqlClient;
using System.Data;
using RevCord.DataContracts.ReportEntities;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.Response;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.Util;
using RevCord.DataContracts;

namespace RevCord.DataAccess
{
    public class RPTCallInfoDAL
    {
        private int _tenantId;
        public RPTCallInfoDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        public List<RPTCallInfo> GetSearchResults(ReportRequest reportRequest)
        {
            SqlConnection con = DALHelper.GetConnection(_tenantId);
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);
            cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", reportRequest.IsOnlyIQ3ModeEnabled);
            cmd.Parameters.AddWithValue("@JOINString", reportRequest.JOINString);
            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);

            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;

            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSearchResults", _tenantId));

                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"])) 
                                     { }
                                     ).ToList();
                //).OrderBy(c => c.CallDate).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }

        public List<RPTCallInfoDetail> GetDetailSearchResults(ReportRequest reportRequest)
        {
            SqlConnection con = DALHelper.GetConnection(_tenantId);
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_CALLDETAILS_GETALL, con);
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            //cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            //cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            //cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            //cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            //cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            //cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);

            DataTable dtCallInfoDetails = new DataTable();
            List<RPTCallInfoDetail> detailResults = null;

            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetDetailSearchResults", _tenantId));

                dataAdapter.Fill(dtCallInfoDetails);
                detailResults = (from DataRow row in dtCallInfoDetails.Rows
                                 select new RPTCallInfoDetail(
                                     row["Key"].ToString(),
                                     row["CallID"].ToString(),
                                     row["UserName"].ToString(),
                                     row["GroupName"].ToString(),
                                     row["ChannelName"].ToString(),
                                     row["Ext"].ToString(),
                                     row["StartTime"].ToString(),
                                     Convert.ToInt64(row["Duration"]),
                                     (DayOfWeek)Enum.Parse(typeof(DayOfWeek), (Convert.ToInt32(row["Day"]) - 1).ToString())) { }
                                     ).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            detailResults.TrimExcess();
            return detailResults;
        }


        public ReportResponse GetCallSearchResults(ReportRequest reportRequest)
        {
            SqlConnection con = DALHelper.GetConnection(_tenantId);
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);
            cmd.Parameters.AddWithValue("@getCallDetails", reportRequest.GetDetailReportData);
            cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", reportRequest.IsOnlyIQ3ModeEnabled);
            cmd.Parameters.AddWithValue("@JOINString", reportRequest.JOINString);
            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataSet dsCalls = new DataSet();
            List<RPTCallInfo> searchResults = null;
            List<RPTCallInfoDetail> detailResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetCallSearchResults", _tenantId));

                dataAdapter.Fill(dsCalls);
                searchResults = (from DataRow row in dsCalls.Tables[0].Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"])) { }
                                     ).ToList();
                                    //).OrderBy(c => c.CallDate).ToList();
                if (reportRequest.GetDetailReportData)
                {
                    detailResults = (from DataRow row in dsCalls.Tables[1].Rows
                                     select new RPTCallInfoDetail(
                                         row["Key"].ToString(),
                                         row["CallID"].ToString(),
                                         row["UserName"].ToString(),
                                         row["GroupName"].ToString(),
                                         row["ChannelName"].ToString(),
                                         row["Ext"].ToString(),
                                         row["StartTime"].ToString(),
                                         Convert.ToInt64(row["Duration"])) { }
                                         ).ToList();
                }
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess(); detailResults.TrimExcess();
            return new ReportResponse { CallInfos = searchResults, CallInfoDetails = detailResults };
        }



        public List<RPTCallInfo> GetSearchResultsMonthDayOfWeek(ReportRequest reportRequest)
        {
            SqlConnection con = DALHelper.GetConnection(_tenantId);
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);
            cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", reportRequest.IsOnlyIQ3ModeEnabled);
            cmd.Parameters.AddWithValue("@JOINString", reportRequest.JOINString);
            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> monthWeekDays = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSearchResultsMonthDayOfWeek", _tenantId));
                dataAdapter.Fill(dt);
                monthWeekDays = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     (DayOfWeek)Enum.Parse(typeof(DayOfWeek), (Convert.ToInt32(row["Day"]) - 1).ToString()),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"])) { }
                                     ).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            monthWeekDays.TrimExcess();
            return monthWeekDays;
        }



        public List<RPTCallInfo> GetSearchResultsDayOfWeek(ReportRequest reportRequest)
        {
            SqlConnection con = DALHelper.GetConnection(_tenantId);
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);
            cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", reportRequest.IsOnlyIQ3ModeEnabled);
            cmd.Parameters.AddWithValue("@JOINString", reportRequest.JOINString);
            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSearchResultsDayOfWeek", _tenantId));

                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo
                                 {
                                     Key = row["Key"].ToString(),
                                     DayOfWeek = (DayOfWeek)Enum.Parse(typeof(DayOfWeek), (Convert.ToInt32(row["Day"]) - 1).ToString()),
                                     Count = Convert.ToInt64(row["Count"]),
                                     Total = Convert.ToInt64(row["Total"]),
                                     Avg = Convert.ToInt64(row["Avg"])
                                 }).ToList();

            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }


        public List<RPTCallInfo> GetSearchResultsHour(ReportRequest reportRequest)
        {
            SqlConnection con = DALHelper.GetConnection(_tenantId);
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_HOUR_GETALL, con);
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSearchResultsHour", _tenantId));

                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt32(row["CallHour"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"])) { }
                                     ).ToList();
                //).OrderBy(c => c.CallDate).ToList();

            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }



        public bool CollectDataFromLinkedDatabases(string startDate, string endDate)
        {
            try
            {
                SqlConnection con = DALHelper.GetConnection(_tenantId);
                SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.COLLECT_DATA_FROM_LINKED_DATABASES, con);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@s_Date", startDate);
                cmd.Parameters.AddWithValue("@e_Date", endDate);
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "CollectDataFromLinkedDatabases", _tenantId));

                cmd.ExecuteNonQuery();
                return true;
            }
            catch (Exception ex) { throw new Exception(ex.Message, ex); }
        }

    }
}