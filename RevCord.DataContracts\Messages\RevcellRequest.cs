﻿using RevCord.DataContracts.RevcellEntities;
using RevCord.DataContracts.TenantEntities;

namespace RevCord.DataContracts.Messages
{
    public class RevcellRequest
    {
        public string SIPId { get; set; }
        public string SIPName { get; set; }
        public int Ext { get; set; }
        public int UserNum { get; set; }
        public string UserName { get; set; }
        public string UserEmail { get; set; }
        public string Password { get; set; }
        public string SendAs { get; set; }

        public string PhoneNumberId { get; set; }
        public string PhoneNumber { get; set; }
        public string PhoneName { get; set; }

        public string SystemSerialNumber { get; set; }

        public int TenantId { get; set; }

        public PhoneNumber PhoneNumberObj { get; set; }

        #region Plivo
        public string EndpointId { get; set; }
        public string EndpointUserName { get; set; }
        public string Alias { get; set; }
        public PlivoTenantConfiguration PlivoConfig { get; set; }
        #endregion
    }
}