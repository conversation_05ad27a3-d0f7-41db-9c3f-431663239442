﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RevCord.Util
{
    public static class RPTCollectionHelper
    {
        public static Tuple<string, string, string> GetTuple(string reportType, string xAxis, string yAxis)
        {
            return new Tuple<string, string, string>(reportType, xAxis, yAxis);
        }

        public static Tuple<T1, T2, T3, T4> GetTuple<T1, T2, T3, T4>(T1 reportType, T2 reportSubType, T3 xAxis, T4 yAxis)
        {
            return new Tuple<T1, T2, T3, T4>(reportType, reportSubType, xAxis, yAxis);
        }

        //private List<Tuple<int, string>> GetDetails()
        //{
        //    return (from r in dataContext.Tabl
        //            select new
        //                Tuple<int, string>
        //                (r.ID, r.Name)
        //                ).ToList();
        //}

    }
}