<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd7" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Criteria" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd10" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Response" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd12" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd13" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Messages" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd16" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd8" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd17" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.EvaluationEntities" />
  <xs:element name="CallAuditSave">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="callId" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userNum" type="xs:int" />
        <xs:element minOccurs="0" name="ipAddress" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="isSaved" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CallAuditSaveResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CallAuditSaveResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallAuditTrail">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="startDate" type="xs:dateTime" />
        <xs:element minOccurs="0" name="endDate" type="xs:dateTime" />
        <xs:element minOccurs="0" name="callId" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallAuditTrailResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetCallAuditTrailResult" nillable="true" type="q1:ArrayOfCallAuditReport" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAuditedCallsByExtension">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="startDate" type="xs:dateTime" />
        <xs:element minOccurs="0" name="endDate" type="xs:dateTime" />
        <xs:element minOccurs="0" name="ext" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAuditedCallsByExtensionResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetAuditedCallsByExtensionResult" nillable="true" type="q2:ArrayOfCallAuditReport" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetUserActivities">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="startDate" type="xs:dateTime" />
        <xs:element minOccurs="0" name="endDate" type="xs:dateTime" />
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetUserActivitiesResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="GetUserActivitiesResult" nillable="true" type="q3:ArrayOfUserActivity" />
        <xs:element minOccurs="0" name="totalPages" type="xs:int" />
        <xs:element minOccurs="0" name="totalRecords" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallsByLocation">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="callInfoSearchCriteriaDTO" nillable="true" type="q4:CallInfoSearchCriteriaDTO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallsByLocationResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="GetCallsByLocationResult" nillable="true" type="q5:ArrayOfCallInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDefaultSearchResultsDTO">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="startDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="startTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="isGlobal" type="xs:boolean" />
        <xs:element minOccurs="0" name="customSpParam" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="recId" type="xs:int" />
        <xs:element minOccurs="0" name="recName" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDefaultSearchResultsDTOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="GetDefaultSearchResultsDTOResult" nillable="true" type="q6:DALMediaResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAdvanceSearchResultsDTO">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="startDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="startTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="isGlobal" type="xs:boolean" />
        <xs:element minOccurs="0" name="customSpParam" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="recId" type="xs:int" />
        <xs:element minOccurs="0" name="recName" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAdvanceSearchResultsDTOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="GetAdvanceSearchResultsDTOResult" nillable="true" type="q7:DALMediaResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PerformSearchChainedDBs">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Criteria" minOccurs="0" name="callCriteria" nillable="true" type="q8:CallCriteria" />
        <xs:element minOccurs="0" name="customSpParam" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="recId" type="xs:int" />
        <xs:element minOccurs="0" name="recName" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PerformSearchChainedDBsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q9="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="PerformSearchChainedDBsResult" nillable="true" type="q9:DALMediaResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDefaultQASearchResultsDTO">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="tenantId" type="xs:int" />
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element xmlns:q10="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Criteria" minOccurs="0" name="callCriteria" nillable="true" type="q10:CallCriteria" />
        <xs:element minOccurs="0" name="customSpParam" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDefaultQASearchResultsDTOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q11="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="GetDefaultQASearchResultsDTOResult" nillable="true" type="q11:DALMediaResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAdvanceQASearchResultsDTO">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="tenantId" type="xs:int" />
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element xmlns:q12="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Criteria" minOccurs="0" name="callCriteria" nillable="true" type="q12:CallCriteria" />
        <xs:element minOccurs="0" name="customSpParam" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAdvanceQASearchResultsDTOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q13="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="GetAdvanceQASearchResultsDTOResult" nillable="true" type="q13:DALMediaResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PerformQASearchChainedDBs">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="tenantId" type="xs:int" />
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element xmlns:q14="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Criteria" minOccurs="0" name="callCriteria" nillable="true" type="q14:CallCriteria" />
        <xs:element minOccurs="0" name="customSpParam" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PerformQASearchChainedDBsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q15="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="PerformQASearchChainedDBsResult" nillable="true" type="q15:DALMediaResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetUserExtensionInfos">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="recId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetUserExtensionInfosResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q16="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="GetUserExtensionInfosResult" nillable="true" type="q16:ArrayOfUserExtensionInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAppUsers">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q17="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q17:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAppUsersResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q18="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Response" minOccurs="0" name="GetAppUsersResult" nillable="true" type="q18:UserManagementResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAppUserAccount">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q19="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q19:Recorder" />
        <xs:element minOccurs="0" name="uId" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAppUserAccountResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q20="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Response" minOccurs="0" name="GetAppUserAccountResult" nillable="true" type="q20:UserManagementResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RecoverAppUserAccount">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q21="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q21:Recorder" />
        <xs:element minOccurs="0" name="userIds" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RecoverAppUserAccountResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="RecoverAppUserAccountResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateAppUserAccount">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q22="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q22:Recorder" />
        <xs:element xmlns:q23="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="appUser" nillable="true" type="q23:User" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateAppUserAccountResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q24="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="UpdateAppUserAccountResult" nillable="true" type="q24:ArrayOfUser" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteAppUserAccount">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q25="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q25:Recorder" />
        <xs:element minOccurs="0" name="id" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteAppUserAccountResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q26="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Response" minOccurs="0" name="DeleteAppUserAccountResult" nillable="true" type="q26:UserManagementResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveAssignedGroup">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q27="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q27:Recorder" />
        <xs:element minOccurs="0" name="uId" type="xs:long" />
        <xs:element minOccurs="0" name="gId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveAssignedGroupResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="SaveAssignedGroupResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteUserGroup">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q28="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q28:Recorder" />
        <xs:element xmlns:q29="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="uGroup" nillable="true" type="q29:UserGroup" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteUserGroupResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DeleteUserGroupResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetUserData">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q30="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q30:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetUserDataResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q31="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="GetUserDataResult" nillable="true" type="q31:ArrayOfUserInfoLite" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="FetchAllActiveUsers">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="FetchAllActiveUsersResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q32="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="FetchAllActiveUsersResult" nillable="true" type="q32:ArrayOfUser" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTreeViewFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q33="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q33:Recorder" />
        <xs:element minOccurs="0" name="userNum" type="xs:int" />
        <xs:element minOccurs="0" name="userId" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="authNum" type="xs:int" />
        <xs:element minOccurs="0" name="authType" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="type" type="xs:int" />
        <xs:element minOccurs="0" name="getOnlyAudioChannels" type="xs:boolean" />
        <xs:element minOccurs="0" name="selectType" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTreeViewFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q34="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="GetTreeViewFromRecorderResult" nillable="true" type="q34:ArrayOfTreeViewDataDTO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetGroupsTree">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="userNum" type="xs:int" />
        <xs:element minOccurs="0" name="userId" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="authNum" type="xs:int" />
        <xs:element minOccurs="0" name="authType" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="type" type="xs:int" />
        <xs:element minOccurs="0" name="userType" type="xs:int" />
        <xs:element xmlns:q35="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q35:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetGroupsTreeResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q36="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="GetGroupsTreeResult" nillable="true" type="q36:ArrayOfTreeviewData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetGroupsTreeFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q37="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q37:Recorder" />
        <xs:element minOccurs="0" name="userNum" type="xs:int" />
        <xs:element minOccurs="0" name="userId" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="authNum" type="xs:int" />
        <xs:element minOccurs="0" name="authType" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="type" type="xs:int" />
        <xs:element minOccurs="0" name="userType" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetGroupsTreeFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q38="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="GetGroupsTreeFromRecorderResult" nillable="true" type="q38:ArrayOfTreeviewData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetGroupsTreeNonAdminFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q39="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q39:Recorder" />
        <xs:element minOccurs="0" name="userNum" type="xs:int" />
        <xs:element minOccurs="0" name="userId" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="authNum" type="xs:int" />
        <xs:element minOccurs="0" name="authType" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="type" type="xs:int" />
        <xs:element minOccurs="0" name="userType" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetGroupsTreeNonAdminFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q40="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="GetGroupsTreeNonAdminFromRecorderResult" nillable="true" type="q40:ArrayOfTreeviewData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetUsersTreeFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q41="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q41:Recorder" />
        <xs:element minOccurs="0" name="userNum" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetUsersTreeFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q42="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="GetUsersTreeFromRecorderResult" nillable="true" type="q42:ArrayOfTreeviewData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetInquireGroupsTreeforEvaluationReportFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q43="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q43:Recorder" />
        <xs:element minOccurs="0" name="userNum" type="xs:int" />
        <xs:element minOccurs="0" name="selectType" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetInquireGroupsTreeforEvaluationReportFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q44="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="GetInquireGroupsTreeforEvaluationReportFromRecorderResult" nillable="true" type="q44:ArrayOfTreeviewData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetMDGroupsTreeForEvaluationReportFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q45="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q45:Recorder" />
        <xs:element minOccurs="0" name="userNum" type="xs:int" />
        <xs:element minOccurs="0" name="selectType" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetMDGroupsTreeForEvaluationReportFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q46="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="GetMDGroupsTreeForEvaluationReportFromRecorderResult" nillable="true" type="q46:ArrayOfTreeviewData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEnterpriseUserRightsData">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q47="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Messages" minOccurs="0" name="umRequest" nillable="true" type="q47:UMRequest" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEnterpriseUserRightsDataResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q48="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Messages" minOccurs="0" name="GetEnterpriseUserRightsDataResult" nillable="true" type="q48:UMResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEnterpriseUserTree">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="userNum" type="xs:int" />
        <xs:element minOccurs="0" name="userId" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="authNum" type="xs:int" />
        <xs:element minOccurs="0" name="authType" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="type" type="xs:int" />
        <xs:element minOccurs="0" name="userType" type="xs:int" />
        <xs:element xmlns:q49="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q49:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEnterpriseUserTreeResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q50="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="GetEnterpriseUserTreeResult" nillable="true" type="q50:ArrayOfTreeviewData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetChannelsToMonitorFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q51="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q51:Recorder" />
        <xs:element minOccurs="0" name="whereClause" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetChannelsToMonitorFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q52="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="GetChannelsToMonitorFromRecorderResult" nillable="true" type="q52:ArrayOfMonitorChannel" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSurveysFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q53="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q53:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSurveysFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q54="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="GetSurveysFromRecorderResult" nillable="true" type="q54:ArrayOfSurvey" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetPublishedSurveysFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q55="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q55:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetPublishedSurveysFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q56="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="GetPublishedSurveysFromRecorderResult" nillable="true" type="q56:ArrayOfSurvey" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteAndGetSurveysFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element xmlns:q57="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q57:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteAndGetSurveysFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q58="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="DeleteAndGetSurveysFromRecorderResult" nillable="true" type="q58:ArrayOfSurvey" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PublishAndGetSurveysFromRecorders">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element xmlns:q59="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q59:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PublishAndGetSurveysFromRecordersResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q60="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="PublishAndGetSurveysFromRecordersResult" nillable="true" type="q60:ArrayOfSurvey" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSurveyDetailsFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element xmlns:q61="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q61:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSurveyDetailsFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q62="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="GetSurveyDetailsFromRecorderResult" nillable="true" type="q62:Survey" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="IsSurveyExistOnRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="surveyTitle" nillable="true" type="xs:string" />
        <xs:element xmlns:q63="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q63:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="IsSurveyExistOnRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="IsSurveyExistOnRecorderResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreateSurveyOnRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q64="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="survey" nillable="true" type="q64:Survey" />
        <xs:element xmlns:q65="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q65:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreateSurveyOnRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q66="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="CreateSurveyOnRecorderResult" nillable="true" type="q66:Survey" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreateQuestionOnRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q67="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="sQuestion" nillable="true" type="q67:Question" />
        <xs:element xmlns:q68="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q68:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreateQuestionOnRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CreateQuestionOnRecorderResult" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreateAndGetSectionsOnRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q69="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="surveyGroup" nillable="true" type="q69:SurveySection" />
        <xs:element xmlns:q70="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q70:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreateAndGetSectionsOnRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q71="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="CreateAndGetSectionsOnRecorderResult" nillable="true" type="q71:ArrayOfSurveySection" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetQuestionsBySurveyIdFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element xmlns:q72="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q72:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetQuestionsBySurveyIdFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q73="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="GetQuestionsBySurveyIdFromRecorderResult" nillable="true" type="q73:ArrayOfQuestion" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSectionsBySurveyIdFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element xmlns:q74="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q74:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSectionsBySurveyIdFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q75="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="GetSectionsBySurveyIdFromRecorderResult" nillable="true" type="q75:ArrayOfSurveySection" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateQuestionSectionOnRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="questionId" type="xs:int" />
        <xs:element minOccurs="0" name="sectionId" type="xs:int" />
        <xs:element xmlns:q76="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q76:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateQuestionSectionOnRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="UpdateQuestionSectionOnRecorderResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateAndGetSectionsFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q77="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="surveyGroup" nillable="true" type="q77:SurveySection" />
        <xs:element xmlns:q78="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q78:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateAndGetSectionsFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q79="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="UpdateAndGetSectionsFromRecorderResult" nillable="true" type="q79:ArrayOfSurveySection" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteAndGetSectionsFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="sectionId" type="xs:int" />
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element xmlns:q80="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q80:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteAndGetSectionsFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q81="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="DeleteAndGetSectionsFromRecorderResult" nillable="true" type="q81:ArrayOfSurveySection" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AssignUnAssignQuestionsAndGetSectionsFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element minOccurs="0" name="sectionId" type="xs:int" />
        <xs:element minOccurs="0" name="assignedQuestions" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="unassignedQuestions" nillable="true" type="xs:string" />
        <xs:element xmlns:q82="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q82:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AssignUnAssignQuestionsAndGetSectionsFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q83="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="AssignUnAssignQuestionsAndGetSectionsFromRecorderResult" nillable="true" type="q83:ArrayOfSurveySection" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSurveyFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element xmlns:q84="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q84:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSurveyFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q85="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="GetSurveyFromRecorderResult" nillable="true" type="q85:Survey" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateSurveyOnRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q86="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="survey" nillable="true" type="q86:Survey" />
        <xs:element xmlns:q87="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q87:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateSurveyOnRecorderResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateQuestionOnRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q88="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="eQuestion" nillable="true" type="q88:Question" />
        <xs:element xmlns:q89="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q89:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateQuestionOnRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="UpdateQuestionOnRecorderResult" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteQuestionFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="id" type="xs:long" />
        <xs:element xmlns:q90="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q90:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteQuestionFromRecorderResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateIsPublishedOnRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element xmlns:q91="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q91:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateIsPublishedOnRecorderResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateQuestionsOrderOnRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q92="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.SurveyEntities" minOccurs="0" name="eRequest" nillable="true" type="q92:ArrayOfQuestion" />
        <xs:element xmlns:q93="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q93:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateQuestionsOrderOnRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="UpdateQuestionsOrderOnRecorderResult" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SearchCallsPrimaryDB">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="startDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="startTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="isGlobalSearch" type="xs:boolean" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element xmlns:q94="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q94:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SearchCallsPrimaryDBResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q95="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="SearchCallsPrimaryDBResult" nillable="true" type="q95:ArrayOfCallInfo" />
        <xs:element minOccurs="0" name="totalPages" type="xs:int" />
        <xs:element minOccurs="0" name="totalRecords" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SearchRandomCallsPrimaryDB">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="startDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="startTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="isGlobalSearch" type="xs:boolean" />
        <xs:element minOccurs="0" name="isRandom" type="xs:boolean" />
        <xs:element minOccurs="0" name="isPercentage" type="xs:boolean" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="noOfCalls" type="xs:int" />
        <xs:element xmlns:q96="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q96:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SearchRandomCallsPrimaryDBResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q97="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="SearchRandomCallsPrimaryDBResult" nillable="true" type="q97:ArrayOfCallInfo" />
        <xs:element minOccurs="0" name="totalPages" type="xs:int" />
        <xs:element minOccurs="0" name="totalRecords" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SearchCallsChainedDBs">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="startDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="startTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="isGlobalSearch" type="xs:boolean" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element xmlns:q98="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q98:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SearchCallsChainedDBsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q99="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="SearchCallsChainedDBsResult" nillable="true" type="q99:ArrayOfCallInfo" />
        <xs:element minOccurs="0" name="totalPages" type="xs:int" />
        <xs:element minOccurs="0" name="totalRecords" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateCallsCustomFields">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q100="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q100:Recorder" />
        <xs:element minOccurs="0" name="callIds" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fieldType" type="xs:int" />
        <xs:element minOccurs="0" name="fieldText" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateCallsCustomFieldsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="UpdateCallsCustomFieldsResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateCallCustomFields">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q101="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q101:Recorder" />
        <xs:element minOccurs="0" name="callId" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="tag1" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="tag2" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="tag3" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="tag4" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="custName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="callComments" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateCallCustomFieldsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="UpdateCallCustomFieldsResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateCallRetention">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q102="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q102:Recorder" />
        <xs:element minOccurs="0" name="callId" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="retainValue" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateCallRetentionResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="UpdateCallRetentionResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="Inquiremarkerupdate">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q103="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q103:Recorder" />
        <xs:element minOccurs="0" name="callId" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="markerid" type="xs:int" />
        <xs:element minOccurs="0" name="markertext" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="markernotes" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="InquiremarkerupdateResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="InquiremarkerupdateResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallsByIds">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q104="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q104:Recorder" />
        <xs:element minOccurs="0" name="callIds" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallsByIdsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q105="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="GetCallsByIdsResult" nillable="true" type="q105:ArrayOfCallInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="InsertBookmarkAndGetByCallId">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q106="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q106:Recorder" />
        <xs:element xmlns:q107="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="bookmark" nillable="true" type="q107:Bookmark" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="InsertBookmarkAndGetByCallIdResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q108="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="InsertBookmarkAndGetByCallIdResult" nillable="true" type="q108:ArrayOfstring" />
        <xs:element minOccurs="0" name="rowsAffected" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateBookmarkAndGetByCallId">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q109="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q109:Recorder" />
        <xs:element xmlns:q110="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="bookmark" nillable="true" type="q110:Bookmark" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateBookmarkAndGetByCallIdResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q111="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="UpdateBookmarkAndGetByCallIdResult" nillable="true" type="q111:ArrayOfstring" />
        <xs:element minOccurs="0" name="rowsAffected" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallInfoExportResults">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="startDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="startTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="isGlobalSearch" type="xs:boolean" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element xmlns:q112="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q112:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallInfoExportResultsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q113="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="GetCallInfoExportResultsResult" nillable="true" type="q113:ArrayOfCallInfoExportResult" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallInfoExportResultsByIds">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q114="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q114:Recorder" />
        <xs:element minOccurs="0" name="callIds" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="startDate" type="xs:dateTime" />
        <xs:element minOccurs="0" name="endDate" type="xs:dateTime" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallInfoExportResultsByIdsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q115="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="GetCallInfoExportResultsByIdsResult" nillable="true" type="q115:ArrayOfCallInfoExportResult" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAllDrilldownChartsFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q116="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q116:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAllDrilldownChartsFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q117="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" minOccurs="0" name="GetAllDrilldownChartsFromRecorderResult" nillable="true" type="q117:RecorderEvaluation" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="InsertCallsForEvaluation">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="calls" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="InsertCallsForEvaluationResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="InsertCallsForEvaluationResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="InsertEnterpriseEvaluations">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="callIds" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element minOccurs="0" name="evaluatorId" type="xs:int" />
        <xs:element minOccurs="0" name="evaluatorName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="evaluatorEmail" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="InsertEnterpriseEvaluationsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="InsertEnterpriseEvaluationsResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddEnterpriseEvaluations">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="callIds" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element minOccurs="0" name="evaluatorId" type="xs:int" />
        <xs:element minOccurs="0" name="evaluatorName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="evaluatorEmail" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddEnterpriseEvaluationsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AddEnterpriseEvaluationsResult" type="xs:short" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEvaluations">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q118="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q118:Recorder" />
        <xs:element minOccurs="0" name="whereClause" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="SharedRequired" type="xs:boolean" />
        <xs:element minOccurs="0" name="IsEvaluatorSearch" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEvaluationsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q119="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="GetEvaluationsResult" nillable="true" type="q119:ArrayOfCallEvaluationDTO" />
        <xs:element minOccurs="0" name="totalPages" type="xs:int" />
        <xs:element minOccurs="0" name="totalRecords" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEnterpriseAssociatedEvaluations">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q120="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q120:Recorder" />
        <xs:element minOccurs="0" name="whereClause" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="SharedRequired" type="xs:boolean" />
        <xs:element minOccurs="0" name="IsEvaluatorSearch" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEnterpriseAssociatedEvaluationsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q121="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="GetEnterpriseAssociatedEvaluationsResult" nillable="true" type="q121:ArrayOfCallEvaluationDTO" />
        <xs:element minOccurs="0" name="totalPages" type="xs:int" />
        <xs:element minOccurs="0" name="totalRecords" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetUsers">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q122="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q122:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetUsersResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q123="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="GetUsersResult" nillable="true" type="q123:ArrayOfUser" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PerformActionAndGetEnterpriseEvaluationDTO">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q124="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q124:Recorder" />
        <xs:element minOccurs="0" name="actionToPerformOnRecorder" type="xs:int" />
        <xs:element minOccurs="0" name="callSurveyIds" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="whereClause" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="SharedRequired" type="xs:boolean" />
        <xs:element minOccurs="0" name="IsEvaluatorSearch" type="xs:boolean" />
        <xs:element minOccurs="0" name="action" type="xs:int" />
        <xs:element minOccurs="0" name="actionValue" type="xs:int" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="ActionDate" nillable="true" type="xs:dateTime" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PerformActionAndGetEnterpriseEvaluationDTOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q125="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="PerformActionAndGetEnterpriseEvaluationDTOResult" nillable="true" type="q125:ArrayOfCallEvaluationDTO" />
        <xs:element minOccurs="0" name="totalPages" type="xs:int" />
        <xs:element minOccurs="0" name="totalRecords" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ShareUnshareAndGetEvaluationDTO">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q126="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q126:Recorder" />
        <xs:element minOccurs="0" name="actionToPerformOnRecorder" type="xs:int" />
        <xs:element minOccurs="0" name="callSurveyIds" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="whereClause" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="action" type="xs:int" />
        <xs:element minOccurs="0" name="actionValue" type="xs:int" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="shareWith" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="isSharedEvaluatorRetains" type="xs:boolean" />
        <xs:element minOccurs="0" name="ActionDate" nillable="true" type="xs:dateTime" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ShareUnshareAndGetEvaluationDTOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q127="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="ShareUnshareAndGetEvaluationDTOResult" nillable="true" type="q127:ArrayOfCallEvaluationDTO" />
        <xs:element minOccurs="0" name="totalPages" type="xs:int" />
        <xs:element minOccurs="0" name="totalRecords" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallEvaluationDetailsById">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="callEvaluationId" type="xs:int" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallEvaluationDetailsByIdResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q128="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.EvaluationEntities" minOccurs="0" name="GetCallEvaluationDetailsByIdResult" nillable="true" type="q128:CallEvaluation" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateCallEvaluation">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q129="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.EvaluationEntities" minOccurs="0" name="callEvaluation" nillable="true" type="q129:CallEvaluation" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateCallEvaluationResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q130="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.EvaluationEntities" minOccurs="0" name="UpdateCallEvaluationResult" nillable="true" type="q130:CallEvaluation" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateAndGetAssociatedUser">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="evalId" type="xs:int" />
        <xs:element minOccurs="0" name="agentId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateAndGetAssociatedUserResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q131="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="UpdateAndGetAssociatedUserResult" nillable="true" type="q131:User" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEvaluationId">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="surveyId" type="xs:int" />
        <xs:element minOccurs="0" name="callId" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEvaluationIdResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q132="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.EvaluationEntities" minOccurs="0" name="GetEvaluationIdResult" nillable="true" type="q132:ArrayOfUserEvaluation" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PerformActionOnRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q133="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q133:Recorder" />
        <xs:element minOccurs="0" name="callSurveyIds" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="action" type="xs:int" />
        <xs:element minOccurs="0" name="actionValue" type="xs:int" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
        <xs:element minOccurs="0" name="ActionDate" nillable="true" type="xs:dateTime" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="PerformActionOnRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="PerformActionOnRecorderResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SearchRecordedCalls">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pageSize" type="xs:int" />
        <xs:element minOccurs="0" name="pageIndex" type="xs:int" />
        <xs:element minOccurs="0" name="startDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="startTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="endTime" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="isGlobalSearch" type="xs:boolean" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element xmlns:q134="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q134:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SearchRecordedCallsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q135="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="SearchRecordedCallsResult" nillable="true" type="q135:ArrayOfCallInfo" />
        <xs:element minOccurs="0" name="totalPages" type="xs:int" />
        <xs:element minOccurs="0" name="totalRecords" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallDetailsFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q136="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q136:Recorder" />
        <xs:element minOccurs="0" name="callId" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallDetailsFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q137="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="GetCallDetailsFromRecorderResult" nillable="true" type="q137:CallInfoLite" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSearchResults">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="selectKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fromDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="toDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="timeRange" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="optionString" nillable="true" type="xs:string" />
        <xs:element xmlns:q138="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q138:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSearchResultsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q139="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetSearchResultsResult" nillable="true" type="q139:ArrayOfRPTCallInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDetailSearchResults">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="selectKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fromDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="toDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="timeRange" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="optionString" nillable="true" type="xs:string" />
        <xs:element xmlns:q140="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q140:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDetailSearchResultsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q141="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetDetailSearchResultsResult" nillable="true" type="q141:ArrayOfRPTCallInfoDetail" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSearchResultsMonthDayOfWeek">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="selectKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fromDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="toDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="timeRange" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="optionString" nillable="true" type="xs:string" />
        <xs:element xmlns:q142="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q142:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSearchResultsMonthDayOfWeekResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q143="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetSearchResultsMonthDayOfWeekResult" nillable="true" type="q143:ArrayOfRPTCallInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSearchResultsDayOfWeek">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="selectKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fromDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="toDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="timeRange" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="optionString" nillable="true" type="xs:string" />
        <xs:element xmlns:q144="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q144:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSearchResultsDayOfWeekResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q145="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetSearchResultsDayOfWeekResult" nillable="true" type="q145:ArrayOfRPTCallInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSearchResultsHour">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="selectKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fromDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="toDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="timeRange" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="optionString" nillable="true" type="xs:string" />
        <xs:element xmlns:q146="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q146:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSearchResultsHourResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q147="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetSearchResultsHourResult" nillable="true" type="q147:ArrayOfRPTCallInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallByIdFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q148="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q148:Recorder" />
        <xs:element minOccurs="0" name="callId" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallByIdFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q149="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="GetCallByIdFromRecorderResult" nillable="true" type="q149:CallInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSearchResults911">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="selectKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="selectDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fromDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="toDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="timeRange" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="duration" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupKey" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupYear" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupMonth" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="groupDay" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="optionString" nillable="true" type="xs:string" />
        <xs:element xmlns:q150="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q150:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSearchResults911Response">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q151="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetSearchResults911Result" nillable="true" type="q151:ArrayOfRPTCallInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallAuditSearchResults">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="fromDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="toDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="optionStr" nillable="true" type="xs:string" />
        <xs:element xmlns:q152="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q152:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallAuditSearchResultsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q153="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetCallAuditSearchResultsResult" nillable="true" type="q153:ArrayOfRPTCallInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallsNotAuditedSearchResults">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="fromDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="toDate" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="optionStr" nillable="true" type="xs:string" />
        <xs:element xmlns:q154="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q154:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCallsNotAuditedSearchResultsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q155="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetCallsNotAuditedSearchResultsResult" nillable="true" type="q155:ArrayOfRPTCallInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetRPTEvaluationReportFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q156="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q156:Recorder" />
        <xs:element minOccurs="0" name="criteria" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="userId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetRPTEvaluationReportFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q157="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" minOccurs="0" name="GetRPTEvaluationReportFromRecorderResult" nillable="true" type="q157:ArrayOfRPTEvaluation" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateConfiguration">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ipAddress" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateConfigurationResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="UpdateConfigurationResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CheckDBConnection">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="ipAddress" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CheckDBConnectionResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CheckDBConnectionResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAudioChannelsFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q158="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q158:Recorder" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAudioChannelsFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q159="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="GetAudioChannelsFromRecorderResult" nillable="true" type="q159:ArrayOfChannel" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAudioChannelFromRecorder">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q160="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q160:Recorder" />
        <xs:element minOccurs="0" name="channelId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAudioChannelFromRecorderResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q161="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="GetAudioChannelFromRecorderResult" nillable="true" type="q161:Channel" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteAudioChannels">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q162="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q162:Recorder" />
        <xs:element xmlns:q163="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="channelIds" nillable="true" type="q163:ArrayOfint" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="DeleteAudioChannelsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="DeleteAudioChannelsResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreateAudioChannels">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q164="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q164:Recorder" />
        <xs:element minOccurs="0" name="localRecorderId" type="xs:int" />
        <xs:element minOccurs="0" name="NoOfAnalogChannels" type="xs:int" />
        <xs:element minOccurs="0" name="NoOfVoIPChannels" type="xs:int" />
        <xs:element minOccurs="0" name="gatewayId" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreateAudioChannelsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CreateAudioChannelsResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateAudioChannel">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q165="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="recorder" nillable="true" type="q165:Recorder" />
        <xs:element xmlns:q166="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="channel" nillable="true" type="q166:Channel" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateAudioChannelResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="UpdateAudioChannelResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>