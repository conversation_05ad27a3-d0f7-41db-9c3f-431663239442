﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.MGODataEntities
{
    public class MGOTempData
    {
        public int Id { get; set; }
        public string JurisdictionID { get; set; }
        public string WorkOrderID { get; set; }
        public int ProjectTypeID { get; set; }
        public string Notes { get; set; }
        public string ContractorName { get; set; }
        public string ContractorEmail { get; set; }
        public string ContractorPhoneNumber { get; set; }
        public string PermitID { get; set; }
        public string Address { get; set; }
        public DateTime ScheduledDate { get; set; }
        public string Latitude { get; set; }
        public string Longitude { get; set; }

        // Fetched from the get-type MGO API
        public int InspectionTypeID { get; set; }
        public string InspectionType { get; set; }
        public int CategoryID { get; set; }
        public string Category { get; set; }
    }
}
