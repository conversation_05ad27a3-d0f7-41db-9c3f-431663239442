﻿using RevCord.DataContracts.IWBEntities;
using RevCord.DataContracts.MessageBase;
using System.Collections.Generic;

namespace RevCord.DataContracts.Messages
{
    public class IwbResponse : ResponseBase
    {
        public int TotalRecords { get; set; }
        public int TotalPages { get; set; }

        public int LastSavedId { get; set; }
        public int IwbId { get; set; }
        public Wps WPS { get; set; }
        public List<Wps> WPSs { get; set; }
        public Job Job { get; set; }
        public List<Job> Jobs { get; set; }
        public List<JobApplicant> JobApplicants { get; set; }
        public UserManagement.User User { get; set; }
        public List<UserManagement.User> Users { get; set; }

        public List<UserWorkHistory> UserWorkHistories { get; set; }
        public List<Wpq> WPQs { get; set; }

        public List<Organization> Organizations { get; set; }
        public List<OrganizationLocation> Locations { get; set; }
        public List<IwbDocument> Documents { get; set; }
        public List<IwbTestType> TestTypes { get; set; }

        public List<IwbTest> Tests { get; set; }
        public List<IwbTestAttendee> TestAttendees { get; set; }
        public List<SignOffRequestUser> SignOffRequestUsers { get; set; }
        public List<IwbRole> Roles { get; set; }
        public List<IwbPermission> Permissions { get; set; }
        public List<JobWelder> JobWelders { get; set; }

        public List<WelderRatingModel> WelderRatings { get; set; }
        public IwbDocument Document { get; set; }
        public object ResultData { get; set; }
        public List<JobTitleModel> JobTitles { get; set; }
        public JobDetailsModel JobDetails { get; set; }
        public UserInfoData UserInfoData { get; set; }

    }
}