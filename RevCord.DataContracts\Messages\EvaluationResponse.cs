﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.DataContracts.ViewModelEntities;

namespace RevCord.DataContracts.Response
{
    public class EvaluationResponse
    {

        /************* Purpose: used for Paging ****************/
        public int TotalPages { get; set; }
        public long TotalRecords { get; set; }
        /************* Purpose: used for Paging ****************/

        public short StatusFromDB { get; set; }
        public string LastSavedIds { get; set; } //List<string>

        #region Association
        
        public List<CallEvaluationDTO> CallEvaluationDTOs { get; set; }
        public MultiCallEvaluationGroup MultiCallEvaluationGroup { get; set; }
        public List<MultiCallEvaluationGroup> MultiCallEvaluationGroups { get; set; }
        //public List<CallEvaluation> CallEvaluations { get; set; }
        public CallEvaluation CallEvaluation { get; set; }

        public UserEvaluation UserEvaluation { get; set; }
        public List<UserEvaluation> UserEvaluations { get; set; }

        public List<CallEvaluationFeedback> CallEvaluationFeedbacks { get; set; }
        #endregion

        #region Evaluation EC
        public List<RecorderEvaluation> RecorderEvaluations { get; set; }
        #endregion
        public bool Status { get; set; }
    }
}
