﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using System.Xml.Linq;
using RevCord.Util.Configuration;

namespace RevCord.Util
{
    public static class SiteConfig
    {
        private static string CONFIG_PATH = ConfigurationManager.AppSettings["site"];
        private static CryptoHelper cryptoHelper = new CryptoHelper(AppSettingsUtil.GetString("encryptionPassword", "******"));

        static SiteConfiguration _objSiteConfiguration = null;

        public static SiteConfiguration objSiteConfiguration
        {
            get
            {
                if (_objSiteConfiguration == null)
                {
                    _objSiteConfiguration = SiteConfigurationSerializer.Deserialize<SiteConfiguration>(CONFIG_PATH);
                }

                return _objSiteConfiguration;
            }
        }

        public static void HideMarketingLinks()
        {
            if (_objSiteConfiguration != null)
            {
                _objSiteConfiguration.ShowMarketingLinks = false;
                SiteConfigurationSerializer.Serialize(_objSiteConfiguration, CONFIG_PATH);
            }
        }


        public static string DALConnectionString
        {
            get
            {
                if (IsMTEnable)
                    return "";// @"Data Source=.\Revcord;Initial Catalog=01-VoiceRec;User ID=sa;Password=******;Persist Security Info=True;";

                return objSiteConfiguration.DALConnectionString;
            }
        }

        public static string RevLogDBConnectionString
        {
            get
            {
                if (IsMTEnable)
                    return "";// @"Data Source=.\Revcord;Initial Catalog=01-VoiceRec;User ID=sa;Password=******;Persist Security Info=True;";
                return objSiteConfiguration.RevLogDBConnectionString;
            }
        }

        public static string MasterDBConnectionString
        {
            get
            {
                return objSiteConfiguration.MasterDBConnectionString;
            }
        }

        public static string RevSyncDALConnectionString
        {
            get
            {
                return objSiteConfiguration.RevSyncDALConnectionString;
            }
        }

        public static string RevSyncMasterDALConnectionString
        {
            get
            {
                return objSiteConfiguration.RevSyncMasterDALConnectionString;
            }
        }

        public static int SearchPageSize
        {
            get
            {
                return objSiteConfiguration.SearchPageSize;
            }
        }

        public static string WebURL
        {
            get
            {
                return objSiteConfiguration.WebUrl;
            }
        }

        public static string HelperServiceAddress
        {
            get
            {
                return objSiteConfiguration.HelperServiceAddress;
            }
        }

        public static string RevRecAddress
        {
            get
            {
                return objSiteConfiguration.RevRecAddress;
            }
        }

        public static string MMSIQ3URL
        {
            get
            {
                return objSiteConfiguration.MMSIQ3URL;
            }
        }

        public static string RevSyncURL
        {
            get
            {
                return objSiteConfiguration.RevSyncURL;
            }
        }

        public static bool RevSyncEnabled
        {
            get
            {
                return objSiteConfiguration.RevSyncEnabled;
            }
        }

        public static int RevSyncTenantID
        {
            set
            {
                _objSiteConfiguration.RevSyncTenantID = value;
            }
            get
            {
                return objSiteConfiguration.RevSyncTenantID;
            }
        }

        public static bool RevSyncISMT
        {
            get
            {
                return objSiteConfiguration.RevSyncISMT;
            }
        }

        public static bool IsMTEnable
        {
            get
            {
                return objSiteConfiguration.IsMTEnable;
            }
        }

        public static int TenantID
        {
            get
            {
                return objSiteConfiguration.TenantID;
            }
        }

        public static string RealTimeServer
        {
            get
            {
                return objSiteConfiguration.RealTimeServer;
            }
        }

        public static string VodServer
        {
            get
            {
                return objSiteConfiguration.VodServer;
            }
        }

        public static string IRStartupPlayer
        {
            get
            {
                return objSiteConfiguration.IRStartupPlayer;
            }
        }

        public static string IRView
        {
            get
            {
                return objSiteConfiguration.IRView;
            }
        }

        public static string FolderPathToZip
        {
            get
            {
                return objSiteConfiguration.FolderPathToZip;
            }
        }

        public static bool IsECEnabled
        {
            get
            {
                return objSiteConfiguration.IsECEnabled;
            }
        }

        public static bool IsEnterpriseRecorder
        {
            get
            {
                return objSiteConfiguration.IsEnterpriseRecorder;
            }
        }

        public static bool IsChainDBsConfigured
        {
            set
            {
                _objSiteConfiguration.IsChainDBsConfigured = value;
            }
            get
            {
                return objSiteConfiguration.IsChainDBsConfigured;
            }
        }

        public static string GoogleMapApiKey
        {
            get
            {
                return objSiteConfiguration.GoogleMapApiKey;
            }
        }

        public static string PlaylistUploadRootFolder
        {
            get
            {
                return objSiteConfiguration.PlaylistUploadRootFolder;
            }
        }

        public static string PlaylistUploadHttpRoot
        {
            get
            {
                return objSiteConfiguration.PlaylistUploadHttpRoot;
            }
        }

        public static int PreviewImageMaxWidth
        {
            get
            {
                return objSiteConfiguration.PreviewImageMaxWidth;
            }
        }

        public static int PreviewImageMaxHeight
        {
            get
            {
                return objSiteConfiguration.PreviewImageMaxHeight;
            }
        }

        public static string Host
        {
            get
            {
                return objSiteConfiguration.Host;
            }
        }

        public static int Port
        {
            get
            {
                return objSiteConfiguration.Port;
            }
        }

        public static bool EnableSsl
        {
            get
            {
                return objSiteConfiguration.EnableSsl;
            }
        }

        public static string UserName
        {
            get
            {
                return objSiteConfiguration.UserName;
            }
        }

        public static string Password
        {
            get
            {
                return objSiteConfiguration.Password;
            }
        }

        public static int TranscriptLength
        {
            get
            {
                return objSiteConfiguration.TranscriptLength;
            }
        }

        public static bool DVREnabled
        {
            get
            {
                return objSiteConfiguration.DVREnabled;
            }
        }

        public static int MaxExtensionCount
        {
            get
            {
                return objSiteConfiguration.MaxExtensionCount;
            }
        }

        public static string AccountSid
        {
            get
            {
                return objSiteConfiguration.AccountSid;
            }
        }

        public static string ApiSid
        {
            get
            {
                return objSiteConfiguration.ApiSid;
            }

        }

        public static string ApiSecret
        {
            get
            {
                return objSiteConfiguration.ApiSecret;
            }
        }

        public static string ChatServiceSid
        {
            get
            {
                return objSiteConfiguration.ChatServiceSid;
            }
        }

        public static string TwilioSMSAccountSid
        {
            get
            {
                return objSiteConfiguration.TwilioSMSAccountSid;
            }
        }

        public static string TwilioSMSAuthToken
        {
            get
            {
                return objSiteConfiguration.TwilioSMSAuthToken;
            }
        }

        public static string TwilioSMSPhoneNo
        {
            get
            {
                return objSiteConfiguration.TwilioSMSPhoneNo;
            }
        }

        public static string ConversationFilePath
        {
            get
            {
                return objSiteConfiguration.ConversationFilePath;
            }
        }

        public static bool IsRevcellEnabled
        {
            get
            {
                return objSiteConfiguration.IsRevcellEnabled;
            }
        }

        public static string RevcellProjectId
        {
            get
            {
                return objSiteConfiguration.RevcellProjectId;
            }
        }

        public static string RevcellAuthToken
        {
            get
            {
                return objSiteConfiguration.RevcellAuthToken;
            }
        }

        public static bool IsActiveDirectory
        {
            get
            {
                return objSiteConfiguration.IsActiveDirectory;
            }
        }

        public static string PlivoAuthID
        {
            get
            {
                return objSiteConfiguration.PlivoAuthID;
            }
        }

        public static string PlivoAuthToken
        {
            get
            {
                return objSiteConfiguration.PlivoAuthToken;
            }
        }

        public static string PlivoEndpointAppId
        {
            get
            {
                return objSiteConfiguration.PlivoEndpointAppId;
            }
        }

        public static string PlivoPhoneNumberAppId
        {
            get
            {
                return objSiteConfiguration.PlivoPhoneNumberAppId;
            }
        }

        public static string PlivoDomain
        {
            get
            {
                return objSiteConfiguration.PlivoDomain;
            }
        }

        public static bool IsRevLogEnabled
        {
            get
            {
                return objSiteConfiguration.IsRevLogEnabled;
            }
        }
        public static string RevLogServiceURL
        {
            get
            {
                return objSiteConfiguration.RevLogServiceURL;
            }
        }
        public static string RapidSOSClientID
        {
            get
            {
                return objSiteConfiguration.RapidSOSClientID;
            }
        }
        public static string RapidSOSClientSecret
        {
            get
            {
                return objSiteConfiguration.RapidSOSClientSecret;
            }
        }
        public static string RapidSOSUserName
        {
            get
            {
                return objSiteConfiguration.RapidSOSUserName;
            }
        }
        public static string RapidSOSPassword
        {
            get
            {
                return objSiteConfiguration.RapidSOSPassword;
            }
        }

        public static string RevViewServerURL
        {
            get
            {
                return objSiteConfiguration.RevViewServerURL;
            }
        }

        public static string InspectionServerURL
        {
            get
            {
                return objSiteConfiguration.InspectionServerURL;
            }
        }

        public static string RevMTAPIServerURL
        {
            get
            {
                return objSiteConfiguration.RevMTAPIServerURL;
            }
        }

        public static bool IsHostedOnLocalNetwork
        {
            get
            {
                return objSiteConfiguration.IsHostedOnLocalNetwork;
            }
        }

        public static bool IsOnlyIQ3ModeEnabled
        {
            get
            {
                return objSiteConfiguration.IsOnlyIQ3ModeEnabled;
            }
        }

        public static bool IsRoleBasedAccessEnabled
        {
            get
            {
                return objSiteConfiguration.IsRoleBasedAccessEnabled;
            }
        }

        public static bool IsTeamsEnabled
        {
            get
            {
                return objSiteConfiguration.IsTeamsEnabled;
            }
        }

        public static bool ShowMarketingLinks
        {
            get
            {
                return objSiteConfiguration.ShowMarketingLinks;
            }
        }

        public static bool Is2FAEnabled
        {
            get
            {
                return objSiteConfiguration.Is2FAEnabled;
            }
        }

        public static bool IsCustomGraphicMarkerEnabled
        {
            get
            {
                return objSiteConfiguration.IsCustomGraphicMarkerEnabled;
            }
        }

        public static bool IsIWBModeEnabled
        {
            get
            {
                return objSiteConfiguration.IsIWBModeEnabled;
            }
        }

        public static bool IsMTRModeEnabled
        {
            get
            {
                return objSiteConfiguration.IsMTRModeEnabled;
            }
        }

        public static bool IsOnlyIWBModeEnabled
        {
            get
            {
                return objSiteConfiguration.IsOnlyIWBModeEnabled;
            }
        }

        public static string AISERVEROCRURL
        {
            get
            {
                return objSiteConfiguration.AISERVEROCRURL;
            }
        }

    }
}