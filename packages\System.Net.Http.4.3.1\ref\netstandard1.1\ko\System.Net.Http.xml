﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.ByteArrayContent">
      <summary>바이트 배열에 따라 HTTP 콘텐츠를 제공합니다.</summary>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[])">
      <summary>
        <see cref="T:System.Net.Http.ByteArrayContent" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="content">
        <see cref="T:System.Net.Http.ByteArrayContent" />를 초기화하는 데 사용되는 콘텐츠입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> 매개 변수가 null입니다. </exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>
        <see cref="T:System.Net.Http.ByteArrayContent" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="content">
        <see cref="T:System.Net.Http.ByteArrayContent" />를 초기화하는 데 사용되는 콘텐츠입니다.</param>
      <param name="offset">
        <see cref="T:System.Net.Http.ByteArrayContent" />를 초기화하는 데 사용되는 <paramref name="content" /> 매개 변수의 오프셋(바이트)입니다.</param>
      <param name="count">
        <see cref="T:System.Net.Http.ByteArrayContent" />를 초기화하기 위해 사용되는 <paramref name="offset" /> 매개 변수에서 시작하는 <paramref name="content" />의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> 매개 변수가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 매개 변수가 0 미만인 경우또는<paramref name="offset" /> 매개 변수의 값이 <paramref name="content" /> 매개 변수가 지정한 콘텐츠 길이보다 큽니다.또는<paramref name="count " /> 매개 변수가 0보다 작은 경우또는<paramref name="count" /> 매개 변수가 <paramref name="content" /> 매개 변수에서 지정한 콘텐츠 길이에서 <paramref name="offset" /> 매개 변수를 뺀 값보다 큽니다.</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.CreateContentReadStreamAsync">
      <summary>백업 저장소가 <see cref="T:System.Net.Http.ByteArrayContent" />의 메모리인 HTTP 콘텐츠 스트림을 읽기 위한 비동기 작업으로 만듭니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>생성자가 제공한 바이트 배열을 HTTP 콘텐츠 스트림에 비동기 작업으로 serialize하고 씁니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" />를 반환합니다. 비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="stream">대상 스트림입니다.</param>
      <param name="context">채널 바인딩 토큰과 같은 전송에 대한 정보입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.TryComputeLength(System.Int64@)">
      <summary>바이트 배열의 바이트 길이가 유효한지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="length" />가 유효한 길이이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="length">바이트 배열의 길이(바이트)입니다.</param>
    </member>
    <member name="T:System.Net.Http.ClientCertificateOption">
      <summary>클라이언트 인증서 제공 방식을 지정합니다.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Automatic">
      <summary>사용 가능한 모든 클라이언트 인증서를 자동으로 제공하려는 <see cref="T:System.Net.Http.HttpClientHandler" />입니다.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Manual">
      <summary>응용 프로그램은 클라이언트 인증서를 <see cref="T:System.Net.Http.WebRequestHandler" />에 수동으로 제공합니다.이 값이 기본값입니다.</summary>
    </member>
    <member name="T:System.Net.Http.DelegatingHandler">
      <summary>HTTP 응답 메시지의 처리를 내부 처리기라는 다른 처리기로 위임하는 HTTP 처리기의 형식입니다.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor">
      <summary>
        <see cref="T:System.Net.Http.DelegatingHandler" /> 클래스의 새 인스턴스를 만듭니다.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>특정 내부 처리기를 사용하여 <see cref="T:System.Net.Http.DelegatingHandler" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="innerHandler">HTTP 응답 메시지 처리를 담당하는 내부 처리기입니다.</param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.DelegatingHandler" />에서 사용하는 관리되지 않는 리소스를 해제하고, 필요에 따라 관리되는 리소스를 삭제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다. </param>
    </member>
    <member name="P:System.Net.Http.DelegatingHandler.InnerHandler">
      <summary>HTTP 응답 메시지를 처리하는 내부 처리기를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMessageHandler" />를 반환합니다.HTTP 응답 메시지에 대한 내부 처리기입니다.</returns>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>비동기 작업으로 서버에 보내기 위해 HTTP 요청을 내부 처리기에 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다. 비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="request">서버에 보낼 HTTP 요청 메시지입니다.</param>
      <param name="cancellationToken">작업을 취소할 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" />이 null인 경우</exception>
    </member>
    <member name="T:System.Net.Http.FormUrlEncodedContent">
      <summary>application/x-www-form-urlencoded MIME 형식을 사용하여 인코딩된 이름/값 튜플의 컨테이너입니다.</summary>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>이름/값 쌍의 특정 컬렉션을 사용하여 <see cref="T:System.Net.Http.FormUrlEncodedContent" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="nameValueCollection">이름/값 쌍으로 된 컬렉션입니다.</param>
    </member>
    <member name="T:System.Net.Http.HttpClient">
      <summary>URI로 식별되는 리소스에서 HTTP 요청을 보내고 HTTP 응답을 받기 위한 기본 클래스를 제공합니다. </summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>특정 처리기를 사용하여 <see cref="T:System.Net.Http.HttpClient" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="handler">요청을 보내는 데 사용할 HTTP 처리기 스택입니다. </param>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>특정 처리기를 사용하여 <see cref="T:System.Net.Http.HttpClient" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" />는 HTTP 응답 메시지 처리를 담당합니다.</param>
      <param name="disposeHandler">Dispose()가 내부 처리기를 삭제해야 하는 경우 true이고, 내부 처리기를 다시 사용하려고 하는 경우 false입니다.</param>
    </member>
    <member name="P:System.Net.Http.HttpClient.BaseAddress">
      <summary>요청을 보낼 때 사용된 인터넷 리소스의 URI(Uniform Resource Identifier)의 기준 주소를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Uri" />를 반환합니다.요청을 보낼 때 사용된 인터넷 리소스의 URI(Uniform Resource Identifier)의 기준 주소입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.CancelPendingRequests">
      <summary>이 인스턴스에서 보류 중인 모든 요청을 취소합니다.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultRequestHeaders">
      <summary>각 요청과 함께 보내야 하는 헤더를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />를 반환합니다.각 요청과 함께 보내야 하는 헤더입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String)">
      <summary>DELETE 요청을 지정된 URI에 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">요청 메시지를 <see cref="T:System.Net.Http.HttpClient" /> 인스턴스에서 이미 보냈습니다.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String,System.Threading.CancellationToken)">
      <summary>취소 토큰이 포함된 지정한 URI에 DELETE 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">요청 메시지를 <see cref="T:System.Net.Http.HttpClient" /> 인스턴스에서 이미 보냈습니다.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri)">
      <summary>DELETE 요청을 지정된 URI에 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">요청 메시지를 <see cref="T:System.Net.Http.HttpClient" /> 인스턴스에서 이미 보냈습니다.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>취소 토큰이 포함된 지정한 URI에 DELETE 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">요청 메시지를 <see cref="T:System.Net.Http.HttpClient" /> 인스턴스에서 이미 보냈습니다.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" />에서 사용하는 관리되지 않는 리소스를 해제하고 관리되는 리소스를 선택적으로 삭제할 수 있습니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true, 관리되지 않는 리소스만 해제하려면 false입니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String)">
      <summary>GET 요청을 지정된 URI에 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption)">
      <summary>HTTP 완료 옵션이 포함된 지정한 URI에 GET 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="completionOption">작업이 완료된 것으로 간주해야 할 때를 나타내는 HTTP 완료 옵션 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>HTTP 완료 옵션 및 취소 토큰을 사용하여 지정한 URL에 비동기 작업으로 GET 요청을 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="completionOption">작업이 완료된 것으로 간주해야 할 때를 나타내는 HTTP 완료 옵션 값입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Threading.CancellationToken)">
      <summary>취소 토큰이 포함된 지정한 URI에 GET 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri)">
      <summary>GET 요청을 지정된 URI에 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption)">
      <summary>HTTP 완료 옵션이 포함된 지정한 URI에 GET 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="completionOption">작업이 완료된 것으로 간주해야 할 때를 나타내는 HTTP 완료 옵션 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>HTTP 완료 옵션 및 취소 토큰을 사용하여 지정한 URL에 비동기 작업으로 GET 요청을 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="completionOption">작업이 완료된 것으로 간주해야 할 때를 나타내는 HTTP 완료 옵션 값입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>취소 토큰이 포함된 지정한 URI에 GET 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.String)">
      <summary>GET 요청을 지정된 URI에 보내고 비동기 작업에서 바이트 배열로 응답 본문을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.Uri)">
      <summary>GET 요청을 지정된 URI에 보내고 비동기 작업에서 바이트 배열로 응답 본문을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.String)">
      <summary>GET 요청을 지정된 URI에 보내고 비동기 작업에서 스트림으로 응답 본문을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.Uri)">
      <summary>GET 요청을 지정된 URI에 보내고 비동기 작업에서 스트림으로 응답 본문을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.String)">
      <summary>GET 요청을 지정된 URI에 보내고 비동기 작업에서 문자열로 응답 본문을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.Uri)">
      <summary>GET 요청을 지정된 URI에 보내고 비동기 작업에서 문자열로 응답 본문을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.MaxResponseContentBufferSize">
      <summary>응답 콘텐츠를 읽을 경우 버퍼링할 최대 바이트 수를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.응답 콘텐츠를 읽을 경우 버퍼링할 최대 바이트 수입니다.이 속성의 기본값은 2GB입니다.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">지정한 크기가 0보다 작거나 같습니다.</exception>
      <exception cref="T:System.InvalidOperationException">현재 인스턴스에서 이미 작업이 시작되었습니다. </exception>
      <exception cref="T:System.ObjectDisposedException">현재 인스턴스가 삭제된 경우 </exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent)">
      <summary>POST 요청을 지정된 URI에 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="content">서버로 전송된 HTTP 요청 콘텐츠입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>취소 토큰이 포함된 POST 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="content">서버로 전송된 HTTP 요청 콘텐츠입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>POST 요청을 지정된 URI에 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="content">서버로 전송된 HTTP 요청 콘텐츠입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>취소 토큰이 포함된 POST 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="content">서버로 전송된 HTTP 요청 콘텐츠입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent)">
      <summary>PUT 요청을 지정된 URI에 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="content">서버로 전송된 HTTP 요청 콘텐츠입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>취소 토큰이 포함된 PUT 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="content">서버로 전송된 HTTP 요청 콘텐츠입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>PUT 요청을 지정된 URI에 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="content">서버로 전송된 HTTP 요청 콘텐츠입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>취소 토큰이 포함된 PUT 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="requestUri">요청이 전송되는 URI입니다.</param>
      <param name="content">서버로 전송된 HTTP 요청 콘텐츠입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage)">
      <summary>HTTP 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="request">보낼 HTTP 요청 메시지입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" />이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">요청 메시지를 <see cref="T:System.Net.Http.HttpClient" /> 인스턴스에서 이미 보냈습니다.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption)">
      <summary>HTTP 요청을 비동기 작업으로 보냅니다. </summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="request">보낼 HTTP 요청 메시지입니다.</param>
      <param name="completionOption">작업을 완료해야 할 경우(응답을 사용할 수 있게 된 즉시 또는 전체 응답 내용을 읽은 후).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" />이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">요청 메시지를 <see cref="T:System.Net.Http.HttpClient" /> 인스턴스에서 이미 보냈습니다.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>HTTP 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="request">보낼 HTTP 요청 메시지입니다.</param>
      <param name="completionOption">작업을 완료해야 할 경우(응답을 사용할 수 있게 된 즉시 또는 전체 응답 내용을 읽은 후).</param>
      <param name="cancellationToken">작업을 취소할 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" />이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">요청 메시지를 <see cref="T:System.Net.Http.HttpClient" /> 인스턴스에서 이미 보냈습니다.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>HTTP 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="request">보낼 HTTP 요청 메시지입니다.</param>
      <param name="cancellationToken">작업을 취소할 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" />이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">요청 메시지를 <see cref="T:System.Net.Http.HttpClient" /> 인스턴스에서 이미 보냈습니다.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.Timeout">
      <summary>요청 시간 제한 범위 내에서 대기할 기간을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.TimeSpan" />를 반환합니다.요청 시간 제한 범위 내에서 대기할 기간입니다.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">지정된 제한 시간이 0보다 작거나 같고 <see cref="F:System.Threading.Timeout.InfiniteTimeSpan" />가 아닌 경우</exception>
      <exception cref="T:System.InvalidOperationException">현재 인스턴스에서 이미 작업이 시작되었습니다. </exception>
      <exception cref="T:System.ObjectDisposedException">현재 인스턴스가 삭제된 경우</exception>
    </member>
    <member name="T:System.Net.Http.HttpClientHandler">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" />에 사용되는 기본 메시지 처리기입니다.  </summary>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpClientHandler" /> 클래스의 인스턴스를 만듭니다.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect">
      <summary>처리기가 리디렉션 응답을 따르는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.처리기가 리디렉션 응답을 따라야 하는 경우 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AutomaticDecompression">
      <summary>HTTP 콘텐츠 응답의 자동 압축 해체를 위한 처리기에서 사용되는 압축 해제 메서드의 형식을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.DecompressionMethods" />를 반환합니다.처리기에서 사용되는 자동 압축 풀기 방법입니다.기본값은 <see cref="F:System.Net.DecompressionMethods.None" />입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.ClientCertificateOptions">
      <summary>이 처리기와 관련된 보안 인증서의 컬렉션을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.ClientCertificateOption" />를 반환합니다.이 처리기에 연결된 보안 인증서의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.CookieContainer">
      <summary>처리기에서 서버 쿠키를 저장하는 데 사용하는 쿠키 컨테이너를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.CookieContainer" />를 반환합니다.처리기에서 서버 쿠키를 저장하는 데 사용되는 쿠키 컨테이너입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Credentials">
      <summary>이 처리기가 사용하는 인증 정보를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.ICredentials" />를 반환합니다.처리기와 연결된 인증 자격 증명입니다.기본값은 null입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpClientHandler" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 삭제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections">
      <summary>처리기가 따르는 최대 리디렉션 수를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.처리기가 따르는 최대 리디렉션 응답 수입니다.기본값은 50입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxRequestContentBufferSize">
      <summary>처리기에 사용되는 최대 요청 콘텐츠 버퍼 크기를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.최대 요청 콘텐츠 버퍼 크기(바이트)입니다.기본값은 2GB입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.PreAuthenticate">
      <summary>요청과 함께 처리기가 인증 헤더를 보낼 것인지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.인증을 수행한 이후 처리기가 요청과 함께 HTTP 권한 부여 헤더를 함께 보내는 경우 true 이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Proxy">
      <summary>처리기가 사용하는 프록시 정보를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.IWebProxy" />를 반환합니다.처리기에 사용되는 프록시 정보입니다.기본값은 null입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>차단되지 않을 작업으로 <see cref="T:System.Net.Http.HttpRequestMessage" />에서 제공된 정보를 기반으로 <see cref="T:System.Net.Http.HttpResponseMessage" />의 인스턴스를 만듭니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="request">HTTP 요청 메시지입니다.</param>
      <param name="cancellationToken">작업을 취소할 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" />이 null인 경우</exception>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsAutomaticDecompression">
      <summary>처리기가 자동 응답 콘텐츠 압축 해제를 지원하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.처리기가 자동 응답 콘텐츠 압축 풀기를 지원하면 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsProxy">
      <summary>처리기가 프록시 설정을 지원하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.처리기에서 프록시 설정을 지원하면 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsRedirectConfiguration">
      <summary>처리기가 <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> 및 <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> 속성에 대한 구성 설정을 지원하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.처리기에서 <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> 및 <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> 속성에 대한 구성 설정을 지원하면 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseCookies">
      <summary>처리기가 <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> 속성을 사용하여 서버 쿠키를 저장하고 요청을 보낼 때 이러한 쿠키를 사용하는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> 속성을 사용하여 서버 쿠키를 저장하고 요청을 보낼 때 이러한 쿠키를 사용하는 것을 지원하면 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseDefaultCredentials">
      <summary>기본 자격 증명을 처리기의 요청과 함께 보내는지 여부를 제어하는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.기본 자격 증명이 사용되면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseProxy">
      <summary>처리기가 요청에 대해 프록시를 사용하는지 여부를 나타내는 값을 가져오거나 설정합니다. </summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.처리기에서 요청에 대해 프록시를 사용해야 하는 경우 true이고, otherwise false.기본값은 true입니다.</returns>
    </member>
    <member name="T:System.Net.Http.HttpCompletionOption">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" /> 작업이 응답이 가능하면 즉시 완료된 것으로 간주되어야 하는지, 아니면 콘텐츠가 담긴 전체 응답 메시지를 읽은 후 완료된 것으로 간주되어야 하는지 여부를 나타냅니다. </summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseContentRead">
      <summary>콘텐츠를 포함하여 전체 응답을 읽은 후 작업을 완료 해야 합니다.</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseHeadersRead">
      <summary>응답이 가능하고 헤더를 읽는 즉시 작업을 완료해야 합니다.콘텐츠를 아직 읽지 않았습니다.</summary>
    </member>
    <member name="T:System.Net.Http.HttpContent">
      <summary>HTTP 엔터티 본문과 콘텐츠 헤더를 나타내는 기본 클래스입니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpContent" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream)">
      <summary>HTTP 콘텐츠를 바이트 스트림으로 serialize하고 <paramref name="stream" /> 매개 변수로 제공된 스트림 개체로 복사합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="stream">대상 스트림입니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>HTTP 콘텐츠를 바이트 스트림으로 serialize하고 <paramref name="stream" /> 매개 변수로 제공된 스트림 개체로 복사합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="stream">대상 스트림입니다.</param>
      <param name="context">전송(예를 들어, 채널 바인딩 토큰)에 대한 정보입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CreateContentReadStreamAsync">
      <summary>HTTP 콘텐츠를 메모리 스트림에 비동기 작업으로 serialize합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose">
      <summary>관리되지 않는 리소스를 해제하고, <see cref="T:System.Net.Http.HttpContent" />에서 사용하는 관리되는 리소스를 삭제합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpContent" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 삭제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="P:System.Net.Http.HttpContent.Headers">
      <summary>RFC 2616에 정의된 HTTP 콘텐츠 헤더를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpContentHeaders" />를 반환합니다.RFC 2616에 정의된 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync">
      <summary>HTTP 콘텐츠를 메모리 버퍼에 비동기 작업으로 serialize합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync(System.Int64)">
      <summary>HTTP 콘텐츠를 메모리 버퍼에 비동기 작업으로 serialize합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="maxBufferSize">사용할 버퍼의 최대 크기(바이트)입니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsByteArrayAsync">
      <summary>HTTP 콘텐츠를 바이트 배열에 비동기 작업으로 Serialize합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStreamAsync">
      <summary>HTTP 콘텐츠를 serialize하고 콘텐츠를 비동기 작업으로 나타내는 스트림을 반환합니다. </summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStringAsync">
      <summary>HTTP 콘텐츠를 문자열에 비동기 작업으로 serialize합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>HTTP 콘텐츠를 스트림에 비동기 작업으로 serialize합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="stream">대상 스트림입니다.</param>
      <param name="context">전송(예를 들어, 채널 바인딩 토큰)에 대한 정보입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.TryComputeLength(System.Int64@)">
      <summary>HTTP 콘텐츠의 바이트 길이가 유효한지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="length" />가 유효한 길이이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="length">HTTP 콘텐츠의 길이(바이트)입니다.</param>
    </member>
    <member name="T:System.Net.Http.HttpMessageHandler">
      <summary>HTTP 메시지 처리기의 기본 형식입니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpMessageHandler" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose">
      <summary>관리되지 않는 리소스를 해제하고, <see cref="T:System.Net.Http.HttpMessageHandler" />에서 사용하는 관리되는 리소스를 삭제합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpMessageHandler" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 삭제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>HTTP 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="request">보낼 HTTP 요청 메시지입니다.</param>
      <param name="cancellationToken">작업을 취소할 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" />이 null인 경우</exception>
    </member>
    <member name="T:System.Net.Http.HttpMessageInvoker">
      <summary>응용 프로그램이 Http 처리기 체인에서 <see cref="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)" /> 메서드를 호출할 수 있는 특수 클래스입니다. </summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>특정 <see cref="T:System.Net.Http.HttpMessageHandler" />를 사용하여 <see cref="T:System.Net.Http.HttpMessageInvoker" /> 클래스의 인스턴스를 초기화합니다.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" />는 HTTP 응답 메시지의 처리를 담당합니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>특정 <see cref="T:System.Net.Http.HttpMessageHandler" />를 사용하여 <see cref="T:System.Net.Http.HttpMessageInvoker" /> 클래스의 인스턴스를 초기화합니다.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" />는 HTTP 응답 메시지의 처리를 담당합니다.</param>
      <param name="disposeHandler">Dispose()가 내부 처리기를 삭제해야 하는 경우 true이고, 내부 처리기를 다시 사용하려고 하는 경우 false입니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose">
      <summary>관리되지 않는 리소스를 해제하고, <see cref="T:System.Net.Http.HttpMessageInvoker" />에서 사용하는 관리되는 리소스를 삭제합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpMessageInvoker" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 삭제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>HTTP 요청을 비동기 작업으로 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="request">보낼 HTTP 요청 메시지입니다.</param>
      <param name="cancellationToken">작업을 취소할 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" />이 null인 경우</exception>
    </member>
    <member name="T:System.Net.Http.HttpMethod">
      <summary>표준 HTTP 메서드를 검색 및 비교하고 새 HTTP 메서드를 만들기 위한 도우미 클래스입니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMethod.#ctor(System.String)">
      <summary>특정 HTTP 메서드를 사용하여 <see cref="T:System.Net.Http.HttpMethod" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="method">HTTP 메서드입니다.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Delete">
      <summary>HTTP DELETE 프로토콜 메서드를 나타냅니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" />를 반환합니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Net.Http.HttpMethod)">
      <summary>지정한 <see cref="T:System.Net.Http.HttpMethod" />가 현재 <see cref="T:System.Object" />와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정한 개체가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 개체와 비교할 HTTP 메서드입니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Object" />와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정한 개체가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Get">
      <summary>HTTP GET 프로토콜 메서드를 나타냅니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" />를 반환합니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.GetHashCode">
      <summary>이 형식에 대한 해시 함수 역할을 합니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 <see cref="T:System.Object" />의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Head">
      <summary>HTTP HEAD 프로토콜 메서드를 나타냅니다.HEAD 메서드는 서버에서 응답의 메시지 본문 없이 메시지 헤더만 반환한다는 점을 제외하고는 GET 메서드와 동일합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" />를 반환합니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Method">
      <summary>HTTP 메서드입니다. </summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.<see cref="T:System.String" />으로 표시되는 HTTP 메서드입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Equality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>두 <see cref="T:System.Net.Http.HttpMethod" /> 개체를 비교하는 같음 연산자입니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <paramref name="left" />와 <paramref name="right" /> 매개 변수가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">같음 연산자의 왼쪽에 있는 <see cref="T:System.Net.Http.HttpMethod" />입니다.</param>
      <param name="right">같음 연산자의 오른쪽에 있는 <see cref="T:System.Net.Http.HttpMethod" />입니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Inequality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>두 <see cref="T:System.Net.Http.HttpMethod" /> 개체를 비교하는 같지 않음 연산자입니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <paramref name="left" />와 <paramref name="right" /> 매개 변수가 같지 않으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="left">같지 않음 연산자의 왼쪽에 있는 <see cref="T:System.Net.Http.HttpMethod" />입니다.</param>
      <param name="right">같지 않음 연산자의 오른쪽에 있는 <see cref="T:System.Net.Http.HttpMethod" />입니다.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Options">
      <summary>HTTP OPTIONS 프로토콜 메서드를 나타냅니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" />를 반환합니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Post">
      <summary>새 엔터티를 URI에 추가할 항목으로 게시하는 데 사용되는 HTTP POST 프로토콜 메서드를 나타냅니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" />를 반환합니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Put">
      <summary>URI로 식별된 엔터티를 바꾸는 데 사용되는 HTTP PUT 프로토콜 메서드를 나타냅니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" />를 반환합니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.ToString">
      <summary>현재 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Trace">
      <summary>HTTP TRACE 프로토콜 메서드를 나타냅니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" />를 반환합니다.</returns>
    </member>
    <member name="T:System.Net.Http.HttpRequestException">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" /> 및 <see cref="T:System.Net.Http.HttpMessageHandler" /> 클래스가 throw하는 예외의 기본 클래스입니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpRequestException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String)">
      <summary>현재 예외를 설명하는 특정 메시지를 사용하여 <see cref="T:System.Net.Http.HttpRequestException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">현재 예외를 설명하는 메시지입니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String,System.Exception)">
      <summary>현재 예외 및 내부 예외를 설명하는 특정 메시지를 사용하여 <see cref="T:System.Net.Http.HttpRequestException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">현재 예외를 설명하는 메시지입니다.</param>
      <param name="inner">내부 예외입니다.</param>
    </member>
    <member name="T:System.Net.Http.HttpRequestMessage">
      <summary>HTTP 요청 메시지를 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpRequestMessage" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.String)">
      <summary>HTTP 메서드 및 요청 <see cref="T:System.Uri" />를 사용하여 <see cref="T:System.Net.Http.HttpRequestMessage" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="method">HTTP 메서드입니다.</param>
      <param name="requestUri">요청 <see cref="T:System.Uri" />를 나타내는 문자열입니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.Uri)">
      <summary>HTTP 메서드 및 요청 <see cref="T:System.Uri" />를 사용하여 <see cref="T:System.Net.Http.HttpRequestMessage" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="method">HTTP 메서드입니다.</param>
      <param name="requestUri">요청할 <see cref="T:System.Uri" />입니다.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Content">
      <summary>HTTP 메시지의 내용을 가져오거나 설정합니다. </summary>
      <returns>
        <see cref="T:System.Net.Http.HttpContent" />를 반환합니다.메시지 내용</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose">
      <summary>관리되지 않는 리소스를 해제하고, <see cref="T:System.Net.Http.HttpRequestMessage" />에서 사용하는 관리되는 리소스를 삭제합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpRequestMessage" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 삭제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Headers">
      <summary>HTTP 요청 헤더의 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />를 반환합니다.HTTP 요청 헤더의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Method">
      <summary>HTTP 요청 메서드에서 사용하는 HTTP 메서드를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" />를 반환합니다.요청 메시지에서 사용하는 HTTP 메서드입니다.기본값은 GET 메서드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Properties">
      <summary>HTTP 요청의 속성 집합을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />를 반환합니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.RequestUri">
      <summary>HTTP 요청에 대한 <see cref="T:System.Uri" />를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Uri" />를 반환합니다.HTTP 요청에 사용되는 <see cref="T:System.Uri" />입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.ToString">
      <summary>현재 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체의 문자열 표현입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Version">
      <summary>HTTP 메시지 버전을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Version" />를 반환합니다.HTTP 메시지 버전입니다.기본값은 1.1입니다.</returns>
    </member>
    <member name="T:System.Net.Http.HttpResponseMessage">
      <summary>상태 코드와 데이터가 포함된 HTTP 응답 메시지를 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpResponseMessage" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor(System.Net.HttpStatusCode)">
      <summary>특정 <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" />를 사용하여 <see cref="T:System.Net.Http.HttpResponseMessage" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="statusCode">HTTP 응답의 상태 코드입니다.</param>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Content">
      <summary>HTTP 응답 메시지의 텍스트 콘텐츠를 가져오거나 설정합니다. </summary>
      <returns>
        <see cref="T:System.Net.Http.HttpContent" />를 반환합니다.HTTP 응답 메시지의 내용입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose">
      <summary>관리되지 않는 리소스를 해제하고, <see cref="T:System.Net.Http.HttpResponseMessage" />에서 사용하는 관리되지 않는 리소스를 삭제합니다.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpResponseMessage" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 삭제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode">
      <summary>HTTP 응답의 <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode" /> 속성이 false이면 예외를 throw합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpResponseMessage" />를 반환합니다.호출이 성공할 경우 HTTP 응답 메시지입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Headers">
      <summary>HTTP 응답 헤더의 컬렉션을 가져옵니다. </summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpResponseHeaders" />를 반환합니다.HTTP 응답 헤더의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode">
      <summary>HTTP 응답이 성공했는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.HTTP 응답이 성공했는지 여부를 나타내는 값입니다.<see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" />가 200-299 범위에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.ReasonPhrase">
      <summary>일반적으로 서버에서 상태 코드와 함께 보내는 원인 문구를 가져오거나 설정합니다. </summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.서버에서 보낸 원인 문구입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.RequestMessage">
      <summary>이 응답 메시지를 유도하는 요청 메시지를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpRequestMessage" />를 반환합니다.이 응답 메시지를 유도하는 요청 메시지입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.StatusCode">
      <summary>HTTP 응답의 상태 코드를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.HttpStatusCode" />를 반환합니다.HTTP 응답의 상태 코드입니다.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.ToString">
      <summary>현재 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체의 문자열 표현입니다.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Version">
      <summary>HTTP 메시지 버전을 가져오거나 설정합니다. </summary>
      <returns>
        <see cref="T:System.Version" />를 반환합니다.HTTP 메시지 버전입니다.기본값은 1.1입니다.</returns>
    </member>
    <member name="T:System.Net.Http.MessageProcessingHandler">
      <summary>요청 및/또는 응답 메시지의 적은 처리만 수행하는 처리기의 기본 형식입니다.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor">
      <summary>
        <see cref="T:System.Net.Http.MessageProcessingHandler" /> 클래스의 인스턴스를 만듭니다.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>특정 내부 처리기를 사용하여 <see cref="T:System.Net.Http.MessageProcessingHandler" /> 클래스의 인스턴스를 만듭니다.</summary>
      <param name="innerHandler">HTTP 응답 메시지 처리를 담당하는 내부 처리기입니다.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>서버에 보낸 각 요청에서 처리를 수행합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpRequestMessage" />를 반환합니다.처리된 HTTP 요청 메시지입니다.</returns>
      <param name="request">처리할 HTTP 요청 메시지입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessResponse(System.Net.Http.HttpResponseMessage,System.Threading.CancellationToken)">
      <summary>서버에서 각 응답에 처리를 수행합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpResponseMessage" />를 반환합니다.처리된 HTTP 응답 메시지입니다.</returns>
      <param name="response">처리할 HTTP 응답 메시지입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>비동기 작업으로 서버에 보내기 위해 HTTP 요청을 내부 처리기에 보냅니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="request">서버에 보낼 HTTP 요청 메시지입니다.</param>
      <param name="cancellationToken">취소의 통지를 받기 위해 다른 개체나 스레드에서 사용할 수 있는 취소 토큰입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" />이 null인 경우</exception>
    </member>
    <member name="T:System.Net.Http.MultipartContent">
      <summary>multipart/* 콘텐츠 형식 지정을 사용하여 serialize된 <see cref="T:System.Net.Http.HttpContent" /> 개체의 컬렉션을 제공합니다.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor">
      <summary>
        <see cref="T:System.Net.Http.MultipartContent" /> 클래스의 새 인스턴스를 만듭니다.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.MultipartContent" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="subtype">여러 부분으로 구성된 콘텐츠의 하위 형식입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="subtype" />이 null이거나 공백 문자만 있는 경우</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.MultipartContent" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="subtype">여러 부분으로 구성된 콘텐츠의 하위 형식입니다.</param>
      <param name="boundary">여러 부분으로 구성된 콘텐츠에 대한 경계 문자열입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="subtype" />이 null이거나 빈 문자열인 경우<paramref name="boundary" />가 null이거나 공백 문자만 있는 경우또는<paramref name="boundary" />가 공백 문자로 끝나는 경우</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="boundary" />의 길이가 70보다 큽니다.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Add(System.Net.Http.HttpContent)">
      <summary>multipart/* 콘텐츠 형식 지정을 사용하여 serialize된 <see cref="T:System.Net.Http.HttpContent" /> 개체의 컬렉션에 multipart HTTP 콘텐츠를 추가합니다.</summary>
      <param name="content">컬렉션에 추가할 HTTP 콘텐츠입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.MultipartContent" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 삭제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.GetEnumerator">
      <summary>multipart/* 콘텐츠 형식 사양을 통해 serialize된 <see cref="T:System.Net.Http.HttpContent" /> 개체의 컬렉션 사이를 이동하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />를 반환합니다.컬렉션을 반복하는 데 사용할 수 있는 개체입니다.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>multipart HTTP 콘텐츠를 스트림에 비동기 작업으로 serialize합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="stream">대상 스트림입니다.</param>
      <param name="context">전송(예를 들어, 채널 바인딩 토큰)에 대한 정보입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="M:System.Net.Http.MultipartContent.GetEnumerator" /> 메서드의 명시적 구현입니다.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />를 반환합니다.컬렉션을 반복하는 데 사용할 수 있는 개체입니다.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.TryComputeLength(System.Int64@)">
      <summary>HTTP multipart 콘텐츠의 바이트 길이가 유효한지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="length" />가 유효한 길이이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="length">HHTP 콘텐츠의 길이(바이트)입니다.</param>
    </member>
    <member name="T:System.Net.Http.MultipartFormDataContent">
      <summary>multipart/form-data MIME 형식을 사용하여 인코딩된 콘텐츠에 대한 컨테이너를 제공합니다.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor">
      <summary>
        <see cref="T:System.Net.Http.MultipartFormDataContent" /> 클래스의 새 인스턴스를 만듭니다.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.MultipartFormDataContent" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="boundary">데이터 콘텐츠에서 여러 부분으로 구성된 경계 문자열입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="boundary" />가 null이거나 공백 문자만 있는 경우또는<paramref name="boundary" />가 공백 문자로 끝나는 경우</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="boundary" />의 길이가 70보다 큽니다.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent)">
      <summary>multipart/form-data MIME 형식으로 serialize되는 <see cref="T:System.Net.Http.HttpContent" /> 개체의 컬렉션에 HTTP 콘텐츠를 추가합니다.</summary>
      <param name="content">컬렉션에 추가할 HTTP 콘텐츠입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String)">
      <summary>multipart/form-data MIME 형식으로 serialize되는 <see cref="T:System.Net.Http.HttpContent" /> 개체의 컬렉션에 HTTP 콘텐츠를 추가합니다.</summary>
      <param name="content">컬렉션에 추가할 HTTP 콘텐츠입니다.</param>
      <param name="name">추가할 HTTP 콘텐츠의 이름입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" />이 null이거나 공백 문자만 있는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" />이 null인 경우</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String,System.String)">
      <summary>multipart/form-data MIME 형식으로 serialize되는 <see cref="T:System.Net.Http.HttpContent" /> 개체의 컬렉션에 HTTP 콘텐츠를 추가합니다.</summary>
      <param name="content">컬렉션에 추가할 HTTP 콘텐츠입니다.</param>
      <param name="name">추가할 HTTP 콘텐츠의 이름입니다.</param>
      <param name="fileName">컬렉션에 추가할 HTTP 콘텐츠의 파일 이름입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" />이 null이거나 공백 문자만 있는 경우또는<paramref name="fileName" />이 null이거나 공백 문자만 있는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" />이 null인 경우</exception>
    </member>
    <member name="T:System.Net.Http.StreamContent">
      <summary>스트림에 따라 HTTP 콘텐츠를 제공합니다.</summary>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream)">
      <summary>
        <see cref="T:System.Net.Http.StreamContent" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="content">
        <see cref="T:System.Net.Http.StreamContent" />를 초기화하는 데 사용되는 콘텐츠입니다.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream,System.Int32)">
      <summary>
        <see cref="T:System.Net.Http.StreamContent" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="content">
        <see cref="T:System.Net.Http.StreamContent" />를 초기화하는 데 사용되는 콘텐츠입니다.</param>
      <param name="bufferSize">
        <see cref="T:System.Net.Http.StreamContent" />에 대한 버퍼의 크기(바이트)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" />이 null인 경우</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="bufferSize" />가 0보다 작거나 같은 경우 </exception>
    </member>
    <member name="M:System.Net.Http.StreamContent.CreateContentReadStreamAsync">
      <summary>HTTP 스트림을 메모리 스트림에 비동기 작업으로 씁니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.StreamContent" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 삭제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>HTTP 콘텐츠를 스트림에 비동기 작업으로 serialize합니다.</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" />를 반환합니다.비동기 작업(operation)을 나타내는 작업(task) 개체입니다.</returns>
      <param name="stream">대상 스트림입니다.</param>
      <param name="context">전송(예를 들어, 채널 바인딩 토큰)에 대한 정보입니다.이 매개 변수는 null일 수 있습니다.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.TryComputeLength(System.Int64@)">
      <summary>스트림 콘텐츠의 바이트 길이가 유효한지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="length" />가 유효한 길이이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="length">스트림 콘텐츠의 길이(바이트)입니다.</param>
    </member>
    <member name="T:System.Net.Http.StringContent">
      <summary>문자열에 따라 HTTP 콘텐츠를 제공합니다.</summary>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.StringContent" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="content">
        <see cref="T:System.Net.Http.StringContent" />를 초기화하는 데 사용되는 콘텐츠입니다.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding)">
      <summary>
        <see cref="T:System.Net.Http.StringContent" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="content">
        <see cref="T:System.Net.Http.StringContent" />를 초기화하는 데 사용되는 콘텐츠입니다.</param>
      <param name="encoding">콘텐츠에 사용할 인코딩입니다.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding,System.String)">
      <summary>
        <see cref="T:System.Net.Http.StringContent" /> 클래스의 새 인스턴스를 만듭니다.</summary>
      <param name="content">
        <see cref="T:System.Net.Http.StringContent" />를 초기화하는 데 사용되는 콘텐츠입니다.</param>
      <param name="encoding">콘텐츠에 사용할 인코딩입니다.</param>
      <param name="mediaType">콘텐츠에 사용할 미디어 형식입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.AuthenticationHeaderValue">
      <summary>권한 부여, ProxyAuthorization, WWW-Authneticate 및 Proxy-Authenticate 헤더 값의 인증 정보를 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="scheme">권한 부여에 사용할 스키마입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="scheme">권한 부여에 사용할 스키마입니다.</param>
      <param name="parameter">요청되는 리소스에 대한 사용자 에이전트의 인증 정보를 포함하는 자격 증명입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다. </param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Parameter">
      <summary>요청되는 리소스에 대한 사용자 에이전트의 인증 정보를 포함하는 자격 증명을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.인증 정보가 들어 있는 자격 증명입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">인증 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 인증 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Scheme">
      <summary>권한 부여에 사용할 스키마를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.권한 부여에 사용할 스키마입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.TryParse(System.String,System.Net.Http.Headers.AuthenticationHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.CacheControlHeaderValue">
      <summary>Cache-Control 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.#ctor">
      <summary>
        <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Extensions">
      <summary>할당된 선택적 값이 각각 있는 캐시 확장 토큰입니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.할당된 선택적 값이 각각 있는 캐시 확장 토큰의 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 개체에 대한 해시 함수 역할을 합니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxAge">
      <summary>HTTP 클라이언트가 응답을 허용하는 최대 보관 기간(초로 지정)입니다. </summary>
      <returns>
        <see cref="T:System.TimeSpan" />를 반환합니다.초 단위 시간입니다. </returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStale">
      <summary>HTTP 클라이언트에서 만료 시간을 초과한 응답을 수락할지 여부입니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.HTTP 클라이언트에서 만료 시간을 초과한 응답을 수락하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStaleLimit">
      <summary>HTTP 클라이언트가 만료 시간을 초과한 응답을 수락하기까지의 최대 시간(초)입니다.</summary>
      <returns>
        <see cref="T:System.TimeSpan" />를 반환합니다.초 단위 시간입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MinFresh">
      <summary>HTTP 클라이언트가 응답을 허용하는 새로 고침 수명(초)입니다.</summary>
      <returns>
        <see cref="T:System.TimeSpan" />를 반환합니다.초 단위 시간입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MustRevalidate">
      <summary>원래 서버에서 캐시 엔트리가 오래되면 이후에 사용하는 모든 캐시 엔트리에 대해 유효성 재검사를 필요로 하는지 여부</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.원래 서버에서 캐시 엔트리가 오래되면 이후에 사용하는 모든 캐시 엔트리에 대해 유효성 재검사를 필요로 하는 경우 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCache">
      <summary>HTTP 클라이언트가 캐시된 응답을 수용할지 여부입니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.HTTP 클라이언트가 캐시된 응답을 허용하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCacheHeaders">
      <summary>HTTP 응답의 cache-control 헤더 필드에서 "no-cache" 지시문에 있는 필드 이름의 컬렉션입니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.필드 이름의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoStore">
      <summary>캐시가 HTTP 요청 메시지나 응답 중 어느 부분도 저장하면 안 되는지 여부입니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.캐시가 HTTP 요청 메시지나 응답 중 어느 부분도 저장하면 안 되는 경우에는 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoTransform">
      <summary>캐시 또는 프록시가 엔터티 본문을 변경해서는 안 되는지 여부입니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.캐시나 프록시가 전체 본문의 어떤 부분도 변경하지 않아야 하는 경우 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.OnlyIfCached">
      <summary>캐시가 다른 HTTP 요청의 제약 조건과 일치하는 캐시 엔트리를 사용하여 반응하는지, 또는 504(게이트웨이 제한 시간) 상태로 반응하는지 여부</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.캐시에서 HTTP 요청의 다른 제약 조건과 일관된 캐시된 엔트리를 사용하여 응답하거나 504(게이트웨이 시간 초과) 상태로 응답해야 하는 경우에는 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">캐시 제어 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 캐시 제어 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Private">
      <summary>HTTP 응답 메시지의 전체 또는 일부가 단일 사용자만 사용하기 위한 것이며 공유 캐시에서 캐시되지 않아야 하는지 여부</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.HTTP 응답 메시지가 단일 사용자용이므로 공유 캐시에 캐시되지 않아야 하는 경우에는 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.PrivateHeaders">
      <summary>HTTP 응답의 cache-control 헤더 필드에서 "private" 지시문에 있는 필드 이름의 컬렉션입니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.필드 이름의 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.ProxyRevalidate">
      <summary>원래 서버에서 공유 사용자 에이전트 캐시에 대한 캐시 엔트리가 오래되면 이후에 사용하는 모든 캐시 엔트리에 대해 유효성 재검사를 필요로 하는지 여부</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.원래 서버에서 공유 사용자 에이전트 캐시에 대한 캐시 엔트리가 오래되면 이후에 사용하는 모든 캐시 엔트리에 대해 유효성 재검사를 필요로 하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Public">
      <summary>대게 HTTP 응답이 공유되지 않는 캐시 내에서만 캐시할 수 있는지 여부도 포함하여 모든 캐시에서 캐시될 수 있는지 여부</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.일반적으로 캐시할 수 없거나 비공유 캐시 내에서만 캐시할 수 있더라도, HTTP 응답을 캐시에서 캐시할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.SharedMaxAge">
      <summary>초 단위로 지정된 공유된 최대 보관 기간으로, 공유 캐시의 경우 캐시 컨트롤 헤더나 Expires 헤더에서 "max-age" 지시문을 재정의하는 HTTP 응답에 사용됩니다.</summary>
      <returns>
        <see cref="T:System.TimeSpan" />를 반환합니다.초 단위 시간입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.TryParse(System.String,System.Net.Http.Headers.CacheControlHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentDispositionHeaderValue">
      <summary>Content-Disposition 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.Net.Http.Headers.ContentDispositionHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="source">
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />
      </param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dispositionType">
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />를 포함하는 문자열입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.CreationDate">
      <summary>파일을 만든 날짜입니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.파일 작성 날짜입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.DispositionType">
      <summary>콘텐츠 본문 부분에 대한 처리 형식입니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.처리 형식입니다. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileName">
      <summary>엔터티가 분리되고 별도 파일에 저장되는 경우 사용할 메시지 페이로드를 저장하기 위한 파일 이름을 생성하는 방법에 대한 제안입니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.제안된 파일 이름입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileNameStar">
      <summary>엔터티가 분리되고 별도 파일에 저장되는 경우 사용할 메시지 페이로드를 저장하기 위한 파일 이름을 생성하는 방법에 대한 제안입니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.폼 파일 이름*의 제안된 파일 이름입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ModificationDate">
      <summary>마지막으로 파일을 수정한 날짜입니다. </summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.파일 수정 날짜입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Name">
      <summary>콘텐츠 본문 부분에 대한 이름입니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.콘텐츠 본문 부분에 대한 이름입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Parameters">
      <summary>Content-Disposition 헤더를 포함하는 매개 변수의 집합입니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.매개 변수 컬렉션입니다. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">콘텐츠 처리 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 콘텐츠 처리 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ReadDate">
      <summary>파일에 마지막으로 읽은 날짜입니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.마지막으로 읽은 날짜입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Size">
      <summary>파일의 대략적인 크기(바이트)입니다. </summary>
      <returns>
        <see cref="T:System.Int64" />를 반환합니다.대략적인 크기(바이트)입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentDispositionHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentRangeHeaderValue">
      <summary>Content-Range 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="length">범위의 시작점이나 끝점(바이트 단위)입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="from">데이터를 보내기 시작하는 위치(바이트 단위)입니다.</param>
      <param name="to">데이터 보내기를 중지하는 위치(바이트 단위)입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64,System.Int64)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="from">데이터를 보내기 시작하는 위치(바이트 단위)입니다.</param>
      <param name="to">데이터 보내기를 중지하는 위치(바이트 단위)입니다.</param>
      <param name="length">범위의 시작점이나 끝점(바이트 단위)입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Equals(System.Object)">
      <summary>지정한 개체가 현재 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.From">
      <summary>데이터를 보내기 시작하는 위치를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Int64" />를 반환합니다.데이터를 보내기 시작하는 위치(바이트 단위)입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasLength">
      <summary>콘텐츠 범위 헤더에 길이가 지정되어 있는지 여부를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.Content-Range에 길이가 지정되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasRange">
      <summary>콘텐츠 범위에 범위가 지정되어 있는지 여부를 가져옵니다. </summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.Content-Range에 범위가 지정되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Length">
      <summary>전체 엔티티 본문의 길이를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Int64" />를 반환합니다.전체 엔티티 본문의 길이입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">콘텐츠 범위 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 콘텐츠 범위 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.To">
      <summary>데이터 보내기를 중지하는 위치를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Int64" />를 반환합니다.데이터 보내기를 중지하는 위치입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentRangeHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Unit">
      <summary>사용된 범위의 단위입니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.단위 범위를 포함하는 <see cref="T:System.String" /></returns>
    </member>
    <member name="T:System.Net.Http.Headers.EntityTagHeaderValue">
      <summary>엔터티 태그 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="tag">
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />를 포함하는 문자열입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String,System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="tag">
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />를 포함하는 문자열입니다.</param>
      <param name="isWeak">이 엔터티 태그 헤더가 약한 유효성 검사기인지 여부를 나타내는 값입니다.엔터티 태그 헤더가 약한 유효성 검사기이면 <paramref name="isWeak" />를 true로 설정해야 합니다.엔터티 태그 헤더가 강한 유효성 검사기이면 <paramref name="isWeak" />를 false로 설정해야 합니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Any">
      <summary>엔터티 태그 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />를 반환합니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.IsWeak">
      <summary>엔터티 태그가 약점 표시로 시작하는지 여부를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.취약성 표시기에 의해 엔터티 태그가 앞에 오면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">엔터티 태그 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 엔터티 태그 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Tag">
      <summary>불투명한 따옴표가 붙은 문자열을 가져옵니다. </summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.불투명한 따옴표가 붙은 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.TryParse(System.String,System.Net.Http.Headers.EntityTagHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpContentHeaders">
      <summary>RFC 2616에 정의된 콘텐츠 헤더의 컬렉션을 나타냅니다.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Allow">
      <summary>HTTP 응답의 Allow 콘텐츠 헤더 값을 가져옵니다. </summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.HTTP 응답의 Allow 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentDisposition">
      <summary>HTTP 응답의 Content-Disposition 콘텐츠 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />를 반환합니다.HTTP 응답의 Content-Disposition 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentEncoding">
      <summary>HTTP 응답의 Content-Encoding 콘텐츠 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.HTTP 응답의 Content-Encoding 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLanguage">
      <summary>HTTP 응답의 Content-Language 콘텐츠 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.HTTP 응답의 Content-Language 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLength">
      <summary>HTTP 응답의 Content-Length 콘텐츠 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Int64" />를 반환합니다.HTTP 응답의 Content-Length 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLocation">
      <summary>HTTP 응답의 Content-Location 콘텐츠 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Uri" />를 반환합니다.HTTP 응답의 Content-Location 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentMD5">
      <summary>HTTP 응답의 Content-MD5 콘텐츠 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Byte" />를 반환합니다.HTTP 응답의 Content-MD5 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentRange">
      <summary>HTTP 응답의 Content-Range 콘텐츠 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />를 반환합니다.HTTP 응답의 Content-Range 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentType">
      <summary>HTTP 응답의 Content-Type 콘텐츠 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />를 반환합니다.HTTP 응답의 Content-Type 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Expires">
      <summary>HTTP 응답의 Expires 콘텐츠 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.HTTP 응답의 Expires 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.LastModified">
      <summary>HTTP 응답의 Last-Modified 콘텐츠 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.HTTP 응답의 Last-Modified 콘텐츠 헤더입니다.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaders">
      <summary>RFC 2616에 정의된 헤더와 값의 컬렉션입니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.#ctor">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>지정된 헤더 및 헤더 값을 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 컬렉션에 추가합니다.</summary>
      <param name="name">컬렉션에 추가할 헤더입니다.</param>
      <param name="values">컬렉션에 추가할 헤드 값의 목록입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.String)">
      <summary>지정된 헤더 및 헤더 값을 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 컬렉션에 추가합니다.</summary>
      <param name="name">컬렉션에 추가할 헤더입니다.</param>
      <param name="value">헤더의 내용입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Clear">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 컬렉션에서 헤더를 모두 제거합니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Contains(System.String)">
      <summary>특정 헤더가 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 컬렉션에 있는 경우 반환합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 헤더가 컬렉션에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="name">특정 헤더입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetEnumerator">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 인스턴스에서 반복할 수 있는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />를 반환합니다.<see cref="T:System.Net.Http.Headers.HttpHeaders" />에 대한 열거자입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetValues(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 컬렉션에 저장된 지정된 헤더에 대한 모든 헤더 값을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerable`1" />를 반환합니다.헤더 문자열의 배열입니다.</returns>
      <param name="name">값을 반환할 지정된 헤더입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Remove(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 컬렉션에서 지정된 헤더를 제거합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.</returns>
      <param name="name">컬렉션에서 제거할 헤더의 이름입니다. </param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" />을 반복할 수 있는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />를 반환합니다.<see cref="T:System.Net.Http.Headers.HttpHeaders" />을 반복할 수 있는 <see cref="T:System.Collections.IEnumerator" /> 인터페이스 구현의 인스턴스입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>지정된 헤더 및 값이 제공된 정보의 유효성을 검사하지 않고 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 컬렉션에 추가되었는지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 헤더 <paramref name="name" /> 및 <paramref name="values" />를 컬렉션에 추가할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="name">컬렉션에 추가할 헤더입니다.</param>
      <param name="values">헤더의 값입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.String)">
      <summary>지정된 헤더 및 해당 값이 제공된 정보의 유효성을 검사하지 않고 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 컬렉션에 추가되었는지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 헤더 <paramref name="name" /> 및 <paramref name="value" />를 컬렉션에 추가할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="name">컬렉션에 추가할 헤더입니다.</param>
      <param name="value">헤더의 내용입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
      <summary>지정된 헤더와 지정된 값이 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 컬렉션에 저장되는 경우 반환합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.true는 지정된 헤더 <paramref name="name" />이고 values는 컬렉션에 저장됩니다. 그렇지 않으면 false입니다.</returns>
      <param name="name">지정된 헤더입니다.</param>
      <param name="values">지정된 헤더 값입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaderValueCollection`1">
      <summary>헤더 값의 컬렉션을 나타냅니다.</summary>
      <typeparam name="T">헤더 컬렉션 형식입니다.</typeparam>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Add(`0)">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />에 엔트리를 추가합니다.</summary>
      <param name="item">헤더 컬렉션에 추가할 항목입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Clear">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />에서 모든 엔트리를 제거합니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Contains(`0)">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />에 항목이 포함되어 있는지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.엔트리가 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 인스턴스에 포함되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">헤더 컬렉션을 찾을 항목입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.CopyTo(`0[],System.Int32)">
      <summary>대상 배열의 지정된 인덱스에서 시작하여 전체 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />을 호환되는 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.Count">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />의 헤더 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.컬렉션의 헤더 수입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.GetEnumerator">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" />를 반환합니다.<see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 인스턴스에 대한 열거자입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.IsReadOnly">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 인스턴스가 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 인스턴스가 읽기 전용이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ParseAdd(System.String)">
      <summary>엔트리를 구문 분석하고 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />에 추가합니다.</summary>
      <param name="input">추가할 엔트리입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Remove(`0)">
      <summary>지정된 항목을 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />에서 제거합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="item" />이 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 인스턴스에서 제거되면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">제거할 항목입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />를 반환합니다.<see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 인스턴스에 대한 열거자입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.TryParseAdd(System.String)">
      <summary>입력을 구문 분석하고 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />에 추가할 수 있는지 여부를 결정합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />을 구문 분석하고 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 인스턴스에 추가할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 검사할 엔트리입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpRequestHeaders">
      <summary>RFC 2616에 정의된 요청 헤더의 컬렉션을 나타냅니다.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Accept">
      <summary>HTTP 요청의 Accept 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Accept 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptCharset">
      <summary>HTTP 요청의 Accept-Charset 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Accept-Charset 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptEncoding">
      <summary>HTTP 요청의 Accept-Encoding 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Accept-Encoding 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptLanguage">
      <summary>HTTP 요청의 Accept-Language 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Accept-Language 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Authorization">
      <summary>HTTP 요청의 Authorization 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />를 반환합니다.HTTP 요청의 Authorization 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.CacheControl">
      <summary>HTTP 요청의 Cache-Control 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />를 반환합니다.HTTP 요청의 Cache-Control 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Connection">
      <summary>HTTP 요청의 Connection 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Connection 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ConnectionClose">
      <summary>HTTP 요청에 대한 Connection 헤더에 Close가 포함되는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.Connection 헤더에 닫기가 들어 있으면 true 이고, otherwise false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Date">
      <summary>HTTP 요청의 Date 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.HTTP 요청의 Date 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Expect">
      <summary>HTTP 요청의 Expect 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Expect 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ExpectContinue">
      <summary>HTTP 요청에 대한 Expect 헤더에 Continue가 포함되는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.Expect 헤더에 계속이 들어 있으면 true이고, otherwise false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.From">
      <summary>HTTP 요청의 From 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.HTTP 요청의 From 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Host">
      <summary>HTTP 요청의 Host 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.HTTP 요청의 Host 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfMatch">
      <summary>HTTP 요청의 If-Match 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 If-Match 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfModifiedSince">
      <summary>HTTP 요청의 If-Modified-Since 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.HTTP 요청의 If-Modified-Since 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfNoneMatch">
      <summary>HTTP 요청의 If-None-Match 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 If-None-Match 헤더 값을 가져옵니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfRange">
      <summary>HTTP 요청의 If-Range 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />를 반환합니다.HTTP 요청의 If-Range 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfUnmodifiedSince">
      <summary>HTTP 요청의 If-Unmodified-Since 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.HTTP 요청의 If-Unmodified-Since 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.MaxForwards">
      <summary>HTTP 요청의 Max-Forwards 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.HTTP 요청의 Max-Forwards 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Pragma">
      <summary>HTTP 요청의 Pragma 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Pragma 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ProxyAuthorization">
      <summary>HTTP 요청의 Proxy-Authorization 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />를 반환합니다.HTTP 요청의 Proxy-Authorization 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Range">
      <summary>HTTP 요청의 Range 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />를 반환합니다.HTTP 요청의 Range 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Referrer">
      <summary>HTTP 요청의 Referer 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Uri" />를 반환합니다.HTTP 요청의 Referer 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TE">
      <summary>HTTP 요청의 TE 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 TE 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Trailer">
      <summary>HTTP 요청의 Trailer 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Trailer 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncoding">
      <summary>HTTP 요청의 Transfer-Encoding 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Transfer-Encoding 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncodingChunked">
      <summary>HTTP 요청에 대한 Transfer-Encoding 헤더에 chunked가 포함되는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.Transfer-Encoding 헤더에 청크가 들어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Upgrade">
      <summary>HTTP 요청의 Upgrade 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Upgrade 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.UserAgent">
      <summary>HTTP 요청의 User-Agent 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 User-Agent 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Via">
      <summary>HTTP 요청의 Via 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Via 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Warning">
      <summary>HTTP 요청의 Warning 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 요청의 Warning 헤더 값입니다.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpResponseHeaders">
      <summary>RFC 2616에 정의된 응답 헤더의 컬렉션을 나타냅니다.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.AcceptRanges">
      <summary>HTTP 응답의 Accept-Ranges 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Accept-Ranges 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Age">
      <summary>HTTP 응답의 Age 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.TimeSpan" />를 반환합니다.HTTP 응답의 Age 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.CacheControl">
      <summary>HTTP 응답의 Cache-Control 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />를 반환합니다.HTTP 응답의 Cache-Control 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Connection">
      <summary>HTTP 응답의 Connection 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Connection 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ConnectionClose">
      <summary>HTTP 응답에 대한 Connection 헤더에 Close가 포함되는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.Connection 헤더에 닫기가 들어 있으면 true 이고, otherwise false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Date">
      <summary>HTTP 응답의 Date 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.HTTP 응답의 Date 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ETag">
      <summary>HTTP 응답의 ETag 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />를 반환합니다.HTTP 응답의 ETag 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Location">
      <summary>HTTP 응답의 Location 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Uri" />를 반환합니다.HTTP 응답의 Location 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Pragma">
      <summary>HTTP 응답의 Pragma 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Pragma 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ProxyAuthenticate">
      <summary>HTTP 응답의 Proxy-Authenticate 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Proxy-Authenticate 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.RetryAfter">
      <summary>HTTP 응답의 Retry-After 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />를 반환합니다.HTTP 응답의 Retry-After 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Server">
      <summary>HTTP 응답의 Server 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Server 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Trailer">
      <summary>HTTP 응답의 Trailer 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Trailer 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncoding">
      <summary>HTTP 응답의 Transfer-Encoding 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Transfer-Encoding 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncodingChunked">
      <summary>HTTP 응답에 대한 Transfer-Encoding 헤더에 chunked가 포함되는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.Transfer-Encoding 헤더에 청크가 들어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Upgrade">
      <summary>HTTP 응답의 Upgrade 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Upgrade 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Vary">
      <summary>HTTP 응답의 Vary 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Vary 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Via">
      <summary>HTTP 응답의 Via 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Via 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Warning">
      <summary>HTTP 응답의 Warning 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 Warning 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.WwwAuthenticate">
      <summary>HTTP 응답의 WWW-Authenticate 헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />를 반환합니다.HTTP 응답의 WWW-Authenticate 헤더 값입니다.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeHeaderValue">
      <summary>RFC 2616에 정의된 대로 Content-Type 헤더에 사용된 미디어 형식을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="source"> 새 인스턴스를 초기화하는 데 사용되는 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="mediaType">새 인스턴스를 초기화하는 문자열로 나타낸 소스입니다. </param>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.CharSet">
      <summary>문자 집합을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.문자 집합입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.MediaType">
      <summary>미디어 형식의 헤더 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.미디어 형식의 헤더 값입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.Parameters">
      <summary>미디어 형식의 헤더 값 매개 변수를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.미디어 형식의 헤더 값 매개 변수입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">미디어 유형 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 미디어 형식 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue">
      <summary>Content-Type 헤더에 사용된 추가 품질 요소를 가진 미디어 형식을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="mediaType">새 인스턴스를 초기화하는 문자열로 나타내는 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />입니다. </param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="mediaType">새 인스턴스를 초기화하는 문자열로 나타내는 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />입니다.</param>
      <param name="quality">이 헤더 값과 연결된 품질입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">미디어 형식과 품질 헤더 값 정보를 나타내는 문자열</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 품질 헤더 값 정보가 포함된 유효한 미디어 형식이 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Quality">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />에 대한 품질 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Double" />를 반환합니다.<see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 개체의 품질 값입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeWithQualityHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueHeaderValue">
      <summary>RFC 2616에 정의된 대로 다양한 헤더에 사용된 이름/값 쌍을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.Net.Http.Headers.NameValueHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="source">새 인스턴스를 초기화하는 데 사용되는 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">헤더 이름입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">헤더 이름입니다.</param>
      <param name="value">헤더 값입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Name">
      <summary>헤더 이름을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.헤더 이름입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">이름 값 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 이름 값 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Value">
      <summary>헤더 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.헤더 값입니다.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue">
      <summary>RFC 2616에 정의된 대로 다양한 헤더에 사용된 매개 변수를 가진 이름/값 쌍을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.Net.Http.Headers.NameValueWithParametersHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="source">새 인스턴스를 초기화하는 데 사용되는 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">헤더 이름입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">헤더 이름입니다.</param>
      <param name="value">헤더 값입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parameters">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 개체에서 매개 변수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.매개 변수를 포함하는 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">이름 값과 매개 변수 헤더 값 정보를 나타내는 문자열</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 매개 변수 헤더 값 정보가 포함된 유효한 이름 값이 아닙니다.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueWithParametersHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ProductHeaderValue">
      <summary>User-Agent 헤더의 제품 토큰 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">제품 이름입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">제품 이름 값입니다.</param>
      <param name="version">제품 버전 값입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Name">
      <summary>제품 토큰의 이름을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.제품 토큰의 이름입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">제품 헤더 값 정보를 나타내는 문자열입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Version">
      <summary>제품 토큰의 버전을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.제품 토큰의 버전입니다. </returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductInfoHeaderValue">
      <summary>User-Agent 헤더에서 제품 또는 메모일 수 있는 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.Net.Http.Headers.ProductHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="product">새 인스턴스를 초기화하는 데 사용되는 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="comment">주석 값입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="productName">제품 이름 값입니다.</param>
      <param name="productVersion">제품 버전 값입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Comment">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 개체에서 주석을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.이 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />의 주석 값입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">제품 정보 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 제품 정보 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Product">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 개체에서 제품을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />를 반환합니다.이 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />의 제품 값입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductInfoHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeConditionHeaderValue">
      <summary>날짜/시간 또는 엔터티 태그 값일 수 있는 If-Range 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="date">새 인스턴스를 초기화하는 데 사용되는 날짜 값입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.Net.Http.Headers.EntityTagHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="entityTag">새 인스턴스를 초기화하는 데 사용되는 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="entityTag">새 인스턴스를 초기화하는 데 사용하는 문자열로 나타내는 엔터티 태그입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.Date">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 개체에서 날짜를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.<see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 개체의 날짜입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.EntityTag">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 개체에서 엔터티 태그를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 개체의 엔터티 태그입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">범위 조건 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 범위 조건 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeConditionHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeHeaderValue">
      <summary>범위 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>바이트 범위를 사용하여 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="from">데이터를 보내기 시작하는 위치입니다.</param>
      <param name="to">데이터 보내기를 중지하는 위치입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" />이 <paramref name="to" />보다 큽니다.또는 <paramref name="from" /> 또는 <paramref name="to" />가 0 미만인 경우 </exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">범위 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 범위 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Ranges">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 개체에서 지정된 범위를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.<see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 개체의 범위입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Unit">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 개체에서 단위를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.<see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 개체의 단위입니다.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeItemHeaderValue">
      <summary>범위 헤더 값에서 바이트 범위를 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="from">데이터를 보내기 시작하는 위치입니다.</param>
      <param name="to">데이터 보내기를 중지하는 위치입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" />이 <paramref name="to" />보다 큽니다.또는 <paramref name="from" /> 또는 <paramref name="to" />가 0 미만인 경우 </exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.From">
      <summary>데이터를 보내기 시작하는 위치를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Int64" />를 반환합니다.데이터를 보내기 시작하는 위치입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.To">
      <summary>데이터 보내기를 중지하는 위치를 가져옵니다. </summary>
      <returns>
        <see cref="T:System.Int64" />를 반환합니다.데이터 보내기를 중지하는 위치입니다. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RetryConditionHeaderValue">
      <summary>날짜/시간 또는 timespan 값일 수 있는 Retry-After 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="date">새 인스턴스를 초기화하는 데 사용되는 날짜와 시간 오프셋입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.TimeSpan)">
      <summary>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="delta">새 인스턴스를 초기화하는 데 사용하는 델타(초)입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Date">
      <summary>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 개체의 날짜와 시간 오프셋을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.<see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 개체의 날짜와 시간 오프셋입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Delta">
      <summary>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 개체에서 델타(초)를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.TimeSpan" />를 반환합니다.<see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 개체의 델타(초)입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">다시 시도 조건 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 다시 시도 조건 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RetryConditionHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.StringWithQualityHeaderValue">
      <summary>선택적 품질의 문자열 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">새 인스턴스를 초기화하는 데 사용되는 문자열입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">새 인스턴스를 초기화하는 데 사용되는 문자열입니다.</param>
      <param name="quality">새 인스턴스를 초기화하는 데 사용되는 품질 요소입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Equals(System.Object)">
      <summary>지정한 개체가 현재 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">품질 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 품질 헤더 값 정보가 포함된 유효한 문자열이 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Quality">
      <summary>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 개체에서 품질 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Double" />를 반환합니다.<see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 개체의 품질 요소입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.StringWithQualityHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Value">
      <summary>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 개체의 문자열 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.<see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 개체의 문자열 값입니다.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingHeaderValue">
      <summary>accept-encoding 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.Net.Http.Headers.TransferCodingHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="source">새 인스턴스를 초기화하는 데 사용되는 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 개체입니다. </param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">새 인스턴스를 초기화하는 데 사용되는 문자열입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Equals(System.Object)">
      <summary>지정한 개체가 현재 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Parameters">
      <summary>전송 코딩 매개 변수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />를 반환합니다.전송 코딩 매개 변수입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">전송 코딩 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 전송 코딩 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Value">
      <summary>전송 코딩 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.전송 코딩 값입니다.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue">
      <summary>선택적 품질 요소의 Accept-Encoding 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">새 인스턴스를 초기화하는 데 사용되는 문자열입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">새 인스턴스를 초기화하는 데 사용되는 문자열입니다.</param>
      <param name="quality">품질 요소의 값입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">전송 코딩 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 품질 헤더 값 정보가 포함된 유효한 전송 코딩이 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Quality">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />에서 품질 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Double" />를 반환합니다.<see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />의 품질 요소입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingWithQualityHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ViaHeaderValue">
      <summary>Via 헤더 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="protocolVersion">받은 프로토콜의 프로토콜 버전입니다.</param>
      <param name="receivedBy">요청 또는 응답을 받은 호스트 및 포트입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="protocolVersion">받은 프로토콜의 프로토콜 버전입니다.</param>
      <param name="receivedBy">요청 또는 응답을 받은 호스트 및 포트입니다.</param>
      <param name="protocolName">받은 프로토콜의 프로토콜 이름입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="protocolVersion">받은 프로토콜의 프로토콜 버전입니다.</param>
      <param name="receivedBy">요청 또는 응답을 받은 호스트 및 포트입니다.</param>
      <param name="protocolName">받은 프로토콜의 프로토콜 이름입니다.</param>
      <param name="comment">수신자 프록시 또는 게이트웨이의 소프트웨어를 식별하는 데 사용되는 설명 필드입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.Comment">
      <summary>수취 프록시 또는 게이트웨이의 소프트웨어를 식별하는데 사용되는 설명 필드를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.수신자 프록시 또는 게이트웨이의 소프트웨어를 식별하는 데 사용되는 설명 필드입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드를 반환합니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />를 반환합니다.<see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 인스턴스입니다.</returns>
      <param name="input">헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 Via 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolName">
      <summary>받은 프로토콜의 프로토콜 이름을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.프로토콜 이름입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolVersion">
      <summary>받은 프로토콜의 프로토콜 버전을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.프로토콜 버전입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ReceivedBy">
      <summary>요청 또는 응답을 받은 호스트 및 포트를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.요청 또는 응답을 받은 호스트 및 포트입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.TryParse(System.String,System.Net.Http.Headers.ViaHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 버전입니다.</param>
    </member>
    <member name="T:System.Net.Http.Headers.WarningHeaderValue">
      <summary>경고 헤더에서 사용하는 경고 값을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="code">특정 경고 코드입니다.</param>
      <param name="agent">경고를 연결하는 호스트입니다.</param>
      <param name="text">경고 텍스트를 포함하는 따옴표 붙은 문자열</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String,System.DateTimeOffset)">
      <summary>
        <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="code">특정 경고 코드입니다.</param>
      <param name="agent">경고를 연결하는 호스트입니다.</param>
      <param name="text">경고 텍스트를 포함하는 따옴표 붙은 문자열</param>
      <param name="date">경고의 날짜/시간 스탬프입니다.</param>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Agent">
      <summary>경고를 연결하는 호스트를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.경고를 연결하는 호스트입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Code">
      <summary>특정 경고 코드를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.특정 경고 코드입니다.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Date">
      <summary>경고의 날짜/타임 스탬프를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" />를 반환합니다.경고의 날짜/시간 스탬프입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 개체와 같은지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.지정된 <see cref="T:System.Object" />가 현재 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 개체와 비교할 개체입니다.</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 개체에 대한 해시 함수로 사용됩니다.</summary>
      <returns>
        <see cref="T:System.Int32" />를 반환합니다.현재 개체의 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Parse(System.String)">
      <summary>문자열을 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 인스턴스로 변환합니다.</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 인스턴스를 반환합니다.</returns>
      <param name="input">인증 헤더 값 정보를 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" />이 null 참조인 경우</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" />은 유효한 인증 헤더 값 정보가 아닙니다.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Text">
      <summary>경고 텍스트가 포함된 따옴표 붙은 문자열을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.경고 텍스트를 포함하는 따옴표 붙은 문자열</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.ToString">
      <summary>현재 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 개체를 나타내는 문자열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.String" />를 반환합니다.현재 개체를 나타내는 문자열입니다.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.TryParse(System.String,System.Net.Http.Headers.WarningHeaderValue@)">
      <summary>문자열이 유효한 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 정보인지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Boolean" />를 반환합니다.<paramref name="input" />이 유효한 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 정보이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="input">유효성을 확인할 문자열입니다.</param>
      <param name="parsedValue">문자열의 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 버전입니다.</param>
    </member>
  </members>
</doc>