﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.ViewModelEntities;

namespace RevCord.DataContracts.Request
{
    public class UserManagementRequest : RequestBase
    {
        #region Properties

        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public long UserId { get; set; }
        public bool ChAppUserName { get; set; }
        public bool GetOnlyAudioChannels { get; set; }
        public bool GetOnlyInquireChannels { get; set; }
        public bool ForcePasswordChange { get; set; }
        public bool IsRoleBasedAccessEnabled { get; set; }
        public bool IsOnlyIQ3ModeEnabled { get; set; }
        public bool Is2FAEnabled { get; set; }


        #endregion

        #region Associations

        public AppUser AppUser { get; set; }
        public GlobalGroup GlobalGroup { get; set; }
        public User User { get; set; }
        public EventInvitation EventInvitation { get; set; }
        public EventDispatcherInfo EventDispatcherInfo { get; set; }
        public UserLicenseAgreement UserLicenseAgreement { get; set; }
        #endregion

        public Recorder Recorder { get; set; }
        public List<Recorder> Recorders { get; set; }
        public List<RecUser> RecUserIds { get; set; }

        #region TreeView

        public int UserNum { get; set; }
        public int RevSyncServerUserNum { get; set; }
        public string UserIdTree { get; set; }
        public int AuthNum { get; set; }
        public string AuthType { get; set; }
        public int Type { get; set; }
        public int SelectType { get; set; }
        public bool IsEnterpriseTree { get; set; }
        #endregion

        #region Invitation
        public int InvitationId { get; set; }

        public InvitationStatus InvitationStatus { get; set; }

        public Invitation Invitation { get; set; }

        public InvitationCriteria Criteria { get; set; }

        public RegisterUser RegisterUser { get; set; }
        #endregion
		
		#region Inquire Custom Marker
        public CustomMarkersData cmData { get; set; }
        #endregion
    }
}
