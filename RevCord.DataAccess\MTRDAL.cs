﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using RevCord.DataContracts.VoiceRecEntities;
using System.Data.SqlClient;
using RevCord.Util;
using RevCord.DataContracts;
using System.Text.RegularExpressions;
using RevCord.DataAccess.Util;
using Newtonsoft.Json;
using RevCord.DataContracts.IQ3InspectionEntities;

namespace RevCord.DataAccess
{
    public class MTRDAL
    {
        private int _tenantId;
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("dashboardCommandTimeout", 300);
        public MTRDAL(int tenantId)
        {
            _tenantId = tenantId;
        }
        #region Playlist
        public void SaveMTRReport(MTRCertificate certificate, int playlistId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.MTR_JSON_INSERT;

                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
                    cmd.Parameters.AddWithValue("@CompanyName", certificate.CompanyName ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@HeatAndPiece", certificate.HeatAndPiece ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Date", certificate.Date);

                    cmd.Parameters.AddWithValue("@SoldTo", JsonConvert.SerializeObject(new[] { certificate.SoldTo }) ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ShipTo", JsonConvert.SerializeObject(new[] { certificate.ShipTo }) ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Specification", JsonConvert.SerializeObject(certificate.Specification) ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@MechanicalTests", JsonConvert.SerializeObject(certificate.MechanicalTests) ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ChemicalCompositions", JsonConvert.SerializeObject(certificate.ChemicalCompositions) ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Certification", JsonConvert.SerializeObject(certificate.Certification) ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Signature", JsonConvert.SerializeObject(certificate.Signature) ?? (object)DBNull.Value);

                    cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<Signature> GetSignOffHistory(int signDocumentId, int tenantId)
        {
            List<Signature> mtrSignOff = new List<Signature>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.GETMTR_SIGNOFFHISTORTY;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@DocumentId", signDocumentId);

                    conn.Open();

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                var signature = new Signature();
                                signature.ReportID = Convert.ToInt32(dr["DocumentID"]);
                                signature.SignatureID = Convert.ToInt32(dr["SignerID"]);
                                signature.SignatoryName = Convert.ToString(dr["Name"]);
                                signature.SignatureDate = Convert.ToDateTime(dr["CreatedDate"]);
                                mtrSignOff.Add(signature);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return mtrSignOff;
        }

        public void SaveMTRChecklist(Checklist certificate, int playlistId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.MTR_CHECKLIST_INSERT;

                    cmd.Parameters.AddWithValue("@PlaylistId", playlistId);
                    cmd.Parameters.AddWithValue("@ReportID", certificate.ReportID);
                    cmd.Parameters.AddWithValue("@IsVerified", certificate.IsVerified);
                    cmd.Parameters.AddWithValue("@VerifiedDate", (object)certificate.VerifiedDate ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@VerifiedBy", certificate.VerifiedBy);

                    cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion
    }
}
