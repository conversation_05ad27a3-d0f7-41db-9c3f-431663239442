﻿using System;
using System.Collections.Generic;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.VoiceRecEntities;


namespace RevCord.ServiceContracts
{
    public interface IVoiceLoggingService
    {
        VRResponse SearchCalls(VRRequest request);
        VRResponse SearchCallsLite(VRRequest request);
        //VRResponse SearchCallsInPrimaryDB(VRRequest request);
        //VRResponse SearchCallsInChainedDBs(VRRequest request);
        //VRResponse SearchCallsByIdsInChainedDBs(VRRequest request);
        VRResponse GetCall(string callId, int tenantId);
        VRResponse GetCallDetailsFromRecorder(Recorder recorder, string callId, int tenantId);
        VRResponse GetCallByIdFromRecorder(Recorder recorder, string callId, int tenantId);

        VRResponse SearchCallsInAllRecorders(VRRequest request);
        VRResponse FindCallsInAllRecorders(VRRequest request);
        VRResponse PerformCustomSearch(VRRequest request);

        VRResponse UpdateCallsCustomFields(string callIds, string fieldName, string fieldText, int userId, int tenantId);
        VRResponse UpdateCustomFieldsByRecorder(VRRequest request);
        VRResponse UpdateCallCustomFields(VRRequest request);
        VRResponse UpdateCallRetention(string callId, bool retainValue, int tenantId);
        VRResponse UpdateCallRetentionByRecorder(VRRequest request, string callId, bool retainValue);
        VRResponse SaveBookmark(VRRequest request);
        VRResponse GetSearchPageData(VRRequest request);
        VRResponse GetSearchPageDataLite(VRRequest request);
        VRResponse GetTreeViewData(VRRequest request);
        //List<MonitorChannel> GetMonitorChannels(VRRequest vrReq);
        VRResponse GetAudioChannels(VRRequest request);
        VRResponse GetAudioChannel(VRRequest request);
        VRResponse CreateAudioChannels(VRRequest request);
        VRResponse UpdateAudioChannel(VRRequest request);

        VRResponse DeleteAudioChannels(VRRequest request);
        VRResponse GetChannelsToMonitor(VRRequest request);
        VRResponse GetChannelsToMonitorFromAllRecorders(VRRequest request);
        VRResponse SaveTranscription(VRRequest request);
        VRResponse GetAllSites(VRRequest vrRequest);
        VRResponse GetChatTranscript(string eventId);
        VRResponse FetchConversation(string jsonFilePath, int tenantId);

        VRResponse GetChatTranscript(string eventId, int tenantId);

        VRResponse GetAllScheduledEvent(VRRequest vrRequest);
        VRResponse GetScheduledEventDetails(VRRequest vrRequest);
        VRResponse SaveScheduledEventAndGetAll(VRRequest vrRequest);
        VRResponse UpdateScheduledEventAndGetAll(VRRequest vrRequest);
        List<ScheduleEventParticipant> FetchAllParticipant(VRRequest vrRequest);
        List<ScheduleEventParticipant> FetchParticipantsByEventId(VRRequest vrRequest);

        bool DeleteScheduledEvent(VRRequest vrRequest);
        VRResponse DeleteAndGetAllScheduledEvents(VRRequest vrRequest);
        VRResponse SaveScheduledEvent(VRRequest vrRequest);
        VRResponse UpdateScheduledEvent(VRRequest vrRequest);

        bool IsScheduledEventInvitationActive(VRRequest vrRequest);
        string GetCalendarId(VRRequest vrRequest);
        int InsertCalendarId(string calendarId, int tenantId);

        bool UpdateCalendarEventId(int schduleEventId, string eventId, int tenantId);
        VRResponse CallAuditSave(VRRequest vRRequest);

        VRResponse SaveUserSearch(VRRequest vRRequest);
        VRResponse GetUserSearches(VRRequest vRRequest);
        VRResponse GetUserSearch(int id, int tenantId);
        bool DeleteUserSearch(int id, int tenantId);
        VRResponse UpdateBookmarkNotes(int bookmarkId, string modifiedBookMarkNotes, int tenantId);
        VRResponse UpdateBookmark(int bookmarkId, string modifiedBookMar, int tenantId);
        VRResponse DeleteBookmark(int bookmarkId, int tenantId);
        VRResponse UpdateEventName(string eventId, string eventName, int tenantId);
        VRResponse GetEventCurrentStatus(string eventId, int tenantId);
        Tuple<string, string> GetAppUserCredentialsByExt(int extId, int tenantId);

        VRResponse SaveMultiCallEvaluation(VRRequest vRRequest);
        VRResponse GetCustomFields(VRRequest vRRequest);

        VRResponse FetchTenantGateways(VRRequest vrRequest);
        VRResponse SaveTenantGateway(VRRequest vRRequest);
        VRResponse UpdateTenantGateway(VRRequest vRRequest);
        VRResponse DeleteTenantGateway(VRRequest request);
        VRResponse FetchAvailableTenantGateways(VRRequest vrRequest);

        bool VerifyInvitationAccessCode(int invitationId, int tenatId, string accessCode);
        VRResponse FetchPreInspectionData(VRRequest vrRequest);
    }
}