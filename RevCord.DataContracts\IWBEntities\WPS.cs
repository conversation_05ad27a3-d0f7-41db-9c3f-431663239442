﻿using System;
using System.Collections.Generic;

namespace RevCord.DataContracts.IWBEntities
{
    public class Wps
    {
        public int Id { get; set; }
        public string ReferenceDocs { get; set; }
        public string Scope { get; set; }
        public string RecordNo { get; set; }
        public DateTime DateQualified { get; set; }
        public string CompanyName { get; set; }
        public string Comments { get; set; }


        public IList<WPSSection> Sections { get; set; }


        public int CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int? LastModifiedBy { get; set; }


    }
}
