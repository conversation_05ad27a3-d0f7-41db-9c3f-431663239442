﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.ByteArrayContent">
      <summary>バイト配列に基づいて HTTP コンテンツを提供します。</summary>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[])">
      <summary>
        <see cref="T:System.Net.Http.ByteArrayContent" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="content">
        <see cref="T:System.Net.Http.ByteArrayContent" /> の初期化に使用されるコンテンツ。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> パラメーターが null です。</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>
        <see cref="T:System.Net.Http.ByteArrayContent" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="content">
        <see cref="T:System.Net.Http.ByteArrayContent" /> の初期化に使用されるコンテンツ。</param>
      <param name="offset">
        <see cref="T:System.Net.Http.ByteArrayContent" /> の初期化に使用される <paramref name="content" /> パラメーターのオフセット (バイト単位)。</param>
      <param name="count">
        <see cref="T:System.Net.Http.ByteArrayContent" /> を初期化するために使用される <paramref name="offset" /> パラメーターから始まる <paramref name="content" /> のバイト数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> パラメーターが null です。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> パラメーターが 0 未満です。または<paramref name="offset" /> パラメーターが <paramref name="content" /> パラメーターで指定されたコンテンツの長さを超えています。または<paramref name="count " />パラメーターが 0 未満です。または<paramref name="count" /> パラメーターが、<paramref name="content" /> パラメーターで指定されたコンテンツの長さから <paramref name="offset" /> パラメーターを引いた値を超えています。</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.CreateContentReadStreamAsync">
      <summary>バッキング ストアが <see cref="T:System.Net.Http.ByteArrayContent" /> からのメモリである読み取り用 HTTP コンテンツ ストリームを非同期操作として作成します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>非同期操作として HTTP コンテンツ ストリームにコンストラクターで提供されるバイト配列をシリアル化して記述します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="stream">対象のストリーム。</param>
      <param name="context">トランスポートに関する情報 (チャネル バインディング トークンなど)。このパラメーターは、null の場合もあります。</param>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.TryComputeLength(System.Int64@)">
      <summary>バイト配列が有効な長さ (バイト単位) かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="length" /> が有効な長さである場合は true。それ以外の場合は false。</returns>
      <param name="length">バイト配列の長さ (バイト単位)。</param>
    </member>
    <member name="T:System.Net.Http.ClientCertificateOption">
      <summary>クライアント証明書がどのように提供されるかを指定します。</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Automatic">
      <summary>
        <see cref="T:System.Net.Http.HttpClientHandler" /> は、使用できるすべてのクライアント証明書を自動的に提供しようと試みます。</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Manual">
      <summary>アプリケーションは <see cref="T:System.Net.Http.WebRequestHandler" /> に、クライアント証明書を手動で提供します。この値が既定値です。</summary>
    </member>
    <member name="T:System.Net.Http.DelegatingHandler">
      <summary>内部ハンドラーと呼ばれる、別のハンドラーへ HTTP 応答メッセージの処理をデリゲートする HTTP ハンドラーの型。</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor">
      <summary>
        <see cref="T:System.Net.Http.DelegatingHandler" /> クラスの新しいインスタンスを作成します。</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>特定の内部ハンドラーを使用して、<see cref="T:System.Net.Http.DelegatingHandler" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="innerHandler">HTTP 応答メッセージ処理用の内部ハンドラー。</param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.DelegatingHandler" /> が使用しているアンマネージ リソースを解放します。オプションでマネージ リソースも破棄します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="P:System.Net.Http.DelegatingHandler.InnerHandler">
      <summary>HTTP 応答メッセージを処理する内部ハンドラーを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMessageHandler" /> を返します。HTTP 応答メッセージ用の内部ハンドラー。</returns>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>非同期操作としてサーバーに送信する内部ハンドラーに HTTP 要求を送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="request">サーバーに送信する HTTP 要求メッセージ。</param>
      <param name="cancellationToken">操作をキャンセルするキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> が null でした。</exception>
    </member>
    <member name="T:System.Net.Http.FormUrlEncodedContent">
      <summary>application/x-www-form-urlencoded MIME 型を使用してエンコードされた名前と値の組のコンテナー。</summary>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>指定した名前と値のペアのコレクションを使用して、<see cref="T:System.Net.Http.FormUrlEncodedContent" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="nameValueCollection">名前と値のペアのコレクション。</param>
    </member>
    <member name="T:System.Net.Http.HttpClient">
      <summary>URI で識別されるリソースに HTTP 要求を送信し、そのリソースから HTTP 応答を受信するための基本クラスを提供します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>指定したハンドラーを使用して、<see cref="T:System.Net.Http.HttpClient" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="handler">要求の送信に使用する HTTP ハンドラー スタック。</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>指定したハンドラーを使用して、<see cref="T:System.Net.Http.HttpClient" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="handler">HTTP 応答メッセージの処理を行う <see cref="T:System.Net.Http.HttpMessageHandler" />。</param>
      <param name="disposeHandler">内部ハンドラーを Dispose() で破棄する場合は true。内部ハンドラーを再利用する場合は false。</param>
    </member>
    <member name="P:System.Net.Http.HttpClient.BaseAddress">
      <summary>要求を送信するときに使用する、インターネット リソースの Uniform Resource Identifier (URI) のベース アドレスを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Uri" /> を返します。要求を送信するときに使用する、インターネット リソースの Uniform Resource Identifier (URI) のベース アドレス。</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.CancelPendingRequests">
      <summary>このインスタンスの保留中の要求をすべてキャンセルします。</summary>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultRequestHeaders">
      <summary>各要求と一緒に送信する必要があるヘッダーを取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" /> を返します。各要求と一緒に送信する必要があるヘッダー。</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String)">
      <summary>指定された URI に DELETE 要求を非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
      <exception cref="T:System.InvalidOperationException">要求メッセージは既に <see cref="T:System.Net.Http.HttpClient" /> インスタンスによって送信されました。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String,System.Threading.CancellationToken)">
      <summary>非同期操作としてキャンセル トークンを使用して削除要求を指定された Uri に送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
      <exception cref="T:System.InvalidOperationException">要求メッセージは既に <see cref="T:System.Net.Http.HttpClient" /> インスタンスによって送信されました。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri)">
      <summary>指定された URI に DELETE 要求を非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
      <exception cref="T:System.InvalidOperationException">要求メッセージは既に <see cref="T:System.Net.Http.HttpClient" /> インスタンスによって送信されました。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>非同期操作としてキャンセル トークンを使用して削除要求を指定された Uri に送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
      <exception cref="T:System.InvalidOperationException">要求メッセージは既に <see cref="T:System.Net.Http.HttpClient" /> インスタンスによって送信されました。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" /> が使用しているアンマネージ リソースを解放します。オプションとして、マネージ リソースを破棄することもできます。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String)">
      <summary>指定された URI に GET 要求を非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption)">
      <summary>非同期操作としてHTTP 完了オプションを使用して GET 要求を指定された Uri に送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="completionOption">操作が完了したものと見なすタイミングを示す HTTP 完了オプション値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>非同期操作としてキャンセル トークンおよび HTTP 完了オプションを使用して GET 要求を指定された Uri に送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="completionOption">操作が完了したものと見なすタイミングを示す HTTP 完了オプション値。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Threading.CancellationToken)">
      <summary>非同期操作としてキャンセル トークンを使用して GET 要求を指定された Uri に送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri)">
      <summary>指定された URI に GET 要求を非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption)">
      <summary>非同期操作としてHTTP 完了オプションを使用して GET 要求を指定された Uri に送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="completionOption">操作が完了したものと見なすタイミングを示す HTTP 完了オプション値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>非同期操作としてキャンセル トークンおよび HTTP 完了オプションを使用して GET 要求を指定された Uri に送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="completionOption">操作が完了したものと見なすタイミングを示す HTTP 完了オプション値。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>非同期操作としてキャンセル トークンを使用して GET 要求を指定された Uri に送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.String)">
      <summary>指定 URI に GET 要求を送信し、非同期操作で応答本体をバイト配列として返します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.Uri)">
      <summary>指定 URI に GET 要求を送信し、非同期操作で応答本体をバイト配列として返します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.String)">
      <summary>指定 URI に GET 要求を送信し、非同期操作で応答本体をストリームとして返します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.Uri)">
      <summary>指定 URI に GET 要求を送信し、非同期操作で応答本体をストリームとして返します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.String)">
      <summary>指定 URI に GET 要求を送信し、非同期操作で応答本体を文字列として返します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.Uri)">
      <summary>指定 URI に GET 要求を送信し、非同期操作で応答本体を文字列として返します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.MaxResponseContentBufferSize">
      <summary>応答の内容を読み取るときにバッファーに格納できる最大バイト数を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。応答コンテンツを読み取るときに、バッファーに格納できる最大バイト数。このプロパティの既定値は 2 GB です。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">指定されたサイズがゼロ以下です。</exception>
      <exception cref="T:System.InvalidOperationException">操作は現在のインスタンス上で既に開始されています。</exception>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent)">
      <summary>指定された URI に POST 要求を非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="content">サーバーに送信される HTTP 要求の内容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>POST 要求をキャンセル トークンと共に非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="content">サーバーに送信される HTTP 要求の内容。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>指定された URI に POST 要求を非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="content">サーバーに送信される HTTP 要求の内容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>POST 要求をキャンセル トークンと共に非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="content">サーバーに送信される HTTP 要求の内容。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent)">
      <summary>指定された URI に PUT 要求を非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="content">サーバーに送信される HTTP 要求の内容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>PUT 要求をとキャンセル トークンと共に非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="content">サーバーに送信される HTTP 要求の内容。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>指定された URI に PUT 要求を非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="content">サーバーに送信される HTTP 要求の内容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>PUT 要求をとキャンセル トークンと共に非同期操作として送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="requestUri">要求の送信先 URI。</param>
      <param name="content">サーバーに送信される HTTP 要求の内容。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage)">
      <summary>非同期操作として HTTP 要求を送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="request">送信する HTTP 要求メッセージ。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> が null でした。</exception>
      <exception cref="T:System.InvalidOperationException">要求メッセージは既に <see cref="T:System.Net.Http.HttpClient" /> インスタンスによって送信されました。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption)">
      <summary>非同期操作として HTTP 要求を送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="request">送信する HTTP 要求メッセージ。</param>
      <param name="completionOption">操作が完了したとき (応答が使用できる状態になった後か、応答コンテンツ全体が読み取られた後)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> が null でした。</exception>
      <exception cref="T:System.InvalidOperationException">要求メッセージは既に <see cref="T:System.Net.Http.HttpClient" /> インスタンスによって送信されました。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>非同期操作として HTTP 要求を送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="request">送信する HTTP 要求メッセージ。</param>
      <param name="completionOption">操作が完了したとき (応答が使用できる状態になった後か、応答コンテンツ全体が読み取られた後)。</param>
      <param name="cancellationToken">操作をキャンセルするキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> が null でした。</exception>
      <exception cref="T:System.InvalidOperationException">要求メッセージは既に <see cref="T:System.Net.Http.HttpClient" /> インスタンスによって送信されました。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>非同期操作として HTTP 要求を送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="request">送信する HTTP 要求メッセージ。</param>
      <param name="cancellationToken">操作をキャンセルするキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> が null でした。</exception>
      <exception cref="T:System.InvalidOperationException">要求メッセージは既に <see cref="T:System.Net.Http.HttpClient" /> インスタンスによって送信されました。</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.Timeout">
      <summary>要求がタイムアウトするまで待機する期間を取得または設定します。</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> を返します。要求がタイムアウトするまで待機する期間。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">指定したタイムアウトが 0 以下のため <see cref="F:System.Threading.Timeout.InfiniteTimeSpan" /> ではありません。</exception>
      <exception cref="T:System.InvalidOperationException">操作は現在のインスタンス上で既に開始されています。</exception>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
    </member>
    <member name="T:System.Net.Http.HttpClientHandler">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" /> が使用する既定のメッセージ ハンドラー。</summary>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpClientHandler" /> クラスのインスタンスを作成します。</summary>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect">
      <summary>ハンドラーがリダイレクト応答に従うかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。ハンドラーがリダイレクト応答に従う場合は true。それ以外の場合は false。既定値は true です。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AutomaticDecompression">
      <summary>HTTP コンテンツ応答の自動圧縮解除のハンドラーが使用する圧縮解除メソッドの種類を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.DecompressionMethods" /> を返します。ハンドラーが使用する自動圧縮解除のメソッド。既定値は <see cref="F:System.Net.DecompressionMethods.None" /> です。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.ClientCertificateOptions">
      <summary>このハンドラーに関連付けられるセキュリティ証明書のコレクションを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.ClientCertificateOption" /> を返します。このハンドラーに関連付けられているセキュリティ証明書のコレクション。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.CookieContainer">
      <summary>ハンドラーがサーバー クッキーを格納するために使用するクッキー コンテナーを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.CookieContainer" /> を返します。ハンドラーがサーバー クッキーを格納するために使用するクッキー コンテナー。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Credentials">
      <summary>このハンドラーによって使用される認証情報を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.ICredentials" /> を返します。ハンドラーに関連付けられる認証資格情報。既定値は、null です。</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpClientHandler" /> が使用しているアンマネージ リソースを解放します。オプションでマネージ リソースも破棄します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections">
      <summary>ハンドラーが従うリダイレクトの最大数を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。ハンドラーによって実行されるリダイレクト応答の最大数。既定値は 50 です。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxRequestContentBufferSize">
      <summary>ハンドラーが使用する要求コンテンツ バッファーの最大サイズを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。要求コンテンツ バッファーの最大サイズ (バイト単位)。既定値は 2 GB です。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.PreAuthenticate">
      <summary>ハンドラーが要求と共に認証ヘッダーを送信するかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。認証が行われた後で、要求と共に HTTP 認証ヘッダーを送信するハンドラーの場合は true。それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Proxy">
      <summary>ハンドラーが使用するプロキシ情報を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.IWebProxy" /> を返します。ハンドラーにより使用されるプロキシ情報。既定値は null です。</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>
        <see cref="T:System.Net.Http.HttpRequestMessage" /> で提供される情報に基づいて、ブロックされない操作として <see cref="T:System.Net.Http.HttpResponseMessage" /> のインスタンスを作成します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="request">HTTP 要求メッセージ。</param>
      <param name="cancellationToken">操作をキャンセルするキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> が null でした。</exception>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsAutomaticDecompression">
      <summary>ハンドラーが自動的な応答内容の圧縮解除をサポートするかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。ハンドラーが自動応答の内容の圧縮解除をサポートする場合は true。それ以外の場合は false。既定値は true です。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsProxy">
      <summary>ハンドラーがプロキシ設定をサポートしているかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。ハンドラーがプロキシ設定をサポートする場合は true。それ以外の場合は false。既定値は true です。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsRedirectConfiguration">
      <summary>ハンドラーが <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> および <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> プロパティの構成設定をサポートするかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> プロパティと <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> プロパティの構成設定をハンドラーがサポートする場合は true。それ以外の場合は false。既定値は true です。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseCookies">
      <summary>ハンドラーが <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> プロパティを使用してサーバー クッキーを格納し、要求を送信するときにこれらのクッキーを使用するかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。ハンドラーが <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> プロパティを使用してサーバー クッキーを格納し、要求を送信するときにこれらのクッキーを使用する場合は true。それ以外の場合は false。既定値は true です。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseDefaultCredentials">
      <summary>既定の資格情報がハンドラーによって要求と共に送信されるかどうかを制御する値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。既定の資格情報を使用する場合は true。それ以外の場合は false。既定値は false です。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseProxy">
      <summary>ハンドラーが要求のプロキシを使用するかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。ハンドラーが要求に対してプロキシを使用する場合は true。それ以外の場合は false。既定値は true です。</returns>
    </member>
    <member name="T:System.Net.Http.HttpCompletionOption">
      <summary>応答が使用できるようになった場合に <see cref="T:System.Net.Http.HttpClient" /> 操作が完了したと見なすか、内容を含む応答メッセージ全体を読み取った後に完了したと見なすかを示します。</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseContentRead">
      <summary>操作は、コンテンツを含む全体の応答を読んだ後に完了する必要があります。</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseHeadersRead">
      <summary>操作は、応答が使用できる状態になった後か、ヘッダーが読み取られた後すぐに完了する必要があります。コンテンツがまだ読み取られていません。</summary>
    </member>
    <member name="T:System.Net.Http.HttpContent">
      <summary>HTTP のエンティティ本体とコンテンツ ヘッダーを表す基本クラス。</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpContent" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream)">
      <summary>HTTP コンテンツをバイト ストリームにシリアル化し、<paramref name="stream" /> パラメーターとして指定されたストリーム オブジェクトにコピーします。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="stream">対象のストリーム。</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>HTTP コンテンツをバイト ストリームにシリアル化し、<paramref name="stream" /> パラメーターとして指定されたストリーム オブジェクトにコピーします。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="stream">対象のストリーム。</param>
      <param name="context">トランスポートに関する情報 (チャネル バインディング トークンなど)。このパラメーターは、null の場合もあります。</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CreateContentReadStreamAsync">
      <summary>非同期操作としてメモリ ストリームに HTTP コンテンツをシリアル化します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose">
      <summary>
        <see cref="T:System.Net.Http.HttpContent" /> が使用しているアンマネージ リソースを解放し、マネージ リソースを破棄します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpContent" /> が使用しているアンマネージ リソースを解放します。オプションでマネージ リソースも破棄します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="P:System.Net.Http.HttpContent.Headers">
      <summary>RFC 2616 で定義されている HTTP コンテンツ ヘッダーを取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpContentHeaders" /> を返します。RFC 2616 で定義されているコンテンツ ヘッダー。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync">
      <summary>非同期操作としてメモリ バッファーに HTTP コンテンツをシリアル化します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync(System.Int64)">
      <summary>非同期操作としてメモリ バッファーに HTTP コンテンツをシリアル化します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="maxBufferSize">使用するバッファーの最大サイズ (バイト単位)。</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsByteArrayAsync">
      <summary>非同期操作としてバイト配列に HTTP コンテンツをシリアル化します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStreamAsync">
      <summary>HTTP コンテンツをシリアル化して、非同期操作としてコンテンツを表すストリームを返します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStringAsync">
      <summary>非同期操作として文字列に HTTP コンテンツをシリアル化します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>非同期操作としてストリームに HTTP コンテンツをシリアル化します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="stream">対象のストリーム。</param>
      <param name="context">トランスポートに関する情報 (チャネル バインディング トークンなど)。このパラメーターは、null の場合もあります。</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.TryComputeLength(System.Int64@)">
      <summary>HTTP コンテンツが有効な長さ (バイト単位) かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="length" /> が有効な長さである場合は true。それ以外の場合は false。</returns>
      <param name="length">HTTP コンテンツの長さ (バイト単位)。</param>
    </member>
    <member name="T:System.Net.Http.HttpMessageHandler">
      <summary>HTTP メッセージ ハンドラーの基本型。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpMessageHandler" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose">
      <summary>
        <see cref="T:System.Net.Http.HttpMessageHandler" /> が使用しているアンマネージ リソースを解放し、マネージ リソースを破棄します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpMessageHandler" /> が使用しているアンマネージ リソースを解放します。オプションでマネージ リソースも破棄します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>非同期操作として HTTP 要求を送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="request">送信する HTTP 要求メッセージ。</param>
      <param name="cancellationToken">操作をキャンセルするキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> が null でした。</exception>
    </member>
    <member name="T:System.Net.Http.HttpMessageInvoker">
      <summary>アプリケーションが HTTP ハンドラー チェーンに対して <see cref="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)" /> メソッドを呼び出せるようにする専用クラス。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>指定した <see cref="T:System.Net.Http.HttpMessageHandler" /> を使用して、<see cref="T:System.Net.Http.HttpMessageInvoker" /> クラスのインスタンスを初期化します。</summary>
      <param name="handler">HTTP 応答メッセージの処理を行う <see cref="T:System.Net.Http.HttpMessageHandler" />。</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>指定した <see cref="T:System.Net.Http.HttpMessageHandler" /> を使用して、<see cref="T:System.Net.Http.HttpMessageInvoker" /> クラスのインスタンスを初期化します。</summary>
      <param name="handler">HTTP 応答メッセージの処理を行う <see cref="T:System.Net.Http.HttpMessageHandler" />。</param>
      <param name="disposeHandler">内部ハンドラーを Dispose() が破棄する場合は true、内部ハンドラーを再利用する場合は false。</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose">
      <summary>
        <see cref="T:System.Net.Http.HttpMessageInvoker" /> が使用しているアンマネージ リソースを解放し、マネージ リソースを破棄します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpMessageInvoker" /> が使用しているアンマネージ リソースを解放します。オプションでマネージ リソースも破棄します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>非同期操作として HTTP 要求を送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="request">送信する HTTP 要求メッセージ。</param>
      <param name="cancellationToken">操作をキャンセルするキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> が null でした。</exception>
    </member>
    <member name="T:System.Net.Http.HttpMethod">
      <summary>標準的な HTTP メソッドの取得と比較を行い、新しい HTTP メソッドを作成するためのヘルパー クラス。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMethod.#ctor(System.String)">
      <summary>指定した HTTP メソッドを使用して、<see cref="T:System.Net.Http.HttpMethod" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="method">HTTP メソッド。</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Delete">
      <summary>HTTP DELETE プロトコル メソッドを表します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" /> を返します。</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Net.Http.HttpMethod)">
      <summary>指定した <see cref="T:System.Net.Http.HttpMethod" /> が、現在の <see cref="T:System.Object" /> と等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定したオブジェクトが現在のオブジェクトと等しい場合は true。それ以外の場合は false。</returns>
      <param name="other">現在のオブジェクトと比較する HTTP メソッド。</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Object" /> と等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定したオブジェクトが現在のオブジェクトと等しい場合は true。それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Get">
      <summary>HTTP GET プロトコル メソッドを表します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" /> を返します。</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.GetHashCode">
      <summary>この型のハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在の <see cref="T:System.Object" /> のハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Head">
      <summary>HTTP HEAD プロトコル メソッドを表します。HEAD メソッドは、サーバーが応答でメッセージ本文は返さずにメッセージ ヘッダーだけを返すこと以外、GET と同じです。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" /> を返します。</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Method">
      <summary>HTTP メソッド。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。<see cref="T:System.String" /> として表される HTTP メソッド。</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Equality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>2 つの <see cref="T:System.Net.Http.HttpMethod" /> オブジェクトを比較するための等値演算子。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定された <paramref name="left" /> パラメーターと <paramref name="right" /> パラメーターが等しい場合は true。それ以外の場合は false。</returns>
      <param name="left">等値演算子の左辺にある <see cref="T:System.Net.Http.HttpMethod" />。</param>
      <param name="right">等値演算子の右辺の <see cref="T:System.Net.Http.HttpMethod" />。</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Inequality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>2 つの <see cref="T:System.Net.Http.HttpMethod" /> オブジェクトを比較するための非等値演算子。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定された <paramref name="left" /> パラメーターと <paramref name="right" /> パラメーターが等しくない場合は true。それ以外の場合は false。</returns>
      <param name="left">非等値演算子の左辺にある <see cref="T:System.Net.Http.HttpMethod" />。</param>
      <param name="right">非等値演算子の右辺の <see cref="T:System.Net.Http.HttpMethod" />。</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Options">
      <summary>HTTP OPTIONS プロトコル メソッドを表します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" /> を返します。</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Post">
      <summary>URI への追加として新しいエントリをポストするために使用される HTTP POST プロトコル メソッドを表します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" /> を返します。</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Put">
      <summary>URI で識別されるエントリを置き換えるために使用される HTTP PUT プロトコル メソッドを表します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" /> を返します。</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.ToString">
      <summary>現在のオブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Trace">
      <summary>HTTP TRACE プロトコル メソッドを表します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" /> を返します。</returns>
    </member>
    <member name="T:System.Net.Http.HttpRequestException">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" /> と <see cref="T:System.Net.Http.HttpMessageHandler" /> クラスによってスローされる例外の基本クラス。</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpRequestException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String)">
      <summary>現在の例外を記述するメッセージを指定して、<see cref="T:System.Net.Http.HttpRequestException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">現在の例外を説明するメッセージ。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String,System.Exception)">
      <summary>現在の例外と内部例外を記述するメッセージを指定して、<see cref="T:System.Net.Http.HttpRequestException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">現在の例外を説明するメッセージ。</param>
      <param name="inner">内部例外。</param>
    </member>
    <member name="T:System.Net.Http.HttpRequestMessage">
      <summary>HTTP 要求メッセージを表します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpRequestMessage" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.String)">
      <summary>HTTP メソッドと要求 <see cref="T:System.Uri" /> を使用して、<see cref="T:System.Net.Http.HttpRequestMessage" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="method">HTTP メソッド。</param>
      <param name="requestUri">要求 <see cref="T:System.Uri" /> を表す文字列。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.Uri)">
      <summary>HTTP メソッドと要求 <see cref="T:System.Uri" /> を使用して、<see cref="T:System.Net.Http.HttpRequestMessage" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="method">HTTP メソッド。</param>
      <param name="requestUri">要求する <see cref="T:System.Uri" />。</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Content">
      <summary>HTTP メッセージの内容を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpContent" /> を返します。メッセージの内容。</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose">
      <summary>
        <see cref="T:System.Net.Http.HttpRequestMessage" /> が使用しているアンマネージ リソースを解放し、マネージ リソースを破棄します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpRequestMessage" /> が使用しているアンマネージ リソースを解放します。オプションでマネージ リソースも破棄します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Headers">
      <summary>HTTP 要求ヘッダーのコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" /> を返します。HTTP 要求ヘッダーのコレクション。</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Method">
      <summary>HTTP 要求メッセージで使用される HTTP メソッドを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpMethod" /> を返します。要求メッセージによって使用される HTTP メソッド。既定では、GET メソッドです。</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Properties">
      <summary>HTTP 要求のプロパティのセットを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" /> を返します。</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.RequestUri">
      <summary>HTTP 要求で使用する <see cref="T:System.Uri" /> を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Uri" /> を返します。HTTP 要求に使用される <see cref="T:System.Uri" />。</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.ToString">
      <summary>現在のオブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトの文字列形式。</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Version">
      <summary>HTTP メッセージ セキュリティのバージョンを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Version" /> を返します。HTTP メッセージのバージョン。既定値は 1.1 です。</returns>
    </member>
    <member name="T:System.Net.Http.HttpResponseMessage">
      <summary>ステータス コードとデータを含む HTTP 応答メッセージを表します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor">
      <summary>
        <see cref="T:System.Net.Http.HttpResponseMessage" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor(System.Net.HttpStatusCode)">
      <summary>
        <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> を指定して、<see cref="T:System.Net.Http.HttpResponseMessage" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="statusCode">HTTP 応答のステータス コード。</param>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Content">
      <summary>HTTP 応答メッセージの内容を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpContent" /> を返します。HTTP 応答メッセージの内容。</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose">
      <summary>
        <see cref="T:System.Net.Http.HttpResponseMessage" /> が使用しているアンマネージ リソースを解放し、アンマネージ リソースを破棄します。</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.HttpResponseMessage" /> が使用しているアンマネージ リソースを解放します。オプションでマネージ リソースも破棄します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode">
      <summary>HTTP 応答の <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode" /> のプロパティが false である場合、例外がスローされます。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpResponseMessage" /> を返します。呼び出しが成功した場合は HTTP 応答メッセージ。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Headers">
      <summary>HTTP 応答ヘッダーのコレクションを取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpResponseHeaders" /> を返します。HTTP 応答ヘッダーのコレクション。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode">
      <summary>HTTP 応答が成功したかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。HTTP 応答が成功したかどうかを示す値。<see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> が 200 ～ 299 の範囲内にあった場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.ReasonPhrase">
      <summary>通常はステータス コードと共にサーバーによって送信される理由句を取得または設定します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。サーバーから送信される理由語句。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.RequestMessage">
      <summary>この応答メッセージの原因となった要求メッセージを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpRequestMessage" /> を返します。この応答メッセージの原因となった要求メッセージ。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.StatusCode">
      <summary>HTTP 応答のステータス コードを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.HttpStatusCode" /> を返します。HTTP 応答のステータス コード。</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.ToString">
      <summary>現在のオブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトの文字列形式。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Version">
      <summary>HTTP メッセージ セキュリティのバージョンを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Version" /> を返します。HTTP メッセージのバージョン。既定値は 1.1 です。</returns>
    </member>
    <member name="T:System.Net.Http.MessageProcessingHandler">
      <summary>要求と応答メッセージの小規模な処理のみをするハンドラーの基本型。</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor">
      <summary>
        <see cref="T:System.Net.Http.MessageProcessingHandler" /> クラスのインスタンスを作成します。</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>指定した内部ハンドラーを使用して、<see cref="T:System.Net.Http.MessageProcessingHandler" /> クラスのインスタンスを作成します。</summary>
      <param name="innerHandler">HTTP 応答メッセージ処理用の内部ハンドラー。</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>サーバーに送信された各要求の処理を実行します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpRequestMessage" /> を返します。処理された HTTP 要求メッセージ。</returns>
      <param name="request">処理する HTTP 要求メッセージ。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessResponse(System.Net.Http.HttpResponseMessage,System.Threading.CancellationToken)">
      <summary>サーバーからの各応答の処理を実行します。</summary>
      <returns>
        <see cref="T:System.Net.Http.HttpResponseMessage" /> を返します。処理された HTTP 応答メッセージ。</returns>
      <param name="response">処理する HTTP 応答メッセージ。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>非同期操作としてサーバーに送信する内部ハンドラーに HTTP 要求を送信します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="request">サーバーに送信する HTTP 要求メッセージ。</param>
      <param name="cancellationToken">キャンセル通知を受け取るために他のオブジェクトまたはスレッドで使用できるキャンセル トークン。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> が null でした。</exception>
    </member>
    <member name="T:System.Net.Http.MultipartContent">
      <summary>multipart/* のコンテンツ タイプの指定を使用してシリアル化される <see cref="T:System.Net.Http.HttpContent" /> オブジェクトのコレクションを提供します。</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor">
      <summary>
        <see cref="T:System.Net.Http.MultipartContent" /> クラスの新しいインスタンスを作成します。</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.MultipartContent" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="subtype">マルチパート コンテンツのサブタイプ。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="subtype" /> が null であるか、空白文字だけで構成されています。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.MultipartContent" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="subtype">マルチパート コンテンツのサブタイプ。</param>
      <param name="boundary">マルチパート コンテンツの境界の文字列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="subtype" /> は null か空の文字列でした。<paramref name="boundary" /> が null であるか、空白文字だけで構成されています。または<paramref name="boundary" /> が空白文字で終了します。</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="boundary" /> の長さが 70 を超えていました。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Add(System.Net.Http.HttpContent)">
      <summary>multipart/* のコンテンツ タイプの指定を使用してシリアル化される <see cref="T:System.Net.Http.HttpContent" /> オブジェクトのコレクションにマルチパート HTTP コンテンツを追加します。</summary>
      <param name="content">コレクションに追加する HTTP コンテンツ。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.MultipartContent" /> が使用しているアンマネージ リソースを解放します。オプションでマネージ リソースも破棄します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.GetEnumerator">
      <summary>multipart/* のコンテンツ タイプ仕様を使用してシリアル化する <see cref="T:System.Net.Http.HttpContent" /> オブジェクトのコレクション全体を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> を返します。コレクションを反復処理するために使用できるオブジェクト。</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>非同期操作としてストリームにマルチパート HTTP コンテンツをシリアル化します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="stream">対象のストリーム。</param>
      <param name="context">トランスポートに関する情報 (チャネル バインディング トークンなど)。このパラメーターは、null の場合もあります。</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="M:System.Net.Http.MultipartContent.GetEnumerator" /> メソッドの明示的な実装。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> を返します。コレクションを反復処理するために使用できるオブジェクト。</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.TryComputeLength(System.Int64@)">
      <summary>HTTP マルチパート コンテンツが有効な長さ (バイト単位) かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="length" /> が有効な長さである場合は true。それ以外の場合は false。</returns>
      <param name="length">HTTP コンテンツの長さ (バイト単位)。</param>
    </member>
    <member name="T:System.Net.Http.MultipartFormDataContent">
      <summary>マルチパート/フォーム データの MIME タイプを使用してエンコードされたコンテンツのコンテナーを提供します。</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor">
      <summary>
        <see cref="T:System.Net.Http.MultipartFormDataContent" /> クラスの新しいインスタンスを作成します。</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.MultipartFormDataContent" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="boundary">マルチパート フォーム データ コンテンツの境界の文字列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="boundary" /> が null であるか、空白文字だけで構成されています。または<paramref name="boundary" /> が空白文字で終了します。</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="boundary" /> の長さが 70 を超えていました。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent)">
      <summary>マルチパート/フォーム データの MIME の種類にシリアル化される <see cref="T:System.Net.Http.HttpContent" /> オブジェクトのコレクションに HTTP コンテンツを追加します。</summary>
      <param name="content">コレクションに追加する HTTP コンテンツ。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String)">
      <summary>マルチパート/フォーム データの MIME の種類にシリアル化される <see cref="T:System.Net.Http.HttpContent" /> オブジェクトのコレクションに HTTP コンテンツを追加します。</summary>
      <param name="content">コレクションに追加する HTTP コンテンツ。</param>
      <param name="name">追加する HTTP コンテンツの名前。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> が null であるか、空白文字だけで構成されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> が null でした。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String,System.String)">
      <summary>マルチパート/フォーム データの MIME の種類にシリアル化される <see cref="T:System.Net.Http.HttpContent" /> オブジェクトのコレクションに HTTP コンテンツを追加します。</summary>
      <param name="content">コレクションに追加する HTTP コンテンツ。</param>
      <param name="name">追加する HTTP コンテンツの名前。</param>
      <param name="fileName">コレクションに追加する HTTP コンテンツのファイル名。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> が null であるか、空白文字だけで構成されています。または<paramref name="fileName" /> が null であるか、空白文字だけで構成されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> が null でした。</exception>
    </member>
    <member name="T:System.Net.Http.StreamContent">
      <summary>ストリームに基づいて HTTP コンテンツを提供します。</summary>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream)">
      <summary>
        <see cref="T:System.Net.Http.StreamContent" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="content">
        <see cref="T:System.Net.Http.StreamContent" /> の初期化に使用されるコンテンツ。</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream,System.Int32)">
      <summary>
        <see cref="T:System.Net.Http.StreamContent" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="content">
        <see cref="T:System.Net.Http.StreamContent" /> の初期化に使用されるコンテンツ。</param>
      <param name="bufferSize">
        <see cref="T:System.Net.Http.StreamContent" /> のバッファーのサイズ (バイト単位)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> が null でした。</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="bufferSize" /> の値が 0 以下です。</exception>
    </member>
    <member name="M:System.Net.Http.StreamContent.CreateContentReadStreamAsync">
      <summary>非同期操作としてメモリ ストリームに HTTP ストリーム コンテンツを書き込みます。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.StreamContent" /> が使用しているアンマネージ リソースを解放します。オプションでマネージ リソースも破棄します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>非同期操作としてストリームに HTTP コンテンツをシリアル化します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
      <param name="stream">対象のストリーム。</param>
      <param name="context">トランスポートに関する情報 (チャネル バインディング トークンなど)。このパラメーターは、null の場合もあります。</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.TryComputeLength(System.Int64@)">
      <summary>ストリーム コンテンツが有効な長さ (バイト単位) かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="length" /> が有効な長さである場合は true。それ以外の場合は false。</returns>
      <param name="length">ストリーム コンテンツの長さ (バイト単位)。</param>
    </member>
    <member name="T:System.Net.Http.StringContent">
      <summary>文字列に基づいて HTTP コンテンツを提供します。</summary>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.StringContent" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="content">
        <see cref="T:System.Net.Http.StringContent" /> の初期化に使用されるコンテンツ。</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding)">
      <summary>
        <see cref="T:System.Net.Http.StringContent" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="content">
        <see cref="T:System.Net.Http.StringContent" /> の初期化に使用されるコンテンツ。</param>
      <param name="encoding">コンテンツに使用するエンコード。</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding,System.String)">
      <summary>
        <see cref="T:System.Net.Http.StringContent" /> クラスの新しいインスタンスを作成します。</summary>
      <param name="content">
        <see cref="T:System.Net.Http.StringContent" /> の初期化に使用されるコンテンツ。</param>
      <param name="encoding">コンテンツに使用するエンコード。</param>
      <param name="mediaType">コンテンツに使用するメディア タイプ。</param>
    </member>
    <member name="T:System.Net.Http.Headers.AuthenticationHeaderValue">
      <summary>Authorization、ProxyAuthorization、WWW-Authenticate、および Proxy-Authenticate のヘッダー値の認証情報を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="scheme">承認のために使用するスキーム。</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="scheme">承認のために使用するスキーム。</param>
      <param name="parameter">要求されたリソースのユーザー エージェントの認証情報を含む資格情報。</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Parameter">
      <summary>要求されたリソースのユーザー エージェントの認証情報を含む資格情報を取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。認証情報を格納する資格情報。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> のインスタンス。</returns>
      <param name="input">認証ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効な認証ヘッダー値の情報です。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Scheme">
      <summary>承認のために使用するメソッドを取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。承認のために使用するスキーム。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.TryParse(System.String,System.Net.Http.Headers.AuthenticationHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.CacheControlHeaderValue">
      <summary>Cache-Control ヘッダーの値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.#ctor">
      <summary>
        <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Extensions">
      <summary>それぞれが省略可能な代入値を持つキャッシュ拡張トークン。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。それぞれが省略可能な代入値を持つキャッシュ拡張トークンのコレクション。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxAge">
      <summary>HTTP クライアントが応答を受け入れる最大期間 (秒単位)。</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> を返します。時間 (秒) です。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStale">
      <summary>HTTP クライアントが有効期限を過ぎた応答を受け入れるかどうか。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。HTTP クライアントが有効期限を過ぎた応答を受け入れる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStaleLimit">
      <summary>HTTP クライアントが、有効期限を過ぎた応答を受け入れる最長時間 (秒単位)。</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> を返します。時間 (秒) です。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MinFresh">
      <summary>HTTP クライアントが応答を受け入れる鮮度有効期間 (秒単位)。</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> を返します。時間 (秒) です。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MustRevalidate">
      <summary>キャッシュ エントリが古くなった場合に、元のサーバーが後で使用するときにキャッシュ エントリの再認証を必要とするかどうか。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。キャッシュ エントリが古くなった場合に、元のサーバーが次の使用時にキャッシュ エントリの再認証を必要とする場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCache">
      <summary>HTTP クライアントがキャッシュされた応答を受け入れるかどうか。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。HTTP クライアントがキャッシュされた応答を受け入れる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCacheHeaders">
      <summary>HTTP 応答のキャッシュ制御ヘッダー フィールドの "no-cache" ディレクティブにあるフィールド名のコレクション。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。フィールド名のコレクション。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoStore">
      <summary>HTTP 要求メッセージまたは応答の一部をキャッシュに保存できないかどうか。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。HTTP 要求メッセージおよびあらゆる応答のどの部分もキャッシュに保存できない場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoTransform">
      <summary>キャッシュまたはプロキシがエンティティ本体の側面を変更できないかどうか。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。キャッシュまたはプロキシがエンティティ本体のどの側面も変更できない場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.OnlyIfCached">
      <summary>キャッシュが HTTP 要求の他の制約に準拠しているキャッシュ エントリを使用して応答するか、または 504 (ゲートウェイ タイムアウト) ステータスを使用して応答するか。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。キャッシュが、HTTP 要求の他の制約に従っているキャッシュ エントリを使用して応答するか、または 504 (ゲートウェイ タイムアウト) ステータスを使用して応答する場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> のインスタンス。</returns>
      <param name="input">キャッシュ コントロールのヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効なキャッシュ コントロール ヘッダー値の情報です。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Private">
      <summary>HTTP 応答メッセージの全体または一部が 1 人のユーザーを対象にしており、共有キャッシュでキャッシュできないかどうか。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。HTTP 応答メッセージが 1 名のユーザーを対象にしており、共有キャッシュでキャッシュできない場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.PrivateHeaders">
      <summary>HTTP 応答のキャッシュ制御ヘッダー フィールドの "private" ディレクティブにあるコレクション フィールド名。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。フィールド名のコレクション。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.ProxyRevalidate">
      <summary>共有ユーザー エージェント キャッシュのキャッシュ エントリが古くなった場合に、元のサーバーが後で使用するときにキャッシュ エントリの再認証を必要とするかどうか。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。キャッシュ エントリが古くなり、共有ユーザー エージェント キャッシュに適切でない場合に、元のサーバーが次の使用時にキャッシュ エントリの再認証を必要とする場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Public">
      <summary>通常、キャッシュ不可である場合や、非共有キャッシュ内でのみキャッシュ可能である場合でも、HTTP 応答をすべてのキャッシュでキャッシュできるかどうか。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。通常はキャッシュ不可であるか、非共有キャッシュ内でのみキャッシュ可能である場合でも、HTTP 応答を任意のキャッシュでキャッシュできる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.SharedMaxAge">
      <summary>共有キャッシュのキャッシュ制御ヘッダーまたは expires ヘッダーに対する "最大年齢" ディレクティブをオーバーライドする HTTP 応答における、共有最大期間 (秒単位で指定)。</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> を返します。時間 (秒) です。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.TryParse(System.String,System.Net.Http.Headers.CacheControlHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentDispositionHeaderValue">
      <summary>Content-Disposition ヘッダーの値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.Net.Http.Headers.ContentDispositionHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="source">
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />
      </param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="dispositionType">
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> を含む文字列。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.CreationDate">
      <summary>ファイルが作成された日付。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。ファイルの作成日。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.DispositionType">
      <summary>コンテンツ本体の配置タイプ。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。配置タイプ。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileName">
      <summary>エンティティがデタッチされ別のファイルに格納される場合に使用されるメッセージ ペイロードを格納するためのファイル名の構成方法の提案。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。推奨ファイル名。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileNameStar">
      <summary>エンティティがデタッチされ別のファイルに格納される場合に使用されるメッセージ ペイロードを格納するためのファイル名の構成方法の提案。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。フォームの filename* の推奨ファイル名。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ModificationDate">
      <summary>ファイルが最後に変更された日付。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。ファイルの変更日。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Name">
      <summary>コンテンツ本体の名前。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。コンテンツ本体の名前。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Parameters">
      <summary>パラメーター セットには、Content-Disposition ヘッダーが含まれていました。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。パラメーターのコレクション。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> のインスタンス。</returns>
      <param name="input">コンテンツ配置ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効なコンテンツ配置ヘッダー値の情報です。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ReadDate">
      <summary>ファイルの読み取りが行われた最後の日付。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。最後に読み取った日付。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Size">
      <summary>ファイルのおおよそのサイズ (バイト単位)。</summary>
      <returns>
        <see cref="T:System.Int64" /> を返します。おおよそのサイズ (バイト単位)。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentDispositionHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentRangeHeaderValue">
      <summary>Content-Range ヘッダーの値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="length">範囲の開始点または終了点 (バイト単位)。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="from">データの送信を開始する位置 (バイト単位)。</param>
      <param name="to">データの送信を終了する位置 (バイト単位)。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64,System.Int64)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="from">データの送信を開始する位置 (バイト単位)。</param>
      <param name="to">データの送信を終了する位置 (バイト単位)。</param>
      <param name="length">範囲の開始点または終了点 (バイト単位)。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Equals(System.Object)">
      <summary>指定のオブジェクトが現在の <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.From">
      <summary>データの送信を開始する位置を取得します。</summary>
      <returns>
        <see cref="T:System.Int64" /> を返します。データの送信を開始する位置 (バイト単位)。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasLength">
      <summary>Content-Range ヘッダーが指定された長さかどうかを取得します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。Content-Range ヘッダーが指定された長さである場合は true、それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasRange">
      <summary>Content-Range に指定された範囲があるかどうかを取得します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。Content-Range ヘッダーが指定された範囲である場合は true、それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Length">
      <summary>完全なエンティティ本体の長さを取得します。</summary>
      <returns>
        <see cref="T:System.Int64" /> を返します。フル エンティティ本体の長さ。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> のインスタンス。</returns>
      <param name="input">コンテンツ範囲ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効なコンテンツ範囲ヘッダー値の情報です。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.To">
      <summary>データの送信を終了する位置を取得します。</summary>
      <returns>
        <see cref="T:System.Int64" /> を返します。データの送信を終了する位置。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentRangeHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> バージョン。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Unit">
      <summary>使用する範囲の単位。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。範囲の単位を含む <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.EntityTagHeaderValue">
      <summary>エンティティ タグのヘッダー値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="tag">
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> を含む文字列。</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String,System.Boolean)">
      <summary>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="tag">
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> を含む文字列。</param>
      <param name="isWeak">このエンティティ タグ ヘッダーが弱い検証コントロールかどうかを示す値。エンティティ タグ ヘッダーが弱い検証コントロールの場合は、<paramref name="isWeak" /> を true に設定します。エンティティ タグ ヘッダーが強い検証コントロールの場合は、<paramref name="isWeak" /> を false に設定します。</param>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Any">
      <summary>エンティティ タグのヘッダー値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> を返します。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.IsWeak">
      <summary>エンティティ タグの前に脆弱性インジケーターが付いているかどうかを取得します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。エンティティ タグの前に脆弱性インジケーターが付いている場合は true、それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> のインスタンス。</returns>
      <param name="input">エンティティ タグ ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効なエンティティ タグ ヘッダー値の情報です。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Tag">
      <summary>opaque 引用符で囲まれた文字列を取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。不透明な引用符で囲まれた文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.TryParse(System.String,System.Net.Http.Headers.EntityTagHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpContentHeaders">
      <summary>RFC 2616 で定義されているコンテンツ ヘッダーのコレクションを表します。</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Allow">
      <summary>HTTP 応答の Allow コンテンツ ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。HTTP 応答の Allow ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentDisposition">
      <summary>HTTP 応答の Content-Disposition コンテンツ ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> を返します。HTTP 応答の Content-Disposition コンテンツ ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentEncoding">
      <summary>HTTP 応答の Content-Encoding コンテンツ ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。HTTP 応答の Content-Encoding コンテンツ ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLanguage">
      <summary>HTTP 応答の Content-Language コンテンツ ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。HTTP 応答の Content-Language コンテンツ ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLength">
      <summary>HTTP 応答の Content-Length コンテンツ ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Int64" /> を返します。HTTP 応答の Content-Length コンテンツ ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLocation">
      <summary>HTTP 応答の Content-Location コンテンツ ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Uri" /> を返します。HTTP 応答の Content-Location コンテンツ ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentMD5">
      <summary>HTTP 応答の Content-MD5 コンテンツ ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Byte" /> を返します。HTTP 応答の Content-MD5 コンテンツ ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentRange">
      <summary>HTTP 応答の Content-Range コンテンツ ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> を返します。HTTP 応答の Content-Range コンテンツ ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentType">
      <summary>HTTP 応答の Content-Type コンテンツ ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> を返します。HTTP 応答の Content-Type コンテンツ ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Expires">
      <summary>HTTP 応答の Expires コンテンツ ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。HTTP 応答の Expires コンテンツ ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.LastModified">
      <summary>HTTP 応答の Last-Modified コンテンツ ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。HTTP 応答の Last-Modified コンテンツ ヘッダーの値。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaders">
      <summary>RFC 2616 で定義されているヘッダーおよび値のコレクション。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.#ctor">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> コレクションに指定のヘッダーと値を追加します。</summary>
      <param name="name">コレクションに追加するヘッダー。</param>
      <param name="values">コレクションに追加するヘッダー値のリスト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> コレクションに指定のヘッダーと値を追加します。</summary>
      <param name="name">コレクションに追加するヘッダー。</param>
      <param name="value">ヘッダーの内容。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Clear">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> コレクションからすべてのヘッダーを削除します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Contains(System.String)">
      <summary>特定のヘッダーが <see cref="T:System.Net.Http.Headers.HttpHeaders" /> コレクションに存在するかどうかを返します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定したヘッダーがコレクション内に存在する場合は true、それ以外の場合は false。</returns>
      <param name="name">特定のヘッダー。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetEnumerator">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> インスタンスを反復処理できる列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> を返します。<see cref="T:System.Net.Http.Headers.HttpHeaders" /> の列挙子。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetValues(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> コレクションに格納されている指定したヘッダーのすべてのヘッダー値を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerable`1" /> を返します。ヘッダー文字列の配列。</returns>
      <param name="name">値を返す指定されたヘッダー。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Remove(System.String)">
      <summary>指定したヘッダーを <see cref="T:System.Net.Http.Headers.HttpHeaders" /> コレクションから削除します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。</returns>
      <param name="name">コレクションから削除するヘッダーの名前。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaders" /> を反復処理できる列挙子を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> を返します。<see cref="T:System.Net.Http.Headers.HttpHeaders" /> を反復処理できる <see cref="T:System.Collections.IEnumerator" /> を実装するインスタンス。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.HttpHeaders" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>提供された情報を検証せずに <see cref="T:System.Net.Http.Headers.HttpHeaders" /> コレクションに指定されたヘッダーとその値が追加されたかどうかを示す値を返します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定したヘッダーの <paramref name="name" /> と <paramref name="values" /> をコレクションに追加できた場合は true。それ以外の場合は false。</returns>
      <param name="name">コレクションに追加するヘッダー。</param>
      <param name="values">ヘッダーの値。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.String)">
      <summary>提供された情報を検証せずに <see cref="T:System.Net.Http.Headers.HttpHeaders" /> コレクションに指定されたヘッダーとその値が追加されたかどうかを示す値を返します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定したヘッダーの <paramref name="name" /> と <paramref name="value" /> をコレクションに追加できた場合は true。それ以外の場合は false。</returns>
      <param name="name">コレクションに追加するヘッダー。</param>
      <param name="value">ヘッダーの内容。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
      <summary>指定されたヘッダーと指定された値が <see cref="T:System.Net.Http.Headers.HttpHeaders" /> コレクションに格納されているかどうかを返します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定されたヘッダー <paramref name="name" /> および values がコレクションに格納されている場合は true。それ以外の場合は false。</returns>
      <param name="name">指定されたヘッダー。</param>
      <param name="values">指定したヘッダー値。</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaderValueCollection`1">
      <summary>ヘッダー値のコレクションを表します。</summary>
      <typeparam name="T">ヘッダー コレクションの型。</typeparam>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Add(`0)">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> にエントリを追加します。</summary>
      <param name="item">ヘッダー コレクションに追加する項目。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Clear">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> からすべてのエントリを削除します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Contains(`0)">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> に項目が格納されているかどうかを確認します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。エントリが <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> インスタンスに含まれている場合は true。それ以外の場合は false。</returns>
      <param name="item">ヘッダー コレクションから検索する項目。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 全体を互換性のある 1 次元の  <see cref="T:System.Array" /> にコピーします。コピー操作は、コピー先の配列の指定したインデックスから始まります。</summary>
      <param name="array">
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> から要素をコピーする、1 次元の <see cref="T:System.Array" /> です。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="arrayIndex">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.Count">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 内のヘッダーの数を取得します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。コレクション内のヘッダーの数。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.GetEnumerator">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IEnumerator`1" /> を返します。<see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> インスタンスの列挙子。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.IsReadOnly">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> インスタンスが読み取り専用かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> インスタンスが読み取り専用の場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ParseAdd(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> にエントリを解析して追加します。</summary>
      <param name="input">追加するエントリ。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Remove(`0)">
      <summary>指定した項目を <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> から削除します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="item" /> が <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> インスタンスから削除された場合は true。それ以外の場合は false。</returns>
      <param name="item">削除する項目。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を反復処理する列挙子を返します。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> を返します。<see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> インスタンスの列挙子。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.TryParseAdd(System.String)">
      <summary>入力を解析して <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> に追加できるかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> を解析して <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> インスタンスに追加できる場合は true。それ以外の場合は false</returns>
      <param name="input">検証するエントリ。</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpRequestHeaders">
      <summary>RFC 2616 で定義されている要求ヘッダーのコレクションを表します。</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Accept">
      <summary>HTTP 要求の Accept ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Accept ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptCharset">
      <summary>HTTP 要求の Accept-Charset ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Accept-Charset ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptEncoding">
      <summary>HTTP 要求の Accept-Encoding ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Accept-Encoding ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptLanguage">
      <summary>HTTP 要求の Accept-Language ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Accept-Language ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Authorization">
      <summary>HTTP 要求の Authorization ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> を返します。HTTP 要求の Authorization ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.CacheControl">
      <summary>HTTP 要求の Cache-Control ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> を返します。HTTP 要求の Cache-Control ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Connection">
      <summary>HTTP 要求の Connection ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Connection ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ConnectionClose">
      <summary>HTTP 要求の Connection ヘッダーに Close が含まれるかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。Connection ヘッダーに Close が含まれる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Date">
      <summary>HTTP 要求の Date ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。HTTP 要求の Date ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Expect">
      <summary>HTTP 要求の Expect ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Expect ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ExpectContinue">
      <summary>HTTP 要求の Expect ヘッダーに Continue が含まれるかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。Expect ヘッダーに Continue が含まれる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.From">
      <summary>HTTP 要求の From ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。HTTP 要求の From ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Host">
      <summary>HTTP 要求の Host ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。HTTP 要求の Host ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfMatch">
      <summary>HTTP 要求の If-Match ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の If-Match ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfModifiedSince">
      <summary>HTTP 要求の If-Modified-Since ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。HTTP 要求の If-Modified-Since ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfNoneMatch">
      <summary>HTTP 要求の If-None-Match ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の If-None-Match ヘッダーの値を取得します。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfRange">
      <summary>HTTP 要求の If-Range ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> を返します。HTTP 要求の If-Range ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfUnmodifiedSince">
      <summary>HTTP 要求の If-Unmodified-Since ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。HTTP 要求の If-Unmodified-Since ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.MaxForwards">
      <summary>HTTP 要求の Max-Forwards ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。HTTP 要求の Max-Forwards ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Pragma">
      <summary>HTTP 要求の Pragma ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Pragma ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ProxyAuthorization">
      <summary>HTTP 要求の Proxy-Authorization ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> を返します。HTTP 要求の Proxy-Authorization ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Range">
      <summary>HTTP 要求の Range ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> を返します。HTTP 要求の Range ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Referrer">
      <summary>HTTP 要求の Referer ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Uri" /> を返します。HTTP 要求の Referer ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TE">
      <summary>HTTP 要求の TE ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の TE ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Trailer">
      <summary>HTTP 要求の Trailer ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Trailer ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncoding">
      <summary>HTTP 要求の Transfer-Encoding ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Transfer-Encoding ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncodingChunked">
      <summary>HTTP 要求の Transfer-Encoding ヘッダーに chunked が含まれるかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。Transfer-Encoding ヘッダーに chunked が含まれている場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Upgrade">
      <summary>HTTP 要求の Upgrade ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Upgrade ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.UserAgent">
      <summary>HTTP 要求の User-Agent ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の User-Agent ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Via">
      <summary>HTTP 要求の Via ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Via ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Warning">
      <summary>HTTP 要求の Warning ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 要求の Warning ヘッダーの値。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpResponseHeaders">
      <summary>RFC 2616 で定義されている応答ヘッダーのコレクションを表します。</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.AcceptRanges">
      <summary>HTTP 応答の Accept-Ranges ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Accept-Ranges ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Age">
      <summary>HTTP 応答の Age ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> を返します。HTTP 応答の Age ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.CacheControl">
      <summary>HTTP 応答の Cache-Control ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> を返します。HTTP 応答の Cache-Control ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Connection">
      <summary>HTTP 応答の Connection ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Connection ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ConnectionClose">
      <summary>HTTP 応答の Connection ヘッダーに Close が含まれるかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。Connection ヘッダーに Close が含まれる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Date">
      <summary>HTTP 応答の Date ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。HTTP 応答の Date ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ETag">
      <summary>HTTP 応答の ETag ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> を返します。HTTP 応答の ETag ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Location">
      <summary>HTTP 応答の Location ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Uri" /> を返します。HTTP 応答の Location ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Pragma">
      <summary>HTTP 応答の Pragma ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Pragma ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ProxyAuthenticate">
      <summary>HTTP 応答の Proxy-Authenticate ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Proxy-Authenticate ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.RetryAfter">
      <summary>HTTP 応答の Retry-After ヘッダーの値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> を返します。HTTP 応答の Retry-After ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Server">
      <summary>HTTP 応答の Server ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Server ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Trailer">
      <summary>HTTP 応答の Trailer ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Trailer ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncoding">
      <summary>HTTP 応答の Transfer-Encoding ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Transfer-Encoding ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncodingChunked">
      <summary>HTTP 応答の Transfer-Encoding ヘッダーに chunked が含まれるかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。Transfer-Encoding ヘッダーに chunked が含まれている場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Upgrade">
      <summary>HTTP 応答の Upgrade ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Upgrade ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Vary">
      <summary>HTTP 応答の Vary ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Vary ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Via">
      <summary>HTTP 応答の Via ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Via ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Warning">
      <summary>HTTP 応答の Warning ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の Warning ヘッダーの値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.WwwAuthenticate">
      <summary>HTTP 応答の WWW-Authenticate ヘッダーの値を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> を返します。HTTP 応答の WWW-Authenticate ヘッダーの値。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeHeaderValue">
      <summary>RFC 2616 に定義され、コンテンツ タイプのヘッダーに使用されるメディア タイプを表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="source"> 新しいインスタンスを初期化するために使用する <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> オブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="mediaType">新しいインスタンスを初期化する文字列を表すソースです。</param>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.CharSet">
      <summary>文字セットを取得または設定します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。文字セット。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.MediaType">
      <summary>メディア種類のヘッダー値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。メディア タイプ ヘッダー値。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.Parameters">
      <summary>メディア種類のヘッダー値のパラメーターを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。メディア タイプ ヘッダー値パラメーター。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> のインスタンス。</returns>
      <param name="input">メディア種類のヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効なメディア タイプ ヘッダー値の情報です。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue">
      <summary>コンテンツ タイプ ヘッダーで使用される追加の品質ファクターとメディア タイプを表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="mediaType">新しいインスタンスを初期化するための文字列として表現された <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />。</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="mediaType">新しいインスタンスを初期化するための文字列として表現された <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />。</param>
      <param name="quality">このヘッダー値に関連付けられた品質です。</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> のインスタンス。</returns>
      <param name="input">品質ヘッダー値の情報を含むメディア種類を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は品質ヘッダー値の情報を含む有効なメディア タイプではありません。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Quality">
      <summary>
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> の品質評価の値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Double" /> を返します。<see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> オブジェクトの品質評価の値。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeWithQualityHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueHeaderValue">
      <summary>RFC 2616 で定義され、さまざまなヘッダーで使用される名前と値のペアを表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.Net.Http.Headers.NameValueHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="source">新しいインスタンスを初期化するために使用する <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> オブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">ヘッダーの名前。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">ヘッダーの名前。</param>
      <param name="value">ヘッダー値。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Name">
      <summary>ヘッダー名を取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。ヘッダーの名前。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> のインスタンス。</returns>
      <param name="input">名前の値のヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効な名前の値のヘッダー値の情報です。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> バージョン。</param>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Value">
      <summary>ヘッダー値を取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。ヘッダー値。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue">
      <summary>RFC 2616 で定義され、さまざまなヘッダーで使用されるパラメーター付きの名前と値のペアを表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.Net.Http.Headers.NameValueWithParametersHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="source">新しいインスタンスを初期化するために使用する <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> オブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">ヘッダーの名前。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">ヘッダーの名前。</param>
      <param name="value">ヘッダー値。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parameters">
      <summary>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> オブジェクトからパラメーターを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。パラメーターを含むコレクション。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> のインスタンス。</returns>
      <param name="input">パラメーター ヘッダー値の情報を含む、名前の値を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> はパラメーター ヘッダー値の情報を含む有効な名前の値ではありません。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueWithParametersHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.ProductHeaderValue">
      <summary>ユーザー エージェント ヘッダー内の製品トークン値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">製品名。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">製品名の値。</param>
      <param name="version">製品バージョンの値。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Name">
      <summary>製品トークンの名前を取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。製品トークンの名前。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> のインスタンス。</returns>
      <param name="input">製品のヘッダー値の情報を表す文字列。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> バージョン。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Version">
      <summary>製品トークンのバージョンを取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。製品トークンのバージョン。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductInfoHeaderValue">
      <summary>User-Agent ヘッダー内の製品またはコメントのいずれかになる値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.Net.Http.Headers.ProductHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="product">新しいインスタンスを初期化するために使用する <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> オブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="comment">コメント値。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="productName">製品名の値。</param>
      <param name="productVersion">製品バージョンの値。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Comment">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> オブジェクトからコメントを取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。この <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> のコメントの値です。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> のインスタンス。</returns>
      <param name="input">情報のヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効な製品情報ヘッダー値の情報です。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Product">
      <summary>
        <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> オブジェクトから製品を取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> を返します。この <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />からの製品の値です。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductInfoHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeConditionHeaderValue">
      <summary>日付/時刻またはエンティティ タグ値のいずれかとなる If-Range ヘッダー値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="date">新しいインスタンスを初期化するために使用される日付の値。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.Net.Http.Headers.EntityTagHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="entityTag">新しいインスタンスを初期化するために使用する <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> オブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="entityTag">新しいインスタンスを初期化するために使用される文字列として表現されたエンティティ タグ。</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.Date">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> オブジェクトから日付を取得します。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。<see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> オブジェクトからのデータです。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.EntityTag">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> オブジェクトからエンティティ タグを取得します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> オブジェクトのエンティティ タグです。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> のインスタンス。</returns>
      <param name="input">条件ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効な条件ヘッダー値の情報です。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeConditionHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeHeaderValue">
      <summary>Range ヘッダー値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>バイト範囲を使用して、<see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="from">データの送信を開始する位置。</param>
      <param name="to">データの送信を終了する位置。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> は <paramref name="to" /> より大または<paramref name="from" /> または <paramref name="to" /> が 0 未満です。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> のインスタンス。</returns>
      <param name="input">範囲ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効な範囲ヘッダー値の情報です。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Ranges">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> オブジェクトから指定した範囲を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。<see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> オブジェクトからの範囲です。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> バージョン。</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Unit">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> オブジェクトから単位を取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。<see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> オブジェクトからの単位です。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeItemHeaderValue">
      <summary>バイト範囲の Range ヘッダー値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="from">データの送信を開始する位置。</param>
      <param name="to">データの送信を終了する位置。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> は <paramref name="to" /> より大または<paramref name="from" /> または <paramref name="to" /> が 0 未満です。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.From">
      <summary>データの送信を開始する位置を取得します。</summary>
      <returns>
        <see cref="T:System.Int64" /> を返します。データの送信を開始する位置。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.To">
      <summary>データの送信を終了する位置を取得します。</summary>
      <returns>
        <see cref="T:System.Int64" /> を返します。データの送信を終了する位置。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RetryConditionHeaderValue">
      <summary>日付/時刻または期間値のいずれかとなる Retry-After ヘッダー値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="date">新しいインスタンスを初期化するために使用する日付と時刻のオフセット。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.TimeSpan)">
      <summary>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="delta">新しいインスタンスを初期化するために使用される、秒単位の差分です。</param>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Date">
      <summary>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> オブジェクトから日付と時刻のオフセットを取得します。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。<see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> オブジェクトからの日付と時刻のオフセット。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Delta">
      <summary>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> オブジェクトから秒単位の差分を取得します。</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> を返します。<see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> オブジェクトの秒単位の差分です。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> のインスタンス。</returns>
      <param name="input">再試行条件ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効な再試行条件ヘッダー値の情報です。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RetryConditionHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.StringWithQualityHeaderValue">
      <summary>オプションの品質と文字列ヘッダー値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">新しいインスタンスを初期化するために使用する文字列。</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">新しいインスタンスを初期化するために使用する文字列。</param>
      <param name="quality">新しいインスタンスを初期化するために使用される品質要素。</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Equals(System.Object)">
      <summary>指定のオブジェクトが現在の <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> のインスタンス。</returns>
      <param name="input">品質ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は品質ヘッダー値の情報を含む有効な文字列ではありません。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Quality">
      <summary>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> オブジェクトから品質係数を取得します。</summary>
      <returns>
        <see cref="T:System.Double" /> を返します。<see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> オブジェクトからの品質係数です。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.StringWithQualityHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> バージョン。</param>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Value">
      <summary>
        <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> オブジェクトから文字列値を取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。<see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> オブジェクトから取得された文字列値。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingHeaderValue">
      <summary>Accept-Encoding ヘッダー値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.Net.Http.Headers.TransferCodingHeaderValue)">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="source">新しいインスタンスを初期化するために使用する <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> オブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">新しいインスタンスを初期化するために使用する文字列。</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Equals(System.Object)">
      <summary>指定のオブジェクトが現在の <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Parameters">
      <summary>転送コーディング パラメーターを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> を返します。転送コーディング パラメーター。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> のインスタンス。</returns>
      <param name="input">転送コーディング ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効な転送コーディング ヘッダー値の情報です。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> バージョン。</param>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Value">
      <summary>転送コーディング値を取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。転送コーディング値。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue">
      <summary>オプションの品質要素と Accept-Encoding ヘッダー値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">新しいインスタンスを初期化するために使用する文字列。</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="value">新しいインスタンスを初期化するために使用する文字列。</param>
      <param name="quality">品質係数の値。</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> のインスタンス。</returns>
      <param name="input">転送コーディング値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は品質ヘッダー値の情報を含む有効な転送コーディングではありません。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Quality">
      <summary>
        <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> から品質係数を取得します。</summary>
      <returns>
        <see cref="T:System.Double" /> を返します。<see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> からの品質係数です。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingWithQualityHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.ViaHeaderValue">
      <summary>Via ヘッダーの値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="protocolVersion">受信したプロトコルのプロトコル バージョン。</param>
      <param name="receivedBy">要求または応答が受信されたホストとポート。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="protocolVersion">受信したプロトコルのプロトコル バージョン。</param>
      <param name="receivedBy">要求または応答が受信されたホストとポート。</param>
      <param name="protocolName">受信したプロトコルのプロトコル名。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="protocolVersion">受信したプロトコルのプロトコル バージョン。</param>
      <param name="receivedBy">要求または応答が受信されたホストとポート。</param>
      <param name="protocolName">受信したプロトコルのプロトコル名。</param>
      <param name="comment">受信者プロキシまたはゲートウェイのソフトウェアを識別するために使用されるコメント フィールド。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.Comment">
      <summary>受信者プロキシまたはゲートウェイのソフトウェアを識別するために使用されるコメント フィールドを取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。受信者プロキシまたはゲートウェイのソフトウェアを識別するために使用されるコメント フィールド。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コードを返します。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> を返します。<see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> のインスタンス。</returns>
      <param name="input">Via ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効な Via ヘッダー値の情報です。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolName">
      <summary>受信したプロトコルのプロトコル名を取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。プロトコル名。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolVersion">
      <summary>受信したプロトコルのプロトコル バージョンを取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。プロトコル バージョン。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ReceivedBy">
      <summary>要求または応答が受信されたホストとポートを取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。要求または応答が受信されたホストとポート。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.TryParse(System.String,System.Net.Http.Headers.ViaHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> バージョン。</param>
    </member>
    <member name="T:System.Net.Http.Headers.WarningHeaderValue">
      <summary>警告ヘッダーで使用される警告値を表します。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String)">
      <summary>
        <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="code">特定の警告コード。</param>
      <param name="agent">警告をアタッチしたホスト。</param>
      <param name="text">警告テキストを含む引用文字列。</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String,System.DateTimeOffset)">
      <summary>
        <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="code">特定の警告コード。</param>
      <param name="agent">警告をアタッチしたホスト。</param>
      <param name="text">警告テキストを含む引用文字列。</param>
      <param name="date">警告の日付およびタイムスタンプ。</param>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Agent">
      <summary>警告をアタッチしたホストを取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。警告をアタッチしたホスト。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Code">
      <summary>特定の警告コードを取得します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。特定の警告コード。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Date">
      <summary>警告の日付/タイム スタンプを取得します。</summary>
      <returns>
        <see cref="T:System.DateTimeOffset" /> を返します。警告の日付およびタイムスタンプ。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Equals(System.Object)">
      <summary>指定した <see cref="T:System.Object" /> が、現在の <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> オブジェクトと等しいかどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。指定した <see cref="T:System.Object" /> が現在のオブジェクトと等しい場合は true、それ以外の場合は false。</returns>
      <param name="obj">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.GetHashCode">
      <summary>
        <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> オブジェクトのハッシュ関数として機能します。</summary>
      <returns>
        <see cref="T:System.Int32" /> を返します。現在のオブジェクトのハッシュ コード。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Parse(System.String)">
      <summary>文字列を <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> インスタンスに変換します。</summary>
      <returns>
        <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> のインスタンスを返します。</returns>
      <param name="input">認証ヘッダー値の情報を表す文字列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> が null 参照です。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> は無効な認証ヘッダー値の情報です。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Text">
      <summary>警告テキストを含む引用文字列を取得します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。警告テキストを含む引用文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.ToString">
      <summary>現在の <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> オブジェクトを表す文字列を返します。</summary>
      <returns>
        <see cref="T:System.String" /> を返します。現在のオブジェクトを表す文字列。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.TryParse(System.String,System.Net.Http.Headers.WarningHeaderValue@)">
      <summary>文字列が有効な <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 情報かどうかを判断します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。<paramref name="input" /> が有効な <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 情報である場合は true。それ以外の場合は false。</returns>
      <param name="input">検証対象の文字列。</param>
      <param name="parsedValue">文字列の <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> バージョン。</param>
    </member>
  </members>
</doc>