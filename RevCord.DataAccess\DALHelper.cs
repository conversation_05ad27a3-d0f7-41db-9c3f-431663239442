﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using System.Data.SqlClient;
using RevCord.Util;
using System.Xml.Linq;
using Microsoft.AspNet.SignalR.Client;
using Microsoft.AspNet.SignalR.Client.Hubs;
using System.IO;
using System.Security.Cryptography;
using System.Xml;

namespace RevCord.DataAccess
{
    internal static class DALHelper
    {
        //private const string CONN_STR = @"Data Source=.\SQLEXPRESS;AttachDbFilename=""C:\Documents and Settings\Sher\My Documents\Visual Studio 2010\Projects\Rev\RevCordDevelopmentDB.mdf"";Integrated Security=True;Connect Timeout=30;User Instance=True;";
        //private const string CONN_STR = @"Data Source=.\***express;Initial Catalog=RevCordDevelopmentDB;Integrated Security=True;Pooling=True";
        //private static string CONN_STR = "Data Source=.;Initial Catalog=RevCordDevelopmentDB;Persist Security Info=True;User ID=sa;Password=***";
        /// <summary>
        /// WebConfig File.
        /// </summary>
        /// private static string CONN_STR = ConfigurationManager.ConnectionStrings["DALConnectionString"].ConnectionString.ToString();
        //private static string VOICEREC_CONN_STR = ConfigurationManager.ConnectionStrings["VoiceRecConnectionString"].ConnectionString.ToString();

        //private static string CONN_STR = SiteConfiguration.DALConnectionString != null && SiteConfiguration.DALConnectionString != String.Empty
        //                        ? SiteConfiguration.DALConnectionString
        //                        : @"Data Source=.\RevCord;Initial Catalog=VoiceRec;User ID=sa;Password=******;Persist Security Info=True;";

        //private static string CONN_STR = ConfigurationManager.ConnectionStrings["DALConnectionString"].ConnectionString.ToString();

        //private static SqlConnection _connection;
        //private static string CONN_MASTER_DB = ConfigurationManager.ConnectionStrings["MasterDBConnectionString"].ConnectionString.ToString();
        //private static bool IsMTEnabled = AppSettingsHelper.GetValueAsBool("isMTEnable", false);

        private static EncryptionUtil _encryption = new EncryptionUtil();
        private static string MTConnectionHost = AppSettingsHelper.GetValueAsString("MTConnectionHost");

        private static string CONN_MASTER_DB = GetConnMasterDB();
        private static string CONN_MGO_MASTER_DB = GetConnMGOMasterDB();
        private static bool IsMTEnabled = GetIsMTEnabled();
        private static HubConnection hubConnection = GetHubConnection();
        private static IHubProxy HubProxy;

        private static string GetConnMasterDB()
        {
            string CONN_MASTER_DB = SiteConfig.MasterDBConnectionString;

            if (CONN_MASTER_DB.Contains("Data Source"))
                return CONN_MASTER_DB;
            else
                CONN_MASTER_DB = _encryption.DecryptionTechnique(CONN_MASTER_DB, null, null);

            return CONN_MASTER_DB;
        }

        private static string GetConnMGOMasterDB()
        {
            string CONN_MASTER_DB = AppSettingsHelper.GetValueAsString("MGOMasterDBConnectionString");

            if (CONN_MASTER_DB.Contains("Data Source"))
                return CONN_MASTER_DB;
            else
                CONN_MASTER_DB = _encryption.DecryptionTechnique(CONN_MASTER_DB, null, null);

            return CONN_MASTER_DB;
        }

        private static bool GetIsMTEnabled()
        {
            return SiteConfig.IsMTEnable;
        }

        public static SqlConnection GetConnection()
        {
            string conn = SiteConfig.DALConnectionString;
            if (!conn.Contains("Data Source"))
                conn = _encryption.DecryptionTechnique(conn, null, null);

            return new SqlConnection(conn);
        }

        public static SqlConnection GetConnection(int tenantId)
        {
            string connectionString = "";
            if (IsMTEnabled)
            {
                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandText = "SELECT DBConnection FROM mtTenantDatabase WHERE (TenantId = @TenantId)";
                    cmd.Parameters.AddWithValue("@TenantId", tenantId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            connectionString = Convert.ToString(dr["DBConnection"]);

                            if (!string.IsNullOrEmpty(MTConnectionHost))
                            {
                                connectionString = Convert.ToString(dr["DBConnection"]).Replace(@".\Revcord;", MTConnectionHost).Replace(@".\REVCORD;", MTConnectionHost);
                            }
                            //connectionString = Convert.ToString(dr["DBConnection"]).Replace(@".\Revcord;", @"mt.revcord.com\Revcord,1533;").Replace(@".\REVCORD;", @"mt.revcord.com\Revcord,1533;");
                            //connectionString = Convert.ToString(dr["DBConnection"]).Replace(@".\Revcord;", @"mttest.revcord.com\Revcord,1433;").Replace(@".\REVCORD;", @"mttest.revcord.com\Revcord,1433;");
                        }
                    }
                }
            }
            else
            {
                connectionString = SiteConfig.DALConnectionString;

                if (!connectionString.Contains("Data Source"))
                    connectionString = _encryption.DecryptionTechnique(connectionString, null, null);

            }
            return new SqlConnection(connectionString);
        }

        public static List<int> GetTenantsByEmail(string UserEmail)
        {
            List<int> tenancyList = new List<int>();
            if (IsMTEnabled)
            {
                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandText = "SELECT distinct Tenantuser.[TenantId] FROM [mtTenantUser] Tenantuser inner join [mtTenantDatabase] TenantDatabase on Tenantuser.[TenantId] = TenantDatabase.[TenantId] where Email = '" + UserEmail.Trim() + "'";
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                            while (dr.Read())
                                tenancyList.Add(Convert.ToInt32(dr["TenantId"]));
                    }
                }
            }
            else tenancyList.Add(0);
            return tenancyList;
        }

        public static SqlConnection GetRevLogDBConnection(int tenantId)
        {
            string connectionString = "";
            if (IsMTEnabled && tenantId > 0)
            {
                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandText = "SELECT DBConnection FROM mtTenantDatabase WHERE (TenantId = @TenantId)";
                    cmd.Parameters.AddWithValue("@TenantId", tenantId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            connectionString = Convert.ToString(dr["DBConnection"]);
                            connectionString = connectionString.Replace("VoiceRec", "RevLog");

                            if (!string.IsNullOrEmpty(MTConnectionHost))
                            {
                                connectionString = connectionString.Replace(@".\Revcord;", MTConnectionHost).Replace(@".\REVCORD;", MTConnectionHost);
                            }
                        }
                    }
                }
            }
            else
            {
                connectionString = SiteConfig.RevLogDBConnectionString;

                if (!connectionString.Contains("Data Source"))
                    connectionString = _encryption.DecryptionTechnique(connectionString, null, null);
            }
            return new SqlConnection(connectionString);
        }

        public static SqlConnection GetRevSyncConnection()
        {
            string connectionString = "";
            using (var conn = DALHelper.RevSyncMasterDALConnection())
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandText = "SELECT DBConnection FROM mtTenantDatabase WHERE (TenantId = @TenantId)";
                cmd.Parameters.AddWithValue("@TenantId", SiteConfig.RevSyncTenantID);
                conn.Open();
                using (SqlDataReader dr = cmd.ExecuteReader())
                {
                    if (dr.HasRows)
                    {
                        dr.Read();

                        if (!string.IsNullOrEmpty(MTConnectionHost))
                        {
                            connectionString = Convert.ToString(dr["DBConnection"]).Replace(@".\Revcord;", MTConnectionHost).Replace(@".\REVCORD;", MTConnectionHost);
                        }
                        else
                        {
                            connectionString = Convert.ToString(dr["DBConnection"]).Replace(@".\Revcord;", @"mt.revcord.com\Revcord,1533;").Replace(@".\REVCORD;", @"mt.revcord.com\Revcord,1533;");
                        }
                    }
                }
            }
            return new SqlConnection(connectionString);
        }

        public static SqlConnection RevSyncMasterDALConnection()
        {
            string conn = SiteConfig.RevSyncMasterDALConnectionString;
            if (!conn.Contains("Data Source"))
                conn = _encryption.DecryptionTechnique(conn, null, null);

            return new SqlConnection(conn);
        }

        public static SqlConnection GetLocalDBConnection()
        {
            string conn = ConfigurationManager.AppSettings["LocalDBConnection"];
            if (!conn.Contains("Data Source"))
                conn = _encryption.DecryptionTechnique(conn, null, null);
            return new SqlConnection(conn);
        }

        public static SqlConnection GetConnectionMasterDB()
        {
            return new SqlConnection(CONN_MASTER_DB);
        }

        public static SqlConnection GetConnectionMGOMasterDB()
        {
            return new SqlConnection(CONN_MGO_MASTER_DB);
        }

        public static HubConnection GetHubConnection()
        {
            string CONFIG_PATH = ConfigurationManager.AppSettings["site"];
            var xDoc = XDocument.Load(CONFIG_PATH);
            string RevRecAddress = xDoc.Root.Element("RevRecAddress").Value;
            HubConnection _hubConnection = new HubConnection("http://" + RevRecAddress + "/ServerHub/signalr/");
            HubProxy = _hubConnection.CreateHubProxy("ServerHub");
            //_hubConnection.Start().Wait();
            _hubConnection.Start();

            return _hubConnection;
        }

        public static bool SendMessageToHub(int iTenantID, string SDalType, string SMethodName, string SMessage)
        {
            if (hubConnection == null) hubConnection = GetHubConnection();
            if (hubConnection.State.ToString().ToLower() != "connected") hubConnection = GetHubConnection();
            if (hubConnection.State.ToString().ToLower() == "connected") HubProxy.Invoke("RevsyncRequest", iTenantID.ToString(), SDalType, SMethodName, SMessage); else return false;

            return true;
        }

        public static string RevsyncAPICall(int iTenantID, string SDalType, string SMethodName, string SMessage)
        {
            //string sUrl = "http://localhost:56555/RevMTAPI.svc/Sync/?sTenantID={"+ SiteConfig.RevSyncTenantID + "}&sDalType={" + SDalType + "}&sMethod={"+ SMethodName + "}&sValue={" + new Encryption().EncryptionTechnique(SMessage) + "}";
            //string sUrl = "http://localhost:56555/RevMTAPI.svc/Sync/?sTenantID={" + SiteConfig.RevSyncTenantID + "}&sDalType={" + SDalType + "}&sMethod={" + SMethodName + "}&sValue={" + SMessage + "}";
            string sUrl = SiteConfig.RevSyncURL + "/RevMTAPI/RevMTAPI.svc/Sync/?sTenantID={" + SiteConfig.RevSyncTenantID + "}&sDalType={" + SDalType + "}&sMethod={" + SMethodName + "}&sValue={" + new Encryption().EncryptionTechnique(SMessage) + "}";
            System.Net.WebResponse sAPIResponse = System.Net.WebRequest.Create(sUrl).GetResponse();
            string response = new System.IO.StreamReader(sAPIResponse.GetResponseStream()).ReadToEnd().Trim();
            XmlDocument doc = new XmlDocument(); doc.LoadXml(response);
            return doc.GetElementsByTagName("string")[0].InnerText;
            //return response;
        }

        /*public static SqlConnection GetTenantDBConnection(int tenantId)
        {
            //string isMTEnabled = ConfigurationManager.AppSettings["isMTEnable"];
            //bool isMTEnabled = AppSettingsHelper.GetValueAsBool("isMTEnable", false);
            string connectionString = "";
            if (IsMTEnabled)
            {
                using (var conn = DALHelper.GetConnectionMasterDB())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandText = "SELECT DBConnection FROM mtTenantDatabase WHERE (TenantId = @TenantId)";
                    cmd.Parameters.AddWithValue("@TenantId", tenantId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            connectionString = Convert.ToString(dr["DBConnection"]);
                        }
                    }
                }
            }
            else
            {
                string CONFIG_PATH = ConfigurationManager.AppSettings["site"];
                var xDoc = XDocument.Load(CONFIG_PATH);
                connectionString = xDoc.Root.Element("DALConnectionString").Value;
            }
            return new SqlConnection(connectionString);
        }*/

        //public static SqlConnection GetConnectionFromWeb()
        //{
        //    return new SqlConnection(CONN_STR);
        //}

        ////public static SqlConnection GetConnectionAdmin()
        ////{
        ////    return new SqlConnection(CONN_STR);
        ////}

        ////public static SqlConnection sGetConnectionVoiceRec()
        ////{
        ////    return new SqlConnection(VOICEREC_CONN_STR);
        ////}

        ///// <summary>
        ///// Executes SqlConnection to open a connection against database
        ///// </summary>
        ///// <returns>Sql Connection</returns>
        //public static SqlConnection Connection
        //{
        //    get
        //    {
        //        if (_connection == null)
        //        {
        //            _connection = new SqlConnection(CONN_STR);
        //            //_connection.Open();
        //        }
        //        return _connection;
        //    }
        //}
    }

    public class Encryption
    {
        private byte[] _keyByte = { };
        //Default Key
        private static string _key = "z4a4b2y2";
        //Default initial vector
        private byte[] _ivByte = { 0x01, 0x12, 0x23, 0x34, 0x45, 0x56, 0x67, 0x78 };

        public string EncryptionTechnique(string value, string key = null, string iv = null)
        {

            string encryptValue = string.Empty;
            MemoryStream ms = null;
            CryptoStream cs = null;
            if (!string.IsNullOrEmpty(value))
            {
                try
                {
                    if (!string.IsNullOrEmpty(key))
                    {
                        _keyByte = Encoding.UTF8.GetBytes(key.Substring(0, 8));
                        if (!string.IsNullOrEmpty(iv))
                        {
                            _ivByte = Encoding.UTF8.GetBytes(iv.Substring(0, 8));
                        }
                    }
                    else
                    {
                        _keyByte = Encoding.UTF8.GetBytes(_key.Substring(0, 8));
                    }
                    using (DESCryptoServiceProvider des =
                            new DESCryptoServiceProvider())
                    {

                        byte[] inputByteArray = Encoding.UTF8.GetBytes(value);
                        ms = new MemoryStream();
                        cs = new CryptoStream(ms, des.CreateEncryptor(_keyByte, _ivByte), CryptoStreamMode.Write);
                        cs.Write(inputByteArray, 0, inputByteArray.Length);
                        cs.FlushFinalBlock();
                        encryptValue = Convert.ToBase64String(ms.ToArray());
                    }
                }
                catch (Exception ex)
                {
                    //TODO: write log
                    // Response.Write(ex.Message.ToString());
                    return ex.Message.ToString();
                }
                finally
                {
                    cs.Dispose();
                    ms.Dispose();
                }
            }
            return encryptValue;
        }
    }
}
