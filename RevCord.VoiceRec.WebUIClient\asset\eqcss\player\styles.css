﻿/**
 * Element Queries for Player
 */
@element #playerMain and (max-width: 199px) {
	:self {

    }

	#playerMain [class*='col-'] {
		padding-left: 2px;
		padding-right: 2px;
	}

	#playerMain .fileplayer-btn-controls {
        width: 3em;
    }

    #playerMain table#tblPlayerButtonControls {
        width: 2.5em;
    }

    #playerMain .fileplayer-progress-slider {
        width: calc(100% - 3em);
    }

    #playerMain .hideOnXtraSmall {
        display: none !important;
    }

    #playerMain .fileplayer-progress-timer {
        display: none !important;
    }

	#playerMain .fileplayer-expand-close {
		display: none !important;
	}

    #playerSubControls {
        display: none !important;
    }
}

@element #playerMain and (min-width: 200px) and (max-width: 319px) {
	:self {

    }

	#playerMain [class*='col-'] {
		padding-left: 2px;
		padding-right: 2px;
	}

	#playerMain .fileplayer-btn-controls {
        width: 9em;
    }

    #playerMain .fileplayer-progress-slider {
        width: calc(100% - 9em);
    }

    #playerMain .fileplayer-progress-timer {
        display: none !important;
    }

	#playerMain .fileplayer-expand-close {
		display: none !important;
	}

    #playerSubControls {
        display: eval("triggerFPExpandClose()");
    }
}

@element #playerMain and (min-width: 320px) and (max-width: 399px) {
	:self {

    }

	#playerMain [class*='col-'] {
		padding-left: 2px;
		padding-right: 2px;
	}

	#playerMain .fileplayer-btn-controls {
        width: 9em;
    }

    #playerMain .fileplayer-progress-slider {
        width: calc(100% - 9em - 108px);
    }

    #playerMain .fileplayer-progress-timer {
        width: 60px;
    }

	#playerMain .fileplayer-expand-close {
		width: 48px;
	}
}

@element #playerMain and (min-width: 400px) and (max-width: 499px) {
	:self {

    }

	#playerMain [class*='col-'] {
		padding-left: 5px;
		padding-right: 5px;
	}

	#playerMain .fileplayer-btn-controls {
        width: 27%;
    }

    #playerMain .fileplayer-progress-slider {
        width: 46%;
    }

    #playerMain .fileplayer-progress-timer {
        width: 14%;
    }

	#playerMain .fileplayer-expand-close {
		width: 13%;
	}
}

@element #playerMain and (min-width: 500px) and (max-width: 599px) {
	:self {

    }

	#playerMain [class*='col-'] {
		padding-left: 10px;
		padding-right: 10px;
	}

    #playerMain .fileplayer-btn-controls {
        width: 20%;
    }

    #playerMain .fileplayer-progress-slider {
        width: 55%;
    }

    #playerMain .fileplayer-progress-timer, .fileplayer-expand-close {
        width: 12.5%;
    }
}

@element #playerMain and (min-width: 600px) and (max-width: 699px) {
    :self {

    }

	#playerMain [class*='col-'], #playerSubControls [class*='col-'] {
		padding-left: 10px;
		padding-right: 10px;
	}

    #playerMain .fileplayer-btn-controls {
        width: 20%;
    }

    #playerMain .fileplayer-progress-slider {
        width: 60%;
    }

    #playerMain .fileplayer-progress-timer, .fileplayer-expand-close {
        width: 10%;
    }

	#playerSubControls .row {
		margin-left: -15px !important;
		margin-right: -15px !important;
	}
	
	#playerSubControls .fileplayer-volume-control {
		width: 30%;
	}

	#playerSubControls .fileplayer-pitch-control {
		width: 32%;
	}

	#playerSubControls .fileplayer-skip-silence {
		width: 34.5%;
	}

	#playerSubControls .fileplayer-duration {
		width: 32.5%;
	}

	#playerSubControls .fileplayer-additional-btn {
		width: 13%;
		float: right;
	}
}

@element #playerMain and (min-width: 700px) and (max-width: 899px) {
    :self {

    }

	#playerMain [class*='col-'], #playerSubControls [class*='col-'] {
		padding-left: 10px;
		padding-right: 10px;
	}

    #playerMain .fileplayer-btn-controls {
        width: 17%;
    }

    #playerMain .fileplayer-progress-slider {
        width: 60%;
    }

    #playerMain .fileplayer-progress-timer, .fileplayer-expand-close {
        width: 11.5%;
    }

	#playerSubControls .row {
		margin-left: -15px !important;
		margin-right: -15px !important;
	}
	
	#playerSubControls .fileplayer-volume-control {
		width: 14%;
	}

	#playerSubControls .fileplayer-pitch-control {
		width: 16.2%;
	}

	#playerSubControls .fileplayer-skip-silence {
		width: 30%;
	}

	#playerSubControls .fileplayer-duration {
		width: 27.8%;
	}

	#playerSubControls .fileplayer-additional-btn {
		width: 12%;
		float: right;
	}
}

@element #playerMain and (min-width: 900px) and (max-width: 1199px) {
    :self {

    }

    #playerMain .fileplayer-btn-controls {
        width: 14%;
    }

    #playerMain .fileplayer-progress-slider {
        width: 72%;
    }

    #playerMain .fileplayer-progress-timer {
        width: 8%;
    }

	#playerMain .fileplayer-expand-close {
		width: 6%;
	}
}

@element #playerMain and (min-width: 1200px) {
    :self {

    }

    #playerMain .fileplayer-btn-controls {
        width: 10%;
    }

    #playerMain .fileplayer-progress-slider {
        width: 74%;
    }

    #playerMain .fileplayer-progress-timer, .fileplayer-expand-close {
        width: 8%;
    }
}
