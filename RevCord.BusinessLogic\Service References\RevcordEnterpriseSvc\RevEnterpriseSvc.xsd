<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd8" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd11" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.RoleManagement" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd12" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" />
  <xs:complexType name="ArrayOfUserActivity">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="UserActivity" nillable="true" type="tns:UserActivity" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfUserActivity" nillable="true" type="tns:ArrayOfUserActivity" />
  <xs:complexType name="UserActivity">
    <xs:sequence>
      <xs:element minOccurs="0" name="ActivityId" type="xs:int" />
      <xs:element minOccurs="0" name="ActivityPerformed" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ClientIP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Comments" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Id" type="xs:long" />
      <xs:element minOccurs="0" name="MessageData" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MessageKey" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UserActivity" nillable="true" type="tns:UserActivity" />
  <xs:complexType name="AppUserAccess">
    <xs:sequence>
      <xs:element minOccurs="0" name="BothIQ3AndRevcellEnable" type="xs:boolean" />
      <xs:element minOccurs="0" name="ChannelType" type="xs:int" />
      <xs:element minOccurs="0" name="EnableUser" type="xs:boolean" />
      <xs:element minOccurs="0" name="Ext" type="xs:int" />
      <xs:element minOccurs="0" name="IsDeviceUser" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsIQ3OnlyEnable" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsMDEnable" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRevcellOnlyEnable" type="xs:boolean" />
      <xs:element minOccurs="0" name="UserEmail" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserNum" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AppUserAccess" nillable="true" type="tns:AppUserAccess" />
  <xs:complexType name="ArrayOfAppUser">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="AppUser" nillable="true" type="tns:AppUser" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfAppUser" nillable="true" type="tns:ArrayOfAppUser" />
  <xs:complexType name="AppUser">
    <xs:sequence>
      <xs:element minOccurs="0" name="CreatedBy" type="xs:long" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="DeletedDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Email" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Group" nillable="true" type="tns:GlobalGroup" />
      <xs:element minOccurs="0" name="GroupId" type="xs:int" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IsApproved" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsLockOut" type="xs:boolean" />
      <xs:element minOccurs="0" name="LastLockOutDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="LastLoginDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="ModifiedBy" type="xs:long" />
      <xs:element minOccurs="0" name="ModifiedDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Password" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PasswordAnswer" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PasswordQuestion" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Permissions" nillable="true" type="tns:ArrayOfPermission" />
      <xs:element minOccurs="0" name="UserName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserProfile" nillable="true" type="tns:UserProfile" />
      <xs:element minOccurs="0" name="UserTypeId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AppUser" nillable="true" type="tns:AppUser" />
  <xs:complexType name="GlobalGroup">
    <xs:sequence>
      <xs:element name="_x003C_CreatedDate_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_DeletedDate_x003E_k__BackingField" nillable="true" type="xs:dateTime" />
      <xs:element name="_x003C_Depth_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_Description_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_Id_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_IsActive_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsDeleteable_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsDeleted_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_ModifiedDate_x003E_k__BackingField" nillable="true" type="xs:dateTime" />
      <xs:element name="_x003C_Name_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_Ordering_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_ParentId_x003E_k__BackingField" nillable="true" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GlobalGroup" nillable="true" type="tns:GlobalGroup" />
  <xs:complexType name="ArrayOfPermission">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Permission" nillable="true" type="tns:Permission" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfPermission" nillable="true" type="tns:ArrayOfPermission" />
  <xs:complexType name="Permission">
    <xs:sequence>
      <xs:element name="_x003C_Id_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_IsActive_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_Title_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_UserNum_x003E_k__BackingField" nillable="true" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Permission" nillable="true" type="tns:Permission" />
  <xs:complexType name="UserProfile">
    <xs:sequence>
      <xs:element minOccurs="0" name="AppUserId" type="xs:int" />
      <xs:element minOccurs="0" name="CreatedBy" type="xs:int" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="DateOfBirth" type="xs:dateTime" />
      <xs:element minOccurs="0" name="DeletedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="EmailDefault" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="EmailsOther" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FirstName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Gender" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IdentityNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IsCompleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="JoinBeginDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="JoinEndDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="LastName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ModifiedBy" type="xs:int" />
      <xs:element minOccurs="0" name="ModifiedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="SocialSecurityNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserFax" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserMobile" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserPhone" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserPicture" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UserProfile" nillable="true" type="tns:UserProfile" />
  <xs:complexType name="EventInvitation">
    <xs:sequence>
      <xs:element xmlns:q1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="ActiveInvitedEventIds" nillable="true" type="q1:ArrayOfstring" />
      <xs:element minOccurs="0" name="EventId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="InvitationDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="InvitedBy" type="xs:int" />
      <xs:element minOccurs="0" name="IsAnyEventActive" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsEventActive" type="xs:boolean" />
      <xs:element minOccurs="0" name="UserId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserNum" type="xs:int" />
      <xs:element minOccurs="0" name="UserPassword" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EventInvitation" nillable="true" type="tns:EventInvitation" />
  <xs:complexType name="ArrayOfGlobalGroup">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="GlobalGroup" nillable="true" type="tns:GlobalGroup" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfGlobalGroup" nillable="true" type="tns:ArrayOfGlobalGroup" />
  <xs:complexType name="ArrayOfGroupCategory">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="GroupCategory" nillable="true" type="tns:GroupCategory" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfGroupCategory" nillable="true" type="tns:ArrayOfGroupCategory" />
  <xs:complexType name="GroupCategory">
    <xs:sequence>
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="ImageName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GroupCategory" nillable="true" type="tns:GroupCategory" />
  <xs:complexType name="Invitation">
    <xs:sequence>
      <xs:element minOccurs="0" name="AcceptedOn" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Comments" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CreatedBy" type="xs:int" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="InvitationURL" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="InviteCode" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="LastModifiedBy" type="xs:int" />
      <xs:element minOccurs="0" name="LastModifiedDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="SentBy" type="xs:int" />
      <xs:element minOccurs="0" name="SentByEmail" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SentOn" type="xs:dateTime" />
      <xs:element minOccurs="0" name="SentTo" type="xs:int" />
      <xs:element minOccurs="0" name="SentToEmail" nillable="true" type="xs:string" />
      <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="Status" type="q2:InvitationStatus" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Invitation" nillable="true" type="tns:Invitation" />
  <xs:complexType name="ArrayOfInvitation">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Invitation" nillable="true" type="tns:Invitation" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfInvitation" nillable="true" type="tns:ArrayOfInvitation" />
  <xs:complexType name="ArrayOfUserInfoLite">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="UserInfoLite" nillable="true" type="tns:UserInfoLite" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfUserInfoLite" nillable="true" type="tns:ArrayOfUserInfoLite" />
  <xs:complexType name="UserInfoLite">
    <xs:sequence>
      <xs:element minOccurs="0" name="FullName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="RecId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UserInfoLite" nillable="true" type="tns:UserInfoLite" />
  <xs:complexType name="User">
    <xs:sequence>
      <xs:element name="_x003C_AccessRight_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element xmlns:q3="http://schemas.microsoft.com/2003/10/Serialization/Arrays" name="_x003C_AssignedNodes_x003E_k__BackingField" nillable="true" type="q3:ArrayOfstring" />
      <xs:element name="_x003C_CanInvite_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_ChannelType_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_CompanyName_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_Create_T_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_DNISCheck_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_DNIS_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_DeSelectedUsers_x003E_k__BackingField" nillable="true" type="tns:ArrayOfAssignedUsers" />
      <xs:element name="_x003C_Delete_T_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_Descr_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_EOD_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_EnableDisableInquireUser_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_EnterprisePermissions_x003E_k__BackingField" nillable="true" type="tns:ArrayOfPermission" />
      <xs:element name="_x003C_ExtName_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_Ext_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_GlobalGroup_x003E_k__BackingField" nillable="true" type="tns:GlobalGroup" />
      <xs:element name="_x003C_GroupNum_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_HasShiftRest_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IdentityNumber_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_IsADUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsAssignedForChannel_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_IsAvrisView_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsCompactView_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsContinuousPlay_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsDeviceUser_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_IsEnterpriseUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsGroupBased_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsIQ3View_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsLockedOut_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsMultiChannel_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsRevCell_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsSyncedFromClient_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsTagRule_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsTempLogin_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_JoinBeginDate_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_JoinEndDate_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_LastPasswordChanged_x003E_k__BackingField" nillable="true" type="xs:dateTime" />
      <xs:element name="_x003C_MaxT1Ch_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_MinT1Ch_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_Modify_T_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_NoOfEvaluations_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_OriginalSimpleAccessRight_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_POD_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_Pause_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_PermissionStr_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_Permissions_x003E_k__BackingField" nillable="true" type="tns:ArrayOfPermission" />
      <xs:element name="_x003C_RecId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_RecName_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" name="_x003C_RecorderAccessRights_x003E_k__BackingField" nillable="true" type="q4:ArrayOfRecorderAccessRight" />
      <xs:element name="_x003C_RevSyncServerUserNum_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_RoleId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_RoleName_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.RoleManagement" name="_x003C_RolePermissions_x003E_k__BackingField" nillable="true" type="q5:ArrayOfRolePermission" />
      <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.RoleManagement" name="_x003C_Role_x003E_k__BackingField" nillable="true" type="q6:Role" />
      <xs:element name="_x003C_SearchRest_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_SelectType_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_SelectedUsersForSUR_x003E_k__BackingField" nillable="true" type="tns:ArrayOfAssignedUsers" />
      <xs:element name="_x003C_SelectedUsers_x003E_k__BackingField" nillable="true" type="tns:ArrayOfUnAssignedUsers" />
      <xs:element name="_x003C_SimpleAccessRight_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_Status_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_TagRule_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_TempUserPW_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_TenantId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_UMUserType_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_UserEmail_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_UserFax_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_UserGroup_x003E_k__BackingField" nillable="true" type="tns:UserGroup" />
      <xs:element name="_x003C_UserID_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_UserName_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_UserNum_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_UserPW_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_UserPhone_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_UserPic_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_UserType_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_ViewID_x003E_k__BackingField" type="xs:int" />
      <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" name="_x003C_cmDataUser_x003E_k__BackingField" nillable="true" type="q7:ArrayOfCustomMarkersData" />
      <xs:element name="_x003C_isuser_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_simpleuserrightsforuser_x003E_k__BackingField" nillable="true" type="tns:ArrayOfUser" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="User" nillable="true" type="tns:User" />
  <xs:complexType name="ArrayOfAssignedUsers">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="AssignedUsers" nillable="true" type="tns:AssignedUsers" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfAssignedUsers" nillable="true" type="tns:ArrayOfAssignedUsers" />
  <xs:complexType name="AssignedUsers">
    <xs:sequence>
      <xs:element name="_x003C_Ext_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_UserName_x003E_k__BackingField" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AssignedUsers" nillable="true" type="tns:AssignedUsers" />
  <xs:complexType name="ArrayOfUnAssignedUsers">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="UnAssignedUsers" nillable="true" type="tns:UnAssignedUsers" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfUnAssignedUsers" nillable="true" type="tns:ArrayOfUnAssignedUsers" />
  <xs:complexType name="UnAssignedUsers">
    <xs:sequence>
      <xs:element name="_x003C_Extensions_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_UserNum_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_UsersList_x003E_k__BackingField" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UnAssignedUsers" nillable="true" type="tns:UnAssignedUsers" />
  <xs:complexType name="UserGroup">
    <xs:sequence>
      <xs:element name="_x003C_AssignAuth_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_CreateID_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_Create_T_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_Delete_T_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_Depth_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_Descr_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_EnterpriseAssignAuth_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_ExtId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_Ext_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_GroupName_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_GroupNum_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_IndexNum_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_Modify_T_x003E_k__BackingField" type="xs:dateTime" />
      <xs:element name="_x003C_ParentGroup_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_RecId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_RevSyncServerUserNum_x003E_k__BackingField" type="xs:int" />
      <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" name="_x003C_TreeViewDataDTOs_x003E_k__BackingField" nillable="true" type="q8:ArrayOfTreeViewDataDTO" />
      <xs:element name="_x003C_UserName_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_UserNum_x003E_k__BackingField" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UserGroup" nillable="true" type="tns:UserGroup" />
  <xs:complexType name="ArrayOfUser">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="User" nillable="true" type="tns:User" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfUser" nillable="true" type="tns:ArrayOfUser" />
  <xs:complexType name="ArrayOfAssignedSimpleUserRights">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="AssignedSimpleUserRights" nillable="true" type="tns:AssignedSimpleUserRights" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfAssignedSimpleUserRights" nillable="true" type="tns:ArrayOfAssignedSimpleUserRights" />
  <xs:complexType name="AssignedSimpleUserRights">
    <xs:sequence>
      <xs:element name="_x003C_IsAdvanceReportsAssignedForUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsAllAssignedForUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsDashBoardAssignedForUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsEvaluationAssignedForUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsEvaluationReportsAssignedForUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsIRFullAssignedForUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsIRLiteAssignedForUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsMonitorAssignedForUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsSaveAndEmail_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsSearchAssignedForUser_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_IsSetUpAssignedForUser_x003E_k__BackingField" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AssignedSimpleUserRights" nillable="true" type="tns:AssignedSimpleUserRights" />
</xs:schema>