﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.RoleManagement;

namespace RevCord.DataContracts.Response
{
    public class UserManagementResponse : ResponseBase
    {

        #region Properties

        public bool FlagStatus { get; set; }
        public int ChannelType { get; set; }
        #endregion

        #region Associations

        public List<AppUser> AppUsers { get; set; }
        public List<GlobalGroup> GlobalGroups { get; set; }
        public List<Permission> Permissions { get; set; }

        public List<User> Users { get; set; }
        //public List<Agent> Agents { get; set; }
        public User LoginUser { get; set; }
        //public Agent LoginAgent { get; set; }
        public User User { get; set; }
        public EventInvitation EventInvitation { get; set; }
        public List<UserInfoLite> LiteUsers { get; set; }
        #endregion


        public List<GroupCategory> GroupCategories { get; set; }
        public List<TreeViewDataDTO> TreeViewDataDTOs { get; set; }
        public List<Role> Roles { get; set; }

        public List<Recorder> Recorders { get; set; }

        public bool IsChainDBsConfigured { get; set; }
        public bool IsECEnabled { get; set; }
        public bool IsEnterpriseRecorder { get; set; }
        public bool IsUserLocked { get; set; }
        public bool IsUserExists { get; set; }
        public bool IsUserAgreedToLicense { get; set; }

        public bool IsInquire { get; set; }
        public List<int> STTEnabledChannels { get; set; }
        public List<UserExtensionInfo> UserExtensionInfos { get; set; }

        #region Invitation
        public int UserId { get; set; }

        public int InvitationId { get; set; }

        public Invitation Invitation { get; set; }

        public IEnumerable<Invitation> Invitations { get; set; }
        #endregion

        public AppUserAccess AppUserAccess { get; set; }

        public List<string> Last5Passwords { get; set; }
    }
}
