﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.InquireEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.IQ3InspectionEntities;

namespace RevCord.DataContracts.EMREntities
{
    public class Case
    {
        public int Id { get; set; }
        public string FlightNo { get; set; }
        public string PrimaryComplaint { get; set; }
        public string Comments { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public long? LastModifiedBy { get; set; }

        public Event Event { get; set; }
        public Patient Patient { get; set; }
        public List<Drug> Drugs { get; set; }
        public List<string> DrugImages { get; set; }
        public List<Medication> Medications { get; set; }
        public List<string> MedicationImages { get; set; }
        public List<Electroencephalogram> EEGs { get; set; }
        public List<string> EEGImages { get; set; }

        //public List<VitalSign> VitalSigns { get; set; }
        //public Dictionary<VitalSign, string> VitalSigns { get; set; }
        public List<PatientVital> PatientVitals { get; set; }
        public List<CustomField> CustomFields { get; set; }
        public string QBDialogId { get; set; }
        public List<ChatTranscript> ChatTranscript { get; set; }
        public Inspection Inspection { get; set; }
    }
}
