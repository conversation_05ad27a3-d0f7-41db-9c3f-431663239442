﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.UserManagement;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class ADSyncGroupDAL
    {
        private int _tenantId;
        public ADSyncGroupDAL(int tenantId)
        {
            _tenantId = tenantId;
        }

        public List<ADSyncGroup> GetAllADGroups()
        {
            try
            {
                List<ADSyncGroup> objADSyncGroupList = new List<ADSyncGroup>();

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM vrADSyncGroup;";
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ADSyncGroups, "GetAllADGroups", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                ADSyncGroup objGroup = new ADSyncGroup();

                                objGroup.Id = Convert.ToInt32(dr["Id"]);
                                objGroup.GroupName = Convert.ToString(dr["GroupName"]);
                                objGroup.Comments = Convert.ToString(dr["Comments"]);
                                objGroup.IsSynced = Convert.ToBoolean(dr["IsSynced"]);

                                objADSyncGroupList.Add(objGroup);
                            }
                        }
                    }
                }

                return objADSyncGroupList;
            }
            catch (Exception ex) { throw ex; }
        }

        public ADSyncGroup GetADGroupById(int GroupId)
        {
            ADSyncGroup objGroup = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM vrADSyncGroup WHERE Id = @Id;";
                    cmd.Parameters.AddWithValue("@Id", GroupId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ADSyncGroups, "GetADGroupById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.Read())
                        {
                            objGroup = new ADSyncGroup();

                            objGroup.Id = Convert.ToInt32(dr["Id"]);
                            objGroup.GroupName = Convert.ToString(dr["GroupName"]);
                            objGroup.Comments = Convert.ToString(dr["Comments"]);
                            objGroup.IsSynced = Convert.ToBoolean(dr["IsSynced"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return objGroup;
        }

        public ADSyncGroup SaveADSyncGroup(ADSyncGroup group)
        {
            try
            {
                int ADGroupId = -1;

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.ADSYNCGROUP_INSERT;
                    cmd.Parameters.AddWithValue("@GroupName", group.GroupName);
                    cmd.Parameters.AddWithValue("@IsSynced", group.IsSynced);
                    cmd.Parameters.AddWithValue("@Comments", group.Comments);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ADSyncGroups, "SaveADSyncGroup", _tenantId));

                    ADGroupId = Convert.ToInt32(cmd.ExecuteScalar());
                }

                ADSyncGroup objGroup = GetADGroupById(ADGroupId);

                if (objGroup == null)
                {
                    objGroup = new ADSyncGroup
                    {
                        Id = -1
                    };
                }

                return objGroup;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool SyncADSyncGroup(int ADGroupId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE vrADSyncGroup SET IsSynced = 1 WHERE Id = @Id";
                    cmd.Parameters.AddWithValue("@Id", ADGroupId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ADSyncGroups, "SyncADSyncGroup", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    return count > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool UnSyncADSyncGroup(int ADGroupId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE vrADSyncGroup SET IsSynced = 0 WHERE Id = @Id";
                    cmd.Parameters.AddWithValue("@Id", ADGroupId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ADSyncGroups, "UnSyncADSyncGroup", _tenantId));

                    int count = cmd.ExecuteNonQuery();
                    return count > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool RemoveADSyncGroup(int ADGroupId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "DELETE FROM vrADSyncGroup WHERE Id = @Id";
                    cmd.Parameters.AddWithValue("@Id", ADGroupId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.ADSyncGroups, "RemoveADSyncGroup", _tenantId));

                    int count = cmd.ExecuteNonQuery();
                    return count > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }
    }
}
