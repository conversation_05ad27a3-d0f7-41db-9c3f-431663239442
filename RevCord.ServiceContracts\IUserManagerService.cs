﻿using System;
using System.Collections.Generic;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.IQ3;

namespace RevCord.ServiceContracts
{
    public interface IUserManagerService
    {
        UMResponse GetGroupsTree(UMRequest umReq);
        UMResponse GetAudioGroupsTree(UMRequest umReq);
        UMResponse GetAudioGroupsTreeFromRecorder(Recorder recorder, UMRequest umReq);
        UMResponse GetIQ3Tree(UMRequest umReq, int recId);
        UMResponse GetRVITree(UMRequest umReq, int recId);
        UMResponse GetTreeFromAllRecorders(UMRequest umReq);
        UMResponse GetEnterpriseTree(UMRequest umReq);
        
        UMResponse GetUsers(UMRequest umReq);
        UMResponse GetUserNumByExt(UMRequest umReq);
        UMResponse GetInquireGroupsTreeFromRecorder(Recorder recorder, UMRequest umReq);

        UMResponse GetUsersGroupsTree(UMRequest umReq);
        UMResponse GetUsersGroupsTreeFromRecorder(Recorder recorder, UMRequest umReq);

        UMResponse GetEnterpriseSimpleUserRights(UMRequest umReq);
        UMResponse SaveEnterpriseUserRights(UMRequest umReq);
        UMResponse SaveEnterpriseUserGroupBasedRights(UMRequest umRequest);

        UMResponse GetInquireGroupsTreeforEvaluationReportFromRecorder(Recorder recorder, UMRequest umReq, int SelectType);

        int GetActiveChannelCount(int tenantId);

        int AddSubMarker(CustomMarkersData customMarkerData, int tenantId);
        bool UpdateSubMarker(CustomMarkersData customMarkerDetail, int tenantId);
        bool RemoveSubMarker(int id, int tenantId);
        UMResponse GetCustomFields(UMRequest umReq);
        bool UpdateCustomField(CustomField customField, int tenantId);
    }
}