﻿using System;
namespace RevCord.DataContracts.IWBEntities
{
    public class Wpq
    {
        public int Id { get; set; }
        public string FileName { get; set; }

        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string CertifiedAuthority { get; set; }
        public DateTime CertificateDate { get; set; }
        public string InspectorName { get; set; }
        public string LabNo { get; set; }
        public string Notes { get; set; }


        public int JobId { get; set; }
        public int UserId { get; set; }//WelderId


        public int CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int? LastModifiedBy { get; set; }

        public int TestId { get; set; }
        public bool IsDraft { get; set; }

        public string FieldData { get; set; }

        public string UserName { get; set; }
        public string UserNum { get; set; }

        public string FormattedExpiryDate { get; set; }
    }
}
///*

//<PERSON><PERSON>'s Name                           Date 10/4/2024
//I.D. Num.                               Test No.
//WPS No.                                 Job No. 
//Joint type(s)                           Product form Plate


// * /
