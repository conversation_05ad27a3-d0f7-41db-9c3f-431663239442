﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.RevSignEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class RevSignDAL
    {
        private int _tenantId;
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("dashboardCommandTimeout", 300);
        public RevSignDAL(int tenantId)
        {
            _tenantId = tenantId;
        }

        public List<Document> GetDocuments(int ownerId)
        {
            List<Document> documents = new List<Document>();
            List<DocumentSigner> documentSigners = new List<DocumentSigner>();
            Document document = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevSign.REVSIGN_GET_DOCUMENTS;
                    cmd.Parameters.AddWithValue("@OwnerId", ownerId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RevSign, "GetDocuments", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            document = new Document();
                            document.Id = (int)dr["Id"];
                            document.Title = Convert.ToString(dr["Title"]);

                            document.FileName = Convert.ToString(dr["FileName"]);
                            document.PdfLink = Convert.ToString(dr["PdfLink"]);
                            document.SignedFileName = Convert.ToString(dr["SignedFileName"]);
                            document.SignedFileLink = Convert.ToString(dr["SignedFileLink"]);

                            document.IsUploaded = Convert.ToBoolean(dr["IsUploaded"]);
                            document.Notes = Convert.ToString(dr["Notes"]);

                            document.CompleteInDays = Convert.ToInt32(dr["CompleteInDays"]);
                            document.EnableReminder = Convert.ToBoolean(dr["EnableReminder"]);
                            document.ReminderAfterDays = Convert.ToInt32(dr["ReminderAfterDays"]);

                            document.SigningTypeId = (SigningTypeId)Convert.ToInt32(dr["SigningTypeId"]);
                            document.DocumentStatus = (DocumentStatus)Convert.ToInt32(dr["DocumentStatus"]);

                            document.OwnerId = Convert.ToInt32(dr["OwnerId"]);
                            document.OwnerName = Convert.ToString(dr["OwnerName"]);
                            document.OwnerEmail = Convert.ToString(dr["OwnerEmail"]);

                            document.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            document.ExpiryDate = Convert.ToDateTime(dr["ExpiryDate"]);
                            if (!dr.IsDBNull(dr.GetOrdinal("CompletionDate")))
                            {
                                document.CompletionDate = dr.GetDateTime(dr.GetOrdinal("CompletionDate"));
                            }
                            document.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            document.EventDate = Convert.ToString(dr["EventDate"]);
                            
                            documents.Add(document);
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                var documentSigner = new DocumentSigner();
                                documentSigner.Id = (int)dr["Id"];
                                documentSigner.DocumentId = Convert.ToInt32(dr["DocumentId"]);
                                documentSigner.SignerId = Convert.ToInt32(dr["SignerId"]);
                                documentSigner.Name = Convert.ToString(dr["Name"]);
                                documentSigner.Email = Convert.ToString(dr["Email"]);
                                documentSigner.SignerTypeId = Convert.ToInt32(dr["SignerTypeId"]);
                                documentSigner.IsSystemUser = Convert.ToBoolean(dr["SignerTypeId"]);
                                documentSigner.SignStatus = (SignStatus)Convert.ToInt32(dr["SignStatus"]);
                                documentSigner.SignatureFileName = Convert.ToString(dr["SignatureFileName"]);
                                documentSigner.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                documentSigner.ExpiryDate = Convert.ToDateTime(dr["ExpiryDate"]);
                                if (!dr.IsDBNull(dr.GetOrdinal("CompletionDate")))
                                {
                                    documentSigner.CompletionDate = dr.GetDateTime(dr.GetOrdinal("CompletionDate"));
                                }

                                if (!dr.IsDBNull(dr.GetOrdinal("ViewDate")))
                                {
                                    documentSigner.ViewDate = dr.GetDateTime(dr.GetOrdinal("ViewDate"));
                                }
                                if (!dr.IsDBNull(dr.GetOrdinal("TermsAgreeDate")))
                                {
                                    documentSigner.TermsAgreeDate = dr.GetDateTime(dr.GetOrdinal("TermsAgreeDate"));
                                }
                                documentSigner.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                                documentSigners.Add(documentSigner);
                            }
                        }

                        foreach (var doc in documents)
                        {
                            doc.DocumentSigners = documentSigners.Where(c => c.DocumentId == doc.Id).ToList();
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return documents;
        }

        public Document GetDocumentAndSignerById(int documentId, int documentSignerId)
        {
            List<DocumentSigner> documentSigners = null;
            Document document = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevSign.REVSIGN_GET_DOCUMENT_AND_SIGNER_BY_ID;
                    cmd.Parameters.AddWithValue("@DocumentId", documentId);
                    cmd.Parameters.AddWithValue("@DocumentSignerId", documentSignerId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RevSign, "GetDocumentAndSignerById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                            document = new Document();
                        while (dr.Read())
                        {
                            document.Id = (int)dr["Id"];
                            document.Title = Convert.ToString(dr["Title"]);

                            document.FileName = Convert.ToString(dr["FileName"]);
                            document.PdfLink = Convert.ToString(dr["PdfLink"]);
                            document.SignedFileName = Convert.ToString(dr["SignedFileName"]);
                            document.SignedFileLink = Convert.ToString(dr["SignedFileLink"]);

                            document.IsUploaded = Convert.ToBoolean(dr["IsUploaded"]);
                            document.Notes = Convert.ToString(dr["Notes"]);

                            document.CompleteInDays = Convert.ToInt32(dr["CompleteInDays"]);
                            document.EnableReminder = Convert.ToBoolean(dr["EnableReminder"]);
                            document.ReminderAfterDays = Convert.ToInt32(dr["ReminderAfterDays"]);

                            document.SigningTypeId = (SigningTypeId)Convert.ToInt32(dr["SigningTypeId"]);
                            document.DocumentStatus = (DocumentStatus)Convert.ToInt32(dr["DocumentStatus"]);

                            document.OwnerId = Convert.ToInt32(dr["OwnerId"]);
                            document.OwnerName = Convert.ToString(dr["OwnerName"]);
                            document.OwnerEmail = Convert.ToString(dr["OwnerEmail"]);

                            document.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            document.ExpiryDate = Convert.ToDateTime(dr["ExpiryDate"]);
                            if (!dr.IsDBNull(dr.GetOrdinal("CompletionDate")))
                            {
                                document.CompletionDate = dr.GetDateTime(dr.GetOrdinal("CompletionDate"));
                            }
                            document.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            documentSigners = new List<DocumentSigner>();
                            while (dr.Read())
                            {
                                var documentSigner = new DocumentSigner();
                                documentSigner.Id = (int)dr["Id"];
                                documentSigner.DocumentId = Convert.ToInt32(dr["DocumentId"]);
                                documentSigner.SignerId = Convert.ToInt32(dr["SignerId"]);
                                documentSigner.Name = Convert.ToString(dr["Name"]);
                                documentSigner.Email = Convert.ToString(dr["Email"]);
                                documentSigner.SignerTypeId = Convert.ToInt32(dr["SignerTypeId"]);
                                documentSigner.IsSystemUser = Convert.ToBoolean(dr["SignerTypeId"]);
                                documentSigner.SignStatus = (SignStatus)Convert.ToInt32(dr["SignStatus"]);
                                documentSigner.SignatureFileName = Convert.ToString(dr["SignatureFileName"]);
                                documentSigner.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                documentSigner.ExpiryDate = Convert.ToDateTime(dr["ExpiryDate"]);
                                if (!dr.IsDBNull(dr.GetOrdinal("CompletionDate")))
                                {
                                    documentSigner.CompletionDate = dr.GetDateTime(dr.GetOrdinal("CompletionDate"));
                                }
                                if (!dr.IsDBNull(dr.GetOrdinal("ViewDate")))
                                {
                                    documentSigner.ViewDate = dr.GetDateTime(dr.GetOrdinal("ViewDate"));
                                }
                                if (!dr.IsDBNull(dr.GetOrdinal("TermsAgreeDate")))
                                {
                                    documentSigner.TermsAgreeDate = dr.GetDateTime(dr.GetOrdinal("TermsAgreeDate"));
                                }
                                documentSigner.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                                documentSigners.Add(documentSigner);
                            }
                        }

                        document.DocumentSigners = documentSigners;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return document;
        }

        public bool DeleteDocumentSigningRequest(int documentId) {
            try
            {
                var returnValue = false;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();

                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevSign.REVSIGN_DELETE_DOCUMENT;
                    cmd.Parameters.AddWithValue("@DocumentId", documentId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "DeleteDocumentSigningRequest", _tenantId));

                    returnValue = Convert.ToBoolean(cmd.ExecuteNonQuery());

                    tran.Commit();

                    return returnValue;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public Document GetDocumentById(int documentId)
        {
            List<DocumentSigner> documentSigners = null;
            Document document = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevSign.REVSIGN_GET_DOCUMENT_BY_ID;
                    cmd.Parameters.AddWithValue("@DocumentId", documentId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.RevSign, "GetDocumentById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                            document = new Document();
                        while (dr.Read())
                        {
                            document.Id = (int)dr["Id"];
                            document.Title = Convert.ToString(dr["Title"]);

                            document.FileName = Convert.ToString(dr["FileName"]);
                            document.PdfLink = Convert.ToString(dr["PdfLink"]);
                            document.SignedFileName = Convert.ToString(dr["SignedFileName"]);
                            document.SignedFileLink = Convert.ToString(dr["SignedFileLink"]);

                            document.IsUploaded = Convert.ToBoolean(dr["IsUploaded"]);
                            document.Notes = Convert.ToString(dr["Notes"]);

                            document.CompleteInDays = Convert.ToInt32(dr["CompleteInDays"]);
                            document.EnableReminder = Convert.ToBoolean(dr["EnableReminder"]);
                            document.ReminderAfterDays = Convert.ToInt32(dr["ReminderAfterDays"]);

                            document.SigningTypeId = (SigningTypeId)Convert.ToInt32(dr["SigningTypeId"]);
                            document.DocumentStatus = (DocumentStatus)Convert.ToInt32(dr["DocumentStatus"]);

                            document.OwnerId = Convert.ToInt32(dr["OwnerId"]);
                            document.OwnerName = Convert.ToString(dr["OwnerName"]);
                            document.OwnerEmail = Convert.ToString(dr["OwnerEmail"]);

                            document.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            document.ExpiryDate = Convert.ToDateTime(dr["ExpiryDate"]);
                            if (!dr.IsDBNull(dr.GetOrdinal("CompletionDate")))
                            {
                                document.CompletionDate = dr.GetDateTime(dr.GetOrdinal("CompletionDate"));
                            }
                            document.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            documentSigners = new List<DocumentSigner>();
                            while (dr.Read())
                            {
                                var documentSigner = new DocumentSigner();
                                documentSigner.Id = (int)dr["Id"];
                                documentSigner.DocumentId = Convert.ToInt32(dr["DocumentId"]);
                                documentSigner.SignerId = Convert.ToInt32(dr["SignerId"]);
                                documentSigner.Name = Convert.ToString(dr["Name"]);
                                documentSigner.Email = Convert.ToString(dr["Email"]);
                                documentSigner.SignerTypeId = Convert.ToInt32(dr["SignerTypeId"]);
                                documentSigner.IsSystemUser = Convert.ToBoolean(dr["SignerTypeId"]);
                                documentSigner.SignStatus = (SignStatus)Convert.ToInt32(dr["SignStatus"]);
                                documentSigner.SignatureFileName = Convert.ToString(dr["SignatureFileName"]);
                                documentSigner.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                documentSigner.ExpiryDate = Convert.ToDateTime(dr["ExpiryDate"]);
                                if (!dr.IsDBNull(dr.GetOrdinal("CompletionDate")))
                                {
                                    documentSigner.CompletionDate = dr.GetDateTime(dr.GetOrdinal("CompletionDate"));
                                }
                                if (!dr.IsDBNull(dr.GetOrdinal("ViewDate")))
                                {
                                    documentSigner.ViewDate = dr.GetDateTime(dr.GetOrdinal("ViewDate"));
                                }
                                if (!dr.IsDBNull(dr.GetOrdinal("TermsAgreeDate")))
                                {
                                    documentSigner.TermsAgreeDate = dr.GetDateTime(dr.GetOrdinal("TermsAgreeDate"));
                                }
                                documentSigner.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                                documentSigners.Add(documentSigner);
                            }
                        }

                        document.DocumentSigners = documentSigners;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return document;
        }

        #region RevSign
        public Document SaveRevSignoffRequest(Document document, string eventId)
        {
            int documentId = -1;
            int documentSignerId = -1;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevSign.REVSIGN_SAVE_SIGNOFF_REQUEST;
                    cmd.Parameters.AddWithValue("@Title", document.Title);
                    cmd.Parameters.AddWithValue("@FileName", document.FileName);
                    cmd.Parameters.AddWithValue("@IsUploaded", document.IsUploaded);
                    cmd.Parameters.AddWithValue("@Notes", document.Notes);
                    cmd.Parameters.AddWithValue("@CompleteInDays", document.CompleteInDays);
                    cmd.Parameters.AddWithValue("@EnableReminder", document.EnableReminder);
                    cmd.Parameters.AddWithValue("@ReminderAfterDays", document.ReminderAfterDays);
                    cmd.Parameters.AddWithValue("@SigningTypeId", (int)document.SigningTypeId);
                    cmd.Parameters.AddWithValue("@DocumentStatus", (int)document.DocumentStatus);
                    cmd.Parameters.AddWithValue("@OwnerId", document.OwnerId);
                    cmd.Parameters.AddWithValue("@PdfLink", document.PdfLink);
                    cmd.Parameters.AddWithValue("@EventId", eventId);
                    cmd.Parameters.AddWithValue("@IWBDocumentId", document.IwbDocumentId); 
                    conn.Open();
                    documentId = Convert.ToInt32(cmd.ExecuteScalar());
                    document.Id = documentId;
                    conn.Close();
                }

                foreach (var signer in document.DocumentSigners)
                {
                    signer.DocumentId = documentId;
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.RevSign.REVSIGN_SAVE_SIGNOFF_REQUEST_SIGNER;
                        cmd.Parameters.AddWithValue("@DocumentId", signer.DocumentId);
                        cmd.Parameters.AddWithValue("@SignerId", signer.SignerId);
                        cmd.Parameters.AddWithValue("@Name", signer.Name);
                        cmd.Parameters.AddWithValue("@Email", signer.Email);
                        cmd.Parameters.AddWithValue("@SignerTypeId", signer.SignerTypeId);
                        cmd.Parameters.AddWithValue("@IsSystemUser", signer.IsSystemUser);
                        cmd.Parameters.AddWithValue("@SignStatus", (int)signer.SignStatus);
                        cmd.Parameters.AddWithValue("@SignatureFileName", signer.SignatureFileName);
                        conn.Open();
                        documentSignerId = Convert.ToInt32(cmd.ExecuteScalar());
                        signer.Id = documentSignerId;
                        conn.Close();
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return document;
        }

        //public bool UpdateDocumentStatus(int documentId, int documentSignerId, DocumentStatus documentStatus, SignStatus signStatus, string signedFileName, string signedFileLink, int tenantid)
        //{
        //    bool result = false;
        //    try
        //    {
        //        using (var conn = VrecSiteConfiguration.GetConnectionStringbyTenantId(tenantid))
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            conn.Open();
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = "rs_Document_StatusUpdate";
        //            cmd.CommandTimeout = 300;
        //            cmd.Parameters.AddWithValue("@DocumentId", documentId);
        //            cmd.Parameters.AddWithValue("@DocumentStatus", (int)documentStatus);
        //            cmd.Parameters.AddWithValue("@DocumentSignerId", documentSignerId);
        //            cmd.Parameters.AddWithValue("@SignStatus", (int)signStatus);
        //            cmd.Parameters.AddWithValue("@SignedFileName", signedFileName);
        //            cmd.Parameters.AddWithValue("@SignedFileLink", signedFileLink);
        //            result = (int)cmd.ExecuteScalar() > 0 ? true : false;
        //        }
        //    }
        //    catch (Exception ex) { throw ex; }
        //    return result;
        //}
        #endregion

        #region E-sign related methods
        public DocumentResult GetESignedDocuments(DocumentInfo signedData)
        {
            DocumentResult result = new DocumentResult();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevSign.REVSIGN_GET_SIGNED_DOCUMENTS;
                    cmd.Parameters.AddWithValue("@DocumentId", signedData.DocumentId);
                    cmd.Parameters.AddWithValue("@SignerEmail", signedData.SignerEmail);
                    cmd.Parameters.AddWithValue("@IsView", signedData.IsViewOnly);

                    conn.Open();
                    
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.ResultNo = Convert.ToInt32(reader["ResultNo"]);
                            result.ResultMessage = Convert.ToString(reader["ResultMessage"]);
                        }

                        if (result.ResultNo == 1)
                        {
                            reader.NextResult();
                            result.DocumentInfo = BindESignDocumentResult(reader);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return result;
        }

        public DocumentInfo BindESignDocumentResult(SqlDataReader reader)
        {
            DocumentInfo info = new DocumentInfo();
            try
            {
                while (reader.Read())
                {
                    info.DocumentId = Convert.ToInt32(reader["DocumentId"]);
                    info.Title = Convert.ToString(reader["Title"]);
                    info.FileName = Convert.ToString(reader["FileName"]);
                    info.PdfLink = Convert.ToString(reader["PdfLink"]);
                    info.Notes = Convert.ToString(reader["Notes"]);
                    info.CompleteInDays = Convert.ToInt32(reader["CompleteInDays"]);
                    info.DocumentStatus = Convert.ToString(reader["DocumentStatus"]);
                    info.OwnerId = Convert.ToInt32(reader["OwnerId"]);
                    info.OwnerName = Convert.ToString(reader["OwnerName"]);
                    info.OwnerEmail = Convert.ToString(reader["OwnerEmail"]);
                    info.DocumentCreatedDate = Convert.ToDateTime(reader["DocumentCreatedDate"]);
                    info.SignStatus = Convert.ToString(reader["SignStatus"]);
                    info.HasSigned = Convert.ToBoolean(reader["HasSigned"]);
                    info.RsDocumentSignerId = Convert.ToInt32(reader["RsDocumentSignerId"]);
                    info.SignerId = Convert.ToInt32(reader["SignerId"]);
                    info.SignerName = Convert.ToString(reader["SignerName"]);
                    info.SignerEmail = Convert.ToString(reader["SignerEmail"]);
                    info.SignedFileName = Convert.ToString(reader["SignedFileName"]);
                    info.SignedFileLink = Convert.ToString(reader["SignedFileLink"]);
                    info.Reason = Convert.ToString(reader["ReasonForDecline"]);
                    //info.MetaData = Convert.ToString(reader["MetaData"]);
                    info.MetaData = reader["MetaData"] != DBNull.Value ? Convert.ToString(reader["MetaData"]) : "[1]";
                    info.SignedOn = Convert.ToString(reader["SignedOn"]);
                }
            }
            catch (Exception ex) {  }
            return info;
        }

        public DocumentResult SaveESignDocumentAgreedAsync(DocumentInfo signedData)
        {
            DocumentResult result = new DocumentResult();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevSign.REVSIGN_SAVE_DOCUMENTS_AGREED;
                    cmd.Parameters.AddWithValue("@DocumentId", signedData.DocumentId);
                    cmd.Parameters.AddWithValue("@SignerEmail", signedData.SignerEmail);

                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.ResultNo = Convert.ToInt32(reader["ResultNo"]);
                            result.ResultMessage = Convert.ToString(reader["ResultMessage"]);
                        }

                        if (result.ResultNo == 1)
                        {
                            reader.NextResult();
                            result.DocumentInfo = BindESignDocumentResult(reader);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return result;
        }

        public DocumentResult SaveESignedDocumentAsync(DocumentInfo signedData)
        {
            DocumentResult result = new DocumentResult();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevSign.REVSIGN_SAVE_DOCUMENTS_SIGNED;
                    cmd.Parameters.AddWithValue("@DocumentId", signedData.DocumentId);
                    cmd.Parameters.AddWithValue("@SignerEmail", signedData.SignerEmail);
                    cmd.Parameters.AddWithValue("@SignedFileName", signedData.SignedFileName);
                    cmd.Parameters.AddWithValue("@SignedFileLink", signedData.SignedFileLink);
                    cmd.Parameters.AddWithValue("@Notes", signedData.Notes);
                    cmd.Parameters.AddWithValue("@SignedOn", signedData.SignedOn);

                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.ResultNo = Convert.ToInt32(reader["ResultNo"]);
                            result.ResultMessage = Convert.ToString(reader["ResultMessage"]);
                        }

                        if (result.ResultNo == 1)
                        {
                            reader.NextResult();
                            result.DocumentInfo = BindESignDocumentResult(reader);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return result;
        }

        public DocumentResult GetSignedDocumentInfoAsync(DocumentInfo signedData)
        {
            DocumentResult result = new DocumentResult();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevSign.REVSIGN_GET_DOCUMENTS_SIGNED;
                    cmd.Parameters.AddWithValue("@DocumentId", signedData.DocumentId);
                    cmd.Parameters.AddWithValue("@SignerEmail", signedData.SignerEmail);

                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.ResultNo = Convert.ToInt32(reader["ResultNo"]);
                            result.ResultMessage = Convert.ToString(reader["ResultMessage"]);
                        }

                        if (result.ResultNo == 1)
                        {
                            reader.NextResult();
                            result.DocumentInfo = BindESignDocumentResult(reader);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return result;
        }
        #endregion

        public bool AssociateIwbAndRevSignDocument(int iwbDocumentId, int revSignDocumentId)
        {
            try
            {
                int id = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.RevSign.ASSOCIATE_REVSIGN_AND_IWB_DOCUMENT;
                    cmd.Parameters.AddWithValue("@IwbDocumentId", iwbDocumentId);
                    cmd.Parameters.AddWithValue("@RevSignDocumentId", revSignDocumentId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "DeleteScheduledEvent", _tenantId));
                    id = Convert.ToInt32(cmd.ExecuteScalar());
                    return id > 0;
                }
            }
            catch (Exception ex) { throw ex; }
        }
    }
}