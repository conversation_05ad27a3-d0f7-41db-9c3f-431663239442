﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.DashboardEntities;
using RevCord.DataContracts.Messages;

namespace RevCord.ServiceContracts
{
    public interface IDashboardService
    {
        DashboardResponse GetDashboardStatistics(DashboardRequest request); //DashboardOverallStats GetDashboardStatistics();
        DashboardResponse GetDashboardStatisticsLite(DashboardRequest request);
        DashboardResponse ReloadDashboardStatistics(DashboardRequest request); //DashboardOverallStats GetDashboardStatistics();
        DashboardResponse ReloadDashboardStatisticsLite(DashboardRequest request);
        DashboardResponse GetIRData(DashboardRequest request);
        DashboardResponse GetRecentRecords(DashboardRequest request);
        DashboardResponse GetCallCountData(DashboardRequest request);
        DashboardResponse LoadDashboardDataLite(DashboardRequest request);
        DashboardResponse LoadSevenDayCallData(DashboardRequest request);
    }
}
