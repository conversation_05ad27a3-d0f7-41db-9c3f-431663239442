﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.SqlClient;
using RevCord.DataContracts;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.SurveyEntities;
using System.Xml.Linq;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.Request;
using System.IO;
using RevCord.Util;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using Newtonsoft.Json;

namespace RevCord.DataAccess
{
    public class EvaluationDAL
    {
        private int _tenantId;
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 300);

        public EvaluationDAL(int tenantId)
        {
            _tenantId = tenantId;
        }

        #region CallEvaluationDTO

        public List<CallEvaluationDTO> GetCallEvaluationDTOPaged(string whereClause, int userId, int pageIndex, int pageSize, out int totalPages, out int totalRecords, bool SharedRequired = false, bool IsEvaluatorSearch = false)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_GETALL_BY_WHERE_CLAUSE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SharedRequired", SharedRequired);
                    //cmd.Parameters.AddWithValue("@IsEvaluatorSearch", IsEvaluatorSearch);
                    cmd.Parameters.AddWithValue("@IsEvaluatorSearch", SharedRequired == true ? true : IsEvaluatorSearch);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetCallEvaluationDTOPaged", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }


        public List<CallEvaluationDTO> GetCallEvaluationDTOPaged(string whereClause, int userId, bool SharedRequired = false, bool IsEvaluatorSearch = false)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_GETALL_BY_WHERE_CLAUSE;

                    cmd.Parameters.AddWithValue("@WhereClause", whereClause.Replace("OR (  )  OR", " "));
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SharedRequired", SharedRequired);
                    cmd.Parameters.AddWithValue("@IsEvaluatorSearch", IsEvaluatorSearch);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetCallEvaluationDTOPaged", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }

        public List<CallEvaluationDTO> PerformActionAndGetCallEvaluationDTOPaged(string callSurveyIds, string revsyncCallSurveyIds, string whereClause, int action, int actionValue, int userId, DateTime? ActionDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_DO_ACTION_N_GETALL_BY_WHERE_CLAUSE;
                    conn.Open();
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@CallEvaluationIds", callSurveyIds);
                    cmd.Parameters.AddWithValue("@Action", action);
                    cmd.Parameters.AddWithValue("@Value", actionValue);
                    cmd.Parameters.AddWithValue("@ActionDate", ActionDate);

                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "PerformActionAndGetCallEvaluationDTOPaged", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                    if (SiteConfig.RevSyncEnabled && Convert.ToInt32(revsyncCallSurveyIds) > 0)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("CallSurveyIds", Convert.ToString(callSurveyIds));
                        _dic.Add("RevsyncCallSurveyIds", Convert.ToString(revsyncCallSurveyIds));
                        _dic.Add("WhereClause", Convert.ToString(whereClause));
                        _dic.Add("Action", Convert.ToString(action));
                        _dic.Add("ActionValue", Convert.ToString(actionValue));
                        _dic.Add("UserId", Convert.ToString(userId));
                        _dic.Add("ActionDate", Convert.ToString(ActionDate));
                        _dic.Add("PageIndex", Convert.ToString(pageIndex));
                        _dic.Add("PageSize", Convert.ToString(pageSize));
                        _dic.Add("TotalPages", Convert.ToString(totalPages));
                        _dic.Add("TotalRecords", Convert.ToString(totalRecords));


                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "EvaluationDAL", "PerformActionAndGetCallEvaluationDTOPaged", JsonConvert.SerializeObject(_dic)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        //(string callSurveyIds, string revsyncCallSurveyIds, string whereClause, int action, int actionValue, int userId, DateTime? ActionDate, int pageIndex, int pageSize, 
                        //bool revSyncEnabled , out int totalPages, out int totalRecords)
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("CallSurveyIds", Convert.ToString(callSurveyIds));
                        _dic.Add("RevsyncCallSurveyIds", Convert.ToString(revsyncCallSurveyIds));
                        _dic.Add("WhereClause", Convert.ToString(whereClause));
                        _dic.Add("Action", Convert.ToString(action));
                        _dic.Add("ActionValue", Convert.ToString(actionValue));
                        _dic.Add("UserId", Convert.ToString(userId));
                        _dic.Add("ActionDate", Convert.ToString(ActionDate));
                        _dic.Add("PageIndex", Convert.ToString(pageIndex));
                        _dic.Add("PageSize", Convert.ToString(pageSize));
                        _dic.Add("TotalPages", Convert.ToString(totalPages));
                        _dic.Add("TotalRecords", Convert.ToString(totalRecords));


                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "EvaluationDAL", "PerformActionAndGetCallEvaluationDTOPaged", JsonConvert.SerializeObject(_dic));

                        if (isSent)
                            tran.Commit();
                        else
                            tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }


        //public List<CallEvaluationDTO> PerformActionAndGetCallEvaluationDTOPaged(string callSurveyIds, string whereClause, int action, int actionValue, int userId,DateTime? ActionDate)
        //{
        //    List<CallEvaluationDTO> callEvaluationDTOs = null;
        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection())
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_DO_ACTION_N_GETALL_BY_WHERE_CLAUSE;
        //            cmd.Parameters.AddWithValue("@CallEvaluationIds", callSurveyIds);
        //            cmd.Parameters.AddWithValue("@Action", action);
        //            cmd.Parameters.AddWithValue("@Value", actionValue);
        //            cmd.Parameters.AddWithValue("@ActionDate", ActionDate);


        //            cmd.Parameters.AddWithValue("@WhereClause", whereClause.Replace("OR (  )  OR", " "));

        //            cmd.Parameters.AddWithValue("@UserId", userId);

        //            conn.Open();
        //            using (SqlDataReader dr = cmd.ExecuteReader())
        //            {
        //                callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr);
        //            }
        //        }
        //    }
        //    catch (Exception ex) { throw ex; }
        //    return callEvaluationDTOs;
        //}

        public List<CallEvaluationDTO> PerformActionAndGetCallEvaluationDTOPaged(string callSurveyIds, string revsyncCallSurveyIds, string whereClause, int action, int actionValue, int userId, string shareWith, bool isSharedEvaluatorRetains, DateTime? ActionDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_DO_ACTION_N_GETALL_BY_WHERE_CLAUSE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@CallEvaluationIds", callSurveyIds);
                    cmd.Parameters.AddWithValue("@Action", action);
                    cmd.Parameters.AddWithValue("@Value", actionValue);
                    cmd.Parameters.AddWithValue("@ActionDate", ActionDate);

                    cmd.Parameters.AddWithValue("@WhereClause", whereClause.Replace("OR (  )  OR", " "));

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@ShareWith", shareWith);
                    cmd.Parameters.AddWithValue("@IsSharedEvaluatorRetains", isSharedEvaluatorRetains);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "PerformActionAndGetCallEvaluationDTOPaged", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                    if (SiteConfig.RevSyncEnabled)
                    {
                        try
                        {
                            using (var RevSyncConn = DALHelper.GetRevSyncConnection())
                            using (var RevSyncCmd = RevSyncConn.CreateCommand())
                            {
                                RevSyncConn.Open();
                                RevSyncCmd.CommandType = CommandType.StoredProcedure;
                                RevSyncCmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_DO_ACTION_N_GETALL_BY_WHERE_CLAUSE;
                                RevSyncCmd.Parameters.AddWithValue("@CallEvaluationIds", revsyncCallSurveyIds);
                                RevSyncCmd.Parameters.AddWithValue("@Action", action);
                                RevSyncCmd.Parameters.AddWithValue("@Value", actionValue);
                                RevSyncCmd.Parameters.AddWithValue("@ActionDate", ActionDate);
                                RevSyncCmd.Parameters.AddWithValue("@WhereClause", whereClause.Replace("OR (  )  OR", " "));
                                RevSyncCmd.Parameters.AddWithValue("@UserId", userId);
                                RevSyncCmd.Parameters.AddWithValue("@ShareWith", shareWith);
                                RevSyncCmd.Parameters.AddWithValue("@IsSharedEvaluatorRetains", isSharedEvaluatorRetains);
                                RevSyncCmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                                RevSyncCmd.Parameters.AddWithValue("@PageSize", pageSize);
                                RevSyncCmd.Parameters.AddWithValue("@TotalPages", 1);
                                RevSyncCmd.Parameters.AddWithValue("@TotalRecords", 0);
                                RevSyncCmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                                RevSyncCmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;
                                RevSyncCmd.ExecuteNonQuery();
                                tran.Commit();
                            }
                        }
                        catch (Exception e)
                        {
                            tran.Rollback();
                        }
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("CallSurveyIds", Convert.ToString(callSurveyIds));
                        _dic.Add("RevsyncCallSurveyIds", Convert.ToString(revsyncCallSurveyIds));
                        _dic.Add("WhereClause", Convert.ToString(whereClause));
                        _dic.Add("Action", Convert.ToString(action));
                        _dic.Add("ActionValue", Convert.ToString(actionValue));
                        _dic.Add("UserId", Convert.ToString(userId));
                        _dic.Add("ShareWith", Convert.ToString(shareWith));
                        _dic.Add("IsSharedEvaluatorRetains", Convert.ToString(isSharedEvaluatorRetains));
                        _dic.Add("ActionDate", Convert.ToString(ActionDate));
                        _dic.Add("PageIndex", Convert.ToString(pageIndex));
                        _dic.Add("PageSize", Convert.ToString(pageSize));
                        _dic.Add("TotalPages", Convert.ToString(totalPages));
                        _dic.Add("TotalRecords", Convert.ToString(totalRecords));
                        _dic.Add("SharedRequired", Convert.ToString(0));
                        _dic.Add("IsEvaluatorSearch", Convert.ToString(0));

                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "EvaluationDAL", "PerformActionAndGetCallEvaluationDTOPaged", JsonConvert.SerializeObject(_dic));

                        if (isSent)
                            tran.Commit();
                        else
                            tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }



        public List<CallEvaluationDTO> UpdateCallEvaluationAndGetPaged(string whereClause,
                                                                        string callEvaluationIds, int surveyId, int statusId, DateTime? modifiedDate, bool isDeleted, int userId)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_UPDATE_N_GETALL_BY_WHERE_CLAUSE;
                    cmd.Parameters.AddWithValue("@CallEvaluationIds", callEvaluationIds);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("@StatusId", statusId);
                    cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", isDeleted);
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "UpdateCallEvaluationAndGetPaged", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }
        public List<CallEvaluationDTO> GetClosedEvaluationsByAgentId(int agentId)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_GETCLOSED_BY_AGENT_ID;
                    cmd.Parameters.AddWithValue("@AgentId", agentId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetClosedEvaluationsByAgentId", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationLiteDTOs(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }
        #endregion


        #region Call Evaluation

        public CallEvaluation CallCallEvaluationDetailsById(long callEvaluationId, int userId)
        {
            CallEvaluation callEvaluation = new CallEvaluation();
            Survey survey = null;
            List<Answer> answers = null;
            List<Option> options = null;
            //CallInfo callinfo = null;
            //Answer answer = null; 
            //List<SurveyGroup> groups = new List<EvalQuestionGroup>();
            //List<Question> questions = new List<EvalQuestion>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_GET_DETAILS_BY_ID;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", callEvaluationId);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "CallCallEvaluationDetailsById", _tenantId));
                    //Total 8 Tables return.
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Survey ResultSet
                        survey = ORMapper.MapSurvey(dr);//SurveyTable
                        if (survey == null) return callEvaluation;
                        dr.NextResult();
                        //2. Sections
                        survey.Sections = ORMapper.MapSections(dr);//Section Table
                        dr.NextResult();
                        //3. Questions
                        survey.Questions = ORMapper.MapQuestions(dr); //Question Table
                        dr.NextResult();
                        //4. Question Options
                        options = ORMapper.MapOptions(dr); //Option Table
                        //survey.Sections = sections;
                        //survey.Questions = questions;
                        if (survey.Questions != null)
                        {
                            survey.Questions.ForEach(q => q.Options = options.FindAll(o => o.QuestionId == q.Id));
                        }


                        dr.NextResult();
                        //5. CallEvaluation Resultset
                        callEvaluation = ORMapper.MapCallEvaluation(dr);
                        dr.NextResult();
                        //6. Answer ResultSet
                        answers = ORMapper.MapCallEvauationAnswerMaster(dr);
                        callEvaluation.Answers = answers;
                        dr.NextResult();
                        //7. Answer Options ResultSet
                        options = ORMapper.MapCallEvauationAnswerChild(dr);
                        callEvaluation.Answers.ForEach(a => a.Options = options.FindAll(o => o.QuestionId == a.QuestionId));

                        dr.NextResult();
                        //8. CallInfo ResultSet
                        //callEvaluation.CallInfo = ORMapper.MapCallInfo(dr);
                        callEvaluation.CallInfo = ORMapper.MapCallInfoForEvaluation(dr);
                        callEvaluation.Survey = survey;

                        dr.NextResult();
                        //9. Agent ResultSet
                        callEvaluation.Agent = ORMapper.MapUserWithoutChannel(dr);

                        //10.IsSegmented
                        dr.NextResult();
                        if (dr.Read())
                        {
                            callEvaluation.IsSegmented = Convert.ToBoolean(dr["IsSegmented"]);
                        }

                        return callEvaluation;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public CallEvaluation UpdateCallEvaluation(CallEvaluation callEvaluation)
        {
            try
            {
                XElement xEvalCallSurveyDetails = this.CreateCallSurveyDetailsXML(callEvaluation);

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_UPDATE;
                    cmd.CommandTimeout = CMD_TIMEOUT;

                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", callEvaluation.Id);
                    cmd.Parameters.AddWithValue("@StatusId", callEvaluation.StatusId);
                    //cmd.Parameters.AddWithValue("@UserId", evalCallSurvey.UserId);
                    //cmd.Parameters.AddWithValue("@IsShared", callEvaluation.IsShared);
                    cmd.Parameters.AddWithValue("@ModifiedDate", callEvaluation.ModifiedDate);
                    //cmd.Parameters.AddWithValue("@IsDeleted", callEvaluation.IsDeleted);
                    cmd.Parameters.AddWithValue("@CallEvaluationAnswers", xEvalCallSurveyDetails.ToString());

                    cmd.Parameters.AddWithValue("@ScorerComments", callEvaluation.SupervisorComments);
                    cmd.Parameters.AddWithValue("@EvaluatedScore", callEvaluation.EvaluatedScore);
                    cmd.Parameters.AddWithValue("@EvaluationType", callEvaluation.EvaluationType);
                    cmd.Parameters.AddWithValue("@MultiCallEvaluationId", callEvaluation.MultiCallEvaluationId);

                    //cmd.Parameters["@ScorerComments"].Direction = ParameterDirection.InputOutput;
                    cmd.Parameters["@EvaluatedScore"].Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "UpdateCallEvaluation", _tenantId));
                    cmd.ExecuteNonQuery();

                    //callEvaluation.SupervisorComments = Convert.ToString(cmd.Parameters["@ScorerComments"].Value.ToString());
                    if(SiteConfig.RevSyncEnabled && callEvaluation.RevSyncId > 0) {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "EvaluationDAL", "UpdateCallEvaluation", JsonConvert.SerializeObject(callEvaluation));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    } else if (SiteConfig.IsMTEnable) {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "EvaluationDAL", "UpdateCallEvaluation", JsonConvert.SerializeObject(callEvaluation));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    } else tran.Commit();

                    return callEvaluation;
                }
            }
            catch (Exception ex) { throw ex; }
            //return false;
        }

        public User UpdateAndGetAssociatedUser(int evalId, int userNum)
        {
            try
            {
                User user = null;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_UPDATE_ASSOCIATED_AGENT;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", evalId);
                    cmd.Parameters.AddWithValue("@AgentId", userNum);
                    int count = (int)cmd.ExecuteNonQuery();

                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_Account WHERE UserNum = @userNum";
                    cmd.Parameters.AddWithValue("@userNum", userNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "UpdateAndGetAssociatedUser", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        user = ORMapper.MapUserAsAgent(dr);
                    }
                    return user;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool DeleteEvaluation(long evaluationId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_DELETE;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", evaluationId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "DeleteEvaluation", _tenantId));
                    int count = (int)cmd.ExecuteScalar(); //statement:- 1 for Success, -1 for Failure

                    return count == 1 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        /// <summary>
        /// This method will add Call in em_CallEvaluation
        /// </summary>
        /// <param name="calls"></param>
        /// <returns></returns>
        //public CallEvaluation InsertCallEvaluation(CallEvaluation callEval)
        public short InsertCallEvaluation(int surveyId, string callId, int agentId, EvaluationStatus status, int userId, int evaluationType)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_INSERT_SINGLE;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId); //DBNULL
                    cmd.Parameters.AddWithValue("@CallId", callId);
                    cmd.Parameters.AddWithValue("@AppUserId", agentId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)status);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@EvaluationType", evaluationType);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "InsertCallEvaluation", _tenantId));
                    //cmd.ExecuteNonQuery();
                    //return true;
                    return Convert.ToInt16(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
            //return false;
        }

        public short InsertSegmentedCallEvaluation(string callId, int userId, int surveyId, string segmentedCallIds, int evaluationType)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_INSERT_SEGMENTED;
                    cmd.Parameters.AddWithValue("@CallInfos", callId);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("@SegmentedCallIds", segmentedCallIds);
                    cmd.Parameters.AddWithValue("@EvaluationType", evaluationType);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "InsertSegmentedCallEvaluation", _tenantId));
                    //cmd.ExecuteNonQuery();
                    //return true;
                    return Convert.ToInt16(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
            //return false;
        }

        public bool InsertEvaluations(string callIds, int userId, int surveyId, int evaluationType, int groupId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "em_CallEvaluation_Insert_MultiCalls";
                    cmd.Parameters.AddWithValue("@CallInfos", callIds.ToString());
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("@EvaluationType", evaluationType);
                    cmd.Parameters.AddWithValue("@GroupId", groupId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "InsertEvaluations", _tenantId));
                    cmd.ExecuteNonQuery();

                    return true;
                }
            }
            catch (Exception ex)
            {
                return false; //throw ex; }
            }
        }

        public bool InsertEvaluationFeedback(EvaluationRequest evaluationRequest)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONFEEDBACK_INSERT;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", evaluationRequest.CallEvaluationId);
                    cmd.Parameters.AddWithValue("@RespondantId", evaluationRequest.AgentId);
                    cmd.Parameters.AddWithValue("@Comments", evaluationRequest.Comments);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "InsertEvaluationFeedback", _tenantId));
                    cmd.ExecuteNonQuery();

                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
            return false;
        }

        public bool DeleteEvaluationFeedback(EvaluationRequest evaluationRequest)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONFEEDBACK_DELETE;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", evaluationRequest.CallEvaluationId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "DeleteEvaluationFeedback", _tenantId));
                    int count = (int)cmd.ExecuteScalar();

                    return count == 1 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<CallEvaluationFeedback> GetCallEvaluationFeedback(EvaluationRequest evaluationRequest)
        {
            List<CallEvaluationFeedback> callEvaluationFeedbacks = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONFEEDBACK_GET_BY_AGENT_ID;
                    cmd.Parameters.AddWithValue("@AgentId", evaluationRequest.AgentId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetCallEvaluationFeedback", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationFeedbacks = ORMapper.MapCallEvaluationFeedbacks(dr);
                    }
                }
                return callEvaluationFeedbacks;
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        public IList<CallInfo> GetSegmentedCalls(long evaluationId)
        {
            List<CallInfo> segmentedCalls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_GET_SEGMENTED_CALLS;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", evaluationId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetSegmentedCalls", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        segmentedCalls = ORMapper.MapCallInfosForEvaluation(dr);
                    }
                }
                return segmentedCalls;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #region Utility

        private XElement CreateCallSurveyDetailsXML(CallEvaluation callEvaluation)
        {
            XElement xcallSurveyDetails = new XElement("CallEvaluationAnswers");

            if (callEvaluation.Answers.Count == 0) return xcallSurveyDetails; // in-case of NO Answer

            foreach (var answer in callEvaluation.Answers)
            {
                if (answer.Options != null)
                {
                    foreach (var option in answer.Options)
                    {
                        xcallSurveyDetails.Add(new XElement("CallEvaluationAnswer",
                                    new XAttribute("CallEvaluationId", callEvaluation.Id),
                                    new XAttribute("ModifiedDate", callEvaluation.ModifiedDate),
                                    new XAttribute("IsDeleted", false),
                                    new XAttribute("QuestionId", answer.QuestionId),
                                    new XAttribute("QuestionOptionId", option.Id),
                                    new XAttribute("AnswerText", answer.AnswerValue)
                                ));
                    }
                }
                else
                {
                    xcallSurveyDetails.Add(new XElement("CallEvaluationAnswer",
                                    new XAttribute("CallEvaluationId", callEvaluation.Id),
                                    new XAttribute("ModifiedDate", callEvaluation.ModifiedDate),
                                    new XAttribute("IsDeleted", false),
                                    new XAttribute("QuestionId", answer.QuestionId),
                                    new XAttribute("QuestionOptionId", 0),
                                    new XAttribute("AnswerText", answer.AnswerValue)
                                ));
                }
            }
            return xcallSurveyDetails;
        }

        private XElement CreateUserSurveyDetailsXML(UserEvaluation userEval)
        {
            XElement xcallSurveyDetails = new XElement("UserEvaluationAnswers");

            if (userEval.Answers.Count == 0) return xcallSurveyDetails; // in-case of NO Answer

            foreach (var answer in userEval.Answers)
            {
                if (answer.Options != null)
                {
                    foreach (var option in answer.Options)
                    {
                        xcallSurveyDetails.Add(new XElement("UserEvaluationAnswer",
                                    new XAttribute("UserEvaluationId", userEval.Id),
                                    new XAttribute("ModifiedDate", userEval.ModifiedDate),
                                    new XAttribute("IsDeleted", false),
                                    new XAttribute("QuestionId", answer.QuestionId),
                                    new XAttribute("QuestionOptionId", option.Id),
                                    new XAttribute("AnswerText", answer.AnswerValue)
                                ));
                    }
                }
                else
                {
                    xcallSurveyDetails.Add(new XElement("CallEvaluationAnswer",
                                    new XAttribute("CallEvaluationId", userEval.Id),
                                    new XAttribute("ModifiedDate", userEval.ModifiedDate),
                                    new XAttribute("IsDeleted", false),
                                    new XAttribute("QuestionId", answer.QuestionId),
                                    new XAttribute("QuestionOptionId", 0),
                                    new XAttribute("AnswerText", answer.AnswerValue)
                                ));
                }
            }
            return xcallSurveyDetails;
        }

        #endregion

        #region Charts


        public List<Tuple<int, string, int>> ChartGetEvaluationsByStatus(int uID)
        {
            List<Tuple<int, string, int>> evals = null;
            Tuple<int, string, int> eval = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.CALLEVALUATION_BY_STATUS;
                    cmd.Parameters.AddWithValue("@UserId", uID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "ChartGetEvaluationsByStatus", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (evals == null) evals = new List<Tuple<int, string, int>>();
                        while (dr.Read())
                        {
                            eval = new Tuple<int, string, int>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Description"]), Convert.ToInt32(dr["EvalCount"]));

                            evals.Add(eval);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return evals;
        }

        public List<Tuple<int, string, int>> ChartGetEvaluationsByCampaign(int uID)
        {
            List<Tuple<int, string, int>> evals = null;
            Tuple<int, string, int> eval = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.CallEVALUATION_BY_CAMPAIGN;
                    cmd.Parameters.AddWithValue("@UserId", uID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "ChartGetEvaluationsByCampaign", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (evals == null) evals = new List<Tuple<int, string, int>>();
                        while (dr.Read())
                        {
                            eval = new Tuple<int, string, int>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Title"]), Convert.ToInt32(dr["EvalCount"]));

                            evals.Add(eval);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return evals;
        }

        public List<Tuple<int, string, int>> ChartGetEvaluationsByEvaluator(int uID)
        {
            List<Tuple<int, string, int>> evals = null;
            Tuple<int, string, int> eval = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.CallEVALUATION_BY_EVALUATOR;
                    cmd.Parameters.AddWithValue("@UserId", uID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "ChartGetEvaluationsByEvaluator", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        int num = 0;
                        if (evals == null) evals = new List<Tuple<int, string, int>>();
                        while (dr.Read())
                        {
                            eval = new Tuple<int, string, int>(++num, Convert.ToString(dr["Evaluator"]), Convert.ToInt32(dr["EvalCount"]));

                            evals.Add(eval);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return evals;
        }

        public List<Tuple<int, string, int>> ChartGetEvaluationsByTopScorers(int uID)
        {
            List<Tuple<int, string, int>> evals = null;
            Tuple<int, string, int> eval = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.CALLEVALUATION_BY_TOPSCORERS;
                    cmd.Parameters.AddWithValue("@UserId", uID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "ChartGetEvaluationsByTopScorers", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        int num = 0;
                        if (evals == null) evals = new List<Tuple<int, string, int>>();
                        while (dr.Read())
                        {
                            eval = new Tuple<int, string, int>(++num, Convert.ToString(dr["TopScorer"]), Convert.ToInt32(dr["TotalScore"]));

                            evals.Add(eval);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return evals;
        }

        public List<Tuple<int, string, int, string, string>> ChartGetDrilldownEvaluationsByStatus(int uID)
        {
            List<Tuple<int, string, int, string, string>> evals = null;
            Tuple<int, string, int, string, string> eval = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.CALLEVALUATION_BY_STATUS;
                    cmd.Parameters.AddWithValue("@UserId", uID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "ChartGetDrilldownEvaluationsByStatus", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (evals == null) evals = new List<Tuple<int, string, int, string, string>>();
                        while (dr.Read())
                        {
                            if (dr["Id"] != null)
                            {
                                eval = new Tuple<int, string, int, string, string>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Description"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));
                            }
                            evals.Add(eval);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return evals;
        }

        public List<Tuple<int, string, int, string, string>> ChartGetDrilldownEvaluationsByCampaign(int uID)
        {
            List<Tuple<int, string, int, string, string>> evals = null;
            Tuple<int, string, int, string, string> eval = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.CallEVALUATION_BY_CAMPAIGN;
                    cmd.Parameters.AddWithValue("@UserId", uID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "ChartGetDrilldownEvaluationsByCampaign", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (evals == null) evals = new List<Tuple<int, string, int, string, string>>();
                        while (dr.Read())
                        {
                            eval = new Tuple<int, string, int, string, string>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Title"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));

                            evals.Add(eval);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return evals;
        }

        public List<Tuple<int, string, int, string, string>> ChartGetDrilldownEvaluationsByEvaluator(int uID)
        {
            List<Tuple<int, string, int, string, string>> evals = null;
            Tuple<int, string, int, string, string> eval = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.CallEVALUATION_BY_EVALUATOR;
                    cmd.Parameters.AddWithValue("@UserId", uID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "ChartGetDrilldownEvaluationsByEvaluator", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        int num = 0;
                        if (evals == null) evals = new List<Tuple<int, string, int, string, string>>();
                        while (dr.Read())
                        {
                            eval = new Tuple<int, string, int, string, string>(++num, Convert.ToString(dr["Evaluator"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));

                            evals.Add(eval);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return evals;
        }

        public List<Tuple<int, string, int, string, string>> ChartGetDrilldownEvaluationsByTopScorers(int uID)
        {
            List<Tuple<int, string, int, string, string>> evals = null;
            Tuple<int, string, int, string, string> eval = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.CALLEVALUATION_BY_TOPSCORERS;
                    cmd.Parameters.AddWithValue("@UserId", uID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "ChartGetDrilldownEvaluationsByTopScorers", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        int num = 0;
                        if (evals == null) evals = new List<Tuple<int, string, int, string, string>>();
                        while (dr.Read())
                        {
                            eval = new Tuple<int, string, int, string, string>(++num, Convert.ToString(dr["TopScorer"]), Convert.ToInt32(dr["TotalScore"]), dr["EvalIds"] == DBNull.Value ? string.Empty : Convert.ToString(dr["EvalIds"]), dr["EvalScores"] == DBNull.Value ? string.Empty : Convert.ToString(dr["EvalScores"]));

                            evals.Add(eval);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return evals;
        }

        public List<Tuple<int, string, int>> GetEvaluationsByStatus(int userId)
        {
            List<Tuple<int, string, int>> evals = null;
            Tuple<int, string, int> eval = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.EVALUATION_BY_STATUS;
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetEvaluationsByStatus", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (evals == null) evals = new List<Tuple<int, string, int>>();
                        while (dr.Read())
                        {
                            eval = new Tuple<int, string, int>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Description"]), Convert.ToInt32(dr["EvalCount"]));
                            evals.Add(eval);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return evals;
        }

        public List<Tuple<int, string, int, string, string>> GetMultiCallEvaluationByStatus(int uID)
        {
            List<Tuple<int, string, int, string, string>> evals = null;
            Tuple<int, string, int, string, string> eval = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.MULTICALL_EVALUATION_BY_STATUS;
                    cmd.Parameters.AddWithValue("@UserId", uID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetMultiCallEvaluationByStatus", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (evals == null) evals = new List<Tuple<int, string, int, string, string>>();
                        while (dr.Read())
                        {
                            if (dr["Id"] != null)
                            {
                                eval = new Tuple<int, string, int, string, string>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Description"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));
                            }
                            evals.Add(eval);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return evals;
        }
        #endregion

        #region Report Methods

        public List<CallEvaluation> AgentEvaluationReport(string criteria, int userId)
        {
            List<CallEvaluation> callEvaluations = new List<CallEvaluation>();
            Survey survey = null;
            List<Answer> answers = null;
            List<Option> options = null;
            List<User> users = null;
            List<GlobalGroup> groups = new List<GlobalGroup>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.RPT_CALLEVALUATION_GET_DETAILS_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@Criteria", criteria);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "AgentEvaluationReport", _tenantId));

                    //Total 8 Tables return.
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Survey ResultSet
                        survey = ORMapper.MapSurvey(dr);//SurveyTable
                        dr.NextResult();
                        //2. Sections
                        survey.Sections = ORMapper.MapSections(dr);//Section Table
                        dr.NextResult();
                        //3. Questions
                        survey.Questions = ORMapper.MapQuestions(dr); //Question Table
                        dr.NextResult();
                        //4. Question Options
                        options = ORMapper.MapOptions(dr); //Option Table
                        dr.NextResult();
                        if (survey.Questions != null) survey.Questions.ForEach(q => q.Options = options.FindAll(o => o.QuestionId == q.Id));

                        //5. CallEvaluation Resultset
                        callEvaluations = ORMapper.MapCallEvaluations(dr);
                        dr.NextResult();
                        //6. Answer ResultSet
                        answers = ORMapper.MapCallEvauationAnswerMaster(dr);
                        dr.NextResult();
                        //7. Answer Options ResultSet
                        options = ORMapper.MapCallEvauationAnswerChild(dr);
                        dr.NextResult();

                        //8. Account ResultSet
                        users = ORMapper.MapUsers(dr);
                        dr.NextResult();
                        //9. Global Groups ResultSet
                        groups = ORMapper.MapGlobalGroups(dr);


                        answers.ForEach(a => a.Options = options.FindAll(o => o.QuestionId == a.QuestionId && o.CallEvaluationId == a.CallEvaluationId));
                        callEvaluations.ForEach(ce => ce.Answers = answers.FindAll(a => a.CallEvaluationId == ce.Id));

                        users.ForEach(u =>
                        {
                            GlobalGroup gGroup = groups.FirstOrDefault(g => g.Id == u.GroupNum);
                            if (gGroup != null) u.GlobalGroup = (GlobalGroup)gGroup.Clone();
                        });

                        callEvaluations.ForEach(ce =>
                        {
                            User usr = users.FirstOrDefault(u => u.UserNum == ce.AppUserId);
                            if (usr != null) ce.Agent = (User)usr.Clone();
                        });

                        callEvaluations.ForEach(u => { u.Survey = (Survey)survey.Clone(); });

                        return callEvaluations;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion


        #region User Evaluation

        public int SaveUserEvaluation(int userId, int surveyId, int statusId, int evaluatorId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.USER_EVALUATION_INSERT_SINGLE;
                    //cmd.Parameters.AddWithValue("@UserIds", userIds);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("@StatusId", statusId);
                    cmd.Parameters.AddWithValue("@EvaluatorId", evaluatorId);
                    //cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                    //cmd.Parameters.AddWithValue("@IsDeleted", isDeleted);
                    //cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "SaveUserEvaluation", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public string SaveUserEvaluations(string userIds, int surveyId, int statusId, int evaluatorId, char controlState)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.USER_EVALUATION_INSERT;
                    cmd.Parameters.AddWithValue("@UserIds", userIds);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("@StatusId", statusId);
                    cmd.Parameters.AddWithValue("@EvaluatorId", evaluatorId);
                    cmd.Parameters.AddWithValue("@ControlState", controlState);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "SaveUserEvaluations", _tenantId));

                    return Convert.ToString(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public UserEvaluation UserEvaluationDetailsById(long userEvaluationId, int userId)
        {
            UserEvaluation userEvaluation = new UserEvaluation();
            Survey survey = null;
            List<Answer> answers = null;
            List<Option> options = null;
            //CallInfo callinfo = null;
            //Answer answer = null; 
            //List<SurveyGroup> groups = new List<EvalQuestionGroup>();
            //List<Question> questions = new List<EvalQuestion>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.USER_EVALUATION_GET_DETAILS_BY_ID;
                    cmd.Parameters.AddWithValue("@EvaluationId", userEvaluationId);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "UserEvaluationDetailsById", _tenantId));

                    //Total 8 Tables return.
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Survey ResultSet
                        survey = ORMapper.MapSurvey(dr);//SurveyTable
                        if (survey == null) return userEvaluation;
                        dr.NextResult();
                        //2. Sections
                        survey.Sections = ORMapper.MapSections(dr);//Section Table
                        dr.NextResult();
                        //3. Questions
                        survey.Questions = ORMapper.MapQuestions(dr); //Question Table
                        dr.NextResult();
                        //4. Question Options
                        options = ORMapper.MapOptions(dr); //Option Table
                        //survey.Sections = sections;
                        //survey.Questions = questions;
                        if (survey.Questions != null)
                        {
                            survey.Questions.ForEach(q => q.Options = options.FindAll(o => o.QuestionId == q.Id));
                        }

                        dr.NextResult();
                        //5. CallEvaluation Resultset
                        userEvaluation = ORMapper.MapUserEvaluation(dr);
                        dr.NextResult();
                        //6. Answer ResultSet
                        answers = ORMapper.MapCallEvauationAnswerMaster(dr);
                        userEvaluation.Answers = answers;
                        dr.NextResult();
                        //7. Answer Options ResultSet
                        options = ORMapper.MapCallEvauationAnswerChild(dr);
                        userEvaluation.Answers.ForEach(a => a.Options = options.FindAll(o => o.QuestionId == a.QuestionId));

                        dr.NextResult();
                        //8. CallInfo ResultSet
                        userEvaluation.Agent = ORMapper.MapUserWithoutChannel(dr);
                        userEvaluation.Survey = survey;

                        return userEvaluation;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public UserEvaluation UpdateUserEvaluation(UserEvaluation userEval)
        {
            try
            {
                XElement xEvalUserSurveyDetails = this.CreateUserSurveyDetailsXML(userEval);

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.USER_EVALUATION_UPDATE;

                    cmd.Parameters.AddWithValue("@UserEvaluationId", userEval.Id);
                    cmd.Parameters.AddWithValue("@StatusId", userEval.StatusId);
                    cmd.Parameters.AddWithValue("@EvaluationType", 2);
                    //cmd.Parameters.AddWithValue("@IsShared", callEvaluation.IsShared);
                    cmd.Parameters.AddWithValue("@ModifiedDate", userEval.ModifiedDate);
                    //cmd.Parameters.AddWithValue("@IsDeleted", callEvaluation.IsDeleted);
                    cmd.Parameters.AddWithValue("@UserEvaluationAnswers", xEvalUserSurveyDetails.ToString());

                    cmd.Parameters.AddWithValue("@ScorerComments", userEval.SupervisorComments);
                    cmd.Parameters.AddWithValue("@EvaluatedScore", userEval.EvaluatedScore);

                    //cmd.Parameters["@ScorerComments"].Direction = ParameterDirection.InputOutput;
                    cmd.Parameters["@EvaluatedScore"].Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "UpdateUserEvaluation", _tenantId));

                    cmd.ExecuteNonQuery();

                    //callEvaluation.SupervisorComments = Convert.ToString(cmd.Parameters["@ScorerComments"].Value.ToString());
                    userEval.EvaluatedScore = Convert.ToSingle(cmd.Parameters["@EvaluatedScore"].Value.ToString());

                    return userEval;
                }
            }
            catch (Exception ex) { throw ex; }
            //return false;
        }

        public List<UserEvaluation> GetUserEvaluationPaged(string whereClause, int userId, bool SharedRequired = false)
        {
            List<UserEvaluation> userEvals = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.USER_EVALUATION_GETALL_BY_WHERE_CLAUSE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);
                    cmd.Parameters.AddWithValue("@EvaluatorId", userId);
                    cmd.Parameters.AddWithValue("@SharedRequired", SharedRequired);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetUserEvaluationPaged", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        userEvals = ORMapper.MapUserEvaluations(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return userEvals;
        }

        public short UpdateUserEvaluationStatus(string userEvalIds, int statusId, DateTime? actionDate, bool isDeleted)
        {
            try
            {
                //return 1;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.USER_EVALUATION_UPDATE_STATUS;
                    cmd.Parameters.AddWithValue("@UserEvaluationIds", userEvalIds);
                    cmd.Parameters.AddWithValue("@StatusId", statusId);
                    cmd.Parameters.AddWithValue("@ActionDate", actionDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", isDeleted);
                    //cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "UpdateUserEvaluationStatus", _tenantId));

                    short count = Convert.ToInt16(cmd.ExecuteScalar()); //statement:- 1 for Success, -1 for Failure
                    return count;
                }
            }
            catch (Exception ex) { throw ex; }
        }


        #endregion
        #region Evaluation from Search Tab - Arivu
        public List<UserEvaluation> GetEvaluationId(int surveyId, string callId, int userId)
        {
            List<UserEvaluation> userEvals = null;
            try
            {

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.GET_EVALUATION_ID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId); //DBNULL
                    cmd.Parameters.AddWithValue("@CallId", callId);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetEvaluationId", _tenantId));


                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        userEvals = ORMapper.MapUserEvaluationsForSearchPage(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
            return userEvals;
        }
        #endregion Evaluation from Search Tab 

        #region Multi Call Evaluation
        public List<MultiCallEvaluationGroup> GetMultiCallEvaluations(string whereClause, int evaluatorId, int pageIndex, int pageSize, out int totalPages, out int totalRecords)
        {
            List<MultiCallEvaluationGroup> multiCallEvaluationGroups = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MultiCallEvaluation.MULT_CALL_EVALUATION_GETBY_WHERECLAUSE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);
                    cmd.Parameters.AddWithValue("@EvaluatorId", evaluatorId);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetMultiCallEvaluations", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        multiCallEvaluationGroups = ORMapper.MapMultiCallEvaluations(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return multiCallEvaluationGroups;
        }

        public MultiCallEvaluationGroup GetMultiCallEvaluationDetails(int evaluatorId, int multiCallEvaluationId)
        {
            MultiCallEvaluationGroup multiCallEvaluationGroup = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MultiCallEvaluation.MULTI_CALL_EVALUATION_GET_DETAILS;
                    cmd.Parameters.AddWithValue("@EvaluatorId", evaluatorId);
                    cmd.Parameters.AddWithValue("@MultiCallEvaluationId", multiCallEvaluationId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetMultiCallEvaluationDetails", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        multiCallEvaluationGroup = ORMapper.MapMultiCallEvaluationGroupDetails(dr);
                        dr.NextResult();
                        multiCallEvaluationGroup.CallInfos = ORMapper.MapCallInfosForEvaluation(dr);
                    }
                }
                multiCallEvaluationGroup.CallEvaluation = CallCallEvaluationDetailsById(multiCallEvaluationGroup.PrimaryEvalId, evaluatorId);
            }
            catch (Exception ex) { throw ex; }
            return multiCallEvaluationGroup;
        }
        public bool DeleteMultiCallEvaluation(int multiCallEvaluationId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MultiCallEvaluation.MULT_CALL_EVALUATION_DELETE;
                    cmd.Parameters.AddWithValue("@MultiCallEvaluationId", multiCallEvaluationId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "DeleteMultiCallEvaluation", 0));

                    return Convert.ToBoolean(cmd.ExecuteNonQuery());
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public bool CloseMultiCallEvaluation(int multiCallEvaluationId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MultiCallEvaluation.MULT_CALL_EVALUATION_MARK_CLOSED;
                    cmd.Parameters.AddWithValue("@MultiCallEvaluationId", multiCallEvaluationId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "CloseMultiCallEvaluation", 0));

                    return Convert.ToBoolean(cmd.ExecuteNonQuery());
                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion

    }

}
