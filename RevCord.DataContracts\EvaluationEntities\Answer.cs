﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.SurveyEntities;

namespace RevCord.DataContracts.EvaluationEntities
{
    public class Answer
    {
        public long CallEvaluationId { get; set; }
        public long QuestionId { get; set; }
        public string AnswerValue { get; set; }
        public List<Option> Options { get; set; }

        //public DateTime AnswerDate { get; set; }
        //public long Id { get; set; }
        //public long CallSurveyId { get; set; }
        ////public long QuestionId { get; set; }
        ////public long AvailableOptionId { get; set; }
        ////public string AnswerText { get; set; }
        ////public bool IsDeleted { get; set; }

        
    }
}
