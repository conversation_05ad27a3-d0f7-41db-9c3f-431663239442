<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.TenantEntities" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.TenantEntities" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="ArrayOfTenantGateway">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="TenantGateway" nillable="true" type="tns:TenantGateway" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfTenantGateway" nillable="true" type="tns:ArrayOfTenantGateway" />
  <xs:complexType name="TenantGateway">
    <xs:sequence>
      <xs:element minOccurs="0" name="AvailableSlots" type="xs:int" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="GatewayId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TenantId" type="xs:int" />
      <xs:element minOccurs="0" name="UsedSlots" type="xs:int" />
      <xs:element minOccurs="0" name="VacantSlots" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TenantGateway" nillable="true" type="tns:TenantGateway" />
</xs:schema>