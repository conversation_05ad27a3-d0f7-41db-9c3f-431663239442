﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{42AA6686-6DEF-4EB6-BE68-A2302EC493CB}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RevCord.ServiceContracts</RootNamespace>
    <AssemblyName>RevCord.ServiceContracts</AssemblyName>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web.Cors, Version=5.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.0.0\lib\net45\System.Web.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="IADSyncGroupService.cs" />
    <Compile Include="IAuditService.cs" />
    <Compile Include="IBookmarkFlag.cs" />
    <Compile Include="ICallTagging.cs" />
    <Compile Include="ICommonService.cs" />
    <Compile Include="ICustomerDBService.cs" />
    <Compile Include="IDashboardService.cs" />
    <Compile Include="IInquireRxService.cs" />
    <Compile Include="IInquireService.cs" />
    <Compile Include="IInspectionService.cs" />
    <Compile Include="IIWBService.cs" />
    <Compile Include="ILoggingService.cs" />
    <Compile Include="IMGODataService.cs" />
    <Compile Include="IMTRService.cs" />
    <Compile Include="IPlaylistService.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="IRevcellService.cs" />
    <Compile Include="IConditionalLogicService.cs" />
    <Compile Include="IRevSignService.cs" />
    <Compile Include="IRoleManagementService.cs" />
    <Compile Include="IScheduleReportService.cs" />
    <Compile Include="ISearchMediaService.cs" />
    <Compile Include="ITenantService.cs" />
    <Compile Include="IUserManagerService.cs" />
    <Compile Include="IVoiceLoggingService.cs" />
    <Compile Include="IEvaluationService.cs" />
    <Compile Include="IReportingService.cs" />
    <Compile Include="ISurveyService.cs" />
    <Compile Include="IUserManagementService.cs" />
    <Compile Include="IVoiceRecService.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RevCord.DataContracts\RevCord.DataContracts.csproj">
      <Project>{84c8cf04-1c65-4c93-8391-df8d74bb0b96}</Project>
      <Name>RevCord.DataContracts</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>