﻿using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.CustomerDBEntities;
using RevCord.DataContracts.EMREntities;
using RevCord.DataContracts.InquireEntities;
using RevCord.DataContracts.IQ3InspectionEntities;
using RevCord.DataContracts.MGODataEntities;
using RevCord.DataContracts.ViewModelEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace RevCord.DataContracts.ReportEntities
{
    public class StandardInspection
    {
        public string SurveyCompanyName { get; set; }
        public string Interviewee { get; set; }
        public string ReportDate { get; set; }
        public string SurveyDate { get; set; }
        public string InspectionNumber { get; set; }
        public string InspectionName { get; set; }
        public string UserName { get; set; }
        public string UserId { get; set; }
        public string UserPhone { get; set; }
        public string Location { get; set; }
        public string TitleNotes { get; set; }
        public string PrimaryArea { get; set; }
        public List<CustomField> CustomFields { get; set; }
        public string SummaryNotes { get; set; }
        public string ReportName { get; set; }
        public string FileName { get; set; }
        public string InspectionType { get; set; }
        public bool HasPhotoMarker { get; set; }
        public int IQ3AssetPhoto { get; set; }
        public string SelectedPhotoFileName { get; set; }
        public string JobNumber { get; set; }
        public string RevisionNumber { get; set; }
        public PhotoAttachmentType PhotoAttachmentType { get; set; }

        [XmlElement("Inspection")]
        public List<Inspection> Inspections { get; set; }
        public InspectionTitle InspectionTitle { get; set; }

        public RevCord.DataContracts.IQ3InspectionEntities.Inspection Inspection { get; set; }
        public List<CustomMarkersData> NonInspectedMarkers { get; set; }

        public string EventDate { get; set; }
        public List<AssetModel> AssetModel { get; set; }
        public Dictionary<string, object> AssetDictionary { get; set; }
        public List<IQ3AssetDetail> IQ3AssetDetails { get; set; }
        public IQ3AdvancedReportCriteria IQ3AdvancedReportCriteria { get; set; }
        public MGOReportData MGOReportData { get; set; }
    }
}
