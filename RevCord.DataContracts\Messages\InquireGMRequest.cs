﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.MessageBase;

namespace RevCord.DataContracts.Messages
{
    public class InquireGMRequest : RequestBase
    {
        public UserType UserType { get; set; }
        public bool IsOnlyIQ3ModeEnabled { get; set; }
        #region Associations

        public AppUser AppUser { get; set; }
        public GlobalGroup GlobalGroup { get; set; }
        public User User { get; set; }

        #endregion
    }
}
