﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.Util
{
    /// <summary>
    /// Provides a set of static methods for DateTime operations. This class cannot be inherited. 
    /// </summary>
    public static class RPTDateTimeHelper
    {
        /// <summary>
        /// Date Format
        /// </summary>
        public static readonly string DateOutputFormat = "MM-dd-yyyy";

        /// <summary>
        /// Returns No. of days
        /// </summary>
        public static int FullDaysBetween(DateTime highDate, DateTime lowDate)
        {
            return (int)highDate.Date.Subtract(lowDate.Date).TotalDays;
        }

        /// <summary>
        /// Returns No. of months
        /// </summary>
        public static int MonthsBetween(DateTime lowDate, DateTime highDate)
        {
            if (highDate.Month < lowDate.Month)
            {
                return ((12 - lowDate.Month) + highDate.Month);
            }
            return (highDate.Month - lowDate.Month);
        }

        public static string FormatDuration(int hours, int minutes, int seconds)
        {
            return (hours > 0 ?
                    String.Format("{0:00}:{1:00}:{2:00}", hours, minutes, seconds) :
                    String.Format("{0}:{1:00}", minutes, seconds)
                    );
        }

        public static string FormatDuration(List<int> timeList)
        {
            return String.Format("{0:00}:{1:00}:{2:00}", timeList[0], timeList[1], timeList[2]);
        }

        public static IEnumerable<DateTime> AllDatesInMonth(int year, int month)
        {
            int days = DateTime.DaysInMonth(year, month);
            for (int day = 1; day <= days; day++)
            {
                yield return new DateTime(year, month, day);
            }
        }
        
        public static IEnumerable<DateTime> GetDaysBetweenTwoDates(DateTime fromDate, DateTime toDate)
        {
            for (var day = fromDate.Date; day.Date <= toDate.Date; day = day.AddDays(1))
                yield return day;
        }

        public static IEnumerable<DateTime> GetMissingMonths(DateTime startDate, DateTime endDate, IEnumerable<DateTime> source)
        {
            IEnumerable<DateTime> sourceMonths =
              source.Select(x => new DateTime(x.Year, x.Month, 1))
                    .ToList()
                    .Distinct();
            return MonthsBetweenInclusive(startDate, endDate).Except(sourceMonths);
        }

        public static IEnumerable<DateTime> MonthsBetweenInclusive(DateTime startDate, DateTime endDate)
        {
            DateTime currentMonth = new DateTime(startDate.Year, startDate.Month, 1);
            DateTime endMonth = new DateTime(endDate.Year, endDate.Month, 1);

            while (currentMonth <= endMonth)
            {
                yield return currentMonth;
                currentMonth = currentMonth.AddMonths(1);
            }
        }


        public static IEnumerable<DateTime> GetMonthsBetweenTwoDates(DateTime fromDate, DateTime toDate)
        {
            DateTime firstDate = fromDate;
            DateTime lastDate = DateTime.MaxValue;
            
            while (firstDate < toDate)
            {
                if (lastDate.Month != firstDate.Month)
                {
                    lastDate = firstDate;
                    yield return lastDate;
                }
                firstDate = firstDate.AddDays(1);
            }
        }

        //public static IEnumerable<DayOfWeek> GetDaysOfWeek()
        //{
        //}
        public static IEnumerable<DayOfWeek> EachDayOfTheWeek
        {
            get
            {
                yield return DayOfWeek.Sunday;
                yield return DayOfWeek.Monday;
                yield return DayOfWeek.Tuesday;
                yield return DayOfWeek.Wednesday;
                yield return DayOfWeek.Thursday;
                yield return DayOfWeek.Friday;
                yield return DayOfWeek.Saturday;
            }
        }

        public static string GetDefaultFormatedTime(long durationInMilliseconds)
        {
            decimal hours = durationInMilliseconds / (1000 * 60 * 60);
            decimal minutes = (durationInMilliseconds % (1000 * 60 * 60)) / (1000 * 60);
            decimal seconds = ((durationInMilliseconds % (1000 * 60 * 60)) % (1000 * 60)) / 1000;
            return string.Format("{0:00}:{1:00}:{2:00}", ((int)hours), ((int)minutes), ((int)seconds));
        }

        


        //Public Shared Function MilliSecondsToHoursMinutesSeconds(ByVal durationInMS As int64) As String

        //    Dim Hrs As Decimal
        //    Dim Mins As Decimal
        //    Dim Secs As Decimal

        //    durationInMS = IIF(durationInMS <= 0, 0, durationInMS)
        //    Hrs = durationInMS / (1000 * 60 * 60)
        //    Mins = (durationInMS Mod (1000 * 60 * 60)) / (1000 * 60)
        //    Secs = ((durationInMS Mod (1000 * 60 * 60)) Mod (1000 * 60)) / 1000
         

        //     Dim sReturn as String
        //     sReturn =  CStr(Int(Hrs)) & ":"
        //     sReturn = sReturn & Right("0" & CStr(Mins),2) & ":"
        //     sReturn = sReturn & Right("0" & CStr(Secs),2) 
        //     MilliSecondsToHoursMinutesSeconds = sReturn
        //End Function


        //Function MinutesToHoursMinutes(ByVal vMins As Integer) As String
        //    Dim Mins As Integer
        //    Dim Hours As Integer

        //    vMins = IIF(vMins <= 0, 0, vMins)
        //    Hours = Floor(vMins / 60)
        //    Mins = vMins - (Hours * 60)
        //    MinutesToHoursMinutes = IIF(Hours < 9, "0" & CStr(Hours), CStr(Hours)) & ":" & IIF(Mins < 9, "0" & CStr(Mins), CStr(Mins))
        //    Return MinutesToHoursMinutes
        //End Function



            //Function HrsMinSec(ByVal durationInMS as Double) as String
                
            //    Dim sReturn as String
            //    Dim iHrs as Integer
            //    Dim iMin as Integer
            //    Dim iSec as Integer

            //    iHrs = Int(durationInMS / (1000 * 60 * 60))
            //    iMin = Int((durationInMS Mod (1000 * 60 * 60)) / (1000 * 60))
            //    iSec = ((durationInMS Mod (1000 * 60 * 60)) Mod (1000 * 60)) / 1000
 
            //    sReturn =  CStr(iHrs) & ":"
            //    sReturn = sReturn & Right("0" & CStr(iMin),2) & ":"
            //    sReturn = sReturn & Right("0" & CStr(iSec),2) 
 
            //    HrsMinSec = sReturn

            //End Function



    }
}
