﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.MessageBase;

namespace RevCord.DataContracts.Messages
{
    public class DashboardRequest : RequestBase
    {
        public DashboardCriteria DashboardCriteria { get; set; }

        public UserType UserType { get; set; }

        public int UserId { get; set; }

        public string CSVExtensions { get; set; }

        public string LastStartTime { get; set; }
        public int NoOfRecords { get; set; }

        public bool IsAvrisView { get; set; }
    }
}
