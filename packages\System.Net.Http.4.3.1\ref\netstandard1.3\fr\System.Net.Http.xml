﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.ByteArrayContent">
      <summary>Fournit du contenu HTTP basé sur un tableau d'octets.</summary>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[])">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <param name="content">Contenu utilisé pour initialiser le <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="content" /> est null. </exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <param name="content">Contenu utilisé pour initialiser le <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <param name="offset">Décalage, en octets, dans le paramètre <paramref name="content" /> utilisé pour initialiser <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <param name="count">Nombre d'octets dans le <paramref name="content" /> en commençant par le paramètre <paramref name="offset" /> utilisé pour initialiser <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="content" /> est null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Le paramètre <paramref name="offset" /> est inférieur à zéro.ouLe paramètre <paramref name="offset" /> est supérieur à la longueur du contenu spécifié par le paramètre <paramref name="content" /> moins le paramètre.ouLe paramètre <paramref name="count " /> est inférieur à zéro.ouLe paramètre <paramref name="count" /> est supérieur à la longueur du contenu spécifié par le paramètre <paramref name="content" /> moins le paramètre <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.CreateContentReadStreamAsync">
      <summary>Crée un flux de contenu HTTP en tant qu'opération asynchrone pour la lecture dont le magasin de stockage est lié au <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Sérialise et écrit le tableau d'octets fourni dans le constructeur pour un flux de contenu HTTP sous forme d'une opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task" /> ; Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="stream">Flux cible.</param>
      <param name="context">Informations sur le transport, (jeton de liaison de canal, par exemple).Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.TryComputeLength(System.Int64@)">
      <summary>Détermine si un tableau d'octets a une longueur valide en octets.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'élément <paramref name="length" /> correspond à une longueur valide, sinon false.</returns>
      <param name="length">Longueur, en octets, du tableau d'octets.</param>
    </member>
    <member name="T:System.Net.Http.ClientCertificateOption">
      <summary>Spécifie la façon dont les certificats clients sont fournis.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Automatic">
      <summary>
        <see cref="T:System.Net.Http.HttpClientHandler" /> tentera de fournir automatiquement tous les certificats client disponibles.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Manual">
      <summary>L'application fournit manuellement les certificats clients au <see cref="T:System.Net.Http.WebRequestHandler" />.Cette valeur est celle par défaut.</summary>
    </member>
    <member name="T:System.Net.Http.DelegatingHandler">
      <summary>Type pour les gestionnaires HTTP qui délèguent le traitement des messages de réponse HTTP à un autre gestionnaire, appelé le gestionnaire interne.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.DelegatingHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.DelegatingHandler" /> avec un gestionnaire interne spécifique.</summary>
      <param name="innerHandler">Gestionnaire interne chargé de traiter les messages de réponse HTTP.</param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par <see cref="T:System.Net.Http.DelegatingHandler" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées. </param>
    </member>
    <member name="P:System.Net.Http.DelegatingHandler.InnerHandler">
      <summary>Obtient ou définit le gestionnaire interne qui traite les messages de réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpMessageHandler" /> ;Gestionnaire interne des messages de réponse HTTP.</returns>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Envoie une requête HTTP au gestionnaire interne à envoyer au serveur sous forme d'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ; Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="request">Message de requête HTTP à envoyer au serveur.</param>
      <param name="cancellationToken">Jeton d'annulation pour annuler une opération.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> était null.</exception>
    </member>
    <member name="T:System.Net.Http.FormUrlEncodedContent">
      <summary>Conteneur pour les tuples nom/valeur encodés en utilisant le type MIME application/x-www-form-urlencoded.</summary>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.FormUrlEncodedContent" /> avec une collection de paires nom/valeur spécifique.</summary>
      <param name="nameValueCollection">Collection de paires nom/valeur.</param>
    </member>
    <member name="T:System.Net.Http.HttpClient">
      <summary>Fournit une classe de base pour envoyer des requêtes HTTP et recevoir des réponses HTTP d'une ressource identifiée par un URI. </summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpClient" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpClient" /> avec un gestionnaire spécifique.</summary>
      <param name="handler">Pile du gestionnaire HTTP à utiliser pour envoyer des demandes. </param>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpClient" /> avec un gestionnaire spécifique.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" /> chargé de traiter les messages de réponse HTTP.</param>
      <param name="disposeHandler">true si le gestionnaire interne doit être supprimé à l'aide de Dispose(),false si vous prévoyez de réutiliser le gestionnaire interne.</param>
    </member>
    <member name="P:System.Net.Http.HttpClient.BaseAddress">
      <summary>Obtient ou définit l'adresse de base de l'URI de la ressource Internet utilisée pour envoyer des demandes.</summary>
      <returns>Retourne <see cref="T:System.Uri" />.Adresse de base de l'URI de la ressource Internet utilisée pour l'envoi des demandes.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.CancelPendingRequests">
      <summary>Annulez toutes les demandes en attente sur cette instance.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultRequestHeaders">
      <summary>Obtient les en-têtes qui doivent être envoyés avec chaque demande.</summary>
      <returns>Retourne <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />.En-têtes qui doivent être envoyés avec chaque demande.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String)">
      <summary>Envoie une demande DELETE à l'URI spécifié sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
      <exception cref="T:System.InvalidOperationException">Le message de demande a déjà été envoyé par l'instance <see cref="T:System.Net.Http.HttpClient" /> .</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String,System.Threading.CancellationToken)">
      <summary>Envoie une requête DELETE à l'URI spécifié avec un jeton d'annulation sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
      <exception cref="T:System.InvalidOperationException">Le message de demande a déjà été envoyé par l'instance <see cref="T:System.Net.Http.HttpClient" /> .</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri)">
      <summary>Envoie une demande DELETE à l'URI spécifié sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
      <exception cref="T:System.InvalidOperationException">Le message de demande a déjà été envoyé par l'instance <see cref="T:System.Net.Http.HttpClient" /> .</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>Envoie une requête DELETE à l'URI spécifié avec un jeton d'annulation sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
      <exception cref="T:System.InvalidOperationException">Le message de demande a déjà été envoyé par l'instance <see cref="T:System.Net.Http.HttpClient" /> .</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par le <see cref="T:System.Net.Http.HttpClient" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées.</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String)">
      <summary>Envoie une requête GET vers l'URI spécifié sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption)">
      <summary>Envoie une requête GET à l'URI spécifié avec une option d'achèvement HTTP sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="completionOption">Valeur d'option d'achèvement HTTP qui indique quand l'opération doit être considérée comme terminée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Envoie une requête GET à l'URI spécifié avec une option d'achèvement HTTP et un jeton d'annulation sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="completionOption">Valeur d'option d'achèvement HTTP qui indique quand l'opération doit être considérée comme terminée.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Threading.CancellationToken)">
      <summary>Envoie une requête GET à l'URI spécifié avec un jeton d'annulation sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri)">
      <summary>Envoie une requête GET vers l'URI spécifié sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption)">
      <summary>Envoie une requête GET à l'URI spécifié avec une option d'achèvement HTTP sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="completionOption">Valeur d'option d'achèvement HTTP qui indique quand l'opération doit être considérée comme terminée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Envoie une requête GET à l'URI spécifié avec une option d'achèvement HTTP et un jeton d'annulation sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="completionOption">Valeur d'option d'achèvement HTTP qui indique quand l'opération doit être considérée comme terminée.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>Envoie une requête GET à l'URI spécifié avec un jeton d'annulation sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.String)">
      <summary>Envoie une requête GET à l'URI spécifié et retourne le corps de la réponse sous forme de tableau d'octets dans une opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.Uri)">
      <summary>Envoie une requête GET à l'URI spécifié et retourne le corps de la réponse sous forme de tableau d'octets dans une opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.String)">
      <summary>Envoie une requête GET à l'URI spécifié et retourne le corps de la réponse sous forme de flux dans une opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.Uri)">
      <summary>Envoie une requête GET à l'URI spécifié et retourne le corps de la réponse sous forme de flux dans une opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.String)">
      <summary>Envoie une requête GET à l'URI spécifié et retourne le corps de la réponse sous forme de chaîne dans une opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.Uri)">
      <summary>Envoie une requête GET à l'URI spécifié et retourne le corps de la réponse sous forme de chaîne dans une opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.MaxResponseContentBufferSize">
      <summary>Obtient ou définit le nombre maximal d'octets à mettre en mémoire tampon lors de la lecture du contenu de réponse.</summary>
      <returns>Retourne <see cref="T:System.Int32" />.Nombre maximal d'octets à mettre en mémoire tampon lors de la lecture du contenu de réponse.La valeur par défaut de cette propriété est 2 gigaoctets.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">La taille spécifiée est inférieure ou égale à zéro.</exception>
      <exception cref="T:System.InvalidOperationException">Opération a déjà démarrée sur l'instance actuelle. </exception>
      <exception cref="T:System.ObjectDisposedException">L'instance actuelle a été supprimée. </exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Envoie une requête POST vers l'URI spécifié sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="content">Contenu de requête HTTP envoyé au serveur.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Envoie une requête POST avec un jeton d'annulation sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="content">Contenu de requête HTTP envoyé au serveur.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Envoie une requête POST vers l'URI spécifié sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="content">Contenu de requête HTTP envoyé au serveur.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Envoie une requête POST avec un jeton d'annulation sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="content">Contenu de requête HTTP envoyé au serveur.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Envoie une requête PUT vers l'URI spécifié sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="content">Contenu de requête HTTP envoyé au serveur.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Envoyez une requête PUT avec un jeton d'annulation sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="content">Contenu de requête HTTP envoyé au serveur.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Envoie une requête PUT vers l'URI spécifié sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="content">Contenu de requête HTTP envoyé au serveur.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Envoyez une requête PUT avec un jeton d'annulation sous forme d'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="requestUri">URI auquel la requête est envoyée.</param>
      <param name="content">Contenu de requête HTTP envoyé au serveur.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage)">
      <summary>Envoie une requête HTTP en tant qu'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="request">Message de la requête HTTP à envoyer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> était null.</exception>
      <exception cref="T:System.InvalidOperationException">Le message de demande a déjà été envoyé par l'instance <see cref="T:System.Net.Http.HttpClient" /> .</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption)">
      <summary>Envoie une requête HTTP en tant qu'opération asynchrone. </summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="request">Message de la requête HTTP à envoyer.</param>
      <param name="completionOption">Moment auquel l'opération doit s'exécuter (dès qu'une réponse est disponible ou après avoir pris connaissance du contenu de réponse entier).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> était null.</exception>
      <exception cref="T:System.InvalidOperationException">Le message de demande a déjà été envoyé par l'instance <see cref="T:System.Net.Http.HttpClient" /> .</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Envoie une requête HTTP en tant qu'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="request">Message de la requête HTTP à envoyer.</param>
      <param name="completionOption">Moment auquel l'opération doit s'exécuter (dès qu'une réponse est disponible ou après avoir pris connaissance du contenu de réponse entier).</param>
      <param name="cancellationToken">Jeton d'annulation pour annuler une opération.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> était null.</exception>
      <exception cref="T:System.InvalidOperationException">Le message de demande a déjà été envoyé par l'instance <see cref="T:System.Net.Http.HttpClient" /> .</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Envoie une requête HTTP en tant qu'opération asynchrone.</summary>
      <returns>Retourne <see cref="T:System.Threading.Tasks.Task`1" />.Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="request">Message de la requête HTTP à envoyer.</param>
      <param name="cancellationToken">Jeton d'annulation pour annuler une opération.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> était null.</exception>
      <exception cref="T:System.InvalidOperationException">Le message de demande a déjà été envoyé par l'instance <see cref="T:System.Net.Http.HttpClient" /> .</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.Timeout">
      <summary>Obtient ou définit la période d'attente jusqu'à l'expiration de la demande.</summary>
      <returns>Retourne <see cref="T:System.TimeSpan" />.Période d'attente jusqu'à l'expiration de la demande.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Le délai d'attente spécifié est inférieur ou égal à zéro et n'est pas <see cref="F:System.Threading.Timeout.InfiniteTimeSpan" />.</exception>
      <exception cref="T:System.InvalidOperationException">Opération a déjà démarrée sur l'instance actuelle. </exception>
      <exception cref="T:System.ObjectDisposedException">L'instance actuelle a été supprimée.</exception>
    </member>
    <member name="T:System.Net.Http.HttpClientHandler">
      <summary>Gestionnaire de messages par défaut utilisé par <see cref="T:System.Net.Http.HttpClient" />.  </summary>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.#ctor">
      <summary>Crée une instance d'une classe <see cref="T:System.Net.Http.HttpClientHandler" />.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect">
      <summary>Obtient ou définit une valeur qui indique si le gestionnaire doit suivre les réponses de redirection.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le gestionnaire doit suivre les réponses de redirection ; sinon false.La valeur par défaut est true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AutomaticDecompression">
      <summary>Obtient ou définit le type de méthode de décompression utilisé par le gestionnaire pour la décompression automatique de la réponse de contenu HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.DecompressionMethods" /> ;Méthode de décompression automatique utilisée par le gestionnaire.La valeur par défaut est <see cref="F:System.Net.DecompressionMethods.None" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.ClientCertificateOptions">
      <summary>Obtient ou définit la collection de certificats de sécurité qui sont associés à ce gestionnaire.</summary>
      <returns>retourne <see cref="T:System.Net.Http.ClientCertificateOption" /> ;Collection de certificats de sécurité associés à ce gestionnaire.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.CookieContainer">
      <summary>Obtient ou définit le conteneur de cookies utilisé par le gestionnaire pour stocker des cookies de serveur.</summary>
      <returns>retourne <see cref="T:System.Net.CookieContainer" /> ;Conteneur de cookies utilisé par le gestionnaire pour stocker des cookies de serveur.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Credentials">
      <summary>Obtient ou définit les informations d'authentification utilisées par ce gestionnaire.</summary>
      <returns>retourne <see cref="T:System.Net.ICredentials" /> ;Informations d'authentification associées au gestionnaire.La valeur par défaut est null.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par le <see cref="T:System.Net.Http.HttpClientHandler" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées.</param>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections">
      <summary>Obtient ou définit le nombre maximal de redirections suivies par le gestionnaire.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Nombre maximal de réponses de redirection suivies par le gestionnaire.La valeur par défaut est 50.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxRequestContentBufferSize">
      <summary>Obtient ou définit la taille maximale de mémoire tampon de contenu demandée utilisée par le gestionnaire.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Taille maximale de mémoire tampon de contenu en octets.La valeur par défaut est 2 gigaoctets.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.PreAuthenticate">
      <summary>Obtient ou définit une valeur indiquant si le gestionnaire envoie un en-tête d'autorisation avec la requête.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true pour que le gestionnaire envoie un en-tête HTTP d'autorisation avec les requêtes une fois l'authentification exécutée ; sinon, false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Proxy">
      <summary>Obtient ou définit les informations de proxy utilisées par le gestionnaire.</summary>
      <returns>retourne <see cref="T:System.Net.IWebProxy" /> ;Informations de proxy utilisées par le gestionnaire.La valeur par défaut est null.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Crée une instance de <see cref="T:System.Net.Http.HttpResponseMessage" /> en fonction des informations fournies dans le <see cref="T:System.Net.Http.HttpRequestMessage" /> en tant qu'opération qui ne se bloque pas.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="request">Message de la requête HTTP.</param>
      <param name="cancellationToken">Jeton d'annulation pour annuler l'opération.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> était null.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsAutomaticDecompression">
      <summary>Obtient une valeur qui indique si le gestionnaire prend en charge la décompression de contenu de réponse automatique.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le gestionnaire prend en charge la décompression de contenu de réponse automatique ; sinon false.La valeur par défaut est true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsProxy">
      <summary>Obtient une valeur qui indique si le gestionnaire prend en charge les paramètres du proxy.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le gestionnaire prend en charge les paramètres de proxy ; sinon false.La valeur par défaut est true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsRedirectConfiguration">
      <summary>Obtient une valeur qui indique si le gestionnaire prend en charge les paramètres de configuration pour les propriétés <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> et <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> .</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le gestionnaire prend en charge les paramètres de configuration pour les propriétés <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> et <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> ; sinon false.La valeur par défaut est true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseCookies">
      <summary>Obtient ou définit une valeur qui indique si le gestionnaire utilise la propriété <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> pour stocker des cookies de serveur et utilise ces cookies en envoyant les demandes.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le gestionnaire utilise la propriété <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> pour stocker des cookies de serveur, et utilise ces cookies lors de l'envoi de requêtes ; sinon false.La valeur par défaut est true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseDefaultCredentials">
      <summary>Obtient ou définit une valeur qui contrôle si les informations d'identification par défaut sont envoyées avec les requêtes par le gestionnaire.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si les informations d'identification par défaut sont utilisées ; sinon, false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseProxy">
      <summary>Obtient ou définit une valeur qui indique si le gestionnaire utilise un proxy pour les demandes. </summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le gestionnaire utilise un proxy pour les demandes ; sinon false.La valeur par défaut est true.</returns>
    </member>
    <member name="T:System.Net.Http.HttpCompletionOption">
      <summary>Indique si les opérations <see cref="T:System.Net.Http.HttpClient" /> doivent être considérées comme étant terminées dès qu'une réponse est disponible, ou après avoir pris connaissance de l'intégralité du message de réponse intégrant le contenu. </summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseContentRead">
      <summary>L'opération doit se terminer après la lecture de l'intégralité de la réponse intégrant le contenu.</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseHeadersRead">
      <summary>L'opération doit se terminer dès qu'une réponse est disponible et que les en-têtes sont lus.Le contenu n'est pas encore lu.</summary>
    </member>
    <member name="T:System.Net.Http.HttpContent">
      <summary>Classe de base représentant un corps d'entité HTTP et les en-têtes de contenu.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream)">
      <summary>Sérialise le contenu HTTP dans un flux d'octets et le copie dans l'objet de flux fourni en tant que paramètre <paramref name="stream" />.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="stream">Flux cible.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Sérialise le contenu HTTP dans un flux d'octets et le copie dans l'objet de flux fourni en tant que paramètre <paramref name="stream" />.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="stream">Flux cible.</param>
      <param name="context">Informations sur le transport (jeton de liaison de canal, par exemple).Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CreateContentReadStreamAsync">
      <summary>Sérialise le contenu HTTP dans un flux de mémoire en tant qu'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose">
      <summary>Libère les ressources non managées et supprime les ressources managées utilisées par le <see cref="T:System.Net.Http.HttpContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par le <see cref="T:System.Net.Http.HttpContent" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées.</param>
    </member>
    <member name="P:System.Net.Http.HttpContent.Headers">
      <summary>Obtient les en-têtes de contenu HTTP, tels que définis dans la norme RFC 2616.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpContentHeaders" /> ;En-têtes de contenu, tels que définis dans RFC 2616.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync">
      <summary>Sérialise le contenu HTTP dans un tampon de mémoire en tant qu'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync(System.Int64)">
      <summary>Sérialise le contenu HTTP dans un tampon de mémoire en tant qu'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="maxBufferSize">Taille maximale, en octets, de la mémoire tampon à utiliser.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsByteArrayAsync">
      <summary>Sérialise le contenu HTTP dans un tableau d'octets sous forme d'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStreamAsync">
      <summary>Sérialise le contenu HTTP et retourne un flux qui représente le contenu comme une opération asynchrone. </summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStringAsync">
      <summary>Sérialise le contenu HTTP dans une chaîne sous forme d'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Sérialise le contenu HTTP dans un flux sous forme d'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="stream">Flux cible.</param>
      <param name="context">Informations sur le transport (jeton de liaison de canal, par exemple).Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.TryComputeLength(System.Int64@)">
      <summary>Détermine si le contenu HTTP a une longueur valide en octets.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'élément <paramref name="length" /> correspond à une longueur valide, sinon false.</returns>
      <param name="length">Longueur en octets du contenu HTTP.</param>
    </member>
    <member name="T:System.Net.Http.HttpMessageHandler">
      <summary>Type de base pour les gestionnaires de message HTTP.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose">
      <summary>Libère les ressources non managées et supprime les ressources managées utilisées par le <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par le <see cref="T:System.Net.Http.HttpMessageHandler" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Envoie une requête HTTP en tant qu'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="request">Message de la requête HTTP à envoyer.</param>
      <param name="cancellationToken">Jeton d'annulation pour annuler une opération.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> était null.</exception>
    </member>
    <member name="T:System.Net.Http.HttpMessageInvoker">
      <summary>Classe spécialisée qui permet aux applications d'appeler la méthode <see cref="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)" /> sur une chaîne de gestionnaire HTTP. </summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Initialise une nouvelle instance d'une classe <see cref="T:System.Net.Http.HttpMessageInvoker" /> avec un <see cref="T:System.Net.Http.HttpMessageHandler" /> spécifique.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" /> chargé de traiter les messages de réponse HTTP.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>Initialise une nouvelle instance d'une classe <see cref="T:System.Net.Http.HttpMessageInvoker" /> avec un <see cref="T:System.Net.Http.HttpMessageHandler" /> spécifique.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" /> chargé de traiter les messages de réponse HTTP.</param>
      <param name="disposeHandler">true si le gestionnaire interne doit être supprimé à l'aide de Dispose(),false si vous prévoyez de réutiliser le gestionnaire interne.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose">
      <summary>Libère les ressources non managées et supprime les ressources managées utilisées par le <see cref="T:System.Net.Http.HttpMessageInvoker" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par le <see cref="T:System.Net.Http.HttpMessageInvoker" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Envoie une requête HTTP en tant qu'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="request">Message de la requête HTTP à envoyer.</param>
      <param name="cancellationToken">Jeton d'annulation pour annuler une opération.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> était null.</exception>
    </member>
    <member name="T:System.Net.Http.HttpMethod">
      <summary>Classe d'assistance pour récupérer et comparer les méthodes HTTP standard et pour créer de nouvelles méthodes HTTP.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMethod.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpMethod" /> avec une méthode HTTP spécifique.</summary>
      <param name="method">Méthode HTTP.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Delete">
      <summary>Représente une méthode de protocole HTTP DELETE.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpMethod" /> ;</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Net.Http.HttpMethod)">
      <summary>Détermine si le <see cref="T:System.Net.Http.HttpMethod" /> spécifié est égal au <see cref="T:System.Object" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'objet spécifié est égal à l'objet actuel ; sinon, false.</returns>
      <param name="other">Méthode HTTP à comparer à l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Object)">
      <summary>Détermine si le <see cref="T:System.Object" /> spécifié est égal au <see cref="T:System.Object" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'objet spécifié est égal à l'objet actuel ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Get">
      <summary>Représente une méthode de protocole HTTP GET.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpMethod" /> ;</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.GetHashCode">
      <summary>Sert de fonction de hachage pour ce type.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage du <see cref="T:System.Object" /> en cours.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Head">
      <summary>Représente une méthode de protocole HTTP HEAD.La méthode HEAD est identique à GET, mais le serveur retourne uniquement des en-têtes de message dans la réponse, sans corps du message.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpMethod" /> ;</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Method">
      <summary>Méthode HTTP. </summary>
      <returns>retourne <see cref="T:System.String" /> ;Méthode HTTP représentée en tant que <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Equality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>Opérateur d'égalité pour la comparaison de deux objets <see cref="T:System.Net.Http.HttpMethod" />.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si les paramètres <paramref name="left" /> et <paramref name="right" /> sont égaux ; sinon, false.</returns>
      <param name="left">
        <see cref="T:System.Net.Http.HttpMethod" /> gauche d'un opérateur d'égalité.</param>
      <param name="right">
        <see cref="T:System.Net.Http.HttpMethod" /> droit pour un opérateur d'égalité.</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Inequality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>Opérateur d'inégalité pour la comparaison de deux objets <see cref="T:System.Net.Http.HttpMethod" />.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si les paramètres <paramref name="left" /> et <paramref name="right" /> ne sont pas égaux ; sinon, false.</returns>
      <param name="left">
        <see cref="T:System.Net.Http.HttpMethod" /> gauche d'un opérateur d'inégalité.</param>
      <param name="right">
        <see cref="T:System.Net.Http.HttpMethod" /> droit pour un opérateur d'inégalité.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Options">
      <summary>Représente une méthode de protocole HTTP OPTIONS.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpMethod" /> ;</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Post">
      <summary>Représente une méthode de protocole HTTP POST utilisée pour publier une nouvelle entité en plus d'un URI.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpMethod" /> ;</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Put">
      <summary>Représente une méthode de protocole HTTP PUT utilisée pour remplacer une entité identifiée par un URI.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpMethod" /> ;</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.ToString">
      <summary>Retourne une chaîne qui représente l'objet actif.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Trace">
      <summary>Représente une méthode de protocole HTTP TRACE.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpMethod" /> ;</returns>
    </member>
    <member name="T:System.Net.Http.HttpRequestException">
      <summary>Classe de base pour les exceptions levées par les classes <see cref="T:System.Net.Http.HttpClient" /> et <see cref="T:System.Net.Http.HttpMessageHandler" /> .</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpRequestException" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpRequestException" /> avec un message spécifique qui décrit l'exception actuelle.</summary>
      <param name="message">Message qui décrit l'exception en cours.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpRequestException" /> avec un message spécifique décrivant l'exception actuelle et une exception interne.</summary>
      <param name="message">Message qui décrit l'exception en cours.</param>
      <param name="inner">Exception interne.</param>
    </member>
    <member name="T:System.Net.Http.HttpRequestMessage">
      <summary>Représente un message de requête HTTP.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpRequestMessage" /> avec une méthode HTTP et une requête <see cref="T:System.Uri" />.</summary>
      <param name="method">Méthode HTTP.</param>
      <param name="requestUri">Chaîne qui représente la requête <see cref="T:System.Uri" />.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.Uri)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpRequestMessage" /> avec une méthode HTTP et une requête <see cref="T:System.Uri" />.</summary>
      <param name="method">Méthode HTTP.</param>
      <param name="requestUri">
        <see cref="T:System.Uri" /> à demander.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Content">
      <summary>Obtient ou définit le contenu du message HTTP. </summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpContent" /> ;Contenu d'un message.</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose">
      <summary>Libère les ressources non managées et supprime les ressources managées utilisées par le <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par le <see cref="T:System.Net.Http.HttpRequestMessage" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Headers">
      <summary>Obtient la collection d'en-têtes de requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" /> ;Collection d'en-têtes de demande HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Method">
      <summary>Obtient ou définit la méthode HTTP utilisée par le message de requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpMethod" /> ;Méthode HTTP utilisée par le message de requête.La valeur par défaut est la méthode GET.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Properties">
      <summary>Obtient un ensemble de propriétés pour la requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.IDictionary`2" /> ;</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.RequestUri">
      <summary>Obtient ou définit l'<see cref="T:System.Uri" /> utilisé pour la requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Uri" /> ;<see cref="T:System.Uri" /> utilisé pour la requête HTTP.</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.ToString">
      <summary>Retourne une chaîne qui représente l'objet actif.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Représentation sous forme de chaîne de l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Version">
      <summary>Obtient ou définit la version de messages HTTP.</summary>
      <returns>retourne <see cref="T:System.Version" /> ;Version du message HTTP.La valeur par défaut est 1.1.</returns>
    </member>
    <member name="T:System.Net.Http.HttpResponseMessage">
      <summary>Représente un message de réponse HTTP avec le code et les données d'état.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor(System.Net.HttpStatusCode)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.HttpResponseMessage" /> avec un <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> spécifique.</summary>
      <param name="statusCode">Code d'état de la réponse HTTP.</param>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Content">
      <summary>Obtient ou définit le contenu d'un message de réponse HTTP. </summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpContent" /> ;Contenu du message de réponse HTTP.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose">
      <summary>Libère les ressources non managées et supprime les ressources non managées utilisées par le <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par le <see cref="T:System.Net.Http.HttpResponseMessage" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées.</param>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode">
      <summary>Lève une exception si la propriété <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode" /> de la réponse HTTP est false.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpResponseMessage" /> ;Message de réponse HTTP si l'appel a réussi.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Headers">
      <summary>Obtient la collection d'en-têtes de réponse HTTP. </summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpResponseHeaders" /> ;Collection d'en-têtes de réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode">
      <summary>Obtient une valeur qui indique si la réponse HTTP a abouti.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;Valeur qui indique si la réponse HTTP a abouti.true si <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> était compris entre 200 et 299 ; sinon false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.ReasonPhrase">
      <summary>Obtient ou définit l'expression de raison qui en général est envoyée par les serveurs avec le code d'état. </summary>
      <returns>retourne <see cref="T:System.String" /> ;Expression de raison envoyée par le serveur.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.RequestMessage">
      <summary>Obtient ou définit le message de demande qui a conduit à ce message de réponse.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpRequestMessage" /> ;Message de requête qui a conduit à ce message de réponse.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.StatusCode">
      <summary>Obtient ou définit le code d'état de la réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.HttpStatusCode" /> ;Code d'état de la réponse HTTP.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.ToString">
      <summary>Retourne une chaîne qui représente l'objet actif.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Représentation sous forme de chaîne de l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Version">
      <summary>Obtient ou définit la version de messages HTTP. </summary>
      <returns>retourne <see cref="T:System.Version" /> ;Version du message HTTP.La valeur par défaut est 1.1.</returns>
    </member>
    <member name="T:System.Net.Http.MessageProcessingHandler">
      <summary>Type de base pour les gestionnaires qui traitent uniquement des messages de demande et/ou de réponse.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor">
      <summary>Crée une instance d'une classe <see cref="T:System.Net.Http.MessageProcessingHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Crée une instance d'une classe <see cref="T:System.Net.Http.MessageProcessingHandler" /> avec un gestionnaire interne spécifique.</summary>
      <param name="innerHandler">Gestionnaire interne chargé de traiter les messages de réponse HTTP.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Exécute le traitement sur chaque demande envoyée au serveur.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpRequestMessage" /> ;Message de requête HTTP qui a été traité.</returns>
      <param name="request">Message de la requête HTTP à traiter.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessResponse(System.Net.Http.HttpResponseMessage,System.Threading.CancellationToken)">
      <summary>Exécute le traitement sur chaque réponse du serveur.</summary>
      <returns>retourne <see cref="T:System.Net.Http.HttpResponseMessage" /> ;Message de réponse HTTP qui a été traité.</returns>
      <param name="response">Message de réponse HTTP à traiter.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Envoie une requête HTTP au gestionnaire interne à envoyer au serveur sous forme d'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="request">Message de requête HTTP à envoyer au serveur.</param>
      <param name="cancellationToken">Jeton d'annulation qui peut être utilisé par d'autres objets ou threads pour être informés de l'annulation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> était null.</exception>
    </member>
    <member name="T:System.Net.Http.MultipartContent">
      <summary>Fournit une collection d'objets <see cref="T:System.Net.Http.HttpContent" /> qui sont sérialisés à l'aide de la spécification de type de contenu multipart/*.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.MultipartContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String)">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.MultipartContent" />.</summary>
      <param name="subtype">Sous-type du contenu Multipart.</param>
      <exception cref="T:System.ArgumentException">La <paramref name="subtype" /> a la valeur null ou ne contient que des espaces blancs.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String,System.String)">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.MultipartContent" />.</summary>
      <param name="subtype">Sous-type du contenu Multipart.</param>
      <param name="boundary">Chaîne limite pour le contenu Multipart.</param>
      <exception cref="T:System.ArgumentException">Le <paramref name="subtype" /> était null ou était une chaîne vide.La <paramref name="boundary" /> a la valeur null ou ne contient que des espaces blancs.ou<paramref name="boundary" /> se termine par un espace.</exception>
      <exception cref="T:System.OutOfRangeException">La longueur de la <paramref name="boundary" /> est supérieure à 70.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Add(System.Net.Http.HttpContent)">
      <summary>Ajoute le contenu multipart HTTP à une collection d'objets de <see cref="T:System.Net.Http.HttpContent" /> qui sont sérialisés à l'aide de la spécification de type de contenu multipart/*</summary>
      <param name="content">Contenu HTTP à ajouter à la collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par le <see cref="T:System.Net.Http.MultipartContent" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.GetEnumerator">
      <summary>Retourne un énumérateur qui itère dans la collection d'objets <see cref="T:System.Net.Http.HttpContent" /> qui sont sérialisés à l'aide de la spécification du type de contenu multipart/*.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.IEnumerator`1" /> ;Objet qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Sérialise le contenu multipart HTTP dans un flux sous forme d'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="stream">Flux cible.</param>
      <param name="context">Informations sur le transport (jeton de liaison de canal, par exemple).Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.System#Collections#IEnumerable#GetEnumerator">
      <summary>Implémentation explicite de la méthode <see cref="M:System.Net.Http.MultipartContent.GetEnumerator" />.</summary>
      <returns>retourne <see cref="T:System.Collections.IEnumerator" /> ;Objet qui peut être utilisé pour itérer au sein de la collection.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.TryComputeLength(System.Int64@)">
      <summary>Détermine si le contenu multipart HTTP a une longueur valide en octets.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'élément <paramref name="length" /> correspond à une longueur valide, sinon false.</returns>
      <param name="length">Longueur en octets du contenu HTTP.</param>
    </member>
    <member name="T:System.Net.Http.MultipartFormDataContent">
      <summary>Fournit un conteneur pour le contenu encodé à l'aide du type MIME multipart/form-data.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.MultipartFormDataContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor(System.String)">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.MultipartFormDataContent" />.</summary>
      <param name="boundary">Chaîne limite pour le contenu multipart/form-data.</param>
      <exception cref="T:System.ArgumentException">La <paramref name="boundary" /> a la valeur null ou ne contient que des espaces blancs.ou<paramref name="boundary" /> se termine par un espace.</exception>
      <exception cref="T:System.OutOfRangeException">La longueur de la <paramref name="boundary" /> est supérieure à 70.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent)">
      <summary>Ajoute le contenu HTTP à une collection d'objets <see cref="T:System.Net.Http.HttpContent" /> qui sont sérialisés au type MIME multipart/form-data.</summary>
      <param name="content">Contenu HTTP à ajouter à la collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String)">
      <summary>Ajoute le contenu HTTP à une collection d'objets <see cref="T:System.Net.Http.HttpContent" /> qui sont sérialisés au type MIME multipart/form-data.</summary>
      <param name="content">Contenu HTTP à ajouter à la collection.</param>
      <param name="name">Nom du contenu HTTP à ajouter.</param>
      <exception cref="T:System.ArgumentException">La <paramref name="name" /> a la valeur null ou ne contient que des espaces blancs.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> était null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String,System.String)">
      <summary>Ajoute le contenu HTTP à une collection d'objets <see cref="T:System.Net.Http.HttpContent" /> qui sont sérialisés au type MIME multipart/form-data.</summary>
      <param name="content">Contenu HTTP à ajouter à la collection.</param>
      <param name="name">Nom du contenu HTTP à ajouter.</param>
      <param name="fileName">Nom de fichier du contenu HTTP à ajouter à la collection.</param>
      <exception cref="T:System.ArgumentException">La <paramref name="name" /> a la valeur null ou ne contient que des espaces blancs.ouLa <paramref name="fileName" /> a la valeur null ou ne contient que des espaces blancs.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> était null.</exception>
    </member>
    <member name="T:System.Net.Http.StreamContent">
      <summary>Fournit du contenu HTTP basé sur un flux.</summary>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream)">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.StreamContent" />.</summary>
      <param name="content">Contenu utilisé pour initialiser le <see cref="T:System.Net.Http.StreamContent" />.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream,System.Int32)">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.StreamContent" />.</summary>
      <param name="content">Contenu utilisé pour initialiser le <see cref="T:System.Net.Http.StreamContent" />.</param>
      <param name="bufferSize">Taille, en octets, de la mémoire tampon disponible pour <see cref="T:System.Net.Http.StreamContent" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> était null.</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="bufferSize" /> était inférieur ou égal à zéro. </exception>
    </member>
    <member name="M:System.Net.Http.StreamContent.CreateContentReadStreamAsync">
      <summary>Écrit le contenu de flux HTTP dans un flux de mémoire sous forme d'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task`1" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par le <see cref="T:System.Net.Http.StreamContent" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Sérialise le contenu HTTP dans un flux sous forme d'opération asynchrone.</summary>
      <returns>retourne <see cref="T:System.Threading.Tasks.Task" /> ;Objet de tâche représentant l'opération asynchrone.</returns>
      <param name="stream">Flux cible.</param>
      <param name="context">Informations sur le transport (jeton de liaison de canal, par exemple).Ce paramètre peut être null.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.TryComputeLength(System.Int64@)">
      <summary>Détermine si le contenu de flux a une longueur valide en octets.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'élément <paramref name="length" /> correspond à une longueur valide, sinon false.</returns>
      <param name="length">Longueur en octets du flux de contenu.</param>
    </member>
    <member name="T:System.Net.Http.StringContent">
      <summary>Fournit du contenu HTTP basé sur une chaîne.</summary>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String)">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.StringContent" />.</summary>
      <param name="content">Contenu utilisé pour initialiser le <see cref="T:System.Net.Http.StringContent" />.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding)">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.StringContent" />.</summary>
      <param name="content">Contenu utilisé pour initialiser le <see cref="T:System.Net.Http.StringContent" />.</param>
      <param name="encoding">Encodage à utiliser pour le contenu.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding,System.String)">
      <summary>Crée une instance de la classe <see cref="T:System.Net.Http.StringContent" />.</summary>
      <param name="content">Contenu utilisé pour initialiser le <see cref="T:System.Net.Http.StringContent" />.</param>
      <param name="encoding">Encodage à utiliser pour le contenu.</param>
      <param name="mediaType">Type de média à utiliser pour le contenu.</param>
    </member>
    <member name="T:System.Net.Http.Headers.AuthenticationHeaderValue">
      <summary>Représente les informations d'identification dans les valeurs d'en-tête Authorization, ProxyAuthorization, WWW-Authenticate et Proxy-Authenticate.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <param name="scheme">Schéma à utiliser pour l'autorisation.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <param name="scheme">Schéma à utiliser pour l'autorisation.</param>
      <param name="parameter">Informations d'identification contenant les informations d'authentification de l'agent utilisateur pour la ressource demandée.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif. </param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Parameter">
      <summary>Obtient les informations d'identification contenant les informations d'authentification de l'agent utilisateur pour la ressource demandée.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Informations d'authentification contenant les informations d'authentification.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête d'authentification.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête d'authentification valides.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Scheme">
      <summary>Obtient le schéma à utiliser pour l'autorisation.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Schéma à utiliser pour l'autorisation.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.TryParse(System.String,System.Net.Http.Headers.AuthenticationHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.CacheControlHeaderValue">
      <summary>Représente la valeur de l'en-tête Cache-Control.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Extensions">
      <summary>Jetons d'extension en cache ayant chacun une valeur assignée facultative.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Collection de jetons d'extension en cache ayant chacun une valeur assignée facultative.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxAge">
      <summary>Durée maximale, exprimée en secondes, pendant laquelle le client HTTP est prêt à accepter une réponse. </summary>
      <returns>retourne <see cref="T:System.TimeSpan" /> ;Durée en secondes. </returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStale">
      <summary>Indique si un client HTTP est prêt à accepter une réponse qui a dépassé son délai d'attente.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le client HTTP est prêt à accepter une réponse ayant dépassé le délai d'attente ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStaleLimit">
      <summary>Durée maximale, en secondes, pendant laquelle un client HTTP est prêt à accepter une réponse qui a dépassé son délai d'attente.</summary>
      <returns>retourne <see cref="T:System.TimeSpan" /> ;Durée en secondes.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MinFresh">
      <summary>Durée de vie d'actualisation, en secondes, pendant laquelle un client HTTP peut accepter une réponse.</summary>
      <returns>retourne <see cref="T:System.TimeSpan" /> ;Durée en secondes.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MustRevalidate">
      <summary>Indique si le serveur d'origine nécessite une nouvelle validation d'une entrée de cache lorsque cette dernière devient périmée.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le serveur d'origine nécessite une nouvelle validation d'une entrée de cache lorsque cette dernière devient périmée ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCache">
      <summary>Indique si un client HTTP est prêt à accepter une réponse mise en cache.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le client HTTP est prêt à accepter une réponse mise en cache ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCacheHeaders">
      <summary>Collection de fieldname dans la directive "no-cache" dans un champ d'en-tête Cache-Control sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Collection de fieldnames.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoStore">
      <summary>Indique si un cache ne doit stocker aucune partie du message de requête HTTP ou aucune réponse.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si un cache ne doit stocker aucune partie du message de requête HTTP ou d'une réponse ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoTransform">
      <summary>Indique si un cache ou un proxy ne doit modifier aucun aspect du corps d'entité.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si un cache ou un proxy ne doivent modifier aucun aspect du corps d'entité ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.OnlyIfCached">
      <summary>Indique si un cache doit répondre à l'aide d'une entrée mise en cache cohérente par rapport aux autres contraintes de la requête HTTP, ou s'il doit répondre à un état 504 (Dépassement du délai de la passerelle).</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si un cache doit répondre à l'aide d'une entrée mise en cache cohérente par rapport aux autres contraintes de la requête HTTP, ou s'il doit répondre à un état 504 (Dépassement du délai de la passerelle) ; sinon, false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête de contrôle de cache.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête de contrôle de cache valides.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Private">
      <summary>Indique si l'ensemble ou une partie du message de réponse HTTP est destiné à un seul utilisateur et ne doit pas être mis en cache par un cache partagé.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le message de réponse HTTP est destiné à un seul utilisateur et ne doit pas être mis en cache par un cache partagé ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.PrivateHeaders">
      <summary>FieldNames de collection dans la directive "privée" dans un champ d'en-tête Cache-Control sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Collection de fieldnames.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.ProxyRevalidate">
      <summary>Indique si le serveur d'origine nécessite la revalidation d'une entrée de cache lors d'une prochaine utilisation lorsque cette entrée de cache est périmée pour les caches d'agent utilisateur partagés.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si le serveur d'origine nécessite la revalidation d'une entrée de cache lors d'une prochaine utilisation lorsque cette entrée est périmée pour les caches d'agent utilisateur partagés ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Public">
      <summary>Indique si une réponse HTTP peut être mise en cache par n'importe quel cache, même si elle ne peut d'habitude pas être mise en cache ou si elle peut l'être, mais uniquement si le cache n'est pas partagé.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si la réponse HTTP peut être mise en cache par n'importe quel cache, même si elle ne peut d'habitude pas être mise en cache ou si elle peut l'être, mais uniquement si le cache n'est pas partagé ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.SharedMaxAge">
      <summary>Âge maximal partagé, en secondes, dans une réponse HTTP qui substitue la directive « max-age » dans un en-tête de contrôle du cache ou dans un en-tête Expires d'un cache partagé.</summary>
      <returns>retourne <see cref="T:System.TimeSpan" /> ;Durée en secondes.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.TryParse(System.String,System.Net.Http.Headers.CacheControlHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentDispositionHeaderValue">
      <summary>Représente la valeur de l'en-tête Content-Disposition.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.Net.Http.Headers.ContentDispositionHeaderValue)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <param name="source">
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />
      </param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <param name="dispositionType">Chaîne qui contient un <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.CreationDate">
      <summary>Date de création du fichier.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Date de création du fichier.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.DispositionType">
      <summary>Type de disposition d'un élément de corps de contenu.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Type de disposition. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileName">
      <summary>Suggestion sur la façon dont construire un Filename pour stocker la charge de message à utiliser si l'entité est détachée et stockée dans un fichier séparé.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Filename suggéré.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileNameStar">
      <summary>Suggestion sur la façon dont construire des Filenames pour stocker des charges de messages à utiliser si les entités sont détachées et stockées dans des fichiers séparés.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Filename suggéré du nom de fichier* de formulaire.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ModificationDate">
      <summary>Date de modification du fichier. </summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Date de modification du fichier.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Name">
      <summary>Nom d'un élément de corps de contenu.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Nom de l'élément de corps du contenu.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Parameters">
      <summary>Un jeu de paramètres a inclus l'en-tête de Content-Disposition.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Collection de paramètres. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête de disposition de contenu.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête de disposition de contenu valides.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ReadDate">
      <summary>Date de la dernière lecture du fichier.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Date de la dernière lecture.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Size">
      <summary>Taille approximative du fichier, en octets. </summary>
      <returns>retourne <see cref="T:System.Int64" /> ;Taille approximative, en octets.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentDispositionHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentRangeHeaderValue">
      <summary>Représente la valeur de l'en-tête Content-Range.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <param name="length">Point de départ ou de fin de la plage, en octets.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <param name="from">Position, en octets, à laquelle démarrer l'envoi de données.</param>
      <param name="to">Position, en octets, à laquelle arrêter l'envoi de données.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64,System.Int64)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <param name="from">Position, en octets, à laquelle démarrer l'envoi de données.</param>
      <param name="to">Position, en octets, à laquelle arrêter l'envoi de données.</param>
      <param name="length">Point de départ ou de fin de la plage, en octets.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> en cours.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.From">
      <summary>Obtient la position à laquelle démarrer l'envoi de données.</summary>
      <returns>retourne <see cref="T:System.Int64" /> ;Position, en octets, à laquelle démarrer l'envoi de données.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasLength">
      <summary>Obtient une valeur indiquant si une longueur est spécifiée dans l'en-tête Content-Range.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si la longueur de Content-Range est spécifiée ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasRange">
      <summary>Obtient une valeur indiquant si une plage est spécifiée dans l'en-tête Content-Range. </summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si la plage de Content-Range est spécifiée ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Length">
      <summary>Obtient la longueur du corps d'entité entier.</summary>
      <returns>retourne <see cref="T:System.Int64" /> ;Longueur du corps d'entité entier.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête de plage du contenu.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête de plage de contenu valides.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.To">
      <summary>Obtient la position à laquelle arrêter l'envoi de données.</summary>
      <returns>retourne <see cref="T:System.Int64" /> ;Position à laquelle arrêter l'envoi de données.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentRangeHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Unit">
      <summary>Unités de plage utilisées.</summary>
      <returns>retourne <see cref="T:System.String" /> ;<see cref="T:System.String" /> qui contient des unités de plage. </returns>
    </member>
    <member name="T:System.Net.Http.Headers.EntityTagHeaderValue">
      <summary>Représente une valeur d'en-tête de balise d'entité.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <param name="tag">Chaîne qui contient un <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String,System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <param name="tag">Chaîne qui contient un <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</param>
      <param name="isWeak">Valeur qui indique si cet en-tête de balise d'entité est un validateur faible.Si l'en-tête de la balise d'entité est un validateur faible, <paramref name="isWeak" /> doit avoir la valeur true.Si l'en-tête de la balise d'entité est un validateur fort, <paramref name="isWeak" /> doit avoir la valeur false.</param>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Any">
      <summary>Obtient la valeur d'en-tête de la balise d'entité.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> ;</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.IsWeak">
      <summary>Obtient une valeur indiquant si la balise d'entité est précédée d'un indicateur de faiblesse.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si la balise d'entité est préfacée par un indicateur de faiblesse ; sinon, false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête de balise d'entité.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête de balise d'entité valides.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Tag">
      <summary>Obtient la chaîne entre guillemets opaque. </summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne entre guillemets opaque.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.TryParse(System.String,System.Net.Http.Headers.EntityTagHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpContentHeaders">
      <summary>Représente la collection d'en-têtes de contenu comme définie dans RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Allow">
      <summary>Obtient la valeur de l'en-tête de contenu Allow sur une réponse HTTP. </summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Valeur de l'en-tête Allow sur une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentDisposition">
      <summary>Obtient la valeur de l'en-tête de contenu Content-Disposition sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> ;Valeur de l'en-tête de contenu Content-Disposition sur une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentEncoding">
      <summary>Obtient la valeur de l'en-tête de contenu Content-Encoding sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Valeur de l'en-tête de contenu Content-Encoding sur une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLanguage">
      <summary>Obtient la valeur de l'en-tête de contenu Content-Language sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Valeur de l'en-tête de contenu Content-Language sur une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLength">
      <summary>Obtient ou définit la valeur de l'en-tête de contenu Content-Length sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Int64" /> ;Valeur de l'en-tête de contenu Content-Length sur une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLocation">
      <summary>Obtient ou définit la valeur de l'en-tête de contenu Content-Location sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Uri" /> ;Valeur de l'en-tête de contenu Content-Location sur une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentMD5">
      <summary>Obtient ou définit la valeur de l'en-tête de contenu Content-MD5 sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Byte" /> ;Valeur de l'en-tête de contenu Content-MD5 sur une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentRange">
      <summary>Obtient ou définit la valeur de l'en-tête de contenu Content-Range sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> ;Valeur de l'en-tête de contenu Content-Range sur une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentType">
      <summary>Obtient ou définit la valeur de l'en-tête de contenu Content-Type sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> ;Valeur de l'en-tête de contenu Content-Type sur une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Expires">
      <summary>Obtient ou définit la valeur de l'en-tête de contenu Expires sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Valeur de l'en-tête de contenu Expires sur une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.LastModified">
      <summary>Obtient ou définit la valeur de l'en-tête de contenu Last-Modified sur une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Valeur de l'en-tête de contenu Last-Modified sur une réponse HTTP.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaders">
      <summary>Collection d'en-têtes et de leurs valeurs tels que définis dans RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Ajoute l'en-tête spécifié et ses valeurs dans la collection de <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <param name="name">En-tête à ajouter à la collection.</param>
      <param name="values">Liste des valeurs d'en-tête à ajouter à la collection.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.String)">
      <summary>Ajoute l'en-tête spécifié et sa valeur dans la collection de <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <param name="name">En-tête à ajouter à la collection.</param>
      <param name="value">Contenu de l'en-tête.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Clear">
      <summary>Supprime tous les en-têtes de la collection <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Contains(System.String)">
      <summary>Retourne si un en-tête spécifique existe dans la collection <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'en-tête spécifié existe dans la collection ; sinon false.</returns>
      <param name="name">En-tête spécifique.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetEnumerator">
      <summary>Retourne un énumérateur qui peut itérer au sein de l'instance de <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.IEnumerator`1" /> ;Énumérateur pour <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetValues(System.String)">
      <summary>Retourne toutes les valeurs d'en-tête pour un en-tête spécifié stockées dans la collection <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.IEnumerable`1" /> ;Tableau de chaînes d'en-tête.</returns>
      <param name="name">En-tête spécifié pour lequel retourner les valeurs.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Remove(System.String)">
      <summary>Supprime l'en-tête spécifié de la collection <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;</returns>
      <param name="name">Nom de l'en-tête à supprimer de la collection. </param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.System#Collections#IEnumerable#GetEnumerator">
      <summary>Obtient un énumérateur pouvant itérer au sein de <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>retourne <see cref="T:System.Collections.IEnumerator" /> ;Instance d'une implémentation de <see cref="T:System.Collections.IEnumerator" /> pouvant itérer au sein de <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.HttpHeaders" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Retourne une valeur qui indique si l'en-tête spécifié et ses valeurs ont été ajoutés à la collection <see cref="T:System.Net.Http.Headers.HttpHeaders" /> sans valider les informations fournies.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="name" /> et <paramref name="values" /> de l'en-tête spécifié ont pu être ajoutés à la collection ; sinon, false.</returns>
      <param name="name">En-tête à ajouter à la collection.</param>
      <param name="values">Valeur de l'en-tête.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.String)">
      <summary>Retourne une valeur qui indique si l'en-tête spécifié et sa valeur ont été ajoutés à la collection <see cref="T:System.Net.Http.Headers.HttpHeaders" /> sans valider les informations fournies.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="name" /> et <paramref name="value" /> de l'en-tête spécifié ont pu être ajoutés à la collection ; sinon, false.</returns>
      <param name="name">En-tête à ajouter à la collection.</param>
      <param name="value">Contenu de l'en-tête.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
      <summary>Retourne si un en-tête spécifié et les valeurs spécifiées sont stockés dans la collection <see cref="T:System.Net.Http.Headers.HttpHeaders" /> .</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true signifie que les en-têtes spécifiés <paramref name="name" /> et values sont stockés dans la collection ; sinon false.</returns>
      <param name="name">En-tête spécifié.</param>
      <param name="values">Valeurs des en-têtes spécifiées.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaderValueCollection`1">
      <summary>Représente une collection de valeurs d'en-tête.</summary>
      <typeparam name="T">Type de collection d'en-têtes.</typeparam>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Add(`0)">
      <summary>Ajoute une entrée à <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <param name="item">Élément à ajouter à la collection d'en-têtes.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Clear">
      <summary>Supprime toutes les entrées de <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Contains(`0)">
      <summary>Détermine si le <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> contient un élément.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'entrée contient l'instance <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ; sinon, false.</returns>
      <param name="item">Élément à rechercher dans la collection d'en-têtes.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.CopyTo(`0[],System.Int32)">
      <summary>Copie l'ensemble de l'objet <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> vers un objet <see cref="T:System.Array" /> unidimensionnel compatible, en commençant à l'index spécifié du tableau cible.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensionnel qui constitue la destination des éléments copiés à partir d'<see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.<see cref="T:System.Array" /> doit avoir une indexation de base zéro.</param>
      <param name="arrayIndex">Index de base zéro dans <paramref name="array" /> à partir duquel la copie commence.</param>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.Count">
      <summary>Obtient le nombre d'en-têtes contenus dans <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Nombre d'en-têtes contenus dans une collection.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.IEnumerator`1" /> ;Énumérateur pour l'instance <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.IsReadOnly">
      <summary>Obtient une valeur indiquant si l'instance <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> est en lecture seule.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'instance du <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> est en lecture seule ; sinon, false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ParseAdd(System.String)">
      <summary>Analyse et ajoute une entrée à <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <param name="input">Entrée à ajouter.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Remove(`0)">
      <summary>Enlève l'élément spécifié du <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="item" /> a été correctement supprimé de l'instance <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ; sinon, false.</returns>
      <param name="item">Élément à supprimer.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Retourne un énumérateur qui itère au sein de <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>retourne <see cref="T:System.Collections.IEnumerator" /> ;Énumérateur pour l'instance <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> actif.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.TryParseAdd(System.String)">
      <summary>Détermine si l'entrée peut être analysée et ajoutée à <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> peut être analysé et ajouté à l'instance <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />. Sinon, false</returns>
      <param name="input">Entrée à valider.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpRequestHeaders">
      <summary>Représente la collection d'en-têtes de requête comme définie dans RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Accept">
      <summary>Obtient la valeur de l'en-tête Accept pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Accept pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptCharset">
      <summary>Obtient la valeur de l'en-tête Accept-Charset pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Accept-Charset pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptEncoding">
      <summary>Obtient la valeur de l'en-tête Accept-Encoding pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Accept-Encoding pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptLanguage">
      <summary>Obtient la valeur de l'en-tête Accept-Language pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Accept-Language pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Authorization">
      <summary>Obtient ou définit la valeur de l'en-tête Authorization pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> ;Valeur de l'en-tête Authorization pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.CacheControl">
      <summary>Obtient ou définit la valeur de l'en-tête Cache-Control pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> ;Valeur de l'en-tête Cache-Control pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Connection">
      <summary>Obtient la valeur de l'en-tête Connection pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Connection pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ConnectionClose">
      <summary>Obtient ou définit une valeur qui indique si l'en-tête Connection pour une requête HTTP contient Close.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'en-tête Connection contient Close ; sinon false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Date">
      <summary>Obtient ou définit la valeur de l'en-tête Date pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Valeur de l'en-tête Date pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Expect">
      <summary>Obtient la valeur de l'en-tête Expect pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Expect pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ExpectContinue">
      <summary>Obtient ou définit une valeur qui indique si l'en-tête Expect pour une requête HTTP contient Continue.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'en-tête Expect contient Continue ; sinon false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.From">
      <summary>Obtient ou définit la valeur de l'en-tête From pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Valeur de l'en-tête From pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Host">
      <summary>Obtient ou définit la valeur de l'en-tête Host pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Valeur de l'en-tête Host pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfMatch">
      <summary>Obtient la valeur de l'en-tête If-Match pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête If-Match pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfModifiedSince">
      <summary>Obtient ou définit la valeur de l'en-tête If-Modified-Since pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Valeur de l'en-tête If-Modified-Since pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfNoneMatch">
      <summary>Obtient la valeur de l'en-tête If-None-Match pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Obtient la valeur de l'en-tête If-None-Match pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfRange">
      <summary>Obtient ou définit la valeur de l'en-tête If-Range pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> ;Valeur de l'en-tête If-Range pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfUnmodifiedSince">
      <summary>Obtient ou définit la valeur de l'en-tête If-Unmodified-Since pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Valeur de l'en-tête If-Unmodified-Since pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.MaxForwards">
      <summary>Obtient ou définit la valeur de l'en-tête Max-Forwards pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Valeur de l'en-tête Max-Forwards pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Pragma">
      <summary>Obtient la valeur de l'en-tête Pragma pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Pragma pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ProxyAuthorization">
      <summary>Obtient ou définit la valeur de l'en-tête Proxy-Authorization pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> ;Valeur de l'en-tête Proxy-Authorization pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Range">
      <summary>Obtient ou définit la valeur de l'en-tête Range pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> ;Valeur de l'en-tête Range pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Referrer">
      <summary>Obtient ou définit la valeur de l'en-tête Referer pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Uri" /> ;Valeur de l'en-tête Referer pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TE">
      <summary>Obtient la valeur de l'en-tête TE pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête TE pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Trailer">
      <summary>Obtient la valeur de l'en-tête Trailer pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Trailer pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncoding">
      <summary>Obtient la valeur de l'en-tête Transfer-Encoding pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Transfer-Encoding pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncodingChunked">
      <summary>Obtient ou définit une valeur qui indique si l'en-tête Transfer-Encoding pour une requête HTTP contient Chunked.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'en-tête Transfer-Encoding correspond à un encodage de transfert mémorisé en bloc ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Upgrade">
      <summary>Obtient la valeur de l'en-tête Upgrade pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Upgrade pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.UserAgent">
      <summary>Obtient la valeur de l'en-tête User-Agent pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête User-Agent pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Via">
      <summary>Obtient la valeur de l'en-tête Via pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Via pour une requête HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Warning">
      <summary>Obtient la valeur de l'en-tête Warning pour une requête HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Warning pour une requête HTTP.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpResponseHeaders">
      <summary>Représente la collection d'en-têtes de réponse comme définie dans RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.AcceptRanges">
      <summary>Obtient la valeur de l'en-tête Accept-Ranges pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Accept-Ranges pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Age">
      <summary>Obtient ou définit la valeur de l'en-tête Age pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.TimeSpan" /> ;Valeur de l'en-tête Age pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.CacheControl">
      <summary>Obtient ou définit la valeur de l'en-tête Cache-Control pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> ;Valeur de l'en-tête Cache-Control pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Connection">
      <summary>Obtient la valeur de l'en-tête Connection pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Connection pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ConnectionClose">
      <summary>Obtient ou définit une valeur qui indique si l'en-tête Connection pour une réponse HTTP contient Close.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'en-tête Connection contient Close ; sinon false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Date">
      <summary>Obtient ou définit la valeur de l'en-tête Date pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Valeur de l'en-tête Date pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ETag">
      <summary>Obtient ou définit la valeur de l'en-tête ETag pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> ;Valeur de l'en-tête ETag pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Location">
      <summary>Obtient ou définit la valeur de l'en-tête Location pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Uri" /> ;Valeur de l'en-tête Location pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Pragma">
      <summary>Obtient la valeur de l'en-tête Pragma pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Pragma pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ProxyAuthenticate">
      <summary>Obtient la valeur de l'en-tête Proxy-Authenticate pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Proxy-Authenticate pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.RetryAfter">
      <summary>Obtient ou définit la valeur de l'en-tête Retry-After pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> ;Valeur de l'en-tête Retry-After pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Server">
      <summary>Obtient la valeur de l'en-tête Server pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Server pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Trailer">
      <summary>Obtient la valeur de l'en-tête Trailer pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Trailer pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncoding">
      <summary>Obtient la valeur de l'en-tête Transfer-Encoding pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Transfer-Encoding pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncodingChunked">
      <summary>Obtient ou définit une valeur qui indique si l'en-tête Transfer-Encoding pour une réponse HTTP contient Chunked.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'en-tête Transfer-Encoding correspond à un encodage de transfert mémorisé en bloc ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Upgrade">
      <summary>Obtient la valeur de l'en-tête Upgrade pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Upgrade pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Vary">
      <summary>Obtient la valeur de l'en-tête Vary pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Vary pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Via">
      <summary>Obtient la valeur de l'en-tête Via pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Via pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Warning">
      <summary>Obtient la valeur de l'en-tête Warning pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête Warning pour une réponse HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.WwwAuthenticate">
      <summary>Obtient la valeur de l'en-tête WWW-Authenticate pour une réponse HTTP.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ;Valeur de l'en-tête WWW-Authenticate pour une réponse HTTP.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeHeaderValue">
      <summary>Représente un type de média utilisé dans un en-tête Content-Type défini dans la norme RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <param name="source"> Objet <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> utilisé pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <param name="mediaType">Source représentée sous forme de chaîne pour initialiser la nouvelle instance. </param>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.CharSet">
      <summary>Obtient ou définit le jeu de caractères.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Jeu de caractères.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.MediaType">
      <summary>Obtient ou définit la valeur de l'en-tête de type de média.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Valeur d'en-tête de type média.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.Parameters">
      <summary>Obtient ou définit les paramètres de la valeur d'en-tête de type de média.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Paramètres des valeurs d'en-tête de type média.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête du type de média.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête de type de média valides.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue">
      <summary>Représente un type de média avec une considération de qualité supplémentaire, utilisé dans un en-tête Content-Type.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <param name="mediaType">
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> représenté sous forme de chaîne pour initialiser la nouvelle instance. </param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <param name="mediaType">
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> représenté sous forme de chaîne pour initialiser la nouvelle instance.</param>
      <param name="quality">Qualité associée à cette valeur d'en-tête.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente le type de média avec les informations de valeur d'en-tête de qualité.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> n'est pas un type de média valide avec des informations de valeur d'en-tête de qualité.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Quality">
      <summary>Obtenir ou définir le critère de qualité pour <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Double" /> ;Critère de qualité pour l'objet <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeWithQualityHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueHeaderValue">
      <summary>Représente une paire nom/valeur utilisée dans différents en-têtes comme défini dans la norme RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.Net.Http.Headers.NameValueHeaderValue)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <param name="source">Objet <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> utilisé pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <param name="name">Nom de l'en-tête.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <param name="name">Nom de l'en-tête.</param>
      <param name="value">Valeur de l'en-tête.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Name">
      <summary>Obtient le nom de l'en-tête.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Nom de l'en-tête.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête de la valeur de nom.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête de valeur de nom valides.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Value">
      <summary>Obtient la valeur de l'en-tête.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Valeur de l'en-tête.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue">
      <summary>Représente une paire nom/valeur avec des paramètres utilisés dans différents en-têtes comme défini dans la norme RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.Net.Http.Headers.NameValueWithParametersHeaderValue)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <param name="source">Objet <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> utilisé pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <param name="name">Nom de l'en-tête.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <param name="name">Nom de l'en-tête.</param>
      <param name="value">Valeur de l'en-tête.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parameters">
      <summary>Obtient les paramètres de l'objet <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Collection contenant les paramètres.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</returns>
      <param name="input">Chaîne représentant la valeur de nom avec les informations de valeur d'en-tête de paramètre.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> n'est pas une valeur de nom valide avec des informations de valeur d'en-tête de paramètre.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueWithParametersHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ProductHeaderValue">
      <summary>Représente une valeur de jeton de produit dans un en-tête d'agent utilisateur.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <param name="name">Nom du produit.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <param name="name">Valeur du nom de produit.</param>
      <param name="version">Valeur de la version du produit.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Name">
      <summary>Obtient le nom du jeton du produit.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Nom du jeton de produit.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête du produit.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Version">
      <summary>Obtient la version du jeton du produit.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Version du jeton de produit. </returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductInfoHeaderValue">
      <summary>Représente une valeur qui peut être un produit ou un commentaire dans un en-tête User-Agent.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.Net.Http.Headers.ProductHeaderValue)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <param name="product">Objet <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> utilisé pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <param name="comment">Valeur de commentaire.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <param name="productName">Valeur du nom de produit.</param>
      <param name="productVersion">Valeur de la version du produit.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Comment">
      <summary>Obtient le commentaire de l'objet <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Valeur de commentaire de ce <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête des informations.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête d'informations produit valides.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Product">
      <summary>Obtient le produit de l'objet <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> ;Valeur de produit de cet <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductInfoHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeConditionHeaderValue">
      <summary>Représente une valeur d'en-tête If-Range qui peut être une date, une heure ou une valeur de balise d'entité.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <param name="date">Valeur de date utilisée pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.Net.Http.Headers.EntityTagHeaderValue)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <param name="entityTag">Objet <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> utilisé pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <param name="entityTag">Balise d'entité représentée sous forme de chaîne utilisée pour initialiser la nouvelle instance.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.Date">
      <summary>Obtient la date de l'objet <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Date de l'objet <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.EntityTag">
      <summary>Obtient la balise d'entité de l'objet <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> ;Balise d'entité de l'objet <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête de la condition de plage.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête de condition de plage valides.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeConditionHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeHeaderValue">
      <summary>Représente une valeur d'en-tête de plage.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> avec une plage d'octets.</summary>
      <param name="from">Position à laquelle démarrer l'envoi de données.</param>
      <param name="to">Position à laquelle arrêter l'envoi de données.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> est supérieur à <paramref name="to" />.ou <paramref name="from" /> ou <paramref name="to" /> est inférieur à 0. </exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête de plage.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête de plage valides.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Ranges">
      <summary>Obtient les plages spécifiées à partir de l'objet <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Plages de l'objet <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Unit">
      <summary>Obtient l'unité de l'objet <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Unité de l'objet <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeItemHeaderValue">
      <summary>Représente une valeur d'en-tête de plage.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />.</summary>
      <param name="from">Position à laquelle démarrer l'envoi de données.</param>
      <param name="to">Position à laquelle arrêter l'envoi de données.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> est supérieur à <paramref name="to" />.ou <paramref name="from" /> ou <paramref name="to" /> est inférieur à 0. </exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.From">
      <summary>Obtient la position à laquelle démarrer l'envoi de données.</summary>
      <returns>retourne <see cref="T:System.Int64" /> ;Position à laquelle démarrer l'envoi de données.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.To">
      <summary>Obtient la position à laquelle arrêter l'envoi de données. </summary>
      <returns>retourne <see cref="T:System.Int64" /> ;Position à laquelle arrêter l'envoi de données. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RetryConditionHeaderValue">
      <summary>Représente une valeur d'en-tête Retry-After qui peut être une date, une heure ou une valeur TimeSpan.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <param name="date">Offset de la date et de l'heure utilisé pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.TimeSpan)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <param name="delta">Différentiel, en secondes, utilisé pour initialiser la nouvelle instance.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Date">
      <summary>Obtient l'offset de la date et de l'heure de l'objet <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Offset de la date et de l'heure de l'objet <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Delta">
      <summary>Obtient le delta en secondes de l'objet <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.TimeSpan" /> ;Différentiel en secondes de l'objet <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête de la condition de nouvelle tentative.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête de condition de nouvelle tentative valides.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RetryConditionHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.StringWithQualityHeaderValue">
      <summary>Représente une valeur d'en-tête de chaîne avec une qualité facultative.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <param name="value">Chaîne utilisée pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <param name="value">Chaîne utilisée pour initialiser la nouvelle instance.</param>
      <param name="quality">Considération de qualité utilisée pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> en cours.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête de qualité.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> n'est pas une chaîne valide avec des informations de valeur d'en-tête de qualité.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Quality">
      <summary>Obtient la considération de qualité de l'objet <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Double" /> ;Considération de qualité de l'objet <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.StringWithQualityHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Value">
      <summary>Obtient la valeur de chaîne de l'objet <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Valeur de chaîne de l'objet <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingHeaderValue">
      <summary>Représente une valeur d'en-tête Accept-Encoding.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.Net.Http.Headers.TransferCodingHeaderValue)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <param name="source">Objet <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> utilisé pour initialiser la nouvelle instance. </param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <param name="value">Chaîne utilisée pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> en cours.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Parameters">
      <summary>Obtient les paramètres d'encodage de transfert.</summary>
      <returns>retourne <see cref="T:System.Collections.Generic.ICollection`1" /> ;Paramètres d'encodage de transfert.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête de l'encodage de transfert.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête d'encodage de transfert valides.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Value">
      <summary>Obtient la valeur d'encodage de transfert.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Valeur d'encodage de transfert.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue">
      <summary>Représente une valeur d'en-tête Accept-Encoding avec une considération de qualité facultative.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <param name="value">Chaîne utilisée pour initialiser la nouvelle instance.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <param name="value">Chaîne utilisée pour initialiser la nouvelle instance.</param>
      <param name="quality">Valeur pour la considération de qualité.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur de l'encodage de transfert.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> n'est pas un encodage de transfert valide avec des informations de valeur d'en-tête de qualité.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Quality">
      <summary>Obtient la considération de qualité de <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Double" /> ;Considération de qualité de <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingWithQualityHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ViaHeaderValue">
      <summary>Représente la valeur d'un en-tête Via.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <param name="protocolVersion">Version de protocole du protocole reçu.</param>
      <param name="receivedBy">Hôte et port via lesquels la requête ou la réponse a été reçue.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <param name="protocolVersion">Version de protocole du protocole reçu.</param>
      <param name="receivedBy">Hôte et port via lesquels la requête ou la réponse a été reçue.</param>
      <param name="protocolName">Nom de protocole du protocole reçu.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <param name="protocolVersion">Version de protocole du protocole reçu.</param>
      <param name="receivedBy">Hôte et port via lesquels la requête ou la réponse a été reçue.</param>
      <param name="protocolName">Nom de protocole du protocole reçu.</param>
      <param name="comment">Champ de commentaire utilisé pour identifier le logiciel de la passerelle ou du proxy destinataire.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.Comment">
      <summary>Obtient le champ de commentaire utilisé pour identifier le logiciel de la passerelle ou du proxy destinataire</summary>
      <returns>retourne <see cref="T:System.String" /> ;Champ de commentaire utilisé pour identifier le logiciel de la passerelle ou du proxy destinataire.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Retourne un code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> ;Instance de <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> n'est pas valide pour les informations de valeur d'en-tête.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolName">
      <summary>Obtient le nom de protocole du protocole reçu.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Le nom du protocole.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolVersion">
      <summary>Obtient la version de protocole du protocole reçu.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Version du protocole.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ReceivedBy">
      <summary>Obtient l'hôte et le port via lesquels la requête ou la réponse a été reçue.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Hôte et port via lesquels la requête ou la réponse a été reçue.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.TryParse(System.String,System.Net.Http.Headers.ViaHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> de la chaîne.</param>
    </member>
    <member name="T:System.Net.Http.Headers.WarningHeaderValue">
      <summary>Représente une valeur d'avertissement utilisée par l'en-tête Warning.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <param name="code">Code d'avertissement spécifique.</param>
      <param name="agent">Hôte ayant attaché l'avertissement.</param>
      <param name="text">Chaîne entre guillemets contenant le texte d'avertissement.</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String,System.DateTimeOffset)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <param name="code">Code d'avertissement spécifique.</param>
      <param name="agent">Hôte ayant attaché l'avertissement.</param>
      <param name="text">Chaîne entre guillemets contenant le texte d'avertissement.</param>
      <param name="date">Les informations de date et d'heure de l'avertissement.</param>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Agent">
      <summary>Obtient l'hôte ayant attaché l'avertissement.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Hôte ayant attaché l'avertissement.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Code">
      <summary>Obtient le code d'avertissement spécifique.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code d'avertissement spécifique.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Date">
      <summary>Obtient les informations de date et d'heure de l'avertissement.</summary>
      <returns>retourne <see cref="T:System.DateTimeOffset" /> ;Les informations de date et d'heure de l'avertissement.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Equals(System.Object)">
      <summary>Détermine si l'objet <see cref="T:System.Object" /> spécifié est égal à l'objet <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si l'<see cref="T:System.Object" /> spécifié est égal à l'objet actif ; sinon, false.</returns>
      <param name="obj">Objet à comparer avec l'objet actif.</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.GetHashCode">
      <summary>Sert de fonction de hachage pour un objet <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <returns>retourne <see cref="T:System.Int32" /> ;Code de hachage pour l'objet en cours.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Parse(System.String)">
      <summary>Convertit une chaîne en instance <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <returns>Retourne une instance de <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</returns>
      <param name="input">Chaîne qui représente les informations de valeur d'en-tête d'authentification.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> est une référence null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ne correspond pas à des informations de valeur d'en-tête d'authentification valides.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Text">
      <summary>Obtient une chaîne entre guillemets contenant le texte d'avertissement.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne entre guillemets contenant le texte d'avertissement.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.ToString">
      <summary>Retourne une chaîne qui représente l'objet <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> actuel.</summary>
      <returns>retourne <see cref="T:System.String" /> ;Chaîne qui représente l'objet actif.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.TryParse(System.String,System.Net.Http.Headers.WarningHeaderValue@)">
      <summary>Détermine si une chaîne correspond à des informations <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> valides.</summary>
      <returns>retourne <see cref="T:System.Boolean" /> ;true si <paramref name="input" /> est valide <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> ; sinon, false.</returns>
      <param name="input">Chaîne à valider.</param>
      <param name="parsedValue">Version <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> de la chaîne.</param>
    </member>
  </members>
</doc>