﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;

namespace RevCord.DataContracts.IWBEntities
{
    public class Organization
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public OrganizationType Type { get; set; }

        public IList<OrganizationLocation> Locations { get; set; }

        public int CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int? LastModifiedBy { get; set; }


    }
}
