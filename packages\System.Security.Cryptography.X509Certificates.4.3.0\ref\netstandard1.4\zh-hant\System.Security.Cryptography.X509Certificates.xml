﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.X509Certificates</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle">
      <summary>提供表示 X.509 鏈結的安全控制代碼。如需詳細資訊，請參閱<see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />。</summary>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeX509ChainHandle.IsInvalid"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.OpenFlags">
      <summary>指定開啟 X.509 憑證存放區的方式。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.IncludeArchived">
      <summary>開啟 X.509 憑證存放區並加入封存的憑證。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.MaxAllowed">
      <summary>開啟最高存取權所允許的 X.509 憑證存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.OpenExistingOnly">
      <summary>開啟僅有的存放區。如果沒有存放區，<see cref="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)" /> 方法也不會建立新的存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadOnly">
      <summary>開啟只用於讀取的 X.509 憑證存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadWrite">
      <summary>開啟同時用於讀取和寫入的 X.509 憑證存放區。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.PublicKey">
      <summary>表示憑證的公開金鑰 (Public Key) 資訊。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.#ctor(System.Security.Cryptography.Oid,System.Security.Cryptography.AsnEncodedData,System.Security.Cryptography.AsnEncodedData)">
      <summary>使用公開金鑰的物件識別項 (OID) 物件、公開金鑰參數的 ASN.1 編碼表示和公開金鑰值的 ASN.1 編碼表示，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> 類別的新執行個體。</summary>
      <param name="oid">物件識別項 (OID) 物件，表示公開金鑰。</param>
      <param name="parameters">公開金鑰參數的 ASN.1 編碼表示。</param>
      <param name="keyValue">公開金鑰值的 ASN.1 編碼表示。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedKeyValue">
      <summary>取得公開金鑰值的 ASN.1 編碼表示。</summary>
      <returns>公開金鑰值的 ASN.1 編碼表示。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedParameters">
      <summary>取得公開金鑰參數的 ASN.1 編碼表示。</summary>
      <returns>公開金鑰參數的 ASN.1 編碼表示。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Key">
      <summary>取得 <see cref="T:System.Security.Cryptography.RSACryptoServiceProvider" /> 或 <see cref="T:System.Security.Cryptography.DSACryptoServiceProvider" /> 物件，表示公開金鑰。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> 物件，表示公開金鑰。</returns>
      <exception cref="T:System.NotSupportedException">不支援此金鑰演算法。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Oid">
      <summary>取得公開金鑰的物件識別項 (OID) 物件。</summary>
      <returns>公開金鑰的物件識別項 (OID) 物件。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreLocation">
      <summary>指定 X.509 憑證存放區的位置。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.CurrentUser">
      <summary>目前使用者使用的 X.509 憑證存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.LocalMachine">
      <summary>指派至本機電腦的 X.509 憑證存放區。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreName">
      <summary>指定要開啟之 X.509 憑證存放區的名稱。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AddressBook">
      <summary>其他使用者的 X.509 憑證存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AuthRoot">
      <summary>協力廠商憑證授權單位 (CA) 的 X.509 憑證存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.CertificateAuthority">
      <summary>中繼憑證授權單位 (CA) 的 X.509 憑證存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Disallowed">
      <summary>已撤銷之憑證的 X.509 憑證存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.My">
      <summary>個人憑證的 X.509 憑證存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Root">
      <summary>信任之根憑證授權單位 (CA) 的 X.509 憑證存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPeople">
      <summary>直接信任之人員和資源的 X.509 憑證存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPublisher">
      <summary>直接信任之發行者的 X.509 憑證存放區。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName">
      <summary>表示 X509 憑證的辨別名稱。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Byte[])">
      <summary>使用來自指定位元組陣列的資訊，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 類別的新執行個體。</summary>
      <param name="encodedDistinguishedName">包含辨別名稱資訊的位元組陣列。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>使用指定的 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 類別的新執行個體。</summary>
      <param name="encodedDistinguishedName">表示辨別名稱的 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.X509Certificates.X500DistinguishedName)">
      <summary>使用指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 物件，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 類別的新執行個體。</summary>
      <param name="distinguishedName">
        <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 物件。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String)">
      <summary>使用來自指定字串的資訊，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 類別的新執行個體。</summary>
      <param name="distinguishedName">表示辨別名稱的字串。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String,System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>使用指定的字串和 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags" /> 旗標，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 類別的新執行個體。</summary>
      <param name="distinguishedName">表示辨別名稱的字串。</param>
      <param name="flag">列舉值的位元組合，這些值會指定辨別名稱的特性。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Decode(System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>使用 <paramref name="flag" /> 參數所指定的特性，對辨別名稱進行解碼。</summary>
      <returns>解碼的辨別名稱。</returns>
      <param name="flag">列舉值的位元組合，這些值會指定辨別名稱的特性。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證的名稱無效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Format(System.Boolean)">
      <summary>傳回 X500 辨別名稱的格式化版本，用於列印或輸出至文字視窗或主控台 (Console)。</summary>
      <returns>格式化字串，表示 X500 辨別名稱。</returns>
      <param name="multiLine">如果傳回的字串應包含歸位字元，則為 true，否則為 false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Name">
      <summary>從 X500 憑證取得逗號分隔的辨別名稱。</summary>
      <returns>X509 憑證的逗號分隔辨別名稱。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags">
      <summary>指定 X.500 辨別名稱的特性。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUsePlusSign">
      <summary>辨別名稱不使用加號。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUseQuotes">
      <summary>辨別名稱不使用引號。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.ForceUTF8Encoding">
      <summary>強制辨別名稱將特定 X.500 金鑰編碼為 UTF-8 字串，而非可列印的 Unicode 字串。如需詳細資訊，以及受影響的 X.500 金鑰清單，請參閱 X500NameFlags 列舉。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.None">
      <summary>辨別名稱沒有特殊的特性。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.Reversed">
      <summary>辨別名稱會反轉。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseCommas">
      <summary>辨別名稱使用逗號。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseNewLines">
      <summary>辨別名稱使用新行字元。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseSemicolons">
      <summary>辨別名稱使用分號。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseT61Encoding">
      <summary>辨別名稱使用 T61 編碼。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseUTF8Encoding">
      <summary>辨別名稱使用 UTF8 編碼，而不是 Unicode 字元編碼。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension">
      <summary>定義憑證上設定的條件約束。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Boolean,System.Boolean,System.Int32,System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 類別的新執行個體。參數會指定下列項目：指出憑證是否為憑證授權單位 (CA) 憑證的值、指出憑證是否限制其允許之路徑層級數目的值、憑證路徑中允許的層級數目，以及指出擴充功能是否重要的值。</summary>
      <param name="certificateAuthority">如果憑證為憑證授權單位 (CA) 憑證，則為 true，否則為 false。</param>
      <param name="hasPathLengthConstraint">如果憑證限制它允許的路徑層級數目，則為 true，否則為 false。</param>
      <param name="pathLengthConstraint">憑證路徑中允許的層級數目。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件和識別擴充功能是否重要的值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 類別的新執行個體。</summary>
      <param name="encodedBasicConstraints">用來建立擴充功能的編碼資料。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CertificateAuthority">
      <summary>取得值，指出憑證是否為憑證授權單位 (CA) 憑證。</summary>
      <returns>如果憑證為憑證授權單位 (CA) 憑證，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> 類別的新執行個體。</summary>
      <param name="asnEncodedData">用來建立擴充功能的編碼資料。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.HasPathLengthConstraint">
      <summary>取得值，指出憑證是否限制它允許的路徑層級數目。</summary>
      <returns>如果憑證限制它允許的路徑層級數目，則為 true，否則為 false。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">無法解碼擴充功能。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.PathLengthConstraint">
      <summary>取得憑證路徑中允許的層級數目。</summary>
      <returns>整數，指出憑證路徑中允許的層級數目。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">無法解碼擴充功能。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate">
      <summary>提供協助您使用 X.509 v.3 憑證的方法。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[])">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 類別的新執行個體 (這個類別是在表示 X.509v3 憑證的位元組序列中定義的)。</summary>
      <param name="data">位元組陣列，包含來自 X.509 憑證的資料。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 參數為 null。-或-<paramref name="rawData" /> 參數的長度為 0。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String)">
      <summary>使用位元組陣列和密碼，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 類別的新執行個體。</summary>
      <param name="rawData">位元組陣列，包含來自 X.509 憑證的資料。</param>
      <param name="password">存取 X.509 憑證資料所需的密碼。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 參數為 null。-或-<paramref name="rawData" /> 參數的長度為 0。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>使用位元組陣列、密碼和金鑰儲存旗標，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 類別的新執行個體。</summary>
      <param name="rawData">位元組陣列，包含來自 X.509 憑證的資料。</param>
      <param name="password">存取 X.509 憑證資料所需的密碼。</param>
      <param name="keyStorageFlags">列舉值的位元組合，會控制匯入憑證的位置和方式。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 參數為 null。-或-<paramref name="rawData" /> 參數的長度為 0。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.IntPtr)">
      <summary>[安全性關鍵] 使用 Unmanaged PCCERT_CONTEXT 結構的控制代碼，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 類別的新執行個體。</summary>
      <param name="handle">Unmanaged PCCERT_CONTEXT 結構的控制代碼。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String)">
      <summary>使用 PKCS7 已簽署檔案的名稱，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 類別的新執行個體。</summary>
      <param name="fileName">PKCS7 已簽署檔案的名稱。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String)">
      <summary>使用 PKCS7 已簽署檔案和用於存取憑證的密碼，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 類別的新執行個體。</summary>
      <param name="fileName">PKCS7 已簽署檔案的名稱。</param>
      <param name="password">存取 X.509 憑證資料所需的密碼。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>使用 PKCS7 已簽署檔案、用於存取憑證的密碼和金鑰儲存旗標，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 類別的新執行個體。</summary>
      <param name="fileName">PKCS7 已簽署檔案的名稱。</param>
      <param name="password">存取 X.509 憑證資料所需的密碼。</param>
      <param name="keyStorageFlags">列舉值的位元組合，會控制匯入憑證的位置和方式。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose">
      <summary>釋放由目前的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件使用的所有資源。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose(System.Boolean)">
      <summary>這所使用的 unmanaged 資源全部釋出<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />並選擇性地釋放 managed 的資源。 </summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源，false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Object)">
      <summary>比較兩個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件是否相等。</summary>
      <returns>如果目前的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件等於 <paramref name="other" /> 參數所指定的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件。 </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>比較兩個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件是否相等。</summary>
      <returns>如果目前的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件等於 <paramref name="other" /> 參數所指定的物件，則為 true，否則為 false。</returns>
      <param name="other">要與目前物件比較的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>以其中一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 值所描述的格式，將目前的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件匯出至位元組陣列。</summary>
      <returns>位元組的陣列，表示目前的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件。</returns>
      <param name="contentType">其中一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 值，其描述如何格式化輸出資料。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">將 <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />、<see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" /> 或 <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> 以外的值傳遞至 <paramref name="contentType" /> 參數。-或-無法匯出憑證。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>以其中一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 值所描述的格式，並使用指定的密碼，將目前的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件匯出至位元組陣列。</summary>
      <returns>位元組的陣列，表示目前的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件。</returns>
      <param name="contentType">其中一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 值，其描述如何格式化輸出資料。</param>
      <param name="password">存取 X.509 憑證資料所需的密碼。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">將 <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />、<see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" /> 或 <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> 以外的值傳遞至 <paramref name="contentType" /> 參數。-或-無法匯出憑證。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHash">
      <summary>將 X.509v3 憑證的雜湊值 (Hash Value) 傳回為位元組陣列。</summary>
      <returns>X.509 憑證的雜湊值。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetFormat">
      <summary>傳回這個 X.509v3 憑證的格式名稱。</summary>
      <returns>這個 X.509 憑證的格式。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetHashCode">
      <summary>將 X.509v3 憑證的雜湊程式碼傳回為整數。</summary>
      <returns>做為整數的 X.509 憑證雜湊碼。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithm">
      <summary>傳回做為字串的這個 X.509v3 憑證金鑰演算法資訊。</summary>
      <returns>做為字串的這個 X.509 憑證金鑰演算法資訊。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證內容無效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParameters">
      <summary>傳回做為位元組陣列的 X.509v3 憑證金鑰演算法參數。</summary>
      <returns>做為位元組陣列的 X.509 憑證金鑰演算法參數。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證內容無效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParametersString">
      <summary>傳回做為十六進位字串的 X.509v3 憑證金鑰演算法參數。</summary>
      <returns>做為十六進位字串的 X.509 憑證金鑰演算法參數。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證內容無效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetPublicKey">
      <summary>傳回做為位元組陣列的 X.509v3 憑證公開金鑰。</summary>
      <returns>做為位元組陣列的 X.509 憑證公開金鑰。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證內容無效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumber">
      <summary>傳回做為位元組陣列的 X.509v3 憑證序號。</summary>
      <returns>做為位元組陣列的 X.509 憑證序號。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證內容無效。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Handle">
      <summary>[安全性關鍵] 取得 Unmanaged PCCERT_CONTEXT 結構所描述之 Microsoft Cryptographic API 憑證內容的控制代碼。</summary>
      <returns>
        <see cref="T:System.IntPtr" /> 結構，表示 Unmanaged PCCERT_CONTEXT 結構。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Issuer">
      <summary>取得核發 X.509v3 憑證的憑證授權單位名稱。</summary>
      <returns>核發 X.509v3 憑證的憑證授權單位名稱。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證控制代碼無效。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Subject">
      <summary>取得憑證的主旨辨別名稱。</summary>
      <returns>憑證的主旨辨別名稱。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證控制代碼無效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString">
      <summary>傳回目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件的字串表示。</summary>
      <returns>目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件的字串表示。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString(System.Boolean)">
      <summary>傳回目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件的字串表示，如果指定，則附帶額外資訊。</summary>
      <returns>目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件的字串表示。</returns>
      <param name="fVerbose">若要產生字串表示的詳細資訊形式，則為 true，否則為 false。</param>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2">
      <summary>表示 X.509 憑證。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[])">
      <summary>使用位元組陣列中的資訊，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 類別的新執行個體。</summary>
      <param name="rawData">位元組陣列，包含來自 X.509 憑證的資料。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String)">
      <summary>使用位元組陣列和密碼，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 類別的新執行個體。</summary>
      <param name="rawData">位元組陣列，包含來自 X.509 憑證的資料。</param>
      <param name="password">存取 X.509 憑證資料所需的密碼。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>使用位元組陣列、密碼和金鑰儲存旗標，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 類別的新執行個體。</summary>
      <param name="rawData">位元組陣列，包含來自 X.509 憑證的資料。</param>
      <param name="password">存取 X.509 憑證資料所需的密碼。</param>
      <param name="keyStorageFlags">列舉值的位元組合，會控制匯入憑證的位置和方式。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.IntPtr)">
      <summary>使用 Unmanaged 控制代碼，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 類別的新執行個體。</summary>
      <param name="handle">指向 Unmanaged 程式碼中憑證內容的指標。C 結構稱為 PCCERT_CONTEXT。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String)">
      <summary>使用憑證檔名，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 類別的新執行個體。</summary>
      <param name="fileName">憑證檔的名稱。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String)">
      <summary>使用憑證檔名和用於存取憑證的密碼，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 類別的新執行個體。</summary>
      <param name="fileName">憑證檔的名稱。</param>
      <param name="password">存取 X.509 憑證資料所需的密碼。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>使用憑證檔名、用於存取憑證的密碼和金鑰儲存旗標，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 類別的新執行個體。</summary>
      <param name="fileName">憑證檔的名稱。</param>
      <param name="password">存取 X.509 憑證資料所需的密碼。</param>
      <param name="keyStorageFlags">列舉值的位元組合，會控制匯入憑證的位置和方式。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">此憑證發生錯誤。例如：憑證檔案不存在。此憑證無效。此憑證的密碼不正確。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Archived">
      <summary>取得或設定值，表示 X.509 憑證已封存。</summary>
      <returns>如果憑證已封存，則為 true，否則為 false。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證無法讀取。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Extensions">
      <summary>取得 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件的集合。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證無法讀取。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.FriendlyName">
      <summary>取得或設定憑證的相關別名 (Alias)。</summary>
      <returns>憑證的易記名稱。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證無法讀取。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.Byte[])">
      <summary>表示位元組陣列中包含的憑證類型。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 物件。</returns>
      <param name="rawData">位元組陣列，包含來自 X.509 憑證的資料。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> 長度為零或為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.String)">
      <summary>表示檔案中包含的憑證類型。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 物件。</returns>
      <param name="fileName">憑證檔的名稱。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> 為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetNameInfo(System.Security.Cryptography.X509Certificates.X509NameType,System.Boolean)">
      <summary>取得憑證的主旨和簽發者名稱。</summary>
      <returns>憑證的名稱。</returns>
      <param name="nameType">主旨的 <see cref="T:System.Security.Cryptography.X509Certificates.X509NameType" /> 值。</param>
      <param name="forIssuer">true 表示包含簽發者名稱，否則為 false。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey">
      <summary>取得值，指出 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件是否包含私密金鑰。</summary>
      <returns>如果 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件包含私密金鑰，則為 true，否則為 false。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證內容無效。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.IssuerName">
      <summary>取得憑證簽發者的辨別名稱。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 物件，包含憑證簽發者的名稱。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證內容無效。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotAfter">
      <summary>取得日期 (當地時間)，憑證在該日期之後就不再有效。</summary>
      <returns>
        <see cref="T:System.DateTime" /> 物件，表示憑證的到期日。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證無法讀取。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotBefore">
      <summary>取得日期 (當地時間)，憑證會在該日期生效。</summary>
      <returns>
        <see cref="T:System.DateTime" /> 物件，表示憑證的生效日期。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證無法讀取。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PrivateKey">
      <summary>取得或設定 <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> 物件，表示與憑證相關聯的私密金鑰。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> 物件，這個物件是 RSA 或 DSA 密碼編譯服務提供者。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">金鑰值不是 RSA 或 DSA 金鑰，或該金鑰無法讀取。</exception>
      <exception cref="T:System.ArgumentNullException">這個屬性目前設定的值為 null。</exception>
      <exception cref="T:System.NotSupportedException">不支援這個私密金鑰的金鑰演算法。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">X.509 金鑰不相符。</exception>
      <exception cref="T:System.ArgumentException">密碼編譯服務提供者金鑰為 null。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey">
      <summary>取得與憑證相關聯的 <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" /> 物件。</summary>
      <returns>
        <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" /> 物件。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">金鑰值不是 RSA 或 DSA 金鑰，或該金鑰無法讀取。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.RawData">
      <summary>取得憑證的未經處理資料 (Raw Data)。</summary>
      <returns>做為位元組陣列之憑證的未經處理資料。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SerialNumber">
      <summary>取得憑證的序號。</summary>
      <returns>憑證的序號。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SignatureAlgorithm">
      <summary>取得用於建立憑證簽章的演算法。</summary>
      <returns>傳回簽章演算法的物件識別項 (<see cref="T:System.Security.Cryptography.Oid" />)。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證無法讀取。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SubjectName">
      <summary>取得憑證的主旨辨別名稱。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> 物件，表示憑證主旨的名稱。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證內容無效。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Thumbprint">
      <summary>取得憑證的指模。</summary>
      <returns>憑證的指模。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString">
      <summary>以文字格式顯示 X.509 憑證。</summary>
      <returns>憑證資訊。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString(System.Boolean)">
      <summary>以文字格式顯示 X.509 憑證。</summary>
      <returns>憑證資訊。</returns>
      <param name="verbose">true 表示顯示公開金鑰、私密金鑰、擴充功能等等，而 false 則表示顯示與 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 類別類似的資訊，包括指模、序號、主旨和簽發者名稱等等。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Version">
      <summary>取得憑證的 X.509 格式版本。</summary>
      <returns>憑證格式。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">憑證無法讀取。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection">
      <summary>表示 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件的集合。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor">
      <summary>不使用任何 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 資訊，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 類別的新執行個體。</summary>
      <param name="certificate">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件，集合的開始位置。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件的陣列，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 類別的新執行個體。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件的陣列。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>使用指定的憑證集合，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 類別的新執行個體。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>將物件加入至 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 的結尾。</summary>
      <returns>已加入 <paramref name="certificate" /> 的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 索引。</returns>
      <param name="certificate">X.509 憑證，表示為 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>將陣列中的多個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件加入 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> 為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>將 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中的多個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件加入另一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> 為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>判斷 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件是否包含特定憑證。</summary>
      <returns>如果 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 包含指定的 <paramref name="certificate" />，則為 true，否則為 false。</returns>
      <param name="certificate">要放置在集合中的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>將 X.509 憑證資訊匯出至位元組陣列。</summary>
      <returns>位元組陣列中的 X.509 憑證資訊。</returns>
      <param name="contentType">支援的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 物件。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>使用密碼將 X.509 憑證資訊匯出至位元組陣列。</summary>
      <returns>位元組陣列中的 X.509 憑證資訊。</returns>
      <param name="contentType">支援的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> 物件。</param>
      <param name="password">用於保護位元組陣列的字串。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">無法讀取憑證、內容無效，或者在憑證需要密碼的情況下，因為提供的密碼不正確而無法匯出私密金鑰。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)">
      <summary>使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> 列舉型別 (Enumeration) 和 <paramref name="findValue" /> 物件指定的搜尋準則，搜尋 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</returns>
      <param name="findType">其中一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> 值。</param>
      <param name="findValue">做為物件的搜尋準則。</param>
      <param name="validOnly">true 表示只允許從搜尋傳回有效的憑證，否則為 false。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="findType" /> 無效。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.GetEnumerator">
      <summary>傳回列舉值，可以逐一查看 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator" /> 物件，可以逐一查看 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[])">
      <summary>將憑證以位元組陣列形式匯入 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</summary>
      <param name="rawData">位元組陣列，包含來自 X.509 憑證的資料。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>以需要密碼才能存取憑證的位元組陣列形式，將憑證匯入 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</summary>
      <param name="rawData">位元組陣列，包含 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件的資料。</param>
      <param name="password">存取憑證資訊所需的密碼。</param>
      <param name="keyStorageFlags">列舉值的位元組合，會控制匯入憑證的方式和位置。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String)">
      <summary>將憑證檔匯入 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</summary>
      <param name="fileName">包含憑證資訊的檔案名稱。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>將需要密碼的憑證檔匯入 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</summary>
      <param name="fileName">包含憑證資訊的檔案名稱。</param>
      <param name="password">存取憑證資訊所需的密碼。</param>
      <param name="keyStorageFlags">列舉值的位元組合，會控制匯入憑證的方式和位置。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>將物件插入 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中的指定索引處。</summary>
      <param name="index">插入 <paramref name="certificate" /> 處之以零起始的索引。</param>
      <param name="certificate">要插入的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。-或-<paramref name="index" /> 大於 <see cref="P:System.Collections.CollectionBase.Count" /> 屬性。</exception>
      <exception cref="T:System.NotSupportedException">集合是唯讀的。-或-集合具有固定大小。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 為 null。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Item(System.Int32)">
      <summary>取得或設定指定之索引處的項目。</summary>
      <returns>在指定之索引處的項目。</returns>
      <param name="index">要取得或設定之以零起始的項目索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。-或-<paramref name="index" /> 等於或大於 <see cref="P:System.Collections.CollectionBase.Count" /> 屬性。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> 為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>移除 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中的第一個憑證。</summary>
      <param name="certificate">要從 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件移除的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>將陣列中的多個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件從 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中移除。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件的陣列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> 為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>將 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中的多個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件從另一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中移除。</summary>
      <param name="certificates">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> 為 null。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator">
      <summary>支援在 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件上的簡易反覆運算。此類別無法被繼承。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Current">
      <summary>取得 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中目前的項目。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中目前的項目。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.MoveNext">
      <summary>將列舉值前移至 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Reset">
      <summary>將列舉值設定至其初始位置，也就是 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中的第一個項目之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Current">
      <summary>如需這個成員的說明，請參閱 <see cref="P:System.Collections.IEnumerator.Current" />。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件中目前的項目。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#MoveNext">
      <summary>如需這個成員的說明，請參閱 <see cref="M:System.Collections.IEnumerator.MoveNext" />。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Reset">
      <summary>如需這個成員的說明，請參閱 <see cref="M:System.Collections.IEnumerator.Reset" />。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection">
      <summary>定義儲存 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件的集合。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>從 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件的陣列，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 類別的新執行個體。</summary>
      <param name="value">用來初始化新物件的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件的陣列。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>從另一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 類別的新執行個體。</summary>
      <param name="value">用來初始化新物件的 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Add(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>將具有指定值的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 加入至目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />。</summary>
      <returns>在目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中插入新 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 所在的索引。</returns>
      <param name="value">要加入至目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>複製 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 型別的陣列元素至目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的結尾。</summary>
      <param name="value">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 型別的陣列，包含要加入至目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>將指定 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的元素複製到目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的結尾。</summary>
      <param name="value">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />，包含要加入至集合的物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Clear"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>取得值，指出目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 是包含指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</summary>
      <returns>如果 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 包含在這個集合中，則為 true，否則為 false。</returns>
      <param name="value">要放置的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Certificate[],System.Int32)">
      <summary>將目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 值複製到指定索引處的一維 <see cref="T:System.Array" /> 執行個體。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 所複製的值的目的端。</param>
      <param name="index">要開始複製之 <paramref name="array" /> 的索引。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 參數為多維。-或-<see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的項目數大於 <paramref name="arrayIndex" /> 和 <paramref name="array" /> 結尾之間的可用空間。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 參數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> 參數小於 <paramref name="array" /> 參數的下限。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Count"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetEnumerator">
      <summary>傳回可以逐一查看 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 來重複的列舉值。</summary>
      <returns>您可以用來重複集合的 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 子項目列舉值。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetHashCode">
      <summary>根據目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中包含的所有值來建置雜湊值 (Hash Value)。</summary>
      <returns>雜湊值，以目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中包含的所有值為基礎。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.IndexOf(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>傳回目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中所指定 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 的索引。</summary>
      <returns>如果找到的話，則為 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中 <paramref name="value" /> 參數所指定之 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 的索引，否則為 -1。</returns>
      <param name="value">要放置的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>在指定索引處將 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 插入目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />。</summary>
      <param name="index">以零啟始的索引，其中應該插入 <paramref name="value" />。</param>
      <param name="value">要插入的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Item(System.Int32)">
      <summary>取得或設定在目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的指定索引處的項目。</summary>
      <returns>在目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 的指定索引處的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</returns>
      <param name="index">位在目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中之項目的以零起始索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 參數在集合索引的有效範圍之外。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>將特定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 從目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 移除。</summary>
      <param name="value">要從目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 移除的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</param>
      <exception cref="T:System.ArgumentException">在目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中找不到 <paramref name="value" /> 參數所指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.RemoveAt(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Add(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Contains(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IndexOf(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Insert(System.Int32,System.Object)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Item(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Remove(System.Object)"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator">
      <summary>列舉 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> 物件。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>為指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator" /> 類別的新執行個體。</summary>
      <param name="mappings">要列舉的 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Current">
      <summary>取得 <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 中的目前 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.MoveNext">
      <summary>將列舉值往前推至下集合中的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在具現化 (Instantiated) 列舉值之後，會修改集合。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Reset">
      <summary>設定列舉值至它的初始位置，這是在集合中第一個元素之前。</summary>
      <exception cref="T:System.InvalidOperationException">集合在將列舉值執行個體化之後被修改。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Current">
      <summary>如需這個成員的說明，請參閱 <see cref="P:System.Collections.IEnumerator.Current" />。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> 物件中目前的 X.509 憑證物件。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#MoveNext">
      <summary>如需這個成員的說明，請參閱 <see cref="M:System.Collections.IEnumerator.MoveNext" />。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在具現化 (Instantiated) 列舉值之後，會修改集合。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Reset">
      <summary>如需這個成員的說明，請參閱 <see cref="M:System.Collections.IEnumerator.Reset" />。</summary>
      <exception cref="T:System.InvalidOperationException">在具現化 (Instantiated) 列舉值之後，會修改集合。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Chain">
      <summary>代表 <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 憑證的鏈結建置引擎。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Build(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 中指定的原則，建置 X.509 鏈結。</summary>
      <returns>如果 X.509 憑證有效，則為 true，否則為 false。</returns>
      <param name="certificate">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="certificate" /> 不是有效憑證或是 null。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="certificate" /> 無法讀取。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainElements">
      <summary>取得 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 物件的集合。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 物件。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainPolicy">
      <summary>取得或設定 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />，以在建置 X.509 憑證鏈結時使用。</summary>
      <returns>與這個 X.509 鏈結關聯的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 物件。</returns>
      <exception cref="T:System.ArgumentNullException">這個屬性目前設定的值為 null。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus">
      <summary>取得 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 物件中每個項目的狀態。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" /> 物件的陣列。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose">
      <summary>釋放這個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 使用的所有資源。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose(System.Boolean)">
      <summary>釋放這個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 所使用的 Unmanaged 資源，並選擇性地釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源，false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.SafeHandle">
      <summary>取得此 <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> 執行個體的安全控制代碼。</summary>
      <returns>傳回 <see cref="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle" />。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElement">
      <summary>表示 X.509 鏈結的項目。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Certificate">
      <summary>取得特定鏈結項目處的 X.509 憑證。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> 物件。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.ChainElementStatus">
      <summary>取得鏈結中目前 X.509 憑證的錯誤狀態。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" /> 物件的陣列。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Information">
      <summary>取得 Unmanaged 憑證鏈結結構的其他錯誤資訊。</summary>
      <returns>字串，表示 Crypto API 中 Unmanaged CERT_CHAIN_ELEMENT 結構的 pwszExtendedErrorInfo 成員。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection">
      <summary>表示 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 物件的集合。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509ChainElement[],System.Int32)">
      <summary>從指定的索引處開始，將 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 物件複製到陣列中。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 物件的陣列。</param>
      <param name="index">整數，表示索引值。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">指定的<paramref name="index" /> 小於零，或是大於或等於陣列長度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 與目前計數的和大於陣列長度。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Count">
      <summary>取得集合中的項目數目。</summary>
      <returns>整數，表示集合中項目的數目。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.GetEnumerator">
      <summary>取得 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" /> 物件，此物件可用於巡覽鏈結項目集合。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" /> 物件。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.IsSynchronized">
      <summary>取得值，指出是否同步處理鏈結項目的集合。</summary>
      <returns>永遠傳回 false。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Item(System.Int32)">
      <summary>取得在指定索引處的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 物件。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> 物件。</returns>
      <param name="index">整數值。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" /> 小於零。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 大於或等於集合的長度。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.SyncRoot">
      <summary>取得物件，此物件可用於同步處理對 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 物件的存取。</summary>
      <returns>目前物件的指標參考。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從指定的索引處開始，將 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 物件複製到陣列中。</summary>
      <param name="array">要將 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 物件複製到其中的陣列。</param>
      <param name="index">要從其開始複製的 <paramref name="array" /> 索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">指定的<paramref name="index" /> 小於零，或是大於或等於陣列長度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 與目前計數的和大於陣列長度。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>取得 <see cref="T:System.Collections.IEnumerator" /> 物件，此物件可用於巡覽鏈結項目集合。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> 物件。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator">
      <summary>支援在 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 上的簡易反覆運算。此類別無法被繼承。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Current">
      <summary>取得 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中目前的項目。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中目前的項目。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.MoveNext">
      <summary>使列舉值前進至 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Reset">
      <summary>將列舉值設定至其初始位置，也就是 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中的第一個項目之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.System#Collections#IEnumerator#Current">
      <summary>取得 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中目前的項目。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> 中目前的項目。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy">
      <summary>表示建置 X509 憑證鏈結時要套用的鏈結原則。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ApplicationPolicy">
      <summary>取得物件識別項 (OID) 的集合，指定憑證支援的應用程式原則或增強金鑰使用方式 (EKU)。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 物件。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.CertificatePolicy">
      <summary>取得物件識別項 (OID) 的集合，指定憑證支援的憑證原則。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 物件。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ExtraStore">
      <summary>表示額外的憑證集合，可在驗證憑證鍊結時由鍊結引擎搜尋。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> 物件。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.Reset">
      <summary>將 <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> 成員重設為其預設值。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationFlag">
      <summary>取得或設定 X509 撤銷旗標的值。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> 物件。</returns>
      <exception cref="T:System.ArgumentException">提供的 <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> 值不是有效的旗標。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationMode">
      <summary>取得或設定 X509 憑證撤銷模式的值。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> 物件。</returns>
      <exception cref="T:System.ArgumentException">提供的 <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> 值不是有效的旗標。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.UrlRetrievalTimeout">
      <summary>取得在線上撤銷驗證或下載憑證撤銷清單 (CRL) 期間，已耗用的時間範圍。</summary>
      <returns>
        <see cref="T:System.TimeSpan" /> 物件。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationFlags">
      <summary>取得憑證的驗證旗標。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> 列舉中的值。</returns>
      <exception cref="T:System.ArgumentException">提供的 <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> 值不是有效的旗標。<see cref="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag" /> 是預設值。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationTime">
      <summary>驗證憑證的時間以當地時間表示。</summary>
      <returns>
        <see cref="T:System.DateTime" /> 物件。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatus">
      <summary>提供簡單的結構，用以存放 X509 鏈結狀態與錯誤資訊。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.Status">
      <summary>設定 X509 鏈結的狀態。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags" /> 值。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.StatusInformation">
      <summary>設定 <see cref="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus" /> 值的說明。</summary>
      <returns>可當地語系化的字串。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags">
      <summary>定義 X509 鏈結的狀態。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotSignatureValid">
      <summary>表示憑證信任清單 (CTL) 中含有無效的簽章。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotTimeValid">
      <summary>表示由於某個時間值無效，例如代表憑證信任清單 (CTL) 到期的時間，因而導致整個 CTL 無效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotValidForUsage">
      <summary>表示憑證信任清單 (CTL) 不適合這種用途。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Cyclic">
      <summary>表示無法建立 X509 鏈結。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasExcludedNameConstraint">
      <summary>表示由於有憑證將某個名稱條件約束排除掉，因此整個 X509 鏈結變成無效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotDefinedNameConstraint">
      <summary>表示憑證含有一個未定義的名稱條件約束。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotPermittedNameConstraint">
      <summary>表示憑證含有一個不許可的名稱條件約束。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotSupportedNameConstraint">
      <summary>表示名稱並未含有支援的名稱條件約束，或者含有不支援的名稱條件約束。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidBasicConstraints">
      <summary>表示由於基本條件約束無效，因此整個 X509 鏈結都無效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidExtension">
      <summary>表示由於某個擴充功能無效，因此整個 X509 鏈結都無效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidNameConstraints">
      <summary>表示由於名稱條件約束無效，因此整個 X509 鏈結都無效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidPolicyConstraints">
      <summary>表示由於原則條件約束無效，因此整個 X509 鏈結都無效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoError">
      <summary>表示 X509 鏈結沒有錯誤。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoIssuanceChainPolicy">
      <summary>表示此憑證內未含有任何憑證原則擴充項目。 如果群組原則指定所有憑證都必須含有憑證原則，就會發生這個錯誤。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotSignatureValid">
      <summary>表示由於憑證簽章無效，因此整個 X509 鏈結都無效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeNested">
      <summary>已取代。指定 CA (憑證授權單位) 憑證和發出的憑證的有效期間未完全涵蓋。例如，CA 憑證的有效日期為一月一日到十二月一日，但所發出的憑證有效日期卻是從一月二日到十二月二日，這就是前者未完全涵蓋後者的狀況。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeValid">
      <summary>表示由於某個時間值無效，例如代表憑證過期的值，因此整個 X509 鏈結無效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotValidForUsage">
      <summary>表示金鑰的使用方式無效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.OfflineRevocation">
      <summary>表示 X509 鏈結所依賴的線上憑證撤銷清單 (CRL) 現在是離線的狀態。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.PartialChain">
      <summary>表示建立 X509 鏈結時，無法一直連通到達根憑證。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.RevocationStatusUnknown">
      <summary>表示無法判斷此憑證是否已經被撤銷，原因可能是憑證撤銷清單 (CRL) 目前離線或無法使用。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Revoked">
      <summary>表示由於某個憑證已被撤銷，因此整個 X509 鏈結無效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.UntrustedRoot">
      <summary>表示由於根憑證不受信任，因此整個 X509 鏈結無效。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ContentType">
      <summary>指定 X.509 憑證的格式。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Authenticode">
      <summary>Authenticode X.509 憑證。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert">
      <summary>單一 X.509 憑證。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pfx">
      <summary>PFX 格式的憑證。Pfx 值和 Pkcs12 值相同。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12">
      <summary>PKCS #12 格式的憑證。Pkcs12 值和 Pfx 值相同。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs7">
      <summary>PKCS #7 格式的憑證。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert">
      <summary>單一序列化的 X.509 憑證。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedStore">
      <summary>序列化的存放區。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Unknown">
      <summary>未知的 X.509 憑證。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension">
      <summary>定義表示使用金鑰之應用程式的物件識別項 (OID) 集合。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件和識別擴充功能是否重要的值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 類別的新執行個體。</summary>
      <param name="encodedEnhancedKeyUsages">用來建立擴充功能的編碼資料。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.OidCollection,System.Boolean)">
      <summary>使用 <see cref="T:System.Security.Cryptography.OidCollection" /> 和識別擴充功能是否重要的值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 類別的新執行個體。</summary>
      <param name="enhancedKeyUsages">
        <see cref="T:System.Security.Cryptography.OidCollection" />。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">指定的 <see cref="T:System.Security.Cryptography.OidCollection" /> 可以包含一或多個毀損的值。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> 類別的新執行個體。</summary>
      <param name="asnEncodedData">用來建立擴充功能的編碼資料。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.EnhancedKeyUsages">
      <summary>取得表示使用金鑰之應用程式的物件識別項 (OID) 集合。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 物件，表示使用金鑰的應用程式。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Extension">
      <summary>表示 X509 擴充功能。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 類別的新執行個體。</summary>
      <param name="encodedExtension">用於建立擴充功能的編碼資料。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.Oid,System.Byte[],System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 類別的新執行個體。</summary>
      <param name="oid">用於識別擴充功能的物件識別項。</param>
      <param name="rawData">用來建立擴充功能的編碼資料。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oid" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="oid" />為空字串 ("")。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.String,System.Byte[],System.Boolean)">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 類別的新執行個體。</summary>
      <param name="oid">表示物件識別項的字串。</param>
      <param name="rawData">用來建立擴充功能的編碼資料。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>複製指定 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件的擴充功能屬性。</summary>
      <param name="asnEncodedData">要複製的 <see cref="T:System.Security.Cryptography.AsnEncodedData" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asnEncodedData" /> 沒有有效的 X.509 擴充功能。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Extension.Critical">
      <summary>取得布林值，指示擴充功能是否具有關鍵性。</summary>
      <returns>如果是關鍵擴充功能則為 true，否則為 false。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection">
      <summary>表示 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件的集合。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Add(System.Security.Cryptography.X509Certificates.X509Extension)">
      <summary>將 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件加入 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件。</summary>
      <returns>
        <paramref name="extension" /> 參數加入的索引位置。</returns>
      <param name="extension">要加入 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="extension" /> 參數的值是 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Extension[],System.Int32)">
      <summary>從指定的索引處開始，將集合複製到陣列中。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件的陣列。</param>
      <param name="index">在陣列中開始複製的位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 是長度為零的字串或包含無效值。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 指定的值不在陣列範圍中。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Count">
      <summary>取得 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件中的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件數目。</summary>
      <returns>整數，表示 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件中 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件的數目。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.GetEnumerator">
      <summary>傳回可以逐一查看 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件的列舉值。</summary>
      <returns>用於逐一查看 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件的 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator" /> 物件。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.IsSynchronized">
      <summary>取得值，指出集合是否保證為執行緒安全。</summary>
      <returns>如果集合為安全執行緒 (Thread Safe)，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.Int32)">
      <summary>取得在指定索引處的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件。</returns>
      <param name="index">要擷取的 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件位置。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" /> 小於零。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 等於或大於陣列的長度。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.String)">
      <summary>取得第一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件，這個物件的值或易記名稱是由物件識別項 (OID) 指定的。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件。</returns>
      <param name="oid">要擷取之擴充功能的物件識別項 (OID)。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.SyncRoot">
      <summary>取得物件，可用於同步處理對 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件的存取。</summary>
      <returns>物件，可用於同步處理對 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件的存取。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從指定的索引處開始，將集合複製到陣列中。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> 物件的陣列。</param>
      <param name="index">在陣列中開始複製的位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 是長度為零的字串或包含無效值。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 指定的值不在陣列範圍中。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回可以逐一查看 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件的列舉值。</summary>
      <returns>用於逐一查看 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 物件的 <see cref="T:System.Collections.IEnumerator" /> 物件。</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator">
      <summary>支援在 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 上的簡易反覆運算。此類別無法被繼承。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Current">
      <summary>取得 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 中目前的項目。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 中目前的項目。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.MoveNext">
      <summary>使列舉值前進至 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 中的下一個項目。</summary>
      <returns>如果列舉值成功地前移至下一個項目，則為 true，如果列舉值已超過集合的結尾，則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Reset">
      <summary>將列舉值設定至其初始位置，也就是 <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 中的第一個項目之前。</summary>
      <exception cref="T:System.InvalidOperationException">在建立列舉值之後，會修改集合。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.System#Collections#IEnumerator#Current">
      <summary>從集合取得物件。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> 中目前的項目。</returns>
      <exception cref="T:System.InvalidOperationException">列舉值位於集合的第一個項目之前，或最後一個項目之後。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509FindType">
      <summary>指定 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法搜尋之值的型別。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByApplicationPolicy">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是一個字串，代表憑證的應用程式原則易記名稱或物件識別項 (OID，或 <see cref="T:System.Security.Cryptography.Oid" />)。例如 "Encrypting File System" 或 "1.3.6.1.4.1.311.10.3.4" 都是可以使用的。未來要針對各種語系當地化的應用程式，由於易記名稱也會跟著語系當地化，因此一定要使用物件識別項 (OID) 值才行。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByCertificatePolicy">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是一個字串，代表憑證原則易記名稱或物件識別項 (OID，或 <see cref="T:System.Security.Cryptography.Oid" />)。最佳做法就是使用物件識別項 (OID)，例如 "1.3.6.1.4.1.311.10.3.4"。未來要針對各種語系當地化的應用程式，由於易記名稱也會跟著語系當地化，因此一定要使用物件識別項 (OID) 才行。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByExtension">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是描述要尋找之擴充部分的字串。物件識別項 (OID) 最常用來引導 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法搜尋擁有符合該 OID 值之擴充部分的所有憑證。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是代表憑證簽發者辨別名稱的字串。這種搜尋方式比 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" /> 列舉值提供的搜尋條件來得嚴格。<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法會使用 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" /> 值對整個辨別名稱進行不區分大小寫的字串比較。根據簽發者的名稱進行搜尋的結果會比較不精確。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是代表憑證簽發者名稱的字串。這種搜尋方式不如 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" /> 列舉值提供的搜尋條件來得嚴格。<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法會使用 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" /> 值對提供值進行不區分大小寫的字串比較。例如將 "MyCA" 傳遞至 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法，就可以找出簽發者名稱含有此字串的所有憑證，完全不考慮簽發者的其他資料值。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByKeyUsage">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是代表金鑰使用方式的字串，或代表位元遮罩的整數，所有要求的金鑰使用方式都放在這組位元遮罩內。如果是字串值，每次只能指定一種金鑰使用方式，但可以連續使用 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法取得所要求的各種使用方式的交集。例如 <paramref name="findValue" /> 參數可以設定成 "KeyEncipherment" 或整數 (0x30 代表 "KeyEncipherment" 和 "DataEncipherment")。您也可以使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 列舉型別中的值。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySerialNumber">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是表示憑證序號的字串，如憑證對話方塊所顯示但不含空格的，或是如 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumberString" /> 方法所傳回的。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是代表憑證主體辨別名稱的字串。這種搜尋方式比 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" /> 列舉值提供的搜尋條件來得嚴格。<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法會使用 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" /> 值對整個辨別名稱進行不區分大小寫的字串比較。根據主體名稱進行搜尋的結果會比較不精確。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectKeyIdentifier">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是一個字串，代表在 UI 上所顯示以十六進位表示的主體金鑰識別項，例如 "F3E815D45E83B8477B9284113C64EF208E897112"。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是代表憑證主體名稱的字串。這種搜尋方式不如 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" /> 列舉值提供的搜尋條件來得嚴格。<see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法會使用 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" /> 值對提供值進行不區分大小寫的字串比較。例如將 "MyCert" 傳遞至 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法，就可以找出主體名稱含有此字串的所有憑證，完全不考慮主體的其他資料值。根據辨別名稱進行搜尋的結果會更精確。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTemplateName">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是代表憑證樣板名稱的字串，例如 "ClientAuth"。樣板名稱是 X509 版本 3 的擴充功能，用以指定憑證的使用方式。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByThumbprint">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是代表憑證指模的字串。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是本地時間的 <see cref="T:System.DateTime" /> 值。例如，您可以在 <see cref="P:System.DateTime.Now" /> 之 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 運算的結果中排除當年度最後一天之 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired" /> 的 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 運算結果，以尋找在當年年底之前有效的所有憑證。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是本地時間的 <see cref="T:System.DateTime" /> 值。值不一定要在未來。例如，您可以使用 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> 尋找當年開始生效的憑證，方法是找出上一年度最後一天之 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> 的 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 運算結果與  <see cref="P:System.DateTime.Now" /> 之 <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid" /> 的 <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 運算的交集。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid">
      <summary>
        <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> 方法的 <paramref name="findValue" /> 參數必須是本地時間的 <see cref="T:System.DateTime" /> 值。您可以使用 <see cref="P:System.DateTime.Now" /> 尋找所有目前有效的憑證。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags">
      <summary>定義匯入 X.509 憑證之私密金鑰的位置與方式。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.DefaultKeySet">
      <summary>使用預設的金鑰設定。使用者金鑰設定通常即是預設金鑰設定。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.Exportable">
      <summary>將匯入的金鑰標記為可匯出。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.MachineKeySet">
      <summary>私密金鑰儲存在本機電腦存放區中，而不是目前的使用者存放區中。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.PersistKeySet">
      <summary>在匯入憑證時，會保存與 PFX 檔案相關聯的金鑰。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserKeySet">
      <summary>私密金鑰會儲存在目前的使用者存放區中，而不是本機電腦存放區中。即使憑證指定金鑰應該在本機電腦存放區中，仍會出現這種情況。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserProtected">
      <summary>透過對話方塊或其他方法告知使用者已存取金鑰。使用中的密碼編譯服務提供者 (CSP) 會定義精確行為。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension">
      <summary>定義 X.509 憑證內所包含之金鑰的使用方式。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件和識別擴充功能是否重要的值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 類別的新執行個體。</summary>
      <param name="encodedKeyUsage">用來建立擴充功能的編碼資料。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.X509Certificates.X509KeyUsageFlags,System.Boolean)">
      <summary>使用指定的 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 值和識別擴充功能是否重要的值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 類別的新執行個體。</summary>
      <param name="keyUsages">其中一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> 值，其描述如何使用金鑰。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> 類別的新執行個體。</summary>
      <param name="asnEncodedData">用來建立擴充功能的編碼資料。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages">
      <summary>取得與憑證相關聯的金鑰使用方式旗標。</summary>
      <returns>其中一個 <see cref="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages" /> 值。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">無法解碼擴充功能。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags">
      <summary>定義如何使用憑證金鑰。如果未定義這個值，則金鑰可用於任何目的。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.CrlSign">
      <summary>可以使用金鑰簽署憑證廢止清單 (CRL)。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DataEncipherment">
      <summary>金鑰可用於資料加密。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DecipherOnly">
      <summary>金鑰只可用於解密。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DigitalSignature">
      <summary>金鑰可用做數位簽章。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.EncipherOnly">
      <summary>金鑰只可用於加密。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyAgreement">
      <summary>金鑰可用於判斷金鑰協議，例如，使用 Diffie-Hellman 金鑰協議演算法建立的金鑰。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyCertSign">
      <summary>金鑰可用於簽章憑證。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyEncipherment">
      <summary>金鑰可用於金鑰加密。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.None">
      <summary>沒有金鑰使用方式參數。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.NonRepudiation">
      <summary>金鑰可用於驗證 (Authentication)。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509NameType">
      <summary>指定 X509 憑證所含名稱的類型。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsFromAlternativeName">
      <summary>與 X.509 憑證主體或簽發者之替代名稱關聯的 DNS 名稱。這個值相當於 <see cref="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName" /> 值。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName">
      <summary>與 X509 憑證主體或簽發者替代名稱關聯的 DNS 名稱。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.EmailName">
      <summary>X509 憑證主體或簽發者的電子郵件地址。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.SimpleName">
      <summary>X509 憑證主體或簽發者的簡單名稱。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UpnName">
      <summary>X509 憑證主體或簽發者的 UPN 名稱。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UrlName">
      <summary>與 X509 憑證主體或簽發者替代名稱關聯的 URL 位址。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag">
      <summary>指定應檢查鏈結中的哪些 X509 憑證是否已被撤銷。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EndCertificateOnly">
      <summary>只檢查尾端的憑證是否已被撤銷。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EntireChain">
      <summary>整個鏈結中的所有憑證都應檢查是否已被撤銷。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.ExcludeRoot">
      <summary>除了根憑證之外，整個鏈結都應檢查是否已被撤銷。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationMode">
      <summary>指定檢查 X509 憑證撤銷狀態所使用的模式。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck">
      <summary>不檢查憑證的撤銷狀態。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Offline">
      <summary>使用快取憑證撤銷清單 (CRL) 檢查撤銷狀態。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Online">
      <summary>使用線上憑證撤銷清單 (CRL) 檢查撤銷狀態。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Store">
      <summary>表示 X.509 存放區，這個存放區是保存和管理憑證的實體存放區。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor">
      <summary>使用目前使用者存放區的個人憑證，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.Security.Cryptography.X509Certificates.StoreName,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>使用指定的 <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 和 <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" /> 值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> 類別的新執行個體。</summary>
      <param name="storeName">其中一個列舉值，指定 X.509 憑證存放區的名稱。</param>
      <param name="storeLocation">其中一個列舉值，指定 X.509 憑證存放區的位置。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> 不是有效位置，或 <paramref name="storeName" /> 不是有效名稱。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.String,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>使用表示 <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 列舉之值和 <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" /> 列舉之值的字串，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> 類別的新執行個體。</summary>
      <param name="storeName">表示 <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> 列舉之值的字串。</param>
      <param name="storeLocation">其中一個列舉值，指定 X.509 憑證存放區的位置。 </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> 包含無效的值。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>將憑證加入 X.509 憑證存放區。</summary>
      <param name="certificate">要加入的憑證。 </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 為 null。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">無法將憑證加入至存放區。</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Certificates">
      <summary>傳回位於 X.509 憑證存放區的憑證集合。</summary>
      <returns>憑證的集合。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Dispose">
      <summary>釋放由此所使用的資源<see cref="T:System.Security.Cryptography.X509Certificates.X509Store" />。</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Location">
      <summary>取得 X.509 憑證存放區的位置。</summary>
      <returns>憑證存放區的位置。</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Name">
      <summary>取得 X.509 憑證存放區的名稱。</summary>
      <returns>憑證存放區的名稱。</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)">
      <summary>開啟 X.509 憑證存放區或建立新的存放區 (視 <see cref="T:System.Security.Cryptography.X509Certificates.OpenFlags" /> 旗標設定而定)。</summary>
      <param name="flags">列舉值的位元組合，指定開啟 X.509 憑證存放區的方式。</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">存放區無法讀取。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
      <exception cref="T:System.ArgumentException">存放區包含無效的值。</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>移除 X.509 憑證存放區中的憑證。</summary>
      <param name="certificate">要移除的憑證。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> 為 null。</exception>
      <exception cref="T:System.Security.SecurityException">呼叫端沒有必要的權限。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension">
      <summary>定義識別憑證之主體金鑰識別元 (SKI) 的字串。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Byte[],System.Boolean)">
      <summary>使用位元組陣列和識別擴充功能是否重要的值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 類別的新執行個體。</summary>
      <param name="subjectKeyIdentifier">位元組陣列，表示用於建立擴充功能的資料。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>使用已編碼的資料和識別擴充功能是否重要的值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 類別的新執行個體。</summary>
      <param name="encodedSubjectKeyIdentifier">
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件，用於建立擴充功能。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Boolean)">
      <summary>使用公開金鑰 (Public Key) 和指出擴充功能是否重要的值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 類別的新執行個體。</summary>
      <param name="key">
        <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> 物件，從這個物件建立主體金鑰識別元 (SKI)。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm,System.Boolean)">
      <summary>使用公開金鑰、雜湊演算法識別項和指出擴充功能是否重要的值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 類別的新執行個體。</summary>
      <param name="key">
        <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> 物件，從這個物件建立主體金鑰識別元 (SKI)。</param>
      <param name="algorithm">其中一個 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm" /> 值，可識別要使用的雜湊演算法。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.String,System.Boolean)">
      <summary>使用字串和識別擴充功能是否重要的值，初始化 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 類別的新執行個體。</summary>
      <param name="subjectKeyIdentifier">以十六進位格式編碼的字串，表示憑證的主體金鑰識別元 (SKI)。</param>
      <param name="critical">如果是關鍵擴充功能則為 true，否則為 false。</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>藉由複製已編碼資料中的資訊，建立 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 類別的新執行個體。</summary>
      <param name="asnEncodedData">
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 物件，用於建立擴充功能。</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.SubjectKeyIdentifier">
      <summary>取得字串，表示憑證的主體金鑰識別元 (SKI)。</summary>
      <returns>以十六進位格式編碼的字串，表示主體金鑰識別元 (SKI)。</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">無法解碼擴充功能。</exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm">
      <summary>定義雜湊演算法的類型以使用 <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> 類別。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.CapiSha1">
      <summary>主體金鑰識別項 (SKI) 是由已編碼的公開金鑰 (包含標記、長度和未使用位元數) 之 160 位元 SHA-1 雜湊所組成。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.Sha1">
      <summary>SKI 是由公開金鑰值 (包含標記、長度和未使用位元數) 之 160 位元 SHA-1 雜湊所組成。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.ShortSha1">
      <summary>SKI 是由四位元類型欄位所組成，值為 0100，後面接著公開金鑰值 (包含標記、長度和未使用位元字串位元數) 之 SHA-1 雜湊的最小顯著性 60 位元。</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags">
      <summary>設定在何種狀況下應該對 X509 鏈結中的憑證進行驗證。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllFlags">
      <summary>所有驗證相關的旗標全部都包含在內。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllowUnknownCertificateAuthority">
      <summary>忽略因憑證授權單位 (CA) 不明而導致鏈結無法通過驗證的情形。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCertificateAuthorityRevocationUnknown">
      <summary>判斷憑證是否通過驗證時，忽略憑證授權單位撤銷狀態不明的情況。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlNotTimeValid">
      <summary>判斷憑證是否通過驗證時，忽略憑證信任清單 (CTL) 無效的狀況，例如 CTL 過期等等。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlSignerRevocationUnknown">
      <summary>判斷憑證是否通過驗證時，忽略憑證信任清單 (CTL) 簽署者撤銷狀態不明的情況。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreEndRevocationUnknown">
      <summary>判斷憑證是否通過驗證時，忽略終端憑證 (使用者的憑證) 撤銷狀態不明的情況。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidBasicConstraints">
      <summary>判斷憑證是否通過驗證時，忽略基本條件約束無效的情形。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidName">
      <summary>判斷憑證是否通過驗證時，忽略憑證名稱無效的情形。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidPolicy">
      <summary>判斷憑證是否通過驗證時，忽略憑證原則無效的情形。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeNested">
      <summary>驗證憑證時，忽略 CA (憑證授權單位) 憑證有效期間未完全涵蓋所發出憑證有效期間的情形。例如，CA 憑證的有效日期為一月一日到十二月一日，但所發出的憑證有效日期卻是從一月二日到十二月二日，這就是前者未完全涵蓋後者的狀況。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeValid">
      <summary>判斷憑證是否有效時，忽略鏈結中無效的憑證，無論這些憑證無效的原因究竟是已經過期或尚未生效。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreRootRevocationUnknown">
      <summary>判斷憑證是否通過驗證時，忽略根撤銷狀態不明的情況。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreWrongUsage">
      <summary>判斷憑證是否通過驗證時，忽略憑證並非核發給目前使用方式的情形。</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag">
      <summary>不包含任何與驗證相關的旗標。</summary>
    </member>
  </members>
</doc>