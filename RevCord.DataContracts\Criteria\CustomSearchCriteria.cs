﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Criteria
{
    public class CustomSearchCriteria
    {
        public List<CustomSearchQuery> Conditions { get; set; }
        public OperatorExpression Operator { get; set; }
    }

    public class CustomSearchQuery
    {
        public string FieldName { get; set; }
        //public OperatorExpression Operator { get; set; } //public string Operator { get; set; }
        public string Operator { get; set; }
        public object Value { get; set; }
    }

    public enum OperatorExpression
    {
        Equal,
        Like
    }

    public static class CustomSearchQueryBuilder
    {
        public static string BuildQuery(List<CustomSearchQuery> whereFields, OperatorExpression operatorExpression)
        {
            //(CI.Tag12 LIKE N'%ROOT%') OR
            var whereClause = new StringBuilder();
            whereClause.Append(" AND (").AppendLine();

            #region In case if Operator is passed for each field
            /*foreach (var field in whereFields)
            {
                switch (operatorExpression)
                {
                    case OperatorExpression.Equals:
                        whereClause.AppendFormat("({0} {1} = {2})", field.FieldName, field.Operator, field.Value).AppendLine(" OR ");
                        break;
                    case OperatorExpression.Like:
                        whereClause.AppendFormat("({0} {1} LIKE '%{2}%')", field.FieldName, field.Operator, field.Value).AppendLine(" OR ");
                        break;
                }
            }*/
            #endregion

            switch (operatorExpression)
            {
                case OperatorExpression.Equal:
                    foreach (var field in whereFields)
                    {
                        whereClause.AppendFormat("({0} = '{1}')", field.FieldName, field.Value).AppendLine(" OR ");
                    }
                    break;
                case OperatorExpression.Like:
                    foreach (var field in whereFields)
                    {
                        //String Condition = " And UserName like ''%" + field.FieldName + "%''";
                        //whereClause.AppendFormat("({0} LIKE N''%{1}%'')", field.FieldName, field.Value).AppendLine(" OR ");
                        whereClause.AppendFormat("({0} LIKE N'%{1}%')", field.FieldName, field.Value).AppendLine(" OR ");
                    }
                    break;
            }
            whereClause.Remove(whereClause.ToString().Length - 5, 3).ToString();

            whereClause.Append(" ) ");

            if ((whereFields != null) && (!whereFields.Any()))
                whereClause.Append(" AND (1 = 1)");//cleanly close the where clause

            System.Diagnostics.Debug.WriteLine(whereClause.ToString());
            return whereClause.ToString();
        }
    }

    //TODO: Create a full Where Clause from this class

    /*public static class SearchQueryBuilder
    {
        public static string BuildQuery(CallCriteria criteria, CustomSearchCriteria customSearchCriteria)
        {
            var whereClause = new StringBuilder();
            whereClause.Append(" AND (").AppendLine();

            #region Custom Search Criteria

            if (criteria.IsCustomSearch)
            {
                switch (customSearchCriteria.Operator)
                {
                    case OperatorExpression.Equals:
                        foreach (var field in customSearchCriteria.Conditions)
                        {
                            whereClause.AppendFormat("({0} = {1})", field.FieldName, field.Value).AppendLine(" OR ");
                        }
                        break;
                    case OperatorExpression.Like:
                        foreach (var field in customSearchCriteria.Conditions)
                        {
                            //String Condition = " And UserName like ''%" + field.FieldName + "%''";
                            //whereClause.AppendFormat("({0} LIKE N''%{1}%'')", field.FieldName, field.Value).AppendLine(" OR ");
                            whereClause.AppendFormat("({0} LIKE N'%{1}%')", field.FieldName, field.Value).AppendLine(" OR ");
                        }
                        break;
                }
                whereClause.Remove(whereClause.ToString().Length - 5, 3).ToString();

                whereClause.Append(" ) ");

                if ((customSearchCriteria.Conditions != null) && (!customSearchCriteria.Conditions.Any()))
                    whereClause.Append(" AND (1 = 1)");//cleanly close the where clause

                System.Diagnostics.Debug.WriteLine(whereClause.ToString());
            }

            #endregion


            #region ------- Group and Extensions -------

            bool secCat = false;
            if (criteria.CategoryGroupExtensions != null && criteria.CategoryGroupExtensions.Count() > 0)
            {

                var mediaExtensions = criteria.CategoryGroupExtensions.Where(cge => cge.GroupType == GroupType.Audio || cge.GroupType == GroupType.Screens || cge.GroupType == GroupType.Text || cge.GroupType == GroupType.IQ3 || cge.GroupType == GroupType.MD);
                if (mediaExtensions.Count() != 0)
                {
                    whereClause.Append(" AND ( "); //1
                    whereClause.AppendLine();

                    foreach (var extension in mediaExtensions)
                    {
                        if (secCat && extension.GroupExtensions.Count > 0)
                            whereClause.Append(" OR ( "); //3
                        if (extension.GroupExtensions.Count > 0)
                        {
                            whereClause.Append(" ( ");//2

                            foreach (var grpExt in extension.GroupExtensions)
                            {
                                whereClause.AppendFormat(@"({0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                whereClause.Append(" OR ");
                                whereClause.AppendLine();
                            }
                            //whereClause.RemoveLast(" OR ");
                            whereClause.Remove(whereClause.ToString().Length - 5, 3).ToString();
                            whereClause.Append(" ) ");//2
                            whereClause.AppendLine();

                            whereClause.AppendFormat(" AND CI.CallType = {0} ", (int)extension.GroupType);//Call Type
                            whereClause.AppendLine();
                            //sbWhereClause.Append(" ) ");//2
                        }
                        if (secCat && extension.GroupExtensions.Count > 0)
                            whereClause.Append(" ) "); //3
                        if (extension.GroupExtensions.Count > 0)
                            secCat = true;
                    }
                    // Added for IQ3

                    if (whereClause.Equals("{ AND (  OR (  )  OR (  ) }"))
                    {
                        whereClause.Replace("{ AND (  OR (  )  OR (  ) }", "");
                        whereClause.Append("AND CI.CallType = 7 ");
                    }
                    else
                    {
                        //  sbWhereClause.Append("or CI.CallType = 7 ");
                    }

                    whereClause.Append(" ) "); //1
                }
            }


            #endregion


            whereClause.Append(" AND CI.[IsShow] = '1' ");


            return whereClause.ToString();
        }
    }*/

}