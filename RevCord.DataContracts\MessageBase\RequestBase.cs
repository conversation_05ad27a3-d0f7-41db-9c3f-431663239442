﻿using System;
namespace RevCord.DataContracts.MessageBase
{
    public class RequestBase
    {
        /// <summary>
        /// Load options indicated what types are to be returned in the request.
        /// </summary>
        public string[] LoadOptions;

        /// <summary>
        /// Crud action: Create, Read, Update, Delete
        /// </summary>
        public string Action;

        public PersistType PersistType { get; set; }

        public DateTime ModifiedDate { get; set; }

        public int TenantId { get; set; }

        public object Data { get; set; }

    }

}
