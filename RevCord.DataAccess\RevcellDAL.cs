﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.RevcellEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class RevcellDAL
    {
        private int _tenantId;
        public RevcellDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        public bool AddRevcellInfo(int userNum, string phoneNumberId, string phoneNumber, string phoneName, string sipId, string sipName)
        {
            try
            {
                int rowsaffected = 0;
                bool returnValue = false;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Revcell.ADD_REVCELL_INFO;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@PhoneNumberId", phoneNumberId);
                    cmd.Parameters.AddWithValue("@PhoneNumber", phoneNumber);
                    cmd.Parameters.AddWithValue("@PhoneName", phoneName);
                    cmd.Parameters.AddWithValue("@SIPId", sipId);
                    cmd.Parameters.AddWithValue("@SIPName", sipName);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Revcell, "AddRevcellInfo", _tenantId));

                    rowsaffected = Convert.ToInt32(cmd.ExecuteScalar());

                    conn.Close();
                    if(rowsaffected > 0)
                        returnValue = true;
                    return returnValue;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool DeleteRevcellInfo(string sipId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Revcell.DELETE_REVCELL_INFO;
                    cmd.Parameters.AddWithValue("@SIPId", sipId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Revcell, "DeleteRevcellInfo", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    return count >= 1 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public RevcellInfo GetRevcellInfoByUserNum(int userNum)
        {
            try
            {
                RevcellInfo revcellInfo = new RevcellInfo();
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Revcell.GET_REVCELL_INFO;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Revcell, "GetRevcellInfoByUserNum", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        revcellInfo = ORMapper.MapRevcellInfo(dr);
                    }
                    conn.Close();
                    return revcellInfo;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #region Plivo
        public bool AddPlivoInfo(int userNum, string phoneNumberId, string phoneNumber, string endPointId, string endPointUserName, string alias)
        {
            bool returnValue = false;

            try
            {
                int rowsaffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Revcell.ADD_PLIVO_INFO;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@PhoneNumberId", phoneNumberId);
                    cmd.Parameters.AddWithValue("@PhoneNumber", phoneNumber);
                    cmd.Parameters.AddWithValue("@Alias", alias);
                    cmd.Parameters.AddWithValue("@EndpointId", endPointId);
                    cmd.Parameters.AddWithValue("@EndpointUserName", endPointUserName);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Revcell, "AddPlivoInfo", _tenantId));

                    rowsaffected = Convert.ToInt32(cmd.ExecuteScalar());

                    conn.Close();
                    if (rowsaffected > 0)
                        returnValue = true;
                }

                // Adding details in mtTenantUser Table
                if (_tenantId > 0)
                {
                    try
                    {
                        using (var conn = DALHelper.GetConnectionMasterDB())
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = DBConstants.Revcell.ADD_PLIVO_INFO_MASTER;
                            cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                            cmd.Parameters.AddWithValue("@UserNum", userNum);
                            cmd.Parameters.AddWithValue("@PhoneNumberId", phoneNumberId);
                            cmd.Parameters.AddWithValue("@PhoneNumber", phoneNumber);
                            cmd.Parameters.AddWithValue("@Alias", alias);
                            cmd.Parameters.AddWithValue("@EndpointId", endPointId);
                            cmd.Parameters.AddWithValue("@EndpointUserName", endPointUserName);

                            conn.Open();
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Revcell, "AddPlivoInfo", _tenantId));

                            Convert.ToInt32(cmd.ExecuteScalar());

                            conn.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        // Ignoring
                    }
                }

            }
            catch (Exception ex) { throw ex; }

            return returnValue;
        }

        public bool DeletePlivoInfo(string endPointId)
        {
            bool returnValue = false;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Revcell.DELETE_PLIVO_INFO;
                    cmd.Parameters.AddWithValue("@EndpointId", endPointId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Revcell, "DeletePlivoInfo", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    returnValue = (count >= 1) ? true : false;
                }

                if (_tenantId > 0)
                {
                    try
                    {
                        using (var conn = DALHelper.GetConnection(_tenantId))
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = DBConstants.Revcell.DELETE_PLIVO_INFO_MASTER;
                            cmd.Parameters.AddWithValue("@EndpointId", endPointId);
                            conn.Open();
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Revcell, "DeletePlivoInfo", _tenantId));

                            cmd.ExecuteNonQuery();
                        }
                    }
                    catch (Exception ex)
                    {
                        // Updating mtTenantUser
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return returnValue;
        }

        public PlivoInfo GetPlivoInfoByUserNum(int userNum)
        {
            try
            {
                PlivoInfo plivoInfo = new PlivoInfo();
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Revcell.GET_PLIVO_INFO;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Revcell, "GetPlivoInfoByUserNum", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        plivoInfo = ORMapper.MapPlivoInfo(dr);
                    }
                    conn.Close();
                    return plivoInfo;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public PlivoInfo GetPlivoInfoByUserEmail(string userEmail)
        {
            try
            {
                PlivoInfo plivoInfo = new PlivoInfo();
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Revcell.GET_PLIVO_INFO_BY_EMAIL;
                    cmd.Parameters.AddWithValue("@UserEmail", userEmail);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Revcell, "GetPlivoInfoByUserEmail", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        plivoInfo = ORMapper.MapPlivoInfo(dr);
                    }
                    conn.Close();
                    return plivoInfo;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion
    }
}