﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Criteria
{
    public class AuditCriteria
    {
        public int Id { get; set; }
        public int AppId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public AuditLogCategory AuditLogCategory { get; set; }
        public int PageSize { get; set; }
        public int PageNumber { get; set; }
        public bool AllPages { get; set; }
        public string WhereClause { get; set; }
    }
}
