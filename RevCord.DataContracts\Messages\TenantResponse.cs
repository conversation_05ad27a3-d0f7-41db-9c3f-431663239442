﻿using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.TenantEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class TenantResponse : ResponseBase
    {
        public List<TenantColumnModel> ColumnsModel { get; set; }
        public List<TenantCustomConfiguration> Configurations { get; set; }
        public TenantInformation TenantInformation { get; set; }
    }
}
