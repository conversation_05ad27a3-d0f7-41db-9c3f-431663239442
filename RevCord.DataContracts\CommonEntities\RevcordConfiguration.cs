﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.CommonEntities
{
    public class RevcordConfiguration
    {
        public int Id { get; set; }
        public bool NextGenRecorder { get; set; }
    }

    public class IPCameraSetting
    {
        public int Id { get; set; }
        public int UserNum { get; set; }
        public string CompanyName { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        public string Url { get; set; }
        public bool IsEnable { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }
    }

    public class IPCamUserAssociation
    {
        public int UserNum { get; set; }
        public int Ext { get; set; }
        public string UserName { get; set; }
        public bool IsIpCamAssociated { get; set; }
    }
}
