﻿using System;
using System.Collections.Generic;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.IQ3;

namespace RevCord.DataContracts.Messages
{
    public class UMResponse : ResponseBase
    {
        public List<GroupTree> AllGroups { get; set; }
        public int NoOfTreeNode { get; set; }

        public GroupTree AudioGroup { get; set; }

        public GroupTree Inquire_group { get; set; }
        public GroupTree MD_group { get; set; }

        public List<TreeviewData> inquireDbNodes { get; set; } // For Inquire Group Management : Arivu

        public List<GridViewData> gvData { get; set; } // For Inquire Group Management : Arivu

        public List<CustomMarkersData> lvData { get; set; } // For Inquire Custom Marker : Arivu
        public List<CustomMarkersData> lvData1 { get; set; } // For Inquire Custom Marker : Arivu

        public List<TreeviewData> DBExtension { get; set; }
        public List<EnterpriseNodeDTO> EnterpriseNodeDTO { get; set; }


        public List<RecorderGroupTree> RecorderGroups { get; set; }
        public RecorderGroupTree RecorderGroup { get; set; }
        public List<RecorderGroupTree> InquireRecorderGroups { get; set; }
        public List<RecorderUserOld> RecorderUsersOld { get; set; }
        public List<Tuple<int, int>> UserRecordersGroups { get; set; }

        public List<User> Users { get; set; }
        public List<AssignedSimpleUserRights> AssignedRights { get; set; } //For Simple User Rights : Arivu

        public long UserActivityId { get; set; }

        public List<UserActivity> UserActivities { get; set; }

        /************* Purpose: used for Paging ****************/
        public int TotalPages { get; set; }
        public long TotalRecords { get; set; }
        /************* Purpose: used for Paging ****************/
        public int UserNum { get; set; }

        public CustomField CustomField { get; set; }
        public List<CustomField> CustomFields { get; set; }
        public object Task { get; set; }
    }
}
