﻿using RevCord.BusinessLogic;
using RevCord.DataContracts.Messages;
using RevCord.ServiceContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.ServiceImplementation
{
    public class ADSyncGroupService : IADSyncGroupService
    {
        public ADSyncGroupResponse GetAllADGroups(ADSyncGroupRequest request)
        {
            return new ADSyncGroupManager().GetAllADGroups(request);
        }

        public ADSyncGroupResponse SaveADSyncGroup(ADSyncGroupRequest request)
        {
            return new ADSyncGroupManager().SaveADSyncGroup(request);
        }

        public ADSyncGroupResponse SyncADSyncGroup(ADSyncGroupRequest request)
        {
            return new ADSyncGroupManager().SyncADSyncGroup(request);
        }

        public ADSyncGroupResponse UnSyncADSyncGroup(ADSyncGroupRequest request)
        {
            return new ADSyncGroupManager().UnSyncADSyncGroup(request);
        }

        public ADSyncGroupResponse RemoveADSyncGroup(ADSyncGroupRequest request)
        {
            return new ADSyncGroupManager().RemoveADSyncGroup(request);
        }
    }
}
