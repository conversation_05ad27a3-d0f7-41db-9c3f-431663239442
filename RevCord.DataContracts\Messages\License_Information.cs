﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RevCord.DataContracts
{
    public class License_Information
    {
        int usernum;
        public int obj_usernum
        {
            get { return usernum; }
            set { usernum = value; }
        }

        int CUSTOMERID;
        public int obj_CUSTOMERID
        {
            get { return CUSTOMERID; }
            set { CUSTOMERID = value; }
        }

        int LICENSEID;
        public int obj_LICENSEID
        {
            get { return LICENSEID; }
            set { LICENSEID = value; }
        }

        string USERNAME;
        public string obj_USERNAME
        {
            get { return USERNAME; }
            set { USERNAME = value; }
        }

        string EMAILID;
        public string obj_EMAILID
        {
            get { return EMAILID; }
            set { EMAILID = value; }
        }
        string LICENSEPASSWORD;
        public string obj_LICENSEPASSWORD
        {
            get { return LICENSEPASSWORD; }
            set { LICENSEPASSWORD = value; }
        }
        string LOGINID;
        public string obj_LOGINID
        {
            get { return LOGINID; }
            set { LOGINID = value; }
        }
        string PASSWORD;
        public string obj_PASSWORD
        {
            get { return PASSWORD; }
            set { PASSWORD = value; }
        }

        string Company_Name;
        public string obj_Company_Name
        {
            get { return Company_Name; }
            set { Company_Name = value; }
        }

        int LICENSESTATUS;
        public int obj_LICENSESTATUS
        {
            get { return LICENSESTATUS; }
            set { LICENSESTATUS = value; }
        }
        DateTime EXPIREDATE;
        public DateTime obj_EXPIREDATE
        {
            get { return EXPIREDATE; }
            set { EXPIREDATE = value; }
        }
        DateTime LICCREATEDDATE;
        public DateTime obj_LICCREATEDDATE
        {
            get { return LICCREATEDDATE; }
            set { LICCREATEDDATE = value; }
        }
        DateTime LICLASTMODIFIEDDATE;
        public DateTime obj_LICLASTMODIFIEDDATE
        {
            get { return LICLASTMODIFIEDDATE; }
            set { LICLASTMODIFIEDDATE = value; }
        }

        int DeviceTypeId;
        public int obj_DeviceTypeId
        {
            get { return DeviceTypeId; }
            set { DeviceTypeId = value; }
        }
    }
}
