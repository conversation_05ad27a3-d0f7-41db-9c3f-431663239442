﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;
using System.Xml.Linq;

namespace RevCord.Util
{
    public static class StringHelper
    {
        /// <summary>
        /// Used to convert Collection of String to CSV String.
        /// </summary>
        /// <param name="timespan"></param>
        /// <returns></returns>
        public static string ConvertArrayToString(List<string> list)
        {
            string questionsids = "";
            int i = 0;
            StringBuilder ids = new StringBuilder();
            int b = list.Count();
            while (i < b)
            {
                ids.Append(Convert.ToString(list[i]));
                if (i < b - 1)
                {
                    ids.Append(",");
                }
                i++;
            }
            questionsids = ids.ToString();
            return questionsids;
        }

        public static StringBuilder RemoveLast(this StringBuilder sb, string value)
        {
            if (sb.Length < 1) return sb;
            sb.Remove(sb.ToString().LastIndexOf(value), value.Length);
            return sb;
        }

        public static string ConvertXmlStringToCsvString(this string xmlString)
        {
            try
            {
                //XmlDocument xmlDoc = new XmlDocument();
                //xmlDoc.LoadXml(xmlString);
                XDocument xmlDoc = XDocument.Parse(xmlString);
                //XDocument xmlDoc = XDocument.Load(xmlString);


                //var bookmarkQuery = from c in xmlDoc.Descendants("Root")
                //                    //where c.Parent.Attribute("Id").Value > "0"
                //                    select new
                //                    {
                //                        BookMark = c.Attribute("Text").Value,
                //                    };
                //return bookmarkQuery.ToString();
                StringBuilder sb = new StringBuilder();
                foreach (var profile in xmlDoc.Descendants("Record"))
                {
                    var id = profile.Element("Id").Value;
                    var callIndex = profile.Element("CallIndex").Value;
                    var pos = profile.Element("Pos").Value;
                    var text = profile.Element("Text").Value;
                    string s_test = profile.ToString();
                    if (s_test.Contains("<Notes>"))
                    {
                        var Notes = profile.Element("Notes").Value;
                    }
                    if (s_test.Contains("MarkerId"))
                    {
                        var MarkerId = profile.Element("MarkerId").Value;
                    }
                        
                    sb.Append(text);
                    sb.Append(", ");
                }
                return sb.RemoveLast(", ").ToString();
            }
            catch (Exception ex) { throw ex; }
        }
		
		       public static string Inquire_ConvertXmlStringToCsvString(this string xmlString, int calltype)
        {
            try
            {
                //XmlDocument xmlDoc = new XmlDocument();
                //xmlDoc.LoadXml(xmlString);
                XDocument xmlDoc = XDocument.Parse(xmlString);
                //XDocument xmlDoc = XDocument.Load(xmlString);


                //var bookmarkQuery = from c in xmlDoc.Descendants("Root")
                //                    //where c.Parent.Attribute("Id").Value > "0"
                //                    select new
                //                    {
                //                        BookMark = c.Attribute("Text").Value,
                //                    };
                //return bookmarkQuery.ToString();
                StringBuilder sb = new StringBuilder();
                foreach (var profile in xmlDoc.Descendants("Record"))
                {
                    var id = profile.Element("Id").Value;
                    var callIndex = profile.Element("CallIndex").Value;
                    var pos = profile.Element("Pos").Value;
                    var text = profile.Element("Text").Value;
                    string s_test = profile.ToString();
                    if (calltype == 7)
                    {
                        if (s_test.Contains("Notes"))
                        {
                            var Notes = profile.Element("Notes").Value;
                        }
                        if (s_test.Contains("MarkerId"))
                        {
                            var MarkerId = profile.Element("MarkerId").Value;
                        }
                    }
                    
                    sb.Append(text);
                    sb.Append(", ");
                }
                return sb.RemoveLast(", ").ToString();
            }
            catch (Exception ex) { throw ex; }
        }

        public static string FormatWith(this string format, params object[] args)
        {
            if (format == null)
                throw new ArgumentNullException("format");
            return string.Format(format, args);
        }

        #region Utility Functions
        public static string ProcesSimpleAccessRights(List<string> accessRights)
        {
            List<int> output = new List<int>();
            int iAccessRight = 0;
            foreach (var accessRight in accessRights)
            {
                iAccessRight = iAccessRight | Convert.ToInt32(accessRight, 2);
            }
            return GenerateBinaryStringFromInt(iAccessRight);
        }
        private static string GenerateBinaryStringFromInt(int intAccessRights)
        {
            int lengthOfAccessRightString = 10;
            char[] binary = new char[lengthOfAccessRightString];
            int position = 9;
            int iterator = 0;

            while (iterator < lengthOfAccessRightString)
            {
                if ((intAccessRights & (1 << iterator)) != 0)
                    binary[position] = '1';
                else
                    binary[position] = '0';
                position--;
                iterator++;
            }
            return new string(binary);
        }
        #endregion
    }
}
