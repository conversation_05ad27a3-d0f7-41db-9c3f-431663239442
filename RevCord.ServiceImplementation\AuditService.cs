﻿using RevCord.ServiceContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.Messages;
using RevCord.BusinessLogic;

namespace RevCord.ServiceImplementation
{
    public class AuditService : IAuditService
    {
        public AuditResponse GetAllAudits(AuditRequest auditRequest)
        {
            return new AuditManager().GetAllAudits(auditRequest);
        }

        public AuditResponse GetAuditsByCategory(AuditRequest auditRequest)
        {
            return new AuditManager().GetAuditsByCategory(auditRequest);
        }

        public AuditResponse GetAuditLogsCount(AuditRequest auditRequest)
        {
            return new AuditManager().GetAuditLogsCount(auditRequest);
        }

        public AuditResponse GetAuditDetails(AuditRequest auditRequest)
        {
            return new AuditManager().GetAuditDetails(auditRequest);
        }
    }
}
