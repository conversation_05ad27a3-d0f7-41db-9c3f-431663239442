﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.ReportEntities
{
    public class SavedReport
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int ReportTypeId { get; set; }
        //public ReportCategory ReportType { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public TypeOfReport TypeOfReport { get; set; }
        
        public string ReportName { get; set; }
        public string Criteria { get; set; }        // Criteria should be in json format, parsed to string
        public DateTime CreatedDate { get; set; }
        public DateTime? LastExecutedOn { get; set; }
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsFavorite { get; set; }
        public List<int> FavoriteOf { get; set; }
        public bool IsShared { get; set; }
        public bool IsScheduled { get; set; }
        public bool Status { get; set; }

        public SharedReportInfo SharedReportInfo { get; set; }
        public string UserName { get; set; }        //Will be used to show Shared By
        //public int GroupId { get; set; }
    }
}
