﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Diagnostics;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.Criteria;
using RevCord.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.MessageBase;
using RevCord.DataAccess.Util;

namespace RevCord.DataAccess
{
    public class SearchMediaDAL
    {
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);
        private int _tenantId;
        public SearchMediaDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        #region Primary Database Search

        public async Task<DALMediaResponse> GetDefaultSearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_PRIMARY_DB;//"vr_Call_SearchPrimarySM";

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;
                    try
                    {
                        long index = 0;
                        await conn.OpenAsync();
                        await Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.SearchMedia, "GetDefaultSearchResultsDTO", _tenantId));
                        sw.Start();
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToInt32(dr["CallType"]) == 7 ? (DBRecordExtensions.HasColumn(dr, "InspectionBookMarkXML") ? Convert.ToString(dr["InspectionBookMarkXML"]) : string.Empty) : Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    RevViewFileName = Convert.ToString(dr["RevViewFileName"]),
                                    RevViewStartTime = Convert.ToString(dr["RevViewStartTime"]),
                                    RevViewPhoneNumber = Convert.ToString(dr["RevViewPhoneNumber"]),
                                    RevViewAgentName = Convert.ToString(dr["RevViewAgentName"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0, //Convert.ToInt32(dr["ErrorInMuxProcess"]),
                                    VesselId = Convert.ToString(dr["VesselID"]),
                                    RapidSOS = Convert.ToString(dr["EmergencyData"]),
                                    CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty, //Convert.ToString(dr["TagName"]),
                                    TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty, //Convert.ToString(dr["TagName"]),
                                    TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty, //Convert.ToString(dr["TagColorID"]),
                                    InspectionTemplate = Convert.ToString(dr["InspectionTemplate"]),
                                    PreInspection = Convert.ToString(dr["PreInspection"]),
                                    InspectionIsPassed = Convert.ToBoolean(dr["InspectionIsPassed"]),
                                    InspectionIsPassFailRequired = Convert.ToBoolean(dr["InspectionIsPassFailRequired"]),
                                    InspectionStatus = Convert.ToString(dr["InspectionStatus"]),
                                    AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty,
                            };

                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }

        public async Task<DALMediaResponse> GetDefaultSearchResultsByEventIdDTO(string EventId)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_PRIMARY_DB_BYEVENTID;//"vr_Call_SearchPrimarySM";

                    cmd.Parameters.AddWithValue("@EventId", EventId);

                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;
                    try
                    {
                        long index = 0;
                        await conn.OpenAsync();
                        await Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.SearchMedia, "GetDefaultSearchResultsDTO", _tenantId));
                        sw.Start();
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToInt32(dr["CallType"]) == 7 ? (DBRecordExtensions.HasColumn(dr, "InspectionBookMarkXML") ? Convert.ToString(dr["InspectionBookMarkXML"]) : string.Empty) : Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    RevViewFileName = Convert.ToString(dr["RevViewFileName"]),
                                    RevViewStartTime = Convert.ToString(dr["RevViewStartTime"]),
                                    RevViewPhoneNumber = Convert.ToString(dr["RevViewPhoneNumber"]),
                                    RevViewAgentName = Convert.ToString(dr["RevViewAgentName"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0, //Convert.ToInt32(dr["ErrorInMuxProcess"]),
                                    VesselId = Convert.ToString(dr["VesselID"]),
                                    RapidSOS = Convert.ToString(dr["EmergencyData"]),
                                    CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty, //Convert.ToString(dr["TagName"]),
                                    TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty, //Convert.ToString(dr["TagName"]),
                                    TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty, //Convert.ToString(dr["TagColorID"]),
                                    InspectionTemplate = Convert.ToString(dr["InspectionTemplate"]),
                                    PreInspection = Convert.ToString(dr["PreInspection"]),
                                    InspectionIsPassed = Convert.ToBoolean(dr["InspectionIsPassed"]),
                                    InspectionIsPassFailRequired = Convert.ToBoolean(dr["InspectionIsPassFailRequired"]),
                                    InspectionStatus = Convert.ToString(dr["InspectionStatus"]),
                                    AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty,
                            };

                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = EventId,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }

        public async Task<IEnumerable<MediaInfo>> GetCustomSearchResultsAsync(int pageSize, int pageIndex, /*out int totalPages, out long totalRecords,*/string duration, string criteria, CallCriteria callCriteria)
        {
            List<MediaInfo> calls = new List<MediaInfo>();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "vr_Call_SearchPrimarySM";// DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        var sw = new Stopwatch();
                        sw.Start();
                        await conn.OpenAsync();
                        await Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.SearchMedia, "GetCustomSearchResultsAsync", _tenantId));
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                var call = new MediaInfo
                                {
                                    RowNo = (long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),

                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    RevViewFileName = Convert.ToString(dr["RevViewFileName"]),
                                    RevViewStartTime = Convert.ToString(dr["RevViewStartTime"]),
                                    RevViewPhoneNumber = Convert.ToString(dr["RevViewPhoneNumber"]),
                                    RevViewAgentName = Convert.ToString(dr["RevViewAgentName"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToInt32(dr["CallType"]) == 7 ? (DBRecordExtensions.HasColumn(dr, "InspectionBookMarkXML") ? Convert.ToString(dr["InspectionBookMarkXML"]) : string.Empty) : Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0,
                                    VesselId = Convert.ToString(dr["VesselID"]),
                                    AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty,
                            };
                                calls.Add(call);
                            }
                            //while (await dr.NextResultAsync())
                            //{
                            //    while (await dr.ReadAsync())
                            //    {
                            //    }
                            //}
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss")); //sw.Elapsed.TotalSeconds.ToString(@"hh\:mm\:ss\:fff"));//.ToString("hh:mm:ss:fff")
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                }
            }
            return calls;
        }


        public async Task<DALMediaResponse> GetAdvanceSearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_PRIMARY_DB;//"vr_Call_SearchPrimarySM";

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        sw.Start();
                        await conn.OpenAsync();
                        await Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.SearchMedia, "GetAdvanceSearchResultsDTO", _tenantId));
                        long index = 0;
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToInt32(dr["CallType"]) == 7 ? (DBRecordExtensions.HasColumn(dr, "InspectionBookMarkXML") ? Convert.ToString(dr["InspectionBookMarkXML"]) : string.Empty) : Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    RevViewFileName = Convert.ToString(dr["RevViewFileName"]),
                                    RevViewStartTime = Convert.ToString(dr["RevViewStartTime"]),
                                    RevViewPhoneNumber = Convert.ToString(dr["RevViewPhoneNumber"]),
                                    RevViewAgentName = Convert.ToString(dr["RevViewAgentName"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0,
                                    VesselId = Convert.ToString(dr["VesselID"]),
                                    RapidSOS = Convert.ToString(dr["EmergencyData"]),
                                    CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty, //Convert.ToString(dr["TagName"]),
                                    TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty, //Convert.ToString(dr["TagName"]),
                                    TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty, //Convert.ToString(dr["TagColorID"]),
                                    InspectionTemplate = Convert.ToString(dr["InspectionTemplate"]),
                                    PreInspection = Convert.ToString(dr["PreInspection"]),
                                    InspectionIsPassed = Convert.ToBoolean(dr["InspectionIsPassed"]),
                                    InspectionIsPassFailRequired = Convert.ToBoolean(dr["InspectionIsPassFailRequired"]),
                                    InspectionStatus = Convert.ToString(dr["InspectionStatus"]),
                                    AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty,
                            };
                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }


        #endregion


        #region Chained Databases Search

        public async Task<DALMediaResponse> PerformSearchChainedDBs(int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = pageIndex == 1 ? DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_SECONDARY_DBS : DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_SECONDARY_DBS_2N;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        long index = 0;
                        sw.Start();
                        await conn.OpenAsync();
                        await Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.SearchMedia, "PerformSearchChainedDBs", _tenantId));

                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToInt32(dr["CallType"]) == 7 ? (DBRecordExtensions.HasColumn(dr, "InspectionBookMarkXML") ? Convert.ToString(dr["InspectionBookMarkXML"]) : string.Empty) : Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    RevViewFileName = Convert.ToString(dr["RevViewFileName"]),
                                    RevViewStartTime = Convert.ToString(dr["RevViewStartTime"]),
                                    RevViewPhoneNumber = Convert.ToString(dr["RevViewPhoneNumber"]),
                                    RevViewAgentName = Convert.ToString(dr["RevViewAgentName"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0,
                                };
                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }


        #endregion


        #region QA Evaluation Search

        public async Task<DALMediaResponse> GetDefaultQASearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLS_SEARCH_MEDIA_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsRandom", callCriteria.IsRandom);
                    cmd.Parameters.AddWithValue("@IsPercentage", callCriteria.IsPercentage);
                    cmd.Parameters.AddWithValue("@IsAgentBasedSearch", callCriteria.IsAgentBasedSearch);
                    cmd.Parameters.AddWithValue("@NoOfCalls", callCriteria.NoOfCalls);
                    cmd.Parameters.AddWithValue("@AgentName", callCriteria.AgentName);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        long index = 0;
                        await conn.OpenAsync();
                        await Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.SearchMedia, "GetDefaultQASearchResultsDTO", _tenantId));

                        sw.Start();
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                #region read data from reader

                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag5 = Convert.ToString(dr["Tag5"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToInt32(dr["CallType"]) == 7 ? (DBRecordExtensions.HasColumn(dr, "InspectionBookMarkXML") ? Convert.ToString(dr["InspectionBookMarkXML"]) : string.Empty) : Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    RevViewFileName = Convert.ToString(dr["RevViewFileName"]),
                                    RevViewStartTime = Convert.ToString(dr["RevViewStartTime"]),
                                    RevViewPhoneNumber = Convert.ToString(dr["RevViewPhoneNumber"]),
                                    RevViewAgentName = Convert.ToString(dr["RevViewAgentName"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0, //Convert.ToInt32(dr["ErrorInMuxProcess"]),
                                    CallTagging = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty, //Convert.ToString(dr["TagName"]),
                                    TagName = DBRecordExtensions.HasColumn(dr, "TagName") ? Convert.ToString(dr["TagName"]) : string.Empty, //Convert.ToString(dr["TagName"]),
                                    TagColorID = DBRecordExtensions.HasColumn(dr, "TagColorID") ? Convert.ToString(dr["TagColorID"]) : string.Empty, //Convert.ToString(dr["TagColorID"]),
                                    InspectionTemplate = Convert.ToString(dr["InspectionTemplate"]),
                                    PreInspection = Convert.ToString(dr["PreInspection"]),
                                    InspectionIsPassed = Convert.ToBoolean(dr["InspectionIsPassed"]),
                                    InspectionIsPassFailRequired = Convert.ToBoolean(dr["InspectionIsPassFailRequired"]),
                                    InspectionStatus = Convert.ToString(dr["InspectionStatus"]),
                                };
                                #endregion

                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }

        public async Task<DALMediaResponse> GetAdvanceQASearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLS_SEARCH_MEDIA_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsRandom", callCriteria.IsRandom);
                    cmd.Parameters.AddWithValue("@IsPercentage", callCriteria.IsPercentage);
                    cmd.Parameters.AddWithValue("@IsAgentBasedSearch", callCriteria.IsAgentBasedSearch);
                    cmd.Parameters.AddWithValue("@NoOfCalls", callCriteria.NoOfCalls);
                    cmd.Parameters.AddWithValue("@AgentName", callCriteria.AgentName);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        long index = 0;
                        sw.Start();
                        await conn.OpenAsync();
                        await Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.SearchMedia, "GetAdvanceQASearchResultsDTO", _tenantId));

                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                #region read data from reader

                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag5 = Convert.ToString(dr["Tag5"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToInt32(dr["CallType"]) == 7 ? (DBRecordExtensions.HasColumn(dr, "InspectionBookMarkXML") ? Convert.ToString(dr["InspectionBookMarkXML"]) : string.Empty) : Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    RevViewFileName = Convert.ToString(dr["RevViewFileName"]),
                                    RevViewStartTime = Convert.ToString(dr["RevViewStartTime"]),
                                    RevViewPhoneNumber = Convert.ToString(dr["RevViewPhoneNumber"]),
                                    RevViewAgentName = Convert.ToString(dr["RevViewAgentName"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0, //Convert.ToInt32(dr["ErrorInMuxProcess"]),
                                    InspectionTemplate = Convert.ToString(dr["InspectionTemplate"]),
                                    PreInspection = Convert.ToString(dr["PreInspection"]),
                                    InspectionIsPassed = Convert.ToBoolean(dr["InspectionIsPassed"]),
                                    InspectionIsPassFailRequired = Convert.ToBoolean(dr["InspectionIsPassFailRequired"]),
                                    InspectionStatus = Convert.ToString(dr["InspectionStatus"]),

                                };
                                #endregion

                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }


        public async Task<DALMediaResponse> PerformQASearchChainedDBs(int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = pageIndex == 1 ? DBConstants.Evaluation.CALLS_SEARCH_MEDIA_SECONDARY_DBS : DBConstants.Evaluation.CALLS_SEARCH_MEDIA_SECONDARY_DBS_2N;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsRandom", callCriteria.IsRandom);
                    cmd.Parameters.AddWithValue("@IsPercentage", callCriteria.IsPercentage);
                    cmd.Parameters.AddWithValue("@IsAgentBasedSearch", callCriteria.IsAgentBasedSearch);
                    cmd.Parameters.AddWithValue("@NoOfCalls", callCriteria.NoOfCalls);
                    cmd.Parameters.AddWithValue("@AgentName", callCriteria.AgentName);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        long index = 0;
                        sw.Start();
                        await conn.OpenAsync();
                        await Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.SearchMedia, "PerformQASearchChainedDBs", _tenantId));

                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                #region read data from reader

                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag5 = Convert.ToString(dr["Tag5"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToInt32(dr["CallType"]) == 7 ? (DBRecordExtensions.HasColumn(dr, "InspectionBookMarkXML") ? Convert.ToString(dr["InspectionBookMarkXML"]) : string.Empty) : Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    RevViewFileName = Convert.ToString(dr["RevViewFileName"]),
                                    RevViewStartTime = Convert.ToString(dr["RevViewStartTime"]),
                                    RevViewPhoneNumber = Convert.ToString(dr["RevViewPhoneNumber"]),
                                    RevViewAgentName = Convert.ToString(dr["RevViewAgentName"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0, //Convert.ToInt32(dr["ErrorInMuxProcess"]),

                                };
                                #endregion

                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }

        #endregion


    }
}