﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.ByteArrayContent">
      <summary>Stellt HTTP-Inhalt auf Grundlage von einem Bytearray bereit.</summary>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.ByteArrayContent" />-Klasse.</summary>
      <param name="content">Der Inhalt, der zum Initialisieren der <see cref="T:System.Net.Http.ByteArrayContent" /> verwendet wird.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="content" />-Parameter ist null. </exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.ByteArrayContent" />-Klasse.</summary>
      <param name="content">Der Inhalt, der zum Initialisieren der <see cref="T:System.Net.Http.ByteArrayContent" /> verwendet wird.</param>
      <param name="offset">Der Offset in Bytes im <paramref name="content" />-Parameter, der verwendet wird, um den <see cref="T:System.Net.Http.ByteArrayContent" /> zu initialisieren.</param>
      <param name="count">Die Anzahl der Bytes in <paramref name="content" /> ab dem <paramref name="offset" />-Parameter, die zum Initialisieren von <see cref="T:System.Net.Http.ByteArrayContent" /> benutzt werden.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="content" />-Parameter ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="offset" />-Parameter ist kleiner als 0 (null).- oder -Der <paramref name="offset" />-Parameter größer als die Länge des Inhalts, der durch den <paramref name="content" />-Parameters angegeben wird.- oder -Der <paramref name="count " />-Parameter ist kleiner als 0 (null).- oder -Der <paramref name="count" />-Parameter größer als die Länge des Inhalts, der durch den <paramref name="content" />-Parameters - minus dem <paramref name="offset" />-Parameter angegeben wird.</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.CreateContentReadStreamAsync">
      <summary>Erstellt einen HTTP-Inhaltsdatenstrom als asynchronen Vorgang zum Lesen, dessen Sicherungsspeicher Arbeitsspeicher von <see cref="T:System.Net.Http.ByteArrayContent" /> ist.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Das Bytearray, das im Konstruktor bereitgestellt wird, als asynchronen Vorgang in einen HTTP-Inhaltsstream serialisieren und schreiben.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task" /> zurück. Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="stream">Die Zielstream.</param>
      <param name="context">Informationen über den Transport, zum Beispiel Channelbindungstoken.Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.TryComputeLength(System.Int64@)">
      <summary>Bestimmt, ob ein Bytearray eine gültige Länge in Bytes enthält.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="length" /> eine gültige Länge ist, andernfalls false.</returns>
      <param name="length">Die Länge des Bytearrays in Bytes.</param>
    </member>
    <member name="T:System.Net.Http.ClientCertificateOption">
      <summary>Gibt an, wie die Clientzertifikate bereitgestellt werden.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Automatic">
      <summary>Der <see cref="T:System.Net.Http.HttpClientHandler" /> versucht, alle verfügbaren Clientzertifikate automatisch bereitzustellen.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Manual">
      <summary>Die Anwendung stellt manuell die Clientzertifikate für den <see cref="T:System.Net.Http.WebRequestHandler" /> bereit.Dies ist der Standardwert.</summary>
    </member>
    <member name="T:System.Net.Http.DelegatingHandler">
      <summary>Ein Typ für HTTP-Handler, die die Verarbeitung von HTTP-Antwortnachrichten in einen anderen Handler (den internen Handler) delegieren.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.DelegatingHandler" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.DelegatingHandler" />-Klasse mit einem bestimmten inneren Handler.</summary>
      <param name="innerHandler">Der innere Handler, der für die Verarbeitung der HTTP-Antwortnachrichten zuständig ist.</param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Http.DelegatingHandler" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen. </param>
    </member>
    <member name="P:System.Net.Http.DelegatingHandler.InnerHandler">
      <summary>Ruft den internen Handler ab, der die HTTP-Antwortnachrichten verarbeitet, oder legt diesen fest.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpMessageHandler" /> zurück.Der innere Handler für HTTP-Antwortnachrichten.</returns>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Sendet eine HTTP-Anforderung an den internen Handler zum Senden an den Server als asynchronen Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück. Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="request">Die an den Server zu sendende HTTP-Anforderungsnachricht.</param>
      <param name="cancellationToken">Ein Abbruchtoken, um den Vorgang abzubrechen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> war null.</exception>
    </member>
    <member name="T:System.Net.Http.FormUrlEncodedContent">
      <summary>Ein Container für die Name/Wert-Tupel, codiert mit dem "application/x-www-form-urlencoded" MIME-Typ.</summary>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.FormUrlEncodedContent" />-Klasse mit einer bestimmten Auflistung von Namen/Wert-Paaren.</summary>
      <param name="nameValueCollection">Eine Sammlung von Name-Wert-Paaren.</param>
    </member>
    <member name="T:System.Net.Http.HttpClient">
      <summary>Stellt eine Basisklasse zum Senden von HTTP-Anforderungen und Empfangen von HTTP-Antworten aus einer Ressource bereit, die von einem URI identifiziert wird. </summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpClient" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpClient" />-Klasse mit einem bestimmten Handler.</summary>
      <param name="handler">Der HTTP-Handlerstapel, der zum Senden von Anforderungen zu verwenden ist. </param>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpClient" />-Klasse mit einem bestimmten Handler.</summary>
      <param name="handler">Der <see cref="T:System.Net.Http.HttpMessageHandler" />, der für die Verarbeitung der HTTP-Antwortnachrichten verantwortlich ist.</param>
      <param name="disposeHandler">true, wenn der innere Handler von Dispose() verworfen werden soll, false, wenn Sie beabsichtigen, den inneren Handler wiederzuverwenden.</param>
    </member>
    <member name="P:System.Net.Http.HttpClient.BaseAddress">
      <summary>Ruft die Basisadresse des URI (Uniform Resource Identifier) der Internetressource ab, die verwendet wird, wenn Anforderungen gesendet werden, oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.Uri" />zurück.Die Basisadresse des URI (Uniform Resource Identifier) der Internetressource, die verwendet wird, wenn Anforderungen gesendet werden.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.CancelPendingRequests">
      <summary>Alle ausstehenden Anforderungen für diese Instanz abbrechen.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultRequestHeaders">
      <summary>Ruft die Header ab, die mit jeder Anforderung gesendet werden sollen.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />zurück.Die Header, die mit jeder Anforderung gesendet werden sollen.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String)">
      <summary>Eine DELETE-Anforderung an den angegebenen URI als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
      <exception cref="T:System.InvalidOperationException">Die Anforderungsnachricht wurde bereits von der <see cref="T:System.Net.Http.HttpClient" />-Instanz gesendet.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String,System.Threading.CancellationToken)">
      <summary>Eine DELETE-Anforderung für den angegebenen URI mit einem Abbruchtoken als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
      <exception cref="T:System.InvalidOperationException">Die Anforderungsnachricht wurde bereits von der <see cref="T:System.Net.Http.HttpClient" />-Instanz gesendet.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri)">
      <summary>Eine DELETE-Anforderung an den angegebenen URI als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
      <exception cref="T:System.InvalidOperationException">Die Anforderungsnachricht wurde bereits von der <see cref="T:System.Net.Http.HttpClient" />-Instanz gesendet.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>Eine DELETE-Anforderung für den angegebenen URI mit einem Abbruchtoken als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
      <exception cref="T:System.InvalidOperationException">Die Anforderungsnachricht wurde bereits von der <see cref="T:System.Net.Http.HttpClient" />-Instanz gesendet.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpClient" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen.</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String)">
      <summary>Eine GET-Anforderung an den angegebenen URI als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption)">
      <summary>Eine GET-Anforderung an den angegebenen URI mit einer HTTP-Abschlussoption als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="completionOption">Ein HTTP-Abschlussoptions-Wert, der angibt, wann die Operation als abgeschlossen betrachtet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Eine GET-Anforderung an den angegebenen URI mit einer HTTP-Abschlussoption und einem Abbruchtoken als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="completionOption">Ein HTTP-Abschlussoptions-Wert, der angibt, wann die Operation als abgeschlossen betrachtet werden soll.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Threading.CancellationToken)">
      <summary>Eine GET-Anforderung für den angegebenen URI mit einem Abbruchtoken als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri)">
      <summary>Eine GET-Anforderung an den angegebenen URI als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption)">
      <summary>Eine GET-Anforderung an den angegebenen URI mit einer HTTP-Abschlussoption als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="completionOption">Ein HTTP-Abschlussoptions-Wert, der angibt, wann die Operation als abgeschlossen betrachtet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Eine GET-Anforderung an den angegebenen URI mit einer HTTP-Abschlussoption und einem Abbruchtoken als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="completionOption">Ein HTTP-Abschlussoptions-Wert, der angibt, wann die Operation als abgeschlossen betrachtet werden soll.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>Eine GET-Anforderung für den angegebenen URI mit einem Abbruchtoken als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.String)">
      <summary>Senden Sie eine GET-Anforderung an den angegebenen URI und geben Sie den Antworttext als Bytearray in einem asynchronen Vorgang zurück.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.Uri)">
      <summary>Senden Sie eine GET-Anforderung an den angegebenen URI und geben Sie den Antworttext als Bytearray in einem asynchronen Vorgang zurück.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.String)">
      <summary>Senden Sie eine GET-Anforderung an den angegebenen URI und geben Sie den Antworttext als Datenstrom in einem asynchronen Vorgang zurück.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.Uri)">
      <summary>Senden Sie eine GET-Anforderung an den angegebenen URI und geben Sie den Antworttext als Datenstrom in einem asynchronen Vorgang zurück.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.String)">
      <summary>Senden Sie eine GET-Anforderung an den angegebenen URI und geben Sie den Antworttext als Zeichenfolge in einem asynchronen Vorgang zurück.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.Uri)">
      <summary>Senden Sie eine GET-Anforderung an den angegebenen URI und geben Sie den Antworttext als Zeichenfolge in einem asynchronen Vorgang zurück.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.MaxResponseContentBufferSize">
      <summary>Ruft beim Lesen des Antwortinhalts die maximale Anzahl zwischenzuspeichernder Bytes ab oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.Int32" />zurück.Die maximale Anzahl zu puffernder Bytes, wenn der Antwortinhalt gelesen wird.Der Standardwert für diese Eigenschaft ist 2 GB.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Der angegebene Größe ist kleiner als oder gleich 0 (null).</exception>
      <exception cref="T:System.InvalidOperationException">Ein Vorgang ist bereits in der aktuellen Instanz gestartet worden. </exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen. </exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Eine POST-Anforderung an den angegebenen URI als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="content">Der Inhalt der HTTP-Anforderung, die an den Server gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Eine POST-Anforderung mit einem Abbruchtoken als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="content">Der Inhalt der HTTP-Anforderung, die an den Server gesendet wird.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Eine POST-Anforderung an den angegebenen URI als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="content">Der Inhalt der HTTP-Anforderung, die an den Server gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Eine POST-Anforderung mit einem Abbruchtoken als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="content">Der Inhalt der HTTP-Anforderung, die an den Server gesendet wird.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Eine PUT-Anforderung an den angegebenen URI als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="content">Der Inhalt der HTTP-Anforderung, die an den Server gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Eine PUT-Anforderung mit einem Abbruchtoken als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="content">Der Inhalt der HTTP-Anforderung, die an den Server gesendet wird.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Eine PUT-Anforderung an den angegebenen URI als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="content">Der Inhalt der HTTP-Anforderung, die an den Server gesendet wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Eine PUT-Anforderung mit einem Abbruchtoken als asynchronen Vorgang senden.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="requestUri">Der URI, an den die Anforderung gesendet wird.</param>
      <param name="content">Der Inhalt der HTTP-Anforderung, die an den Server gesendet wird.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage)">
      <summary>Senden Sie eine HTTP-Anforderung als asynchronen Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="request">Die zu sendende HTTP-Anforderungsmeldung.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> war null.</exception>
      <exception cref="T:System.InvalidOperationException">Die Anforderungsnachricht wurde bereits von der <see cref="T:System.Net.Http.HttpClient" />-Instanz gesendet.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption)">
      <summary>Senden Sie eine HTTP-Anforderung als asynchronen Vorgang. </summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="request">Die zu sendende HTTP-Anforderungsmeldung.</param>
      <param name="completionOption">Wann der Vorgang abgeschlossen werden sollte (sobald eine Antwort verfügbar ist, oder nach dem Lesen des gesamten Inhalts der Antwort).</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> war null.</exception>
      <exception cref="T:System.InvalidOperationException">Die Anforderungsnachricht wurde bereits von der <see cref="T:System.Net.Http.HttpClient" />-Instanz gesendet.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Senden Sie eine HTTP-Anforderung als asynchronen Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="request">Die zu sendende HTTP-Anforderungsmeldung.</param>
      <param name="completionOption">Wann der Vorgang abgeschlossen werden sollte (sobald eine Antwort verfügbar ist, oder nach dem Lesen des gesamten Inhalts der Antwort).</param>
      <param name="cancellationToken">Das Abbruchtoken, um den Vorgang abzubrechen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> war null.</exception>
      <exception cref="T:System.InvalidOperationException">Die Anforderungsnachricht wurde bereits von der <see cref="T:System.Net.Http.HttpClient" />-Instanz gesendet.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Senden Sie eine HTTP-Anforderung als asynchronen Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="request">Die zu sendende HTTP-Anforderungsmeldung.</param>
      <param name="cancellationToken">Das Abbruchtoken, um den Vorgang abzubrechen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> war null.</exception>
      <exception cref="T:System.InvalidOperationException">Die Anforderungsnachricht wurde bereits von der <see cref="T:System.Net.Http.HttpClient" />-Instanz gesendet.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.Timeout">
      <summary>Ruft die Zeitspanne ab, nach der das Zeitlimit der Anforderung überschritten ist, oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.TimeSpan" />zurück.Die Zeitspanne, nach der das Zeitlimit der Anforderung überschritten ist.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Der angegebene Timout ist kleiner oder gleich 0 (null) und nicht <see cref="F:System.Threading.Timeout.InfiniteTimeSpan" />.</exception>
      <exception cref="T:System.InvalidOperationException">Ein Vorgang ist bereits in der aktuellen Instanz gestartet worden. </exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen.</exception>
    </member>
    <member name="T:System.Net.Http.HttpClientHandler">
      <summary>Der standardmäßige Meldungshandler für <see cref="T:System.Net.Http.HttpClient" />.  </summary>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.#ctor">
      <summary>Erstellt eine Instanz einer <see cref="T:System.Net.Http.HttpClientHandler" />-Klasse.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect">
      <summary>Ruft einen Wert ab, der angibt, ob der Handler Umleitungsantworten folgen soll, oder legt diesen Wert fest.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.Wenn true, wenn der Handler Umleitungsantworten folgen soll; andernfalls false.Der Standardwert ist true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AutomaticDecompression">
      <summary>Ruft den Typ der Dekomprimierungsmethode ab, die vom Handler für die automatische Dekomprimierung der HTTP-Inhalt-Antwort verwendet wird, oder legt diesen fest.</summary>
      <returns>Gibt <see cref="T:System.Net.DecompressionMethods" /> zurück.Die vom Handler zu benutzende automatische Dekomprimierungsmethode.Der Standardwert ist <see cref="F:System.Net.DecompressionMethods.None" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.ClientCertificateOptions">
      <summary>Ruft die diesem Handler zugeordnete Auflistung von Sicherheitszertifikaten ab oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.ClientCertificateOption" /> zurück.Die Auflistung von Sicherheitszertifikaten, die diesem Handler zugeordnet sind.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.CookieContainer">
      <summary>Ruft den Cookiecontainer zum Speichern von Servercookies durch den Handler ab oder diesen fest.</summary>
      <returns>Gibt <see cref="T:System.Net.CookieContainer" /> zurück.Der Cookie-Container zum Speichern von Server-Cookies durch den Handler.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Credentials">
      <summary>Ruft die Authentifizierungsinformationen ab, die vom Handler verwendet wurden, oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.Net.ICredentials" /> zurück.Die der Authentifizierungsanforderung zugeordnete Handler.Der Standardwert ist null.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpClientHandler" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen.</param>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections">
      <summary>Ruft die maximale Anzahl von Umleitungen ab, denen der Handler folgt, oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Die maximale Anzahl von Umleitungsantworten, denen der Handler folgt.Der Standardwert ist 50.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxRequestContentBufferSize">
      <summary>Ruft die maximale Größe des Anforderungsinhaltpuffers ab, der vom Handler verwendet wird, oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Die maximale Größe des Anforderungsinhaltpuffers in Byte.Der Standardwert beträgt 2 GB.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.PreAuthenticate">
      <summary>Ruft einen Wert ab, der angibt, ob der Handler mit der Anforderung ein Autorisierungsheader sendet, oder legt diesen fest.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true für den Handler zum Senden eines HTTP-Autorisierungsheaders mit Anforderungen nach einer Authentifizierung, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Proxy">
      <summary>Ruft die Proxyinformationen ab, die vom Handler verwendet werden, oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.Net.IWebProxy" /> zurück.Die Proxyinformationen, die vom Handler verwendet werden.Der Standardwert ist null.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Erstellt eine Instanz von <see cref="T:System.Net.Http.HttpResponseMessage" /> auf Grundlage der Informationen, die in <see cref="T:System.Net.Http.HttpRequestMessage" /> als Operation bereitgestellt werden, der nicht blockiert.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="request">Die HTTP-Anforderungsnachricht.</param>
      <param name="cancellationToken">Ein Abbruchtoken, um den Vorgang abzubrechen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> war null.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsAutomaticDecompression">
      <summary>Ruft einen Wert ab, der angibt, ob der Handler die automatische Antwort-Inhaltsdekomprimierung unterstützt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.Wenn true, wenn der Handler die automatische Dekomprimierung von Antwortinhalt unterstützt; andernfalls false.Der Standardwert ist true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsProxy">
      <summary>Ruft einen Wert ab, der angibt, ob der Handler die Proxyeinstellungen unterstützt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.Wenn true, wenn der Handler die Proxyeinstellungen unterstützt; andernfalls false.Der Standardwert ist true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsRedirectConfiguration">
      <summary>Ruft einen Wert ab, der angibt, ob der Handler Konfigurationseinstellungen für die <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" />- und <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" />-Eigenschaften unterstützt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.Wenn true, wenn der Handler Konfigurationseinstellungen für die <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" />- und <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" />-Eigenschaften unterstützt; andernfalls false.Der Standardwert ist true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseCookies">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob der Handler die <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" />-Eigenschaft zum Speichern von Servercookies verwendet und die Cookies beim Senden von Anforderungen nutzt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.Wenn true, wenn der Handler die <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" />-Eigenschaft zum Speichern von Servercookies verwendet und diese Cookies beim Senden von Anforderungen verwendet; andernfalls false.Der Standardwert ist true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseDefaultCredentials">
      <summary>Ruft einen Wert ab, der steuert, ob mit den Anforderungen vom Handler Standardanmeldeinformationen gesendet werden, oder legt diesen fest.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn die standardmäßigen Anmeldeinformationen verwendet werden, andernfalls false.Der Standardwert ist false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseProxy">
      <summary>Ruft einen Wert ab bzw. legt einen Wert fest, der angibt, ob der Handler einen Proxy für Anforderungen verwendet. </summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der Handler einen Proxy für Anforderungen verwenden soll; andernfalls false.Der Standardwert ist true.</returns>
    </member>
    <member name="T:System.Net.Http.HttpCompletionOption">
      <summary>Gibt an, ob <see cref="T:System.Net.Http.HttpClient" />-Vorgänge als abgeschlossen betrachtet werden, sobald eine Antwort verfügbar ist, oder nachdem die gesamte Antwortnachricht einschließlich Inhalt gelesen wurde. </summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseContentRead">
      <summary>Der Vorgang sollte beendet werden, nachdem er die gesamte Antwort einschließlich des Inhalts gelesen hat.</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseHeadersRead">
      <summary>Der Vorgang sollte beendet werden, sobald eine Antwort vorliegt und die Header gelesen wurden.Der Inhalts noch ist nicht bereit.</summary>
    </member>
    <member name="T:System.Net.Http.HttpContent">
      <summary>Eine Basisklasse, die einen HTTP-Entitätentext und Inhaltsheader darstellt.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpContent" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream)">
      <summary>Serialisieren Sie den HTTP-Inhalt in einen Stream von Bytes und kopieren Sie dieses in das Streamobjekt, das als <paramref name="stream" />-Parameter bereitgestellt wird.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="stream">Die Zielstream.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialisieren Sie den HTTP-Inhalt in einen Stream von Bytes und kopieren Sie dieses in das Streamobjekt, das als <paramref name="stream" />-Parameter bereitgestellt wird.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="stream">Die Zielstream.</param>
      <param name="context">Informationen über den Transport (z. B. Channelbindungstoken).Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CreateContentReadStreamAsync">
      <summary>Serialisiert den HTTP-Inhalt in einen Arbeitsspeicherstream als asynchroner Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpContent" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft die verwalteten Ressourcen.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpContent" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen.</param>
    </member>
    <member name="P:System.Net.Http.HttpContent.Headers">
      <summary>Ruft die HTTP-Inhaltsheader wie in RFC 2616 definiert ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpContentHeaders" /> zurück.Die Inhaltsheader gemäß RFC 2616.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync">
      <summary>Serialisiert den HTTP-Inhalt in einen Arbeitsspeicherpuffer als asynchroner Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync(System.Int64)">
      <summary>Serialisiert den HTTP-Inhalt in einen Arbeitsspeicherpuffer als asynchroner Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="maxBufferSize">Die maximale Größe des zu verwendenden Puffers in Byte.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsByteArrayAsync">
      <summary>Serialisieren Sie den HTTP-Inhalt in ein Bytearray als asynchroner Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStreamAsync">
      <summary>Serialisieren Sie den HTTP-Inhalt und geben Sie einen Stream zurück, der den Inhalt als asynchroner Vorgang darstellt. </summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStringAsync">
      <summary>Den HTTP-Inhalt in eine Zeichenfolge als asynchronen Vorgang serialisieren.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Den HTTP-Inhalt in einen Stream als asynchronen Vorgang serialisieren.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="stream">Die Zielstream.</param>
      <param name="context">Informationen über den Transport (z. B. Channelbindungstoken).Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.TryComputeLength(System.Int64@)">
      <summary>Bestimmt, ob der HTTP-Inhalt eine gültige Länge in Bytes enthält.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="length" /> eine gültige Länge ist, andernfalls false.</returns>
      <param name="length">Die Länge des HTTP-Inhalts in Bytes.</param>
    </member>
    <member name="T:System.Net.Http.HttpMessageHandler">
      <summary>Ein Basistyp für HTTP-Message-Handler.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpMessageHandler" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpMessageHandler" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft die verwalteten Ressourcen.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpMessageHandler" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Senden Sie eine HTTP-Anforderung als asynchroner Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="request">Die zu sendende HTTP-Anforderungsmeldung.</param>
      <param name="cancellationToken">Das Abbruchtoken, um den Vorgang abzubrechen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> war null.</exception>
    </member>
    <member name="T:System.Net.Http.HttpMessageInvoker">
      <summary>Eine Spezialitätenklasse, die es Anwendungen ermöglicht, die <see cref="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)" /> Methode auf einer HTTP-Handlerkette aufzurufen. </summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Initialisiert eine neue Instanz einer <see cref="T:System.Net.Http.HttpMessageInvoker" />-Klasse mit einem bestimmten <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
      <param name="handler">Der <see cref="T:System.Net.Http.HttpMessageHandler" />, der für die Verarbeitung der HTTP-Antwortnachrichten verantwortlich ist.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>Initialisiert eine neue Instanz einer <see cref="T:System.Net.Http.HttpMessageInvoker" />-Klasse mit einem bestimmten <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
      <param name="handler">Der <see cref="T:System.Net.Http.HttpMessageHandler" />, der für die Verarbeitung der HTTP-Antwortnachrichten verantwortlich ist.</param>
      <param name="disposeHandler">true, wenn der innere Handler von Dispose() verworfen werden sollte,false, wenn Sie beabsichtigen, den inneren Handler wiederzuverwenden.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpMessageInvoker" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft die verwalteten Ressourcen.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpMessageInvoker" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Senden Sie eine HTTP-Anforderung als asynchroner Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="request">Die zu sendende HTTP-Anforderungsmeldung.</param>
      <param name="cancellationToken">Das Abbruchtoken, um den Vorgang abzubrechen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> war null.</exception>
    </member>
    <member name="T:System.Net.Http.HttpMethod">
      <summary>Eine Hilfsprogrammklasse für das Abrufen und das Vergleichen von Standard-HTTP-Methoden und zum Erstellen von neuen HTTP-Methoden.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMethod.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpMethod" />-Klasse mit einem bestimmten HTTP-Methode.</summary>
      <param name="method">Die HTTP-Methode</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Delete">
      <summary>Stellt eine HTTP DELTE-Protokollmethode dar.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpMethod" /> zurück.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Net.Http.HttpMethod)">
      <summary>Bestimmt, ob die angegebene <see cref="T:System.Net.Http.HttpMethod" /> und die aktuelle <see cref="T:System.Object" /> gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene Objekt und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="other">Die HTTP-Methode, die mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Object)">
      <summary>Bestimmt, ob die angegebene <see cref="T:System.Object" /> und die aktuelle <see cref="T:System.Object" /> gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene Objekt und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Get">
      <summary>Stellt eine HTTP GET-Protokollmethode dar.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpMethod" /> zurück.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.GetHashCode">
      <summary>Fungiert als Hashfunktion für diesen Typ.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Head">
      <summary>Stellt eine HTTP HEAD-Protokollmethode dar.Die HEAD-Methode ist mit der GET-Methode identisch, bis auf den Unterschied, dass der Server in der Antwort nur Meldungsheader und keinen Meldungstext zurückgibt.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpMethod" /> zurück.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Method">
      <summary>Eine HTTP-Methode. </summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Die HTTP-Methode dargestellt als <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Equality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>Der Gleichheitsoperator (=) zum Vergleichen von zwei <see cref="T:System.Net.Http.HttpMethod" />-Objekten.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der angegebene <paramref name="left" />-Parameter und der angegebene <paramref name="right" />-Parameter gleich sind, andernfalls false.</returns>
      <param name="left">Die linke <see cref="T:System.Net.Http.HttpMethod" /> für einen Gleichheitsoperator.</param>
      <param name="right">Die rechte <see cref="T:System.Net.Http.HttpMethod" /> zu einem Gleichheitsoperator.</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Inequality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>Der Ungleichheitsoperator (=) zum Vergleichen von zwei <see cref="T:System.Net.Http.HttpMethod" />-Objekten.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der angegebene <paramref name="left" />-Parameter und der angegebene <paramref name="right" />-Parameter ungleich sind, andernfalls false.</returns>
      <param name="left">Die linke <see cref="T:System.Net.Http.HttpMethod" /> für einen Ungleichheitsoperator.</param>
      <param name="right">Die rechte <see cref="T:System.Net.Http.HttpMethod" /> zu einem Ungleichheitsoperator.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Options">
      <summary>Stellt eine HTTP OPTIONS-Protokollmethode dar.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpMethod" /> zurück.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Post">
      <summary>Stellt eine HTTP POST-Protokollmethode dar, die verwendet wird, um eine neue Entität als Zusatz zu einem URI zu senden.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpMethod" /> zurück.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Put">
      <summary>Stellt eine HTTP PUT-Protokollmethode dar, die verwendet wird, um eine durch einen URI bezeichnete Entität zu ersetzen.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpMethod" /> zurück.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Trace">
      <summary>Stellt eine HTTP TRACE-Protokollmethode dar.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpMethod" /> zurück.</returns>
    </member>
    <member name="T:System.Net.Http.HttpRequestException">
      <summary>Eine Basisklasse für Ausnahmen, die von den Klassen <see cref="T:System.Net.Http.HttpClient" /> und <see cref="T:System.Net.Http.HttpMessageHandler" /> ausgelöst werden.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpRequestException" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpRequestException" />-Klasse mit einer bestimmten Meldung, die die aktuelle Ausnahme beschreibt.</summary>
      <param name="message">Eine Meldung, die die aktuelle Ausnahme beschreibt.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpRequestException" />-Klasse mit einer bestimmten Meldung, die die aktuelle Ausnahme beschreibt, und einer inneren Ausnahme.</summary>
      <param name="message">Eine Meldung, die die aktuelle Ausnahme beschreibt.</param>
      <param name="inner">Die innere Ausnahme.</param>
    </member>
    <member name="T:System.Net.Http.HttpRequestMessage">
      <summary>Stellt eine HTTP-Nachrichtenanfrage dar.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpRequestMessage" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpRequestMessage" />-Klasse mit einer HTTP-Methode und einer Anforderungs-<see cref="T:System.Uri" />.</summary>
      <param name="method">Die HTTP-Methode</param>
      <param name="requestUri">Eine Zeichenfolge, die die Anforderung <see cref="T:System.Uri" /> darstellt.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.Uri)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpRequestMessage" />-Klasse mit einer HTTP-Methode und einer Anforderungs-<see cref="T:System.Uri" />.</summary>
      <param name="method">Die HTTP-Methode</param>
      <param name="requestUri">Das anzufordernde <see cref="T:System.Uri" />.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Content">
      <summary>Ruft den Inhalt der HTTP-Meldung ab oder legt diesen fest. </summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpContent" /> zurück.Der Nachrichteninhalt.</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpRequestMessage" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft die verwalteten Ressourcen.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpRequestMessage" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Headers">
      <summary>Ruft die Auflistung von HTTP-Anforderungsheadern ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" /> zurück.Eine Auflistung von HTTP-Anforderungsheadern.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Method">
      <summary>Ruft die von der HTTP-Anforderungsmeldung verwendete HTTP-Methode ab oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpMethod" /> zurück.Die HTTP-Methode, die von der Anforderungnachricht benutzt wurde.Standard ist die GET-Methode.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Properties">
      <summary>Ruft eine Gruppe von Eigenschaften für die HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.IDictionary`2" /> zurück.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.RequestUri">
      <summary>Übernimmt oder bestimmt den <see cref="T:System.Uri" />, der für die HTTP-Anforderung verwendet wird.</summary>
      <returns>Gibt <see cref="T:System.Uri" /> zurück.Der <see cref="T:System.Uri" />, der für die aktuelle HTTP-Anforderung verwendet wird.</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolgendarstellung des aktuellen Objekts.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Version">
      <summary>Ruft die HTTP-Meldungsversion ab oder legt sie fest.</summary>
      <returns>Gibt <see cref="T:System.Version" /> zurück.Die HTTP-Nachrichtenversion.Standardwert: 1.1.</returns>
    </member>
    <member name="T:System.Net.Http.HttpResponseMessage">
      <summary>Stellt eine HTTP-Antwortnachricht einschließlich den Statuscodes und der Daten dar.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpResponseMessage" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor(System.Net.HttpStatusCode)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.HttpResponseMessage" />-Klasse mit einem bestimmten <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" />.</summary>
      <param name="statusCode">Der Statuscode der ATTP-Antwort.</param>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Content">
      <summary>Ruft den Inhalt einer HTTP-Antwortmeldung ab oder legt diesen fest. </summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpContent" /> zurück.Der Inhalt des HTTP-Antwortnachricht.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpResponseMessage" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft die nicht verwalteten Ressourcen.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Http.HttpResponseMessage" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen.</param>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode">
      <summary>Löst eine Ausnahme aus, wenn die <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode" />-Eigenschaft der HTTP-Antwort false lautet.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpResponseMessage" /> zurück.Die HTTP-Antwortnachricht, wenn der Aufruf erfolgreich ausgeführt wurde.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Headers">
      <summary>Ruft die Auflistung von HTTP-Antwortheadern ab. </summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpResponseHeaders" /> zurück.Die Auflistung von HTTP-Antwortheadern.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode">
      <summary>Ruft einen Wert ab, der angibt, ob die HTTP-Antwort erfolgreich war.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.Ein Wert, der angibt, ob die HTTP-Antwort erfolgreich war.true, wenn sich <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> im Bereich 200–299 befand; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.ReasonPhrase">
      <summary>Ruft den Begründungsausdruck ab, der üblicherweise von Servern, zusammen mit dem Statuscode, gesendet wird, oder legt diesen fest. </summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Die vom Server gesendete Begründungsphrase.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.RequestMessage">
      <summary>Ruft die Anforderungnachricht ab, die zu dieser Antwortnachricht geführt hat, oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpRequestMessage" /> zurück.Die Anforderungsnachricht, die zu dieser Antwortnachricht geführt hat.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.StatusCode">
      <summary>Übernimmt oder bestimmt den Statuscode der HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Net.HttpStatusCode" /> zurück.Der Statuscode der ATTP-Antwort.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolgendarstellung des aktuellen Objekts.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Version">
      <summary>Ruft die HTTP-Meldungsversion ab oder legt sie fest. </summary>
      <returns>Gibt <see cref="T:System.Version" /> zurück.Die HTTP-Nachrichtenversion.Standardwert: 1.1.</returns>
    </member>
    <member name="T:System.Net.Http.MessageProcessingHandler">
      <summary>Ein Basistyp für Handler, die nur einige kleine Verarbeitung der Anforderungs- und/oder der Antwortnachrichten ausführen.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor">
      <summary>Erstellt eine Instanz einer <see cref="T:System.Net.Http.MessageProcessingHandler" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Erstellt eine Instanz einer <see cref="T:System.Net.Http.MessageProcessingHandler" />-Klasse mit einem bestimmten inneren Handler.</summary>
      <param name="innerHandler">Der innere Handler, der für die Verarbeitung der HTTP-Antwortnachrichten zuständig ist.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Führt die Verarbeitung auf jeder Anforderung aus, die an den Server gesendet wird.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpRequestMessage" /> zurück.Die HTTP-Anforderungsnachricht, die verarbeitet wurde.</returns>
      <param name="request">Die zu verarbeitende HTTP-Anforderungmeldung.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessResponse(System.Net.Http.HttpResponseMessage,System.Threading.CancellationToken)">
      <summary>Führen Sie die Verarbeitung auf jeder Antwort vom Server aus.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.HttpResponseMessage" /> zurück.Die HTTP-Antwortnachricht, die verarbeitet wurde.</returns>
      <param name="response">Die zu verarbeitende HTTP-Antwortmeldung.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Sendet eine HTTP-Anforderung an den internen Handler zum Senden an den Server als asynchronen Vorgang.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="request">Die an den Server zu sendende HTTP-Anforderungsnachricht.</param>
      <param name="cancellationToken">Ein Abbruchtoken, das von anderen Objekten oder Threads verwendet werden kann, um Nachricht vom Abbruch zu empfangen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> war null.</exception>
    </member>
    <member name="T:System.Net.Http.MultipartContent">
      <summary>Stellt eine Auflistung von <see cref="T:System.Net.Http.HttpContent" />-Objekten bereit, die mithilfe der multipart/*-Inhaltstypspezifikation serialisiert werden.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.MultipartContent" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String)">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.MultipartContent" />-Klasse.</summary>
      <param name="subtype">Der Untertyp des Multipart-Inhalts.</param>
      <exception cref="T:System.ArgumentException">Die <paramref name="subtype" /> war null enthält nur Leerzeichen.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String,System.String)">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.MultipartContent" />-Klasse.</summary>
      <param name="subtype">Der Untertyp des Multipart-Inhalts.</param>
      <param name="boundary">Die Begrenzungszeichenfolge für den Multipart-Inhalt.</param>
      <exception cref="T:System.ArgumentException">Der <paramref name="subtype" /> war null oder eine leere Zeichenfolge ().Die <paramref name="boundary" /> war null enthält nur Leerzeichen.- oder -Die <paramref name="boundary" /> endet mit einem Leerzeichen.</exception>
      <exception cref="T:System.OutOfRangeException">Die Länge des <paramref name="boundary" /> war größer als 70.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Add(System.Net.Http.HttpContent)">
      <summary>Fügen Sie Mehrfach-HTTP-Inhalt einer Sammlung von <see cref="T:System.Net.Http.HttpContent" />-Objekten hinzu, die unter Verwendung der multipart/*-Inhaltstypspezifikation serialisiert werden.</summary>
      <param name="content">Der zur Auflistung hinzuzufügende HTTP-Inhalt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Http.MultipartContent" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die Auflistung der <see cref="T:System.Net.Http.HttpContent" />-Objekte durchläuft, die mithilfe der multipart/*-Inhaltstypspezifikation serialisiert werden.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.IEnumerator`1" /> zurück.Ein Objekt, das zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Den Multipart-HTTP-Inhalt in einen Stream als asynchronen Vorgang serialisieren.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="stream">Die Zielstream.</param>
      <param name="context">Informationen über den Transport (z. B. Channelbindungstoken).Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.System#Collections#IEnumerable#GetEnumerator">
      <summary>Eine explizite Implementierung der <see cref="M:System.Net.Http.MultipartContent.GetEnumerator" />-Methode.</summary>
      <returns>Gibt <see cref="T:System.Collections.IEnumerator" /> zurück.Ein Objekt, das zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.TryComputeLength(System.Int64@)">
      <summary>Bestimmt, ob der HTTP-Multipart-Inhalt eine gültige Länge in Bytes enthält.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="length" /> eine gültige Länge ist, andernfalls false.</returns>
      <param name="length">Die Länge des HTTP-Inhalts in Bytes.</param>
    </member>
    <member name="T:System.Net.Http.MultipartFormDataContent">
      <summary>Stellt einen Container für den Inhalt bereit, der mithilfe des multipart/form-data-MIME-Typs codiert wird.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.MultipartFormDataContent" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor(System.String)">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.MultipartFormDataContent" />-Klasse.</summary>
      <param name="boundary">Die Begrenzungszeichenfolge für den Multipart-Form-Dateninhalt.</param>
      <exception cref="T:System.ArgumentException">Die <paramref name="boundary" /> war null enthält nur Leerzeichen.- oder -Die <paramref name="boundary" /> endet mit einem Leerzeichen.</exception>
      <exception cref="T:System.OutOfRangeException">Die Länge des <paramref name="boundary" /> war größer als 70.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent)">
      <summary>Fügen Sie HTTP-Inhalt einer Auflistung von <see cref="T:System.Net.Http.HttpContent" />-Objekten hinzu, die in multipart/form-data-MIME-Typ serialisiert werden.</summary>
      <param name="content">Der zur Auflistung hinzuzufügende HTTP-Inhalt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String)">
      <summary>Fügen Sie HTTP-Inhalt einer Auflistung von <see cref="T:System.Net.Http.HttpContent" />-Objekten hinzu, die in multipart/form-data-MIME-Typ serialisiert werden.</summary>
      <param name="content">Der zur Auflistung hinzuzufügende HTTP-Inhalt.</param>
      <param name="name">Der Name für den HTTP-Inhalt, der hinzugefügt wird.</param>
      <exception cref="T:System.ArgumentException">Die <paramref name="name" /> war null enthält nur Leerzeichen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> war null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String,System.String)">
      <summary>Fügen Sie HTTP-Inhalt einer Auflistung von <see cref="T:System.Net.Http.HttpContent" />-Objekten hinzu, die in multipart/form-data-MIME-Typ serialisiert werden.</summary>
      <param name="content">Der zur Auflistung hinzuzufügende HTTP-Inhalt.</param>
      <param name="name">Der Name für den HTTP-Inhalt, der hinzugefügt wird.</param>
      <param name="fileName">Der Name der Datei, für den zur Auflistung hinzuzufügenden HTTP-Inhalt.</param>
      <exception cref="T:System.ArgumentException">Die <paramref name="name" /> war null enthält nur Leerzeichen.- oder -Die <paramref name="fileName" /> war null enthält nur Leerzeichen.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> war null.</exception>
    </member>
    <member name="T:System.Net.Http.StreamContent">
      <summary>Stellt HTTP-Inhalt auf Grundlage eines Streams bereit.</summary>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream)">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.StreamContent" />-Klasse.</summary>
      <param name="content">Der Inhalt, der zum Initialisieren der <see cref="T:System.Net.Http.StreamContent" /> verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream,System.Int32)">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.StreamContent" />-Klasse.</summary>
      <param name="content">Der Inhalt, der zum Initialisieren der <see cref="T:System.Net.Http.StreamContent" /> verwendet wird.</param>
      <param name="bufferSize">Die verfügbare Größe des Puffers in Byte für die <see cref="T:System.Net.Http.StreamContent" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> war null.</exception>
      <exception cref="T:System.OutOfRangeException">Die <paramref name="bufferSize" /> war kleiner oder gleich 0 (null). </exception>
    </member>
    <member name="M:System.Net.Http.StreamContent.CreateContentReadStreamAsync">
      <summary>Den HTTP-Datenstrominhalt in einen Speicherstream als asynchronen Vorgang schreiben.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Http.StreamContent" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Den HTTP-Inhalt in einen Stream als asynchronen Vorgang serialisieren.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task" /> zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
      <param name="stream">Die Zielstream.</param>
      <param name="context">Informationen über den Transport (z. B. Channelbindungstoken).Dieser Parameter kann null sein.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.TryComputeLength(System.Int64@)">
      <summary>Bestimmt, ob der Stream-Inhalt eine gültige Länge in Bytes enthält.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="length" /> eine gültige Länge ist, andernfalls false.</returns>
      <param name="length">Die Länge des Streamsinhalts in Bytes.</param>
    </member>
    <member name="T:System.Net.Http.StringContent">
      <summary>Stellt HTTP-Inhalt auf Grundlage einer Zeichenfolge bereit.</summary>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String)">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.StringContent" />-Klasse.</summary>
      <param name="content">Der Inhalt, der zum Initialisieren der <see cref="T:System.Net.Http.StringContent" /> verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding)">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.StringContent" />-Klasse.</summary>
      <param name="content">Der Inhalt, der zum Initialisieren der <see cref="T:System.Net.Http.StringContent" /> verwendet wird.</param>
      <param name="encoding">Die für den Inhalt zu verwendende Codierung.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding,System.String)">
      <summary>Erstellt eine neue Instanz der <see cref="T:System.Net.Http.StringContent" />-Klasse.</summary>
      <param name="content">Der Inhalt, der zum Initialisieren der <see cref="T:System.Net.Http.StringContent" /> verwendet wird.</param>
      <param name="encoding">Die für den Inhalt zu verwendende Codierung.</param>
      <param name="mediaType">Der Medientyp, der für den Inhalt verwendet werden soll.</param>
    </member>
    <member name="T:System.Net.Http.Headers.AuthenticationHeaderValue">
      <summary>Stellt Authentifizierungsinformationen in Authorization-, ProxyAuthorization-, WWW-Authenticate- und Proxy-Authenticate-Headerwerten dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Klasse.</summary>
      <param name="scheme">Das Schema für die Autorisierung.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Klasse.</summary>
      <param name="scheme">Das Schema für die Autorisierung.</param>
      <param name="parameter">Die Anmeldeinformationen, die die Authentifizierungsinformationen des Benutzer-Agents für die angeforderte Ressource enthält.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll. </param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Parameter">
      <summary>Ruft die Anmeldeinformationen ab, die die Authentifizierungsinformationen des Benutzer-Agents für die angeforderte Ressource enthält.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Die Anmeldeinformationen, die die Informationen für die Authentifizierung enthalten.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Authentifizierungsheader-Wertinformationen darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist keine gültige Wertinformation für einen Authentifizierungsheader.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Scheme">
      <summary>Ruft das Schema ab, welches für die Autorisierung verwendet werden soll.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Das Schema für die Autorisierung.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.TryParse(System.String,System.Net.Http.Headers.AuthenticationHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.CacheControlHeaderValue">
      <summary>Stellt den Wert des Cachesteuerungsheaders dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Extensions">
      <summary>CACHE-EXTENSION-Tokens, jeweils mit einem optionalen zugeordneten Wert.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Eine Sammlung von Cacheerweiterungtokens, jeweils mit einem optionalen zugeordneten Wert.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxAge">
      <summary>Der Höchstalter in Sekunden, für das der HTTP-Client bereit ist, eine Antwort zu übernehmen. </summary>
      <returns>Gibt <see cref="T:System.TimeSpan" /> zurück.Die Zeit in Sekunden. </returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStale">
      <summary>Ob ein HTTP-Client bereit ist, eine Antwort zu akzeptieren, die die Ablaufzeit überschritten hat.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der HTTP-Client bereit ist, eine Antwort zu akzeptieren, die die Ablaufzeit überschritten hat; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStaleLimit">
      <summary>Die maximale Zeit in Sekunden, die ein HTTP-Client bereit ist, eine Antwort zu akzeptieren, die ihre Ablaufzeit überschritten hat.</summary>
      <returns>Gibt <see cref="T:System.TimeSpan" /> zurück.Die Zeit in Sekunden.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MinFresh">
      <summary>Die Anzahl Sekunden, die ein HTTP-Client bereit ist, eine Antwort zu übernehmen.</summary>
      <returns>Gibt <see cref="T:System.TimeSpan" /> zurück.Die Zeit in Sekunden.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MustRevalidate">
      <summary>Ob der Ursprungsserver eine erneute Validierung eines Cacheeintrags bei nachfolgender Verwendung erfordert, wenn der Cache-Eintrag veraltet ist.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der Ursprungsserver eine erneute Validierung eines Cacheeintrags bei nachfolgender Verwendung erfordert, wenn der Eintrag veraltet ist; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCache">
      <summary>Ob ein HTTP-Client bereit ist, eine zwischengespeicherte Antwort zu akzeptieren.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der HTTP-Client bereit ist, eine zwischengespeicherte Antwort zu übernehmen; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCacheHeaders">
      <summary>Eine Sammlung von Feldnamen in der "no-cache"-Direktive in einem Cachesteuerungsheaderfeld in einer HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Eine Sammlung von Feldnamen.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoStore">
      <summary>Ob ein Cache keinen Teil der HTTP-Anforderungsnachricht oder einer Antwort speichern darf.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn ein Cache keinen Teil der HTTP-Anforderungsnachricht oder einer Antwort speichern darf; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoTransform">
      <summary>Ob ein Cache oder ein Proxy keinen Aspekt des Entitätstexts ändern darf.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn ein Cache oder Proxy keinen Aspekt des Entitätstexts ändern darf; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.OnlyIfCached">
      <summary>Ob ein Cache entweder mithilfe eines zwischengespeicherten Eintrags reagiert, der mit den anderen Einschränkungen der HTTP-Anforderung konsistent ist, oder mit einem 504-Status (Gateway Timeout) reagiert.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true wenn ein Cache entweder mithilfe eines zwischengespeicherten Eintrags reagiert, der mit den anderen Einschränkungen der HTTP-Anforderung konsistent ist, oder mit einem 504-Status (Gateway Timeout) reagiert; andernfalls false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Cachesteuerungsheader-Wertinformationen darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist keine gültige Wertinformation für einen Cachesteuerungsheader.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Private">
      <summary>Gibt an, ob alles oder nur ein Teil der HTTP-Antwortnachricht für einen einzelnen Benutzer bestimmt ist und nicht durch das Ausführen eines freigegebenen Caches zwischengespeichert werden darf.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn die HTTP-Antwortnachricht für einen einzelnen Benutzer bestimmt ist und nicht von einem gemeinsam genutzten Cache zwischengespeichert werden darf; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.PrivateHeaders">
      <summary>Eine Feldnamensammlung in der "private"-Direktive in einem Cachesteuerungsheaderfeld in einer HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Eine Sammlung von Feldnamen.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.ProxyRevalidate">
      <summary>Ob der Ursprungsserver eine erneute Validierung des Cacheeintrags bei nachfolgender Verwendung erfordert, wenn der Cache-Eintrag für freigegebene Benutzer-Agent-Caches veraltet ist.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der Ursprungsserver eine erneute Validierung des Cacheeintrags bei nachfolgender Verwendung erfordert, wenn der Eintrag für freigegebene Benutzer-Agent-Caches veraltet ist; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Public">
      <summary>Ob eine HTTP-Antwort möglicherweise von einem Cache zwischengespeichert wurde, auch wenn sie normalerweise nicht zwischenspeicherbar wäre oder nur innerhalb eines nicht freigegeben Cache zwischengespeichert werden würde.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn die HTTP-Antwort möglicherweise von einem Cache zwischengespeichert wurde, auch wenn sie normalerweise nicht zwischenspeicherbar wäre oder nur innerhalb eines nicht freigegeben Cache zwischengespeichert werden würde; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.SharedMaxAge">
      <summary>Das freigegebene, in Sekunden angegebene Höchstalter in einer HTTP-Antwort, die die "max-age"-Direktive in einem Cache-Control-Header oder einem Expires-Header für einen gemeinsam genutzten Cache überschreibt.</summary>
      <returns>Gibt <see cref="T:System.TimeSpan" /> zurück.Die Zeit in Sekunden.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.TryParse(System.String,System.Net.Http.Headers.CacheControlHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentDispositionHeaderValue">
      <summary>Stellt den Wert des Content-Disposition-Headers dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.Net.Http.Headers.ContentDispositionHeaderValue)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />-Klasse.</summary>
      <param name="source">
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />
      </param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />-Klasse.</summary>
      <param name="dispositionType">Eine Zeichenfolge, die eine <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> enthält.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.CreationDate">
      <summary>Das Datum, zu der die Datei erstellt wurde.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Das Dateierstellungsdatum.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.DispositionType">
      <summary>Der Anordnungstyp für einen Textteil.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Anordnungstyp. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileName">
      <summary>Ein Vorschlag, wie Sie einen Dateinamen zum Speichern der Nachrichtennutzlast erstellen, der verwendet werden soll, wenn die Entität getrennt und in einer separaten Datei gespeichert wird.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Ein vorgeschlagener Dateiname.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileNameStar">
      <summary>Ein Vorschlag, wie Sie Dateinamen zum Speichern der Nachrichtennutzlasten erstellen, der verwendet werden soll, wenn die Entitäten getrennt und in separaten Dateien gespeichert wird.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Ein vorgeschlagener Dateiname des Formulardateinamens*.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ModificationDate">
      <summary>Datum der letzten Dateiänderung. </summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Das Änderungsdatum der Datei.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Name">
      <summary>Der Name für einen Inhaltstextteil.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Name des Inhaltstextteils.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Parameters">
      <summary>Ein Satz von Parametern, enthalten im Content-Disposition-Header.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Eine Auflistung von Parametern. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die die Wertinformation des Inhaltsdisposition-Headers des Inhalts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist keine gültige Wertinformation für einen Inhaltsdispositionsheader.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ReadDate">
      <summary>Das Datum, an dem zuletzt die Datei gelesen wurde.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Das letzte gelesene Datum.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Size">
      <summary>Die ungefähre Größe der aktuellen Datei in Bytes. </summary>
      <returns>Gibt <see cref="T:System.Int64" /> zurück.Die ungefähre Größe in Bytes.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentDispositionHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentRangeHeaderValue">
      <summary>Stellt den Wert des Content-Range-Headers dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Klasse.</summary>
      <param name="length">Der Start- oder Endpunkt des Bereichs in Bytes.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Klasse.</summary>
      <param name="from">Die Position in Bytes, an der mit dem Senden von Daten begonnen werden soll.</param>
      <param name="to">Die Position in Bytes, an der das Senden von Daten beendet werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64,System.Int64)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Klasse.</summary>
      <param name="from">Die Position in Bytes, an der mit dem Senden von Daten begonnen werden soll.</param>
      <param name="to">Die Position in Bytes, an der das Senden von Daten beendet werden soll.</param>
      <param name="length">Der Start- oder Endpunkt des Bereichs in Bytes.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene Objekt und das aktuelle <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.From">
      <summary>Ruft die Position ab, an der mit dem Senden von Daten begonnen werden soll.</summary>
      <returns>Gibt <see cref="T:System.Int64" /> zurück.Die Position in Bytes, an der mit dem Senden von Daten begonnen werden soll.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasLength">
      <summary>Ruft ab, ob für den Inhaltsbereichs-Header eine Länge angegeben wurde.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der Content-Range eine Länge angegeben hat; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasRange">
      <summary>Ruft ab, ob für den Inhaltsbereich ein Bereich bestimmt wurde. </summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der Content-Range einen Bereich angegeben hat; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Length">
      <summary>Ruft die Länge des vollständigen Entitätstexts ab.</summary>
      <returns>Gibt <see cref="T:System.Int64" /> zurück.Die Länge des vollständigen Entitätstexts.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die die Informationen des Inhaltsbereichs-Headerwerts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist keine gültige Information für einen Inhaltsbereich-Headerwert.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.To">
      <summary>Ruft die Position ab, an der das Senden von Daten beendet werden soll.</summary>
      <returns>Gibt <see cref="T:System.Int64" /> zurück.Die Position, an der das Senden von Daten beendet werden soll.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentRangeHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Unit">
      <summary>Die verwendeten Bereichseinheiten.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Ein <see cref="T:System.String" />, das Bereichseinheiten enthält. </returns>
    </member>
    <member name="T:System.Net.Http.Headers.EntityTagHeaderValue">
      <summary>Stellt einen Entitätstag-Headerwert dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />-Klasse.</summary>
      <param name="tag">Eine Zeichenfolge, die <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> enthält.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />-Klasse.</summary>
      <param name="tag">Eine Zeichenfolge, die  <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> enthält.</param>
      <param name="isWeak">Ein Wert, der angibt, ob dieser Entitätstagheader ein schwaches Validierungssteuerelement ist.Wenn der Entitätstagheader ein schwaches Validierungssteuerelement ist, sollte <paramref name="isWeak" /> auf true festgelegt werden.Wenn der Entitätstagheader ein starkes Validierungssteuerelement ist, sollte <paramref name="isWeak" /> auf false festgelegt werden.</param>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Any">
      <summary>Ruft den Entitätstagheaderwert ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> zurück.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.IsWeak">
      <summary>Ruft ab, ob dem Entitätstag ein Schwächeindikator vorangestellt ist.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das Entitätstag durch einen Schwächeindikator vorangestellt wird; andernfalls false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Entitätstag-Headerwerts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> stellt keinen gültigen Entitätstag-Headerwert dar.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Tag">
      <summary>Ruft die nicht transparente Zeichenfolge in Anführungszeichen ab. </summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine nicht transparente Zeichenfolge in Anführungszeichen.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.TryParse(System.String,System.Net.Http.Headers.EntityTagHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpContentHeaders">
      <summary>Stellt die Auflistung von Inhaltsheadern dar, wie in RFC 2616 definiert.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Allow">
      <summary>Ruft den Wert des Allow-Inhaltsheaders für eine HTTP-Antwort ab. </summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Der Wert des Allow-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentDisposition">
      <summary>Ruft den Wert des Content-Disposition-Inhaltsheaders für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> zurück.Der Wert des Content-Disposition-Inhaltsheaders für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentEncoding">
      <summary>Ruft den Wert des Content-Encoding-Inhaltsheaders für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Der Wert des Content-Encoding-Inhaltsheaders für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLanguage">
      <summary>Ruft den Wert des Content-Language-Inhaltsheaders für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Der Wert des Content-Language-Inhaltsheaders für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLength">
      <summary>Übernimmt oder bestimmt den Wert des Content-Length-Inhaltsheaders für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Int64" /> zurück.Der Wert des Content-Length-Inhaltsheaders für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLocation">
      <summary>Übernimmt oder bestimmt den Wert des Content-Location-Inhaltsheaders für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Uri" /> zurück.Der Wert des Content-Location-Inhaltsheaders für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentMD5">
      <summary>Übernimmt oder bestimmt den Wert des Content-MD5-Inhaltsheaders für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Byte" /> zurück.Der Wert des Content-MD5-Inhaltsheaders für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentRange">
      <summary>Übernimmt oder bestimmt den Wert des Content-Range-Inhaltsheaders für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> zurück.Der Wert des Content-Range-Inhaltsheaders für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentType">
      <summary>Übernimmt oder bestimmt den Wert des Content-Type-Inhaltsheaders für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> zurück.Der Wert des Content-Type-Inhaltsheaders für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Expires">
      <summary>Übernimmt oder bestimmt den Wert des Expires-Inhaltsheaders für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Der Wert des Expires-Inhaltsheaders für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.LastModified">
      <summary>Übernimmt oder bestimmt den Wert des Last-Modified-Inhaltsheaders für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Der Wert des Last-Modified-Inhaltsheaders für eine HTTP-Antwort.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaders">
      <summary>Eine Sammlung von Headern und deren Werten, wie in RFC 2616 definiert.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Fügt den angegebenen Header und seine Werte in die <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Sammlung hinzu.</summary>
      <param name="name">Der Header, der der Auflistung hinzugefügt werden soll.</param>
      <param name="values">Eine Liste von Headerwerten, die der Sammlung hinzugefügt werden sollen.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.String)">
      <summary>Fügt den angegebenen Header und den Wert in die <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Sammlung hinzu.</summary>
      <param name="name">Der Header, der der Auflistung hinzugefügt werden soll.</param>
      <param name="value">Der Inhalt des Headers.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Clear">
      <summary>Entfernt alle Header aus der <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Auflistung.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Contains(System.String)">
      <summary>Gibt zurück, ob ein bestimmter Header in der <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Auflistung vorhanden ist.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der angegebene Header in der Auflistung vorhanden ist, andernfalls false.</returns>
      <param name="name">Der spezielle Header.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Instanz durchlaufen kann.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.IEnumerator`1" /> zurück.Ein Enumerator für das <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetValues(System.String)">
      <summary>Gibt alle Headerwerte für einen angegebenen Header zurück, der in der <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Auflistung gespeichert wird.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.IEnumerable`1" /> zurück.Ein Array von Headerzeichenfolgen.</returns>
      <param name="name">Der angegebene Header, für den Werte zurückgegeben werden sollen.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Remove(System.String)">
      <summary>Entfernt den angegebenen Header aus der <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Auflistung.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.</returns>
      <param name="name">Der Name des Headers, der aus der Auflistung entfernt werden soll. </param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.System#Collections#IEnumerable#GetEnumerator">
      <summary>Ruft einen Enumerator ab, der eine <see cref="T:System.Net.Http.Headers.HttpHeaders" /> durchlaufen kann.</summary>
      <returns>Gibt <see cref="T:System.Collections.IEnumerator" /> zurück.Eine Instanz einer <see cref="T:System.Collections.IEnumerator" />-Implementierung, die eine <see cref="T:System.Net.Http.Headers.HttpHeaders" /> durchlaufen kann.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Gibt einen Wert zurück, der angibt, ob der angegebene Header und dessen Werte zur <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Auflistung hinzugefügt wurden, ohne die bereitgestellten Informationen zu überprüfen.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der angegebene Header <paramref name="name" /> und <paramref name="values" /> zur Auflistung hinzugefügt werden konnten, andernfalls false.</returns>
      <param name="name">Der Header, der der Auflistung hinzugefügt werden soll.</param>
      <param name="values">Die Werte des Headers.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.String)">
      <summary>Gibt einen Wert zurück, der angibt, ob der angegebene Header und dessen Wert zur <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Auflistung hinzugefügt wurden, ohne die bereitgestellten Informationen zu überprüfen.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der angegebene Header <paramref name="name" /> und <paramref name="value" /> zur Auflistung hinzugefügt werden konnten, andernfalls false.</returns>
      <param name="name">Der Header, der der Auflistung hinzugefügt werden soll.</param>
      <param name="value">Der Inhalt des Headers.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
      <summary>Zurückgeben, ob ein angegebener Header und angegebene Werte in der <see cref="T:System.Net.Http.Headers.HttpHeaders" />-Auflistung gespeichert sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der angegebene Header <paramref name="name" /> und values in der Auflistung gespeichert sind; andernfalls false.</returns>
      <param name="name">Der angegebene Header.</param>
      <param name="values">Die angegebenen Headerwerte.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaderValueCollection`1">
      <summary>Stellt eine Auflistung von Headerwerten dar.</summary>
      <typeparam name="T">Der Headerauflistungs-Typ.</typeparam>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Add(`0)">
      <summary>Fügt der <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> einen Eintrag hinzu.</summary>
      <param name="item">Das Element, das der Headerauflistung hinzugefügt werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Clear">
      <summary>Entfernt sämtliche Einträge aus dem <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Contains(`0)">
      <summary>Bestimmt, ob der <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ein Element enthält.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn dar Eintrag in der <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> Instanz enthalten ist, andernfalls false.</returns>
      <param name="item">Das Element, das in der Headerauflistung gefunden werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.CopyTo(`0[],System.Int32)">
      <summary>Kopiert die gesamte <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> in ein kompatibles eindimensionales <see cref="T:System.Array" />, beginnend am angegebenen Index des Zielarrays.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.Count">
      <summary>Ruft die Anzahl der Header im <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ab.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Die Anzahl der Header in einer Auflistung.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> durchläuft.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.IEnumerator`1" /> zurück.Ein Enumerator für die <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> Instanz.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />-Instanz schreibgeschützt ist.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn die <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />-Instanz schreibgeschützt ist, andernfalls false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ParseAdd(System.String)">
      <summary>Analysiert und fügt einen Eintrag <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> hinzu.</summary>
      <param name="input">Der hinzuzufügende Eintrag</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Remove(`0)">
      <summary>Entfernt das angegebene Element aus <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der <paramref name="item" /> erfolgreich aus der <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> Instanz entfernt wurde, andernfalls false.</returns>
      <param name="item">Das zu entfernende Element.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> durchläuft.</summary>
      <returns>Gibt <see cref="T:System.Collections.IEnumerator" /> zurück.Ein Enumerator für die <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> Instanz.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.TryParseAdd(System.String)">
      <summary>Bestimmt, ob die Eingabe analysiert und  zu <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> hinzugefügt werden kann.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> analysiert und zu <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />Instanz hinzugefügt werden kann; andernfalls false</returns>
      <param name="input">Der zu überprüfende Eintrag.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpRequestHeaders">
      <summary>Stellt die Auflistung von Anforderungsheadern dar, wie in RFC 2616 definiert.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Accept">
      <summary>Ruft den Wert des Accept-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Accept-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptCharset">
      <summary>Ruft den Wert des Accept-Charset-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Accept-Charset-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptEncoding">
      <summary>Ruft den Wert des Accept-Encoding-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Accept-Encoding-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptLanguage">
      <summary>Ruft den Wert des Accept-Language-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Accept-Language-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Authorization">
      <summary>Übernimmt oder bestimmt den Wert des Authorization-Headers für eine HTTP-Anfrage.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> zurück.Der Wert des Authorization-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.CacheControl">
      <summary>Übernimmt oder bestimmt den Wert des Cache-Control-Headers für eine HTTP-Anfrage.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> zurück.Der Wert des Cache-Control-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Connection">
      <summary>Ruft den Wert des Connection-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Connection-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ConnectionClose">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob der Connection-Header für eine HTTP-Anforderung "Close" enthält.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der Connection-Header "Schließen" enthält; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Date">
      <summary>Übernimmt oder bestimmt den Wert des Date-Headers für eine HTTP-Anfrage.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Der Wert des Date-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Expect">
      <summary>Ruft den Wert des Expect-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Expect-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ExpectContinue">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob der Expect-Header für eine HTTP-Anforderung "Continue" enthält.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der Expect-Header "Fortfahren" enthält; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.From">
      <summary>Übernimmt oder bestimmt den Wert des From-Headers für eine HTTP-Anforderung.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Wert des From-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Host">
      <summary>Übernimmt oder bestimmt den Wert des Host-Headers für eine HTTP-Anforderung.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Wert des Host-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfMatch">
      <summary>Ruft den Wert des If-Match-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des If-Match-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfModifiedSince">
      <summary>Übernimmt oder bestimmt den Wert des If-Modified-Since-Headers für eine HTTP-Anforderung.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Der Wert des If-Modified-Since-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfNoneMatch">
      <summary>Ruft den Wert des If-None-Match-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Ruft den Wert des If-None-Match-Headers für eine HTTP-Anforderung ab.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfRange">
      <summary>Übernimmt oder bestimmt den Wert des If-Range-Headers für eine HTTP-Anforderung.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> zurück.Der Wert des If-Range-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfUnmodifiedSince">
      <summary>Übernimmt oder bestimmt den Wert des If-Unmodified-Since-Headers für eine HTTP-Anforderung.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Der Wert des If-Unmodified-Since-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.MaxForwards">
      <summary>Übernimmt oder bestimmt den Wert des Max-Forwards-Headers für eine HTTP-Anforderung.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Der Wert des Max-Forwards-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Pragma">
      <summary>Ruft den Wert des Pragma-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Pragma-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ProxyAuthorization">
      <summary>Übernimmt oder bestimmt den Wert des Proxy-Authorization-Headers für eine HTTP-Anforderung.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> zurück.Der Wert des Proxy-Authorization-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Range">
      <summary>Übernimmt oder bestimmt den Wert des Range-Headers für eine HTTP-Anforderung.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> zurück.Der Wert des Range-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Referrer">
      <summary>Übernimmt oder bestimmt den Wert des Referer-Headers für eine HTTP-Anforderung.</summary>
      <returns>Gibt <see cref="T:System.Uri" /> zurück.Der Wert des Referer-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TE">
      <summary>Ruft den Wert des TE-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des TE-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Trailer">
      <summary>Ruft den Wert des Trailer-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Trailer-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncoding">
      <summary>Ruft den Wert des Transfer-Encoding-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Transfer-Encoding-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncodingChunked">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob der Transfer-Encoding-Header für eine HTTP-Anforderung "Chunked" enthält.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der Transfer-Encoding-Header segmentierte Elemente enthält; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Upgrade">
      <summary>Ruft den Wert des Upgrade-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Upgrade-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.UserAgent">
      <summary>Ruft den Wert des User-Agent-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des User-Agent-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Via">
      <summary>Ruft den Wert des Via-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Via-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Warning">
      <summary>Ruft den Wert des Warning-Headers für eine HTTP-Anforderung ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Warning-Headers für eine HTTP-Anforderung.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpResponseHeaders">
      <summary>Stellt die Auflistung von Antwortheadern dar, wie in RFC 2616 definiert.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.AcceptRanges">
      <summary>Ruft den Wert des Accept-Ranges-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Accept-Ranges-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Age">
      <summary>Übernimmt oder bestimmt den Wert des Age-Headers für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.TimeSpan" /> zurück.Der Wert des Age-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.CacheControl">
      <summary>Übernimmt oder bestimmt den Wert des Cache-Control-Headers für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> zurück.Der Wert des Cache-Control-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Connection">
      <summary>Ruft den Wert des Connection-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Connection-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ConnectionClose">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob der Connection-Header für eine HTTP-Antwort "Close" enthält.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der Connection-Header "Schließen" enthält; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Date">
      <summary>Übernimmt oder bestimmt den Wert des Date-Headers für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Der Wert des Date-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ETag">
      <summary>Übernimmt oder bestimmt den Wert des ETag-Headers für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> zurück.Der Wert des ETag-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Location">
      <summary>Übernimmt oder bestimmt den Wert des Location-Headers für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Uri" /> zurück.Der Wert des Location-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Pragma">
      <summary>Ruft den Wert des Pragma-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Pragma-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ProxyAuthenticate">
      <summary>Ruft den Wert des Proxy-Authenticate-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Proxy-Authenticate-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.RetryAfter">
      <summary>Übernimmt oder bestimmt den Wert des Retry-After-Headers für eine HTTP-Antwort.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> zurück.Der Wert des Retry-After-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Server">
      <summary>Ruft den Wert des Server-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Server-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Trailer">
      <summary>Ruft den Wert des Trailer-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Trailer-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncoding">
      <summary>Ruft den Wert des Transfer-Encoding-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Transfer-Encoding-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncodingChunked">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob der Transfer-Encoding-Header für eine HTTP-Antwort "Chunked" enthält.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn der Transfer-Encoding-Header segmentierte Elemente enthält; andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Upgrade">
      <summary>Ruft den Wert des Upgrade-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Upgrade-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Vary">
      <summary>Ruft den Wert des Vary-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Vary-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Via">
      <summary>Ruft den Wert des Via-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Via-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Warning">
      <summary>Ruft den Wert des Warning-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des Warning-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.WwwAuthenticate">
      <summary>Ruft den Wert des WWW-Authenticate-Headers für eine HTTP-Antwort ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> zurück.Der Wert des WWW-Authenticate-Headers für eine HTTP-Antwort.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeHeaderValue">
      <summary>Stellt einen Medientyp dar, der in einem Inhaltstypheader verwendet wird, wie im RFC 2616 definiert.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />-Klasse.</summary>
      <param name="source"> Ein <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> Objekt, das zur Initialisierung der neuen Instanz verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />-Klasse.</summary>
      <param name="mediaType">Die Quelle, die als Zeichenfolge dargestellt wird, um die neue Instanz zu initialisieren. </param>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.CharSet">
      <summary>Ruft den Zeichensatz ab oder legt ihn fest.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Zeichensatz.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.MediaType">
      <summary>Ruft den Wert des Medientyp-Headers ab oder legt ihn fest.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Medientyp-Headerwert.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.Parameters">
      <summary>Ruft die Titelwertparameter des Medientyps ab oder legt diese fest.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Die Medientyp-Headerwertparameter.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Medientyp-Headerwerts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> stellt keinen gültigen Medientyp-Headerwert dar.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue">
      <summary>Stellt einen Medientyp mit einem zusätzlichen Qualitätsfaktor dar, der in einem Inhaltstypheader verwendet wird.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />-Klasse.</summary>
      <param name="mediaType">
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> dargestellt als Zeichenfolge, um die neue Instanz zu initialisieren. </param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />-Klasse.</summary>
      <param name="mediaType">
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> dargestellt als Zeichenfolge, um die neue Instanz zu initialisieren.</param>
      <param name="quality">Die Qualität dieses zugeordneten Headerwert.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Medientyp mit Qualitätsheaderwertinformationen darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist ein ungültiger Medientyp mit Qualitätsheaderwertinformationen.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Quality">
      <summary>Abrufen oder Festlegen des Qualitätswerts für <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <returns>Gibt <see cref="T:System.Double" /> zurück.Der Qualitätswert für das <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> Objekt .</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeWithQualityHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueHeaderValue">
      <summary>Stellt ein Name-Wert-Paar dar, das in verschiedenen Headern verwendet wird, die in RFC 2616 definiert sind.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.Net.Http.Headers.NameValueHeaderValue)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Klasse.</summary>
      <param name="source">Ein <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> Objekt, das zur Initialisierung der neuen Instanz verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Klasse.</summary>
      <param name="name">Der Headername.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Klasse.</summary>
      <param name="name">Der Headername.</param>
      <param name="value">Der Headerwert.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Name">
      <summary>Ruft den Headernamen ab.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Headername.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Namenswert-Headerwerts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> stellt keinen gültigen Namenswert-Headerwert dar.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Value">
      <summary>Ruft den Headerwert ab.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Headerwert.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue">
      <summary>Stellt ein Name-Wert-Paar mit Parametern dar, das in verschiedenen Headern verwendet wird, wie in RFC 2616 definiert.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.Net.Http.Headers.NameValueWithParametersHeaderValue)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Klasse.</summary>
      <param name="source">Ein <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> Objekt, das zur Initialisierung der neuen Instanz verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Klasse.</summary>
      <param name="name">Der Headername.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Klasse.</summary>
      <param name="name">Der Headername.</param>
      <param name="value">Der Headerwert.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parameters">
      <summary>Ruft die Parameter vom <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Objekt ab.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Eine Auflistung, die die Parameter enthält.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Namenswert mit Parameterheaderwertinformationen darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist ein ungültiger Namenswert mit Parameterheaderwertinformationen.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueWithParametersHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ProductHeaderValue">
      <summary>Stellt einen Produkttokenwert in einem User-Agent-Header dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />-Klasse.</summary>
      <param name="name">Der Produktname.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />-Klasse.</summary>
      <param name="name">Der Produktnamenswert.</param>
      <param name="version">Die Produktversionswert.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Name">
      <summary>Ruft den Namen des Produkttoken ab.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Name des Produkttoken.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Produkt-Headerwerts darstellt.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Version">
      <summary>Ruft die Version des Produkttoken ab.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Die Version des Produkttoken. </returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductInfoHeaderValue">
      <summary>Stellt einen Wert dar, der entweder ein Produkt oder ein Kommentar in einem Benutzer-Agent-Header sein kann.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.Net.Http.Headers.ProductHeaderValue)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Klasse.</summary>
      <param name="product">Ein <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> Objekt, das zur Initialisierung der neuen Instanz verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Klasse.</summary>
      <param name="comment">Ein Kommentarwert.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Klasse.</summary>
      <param name="productName">Der Produktnamenswert.</param>
      <param name="productVersion">Die Produktversionswert.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Comment">
      <summary>Ruft den Kommentar vom <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Objekt ab.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Kommentarwert dieses <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Produktinfo-Headerwerts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> stellt keinen gültigen Produktinfo-Headerwert dar.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Product">
      <summary>Ruft das Produkt vom <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Objekt ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> zurück.Der Produktwert von diesem <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductInfoHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeConditionHeaderValue">
      <summary>Stellt einen If-Range-Headerwert dar, der entweder ein Datum/Uhrzeit- oder ein Entitätstag-Wert sein kann.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Klasse.</summary>
      <param name="date">Ein Datumswert für die Initialisierung der neuen -Instanz.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.Net.Http.Headers.EntityTagHeaderValue)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Klasse.</summary>
      <param name="entityTag">Ein <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> Objekt, das zur Initialisierung der neuen Instanz verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Klasse.</summary>
      <param name="entityTag">Ein Entitätstag, dargestellt als Zeichenfolge, der verwendet wird, um die neue Instanz zu initialisieren.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.Date">
      <summary>Ruft das Datum aus dem <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> Objekt ab.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Das Datum aus dem <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.EntityTag">
      <summary>Ruft das Entitätstag vom <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Objekt ab.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> zurück.Das Entitätstag vom <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Objekt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Bereichsbedingung-Headerwerts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist keine gültige Information für einen Bereichsbedingung-Headerwert.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeConditionHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeHeaderValue">
      <summary>Stellt einen Bereichsheaderwert des Bytebereichs dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Klasse mit einem Bytebereich.</summary>
      <param name="from">Die Position, an der mit dem Senden von Daten begonnen werden soll.</param>
      <param name="to">Die Position, an der das Senden von Daten beendet werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> ist größer als <paramref name="to" />.- oder - <paramref name="from" /> oder <paramref name="to" /> ist kleiner als 0. </exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Bereichs-Headerwerts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist keine gültige Information für einen Bereich-Headerwert.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Ranges">
      <summary>Ruft die Bereiche ab, die vom <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Objekt angegeben sind.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Die Reichweiten vom <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Objekt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Unit">
      <summary>Ruft die Einheit vom <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Objekt ab.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Die Einheit vom <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />-Objekt.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeItemHeaderValue">
      <summary>Stellt einen Bereichsheaderwert des Bytebereichs dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />-Klasse.</summary>
      <param name="from">Die Position, an der mit dem Senden von Daten begonnen werden soll.</param>
      <param name="to">Die Position, an der das Senden von Daten beendet werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> ist größer als <paramref name="to" />.- oder - <paramref name="from" /> oder <paramref name="to" /> ist kleiner als 0. </exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.From">
      <summary>Ruft die Position ab, an der mit dem Senden von Daten begonnen werden soll.</summary>
      <returns>Gibt <see cref="T:System.Int64" /> zurück.Die Position, an der mit dem Senden von Daten begonnen werden soll.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.To">
      <summary>Ruft die Position ab, an der das Senden von Daten beendet werden soll. </summary>
      <returns>Gibt <see cref="T:System.Int64" /> zurück.Die Position, an der das Senden von Daten beendet werden soll. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RetryConditionHeaderValue">
      <summary>Stellt einen Retry-After-Headerwert dar, der entweder ein Datum/Uhrzeit- oder ein Zeitspannen-Wert sein kann.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Klasse.</summary>
      <param name="date">Der Datum und Zeit Offset, der zum Initialisieren der neuen Instanz verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.TimeSpan)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Klasse.</summary>
      <param name="delta">Das Delta, in Sekunden, das verwendet wird, um die neue Instanz zu initialisieren.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Date">
      <summary>Ruft das Datum und Uhrzeit-Offset vom aktuellen <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Objekt ab.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Das Datum und Uhrzeit-Offset vom aktuellen <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Delta">
      <summary>Ruft das Delta in Sekunden vom <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Objekt ab.</summary>
      <returns>Gibt <see cref="T:System.TimeSpan" /> zurück.Das Delta in Sekunden vom <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Objekt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Headerwerts für die Wiederholungsbedingung darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist keine gültige Information für den Headerwert für die Wiederholungsbedingung.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RetryConditionHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.StringWithQualityHeaderValue">
      <summary>Stellt einen Zeichenfolgenheaderwert mit einer optionalen Qualität dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Klasse.</summary>
      <param name="value">Eine Zeichenfolge, die zur Initialisierung der neuen Instanz verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Klasse.</summary>
      <param name="value">Eine Zeichenfolge, die zur Initialisierung der neuen Instanz verwendet wird.</param>
      <param name="quality">Ein Qualitätsfaktor für die Initialisierung der neuen -Instanz.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene Objekt und das aktuelle <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Qualitäts-Headerwerts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist eine ungültige Zeichenfolge mit Qualitätsheaderwertinformationen.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Quality">
      <summary>Ruft den Qualitätsfaktor vom <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Objekt ab.</summary>
      <returns>Gibt <see cref="T:System.Double" /> zurück.Der Qualitätsfaktor vom <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Objekt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.StringWithQualityHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Value">
      <summary>Ruft den Zeichenfolgewert aus dem <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Objekt ab.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Zeichenfolgewert aus dem <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />-Objekt.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingHeaderValue">
      <summary>Stellt einen Headerwert zum Akzeptieren von Codierung dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.Net.Http.Headers.TransferCodingHeaderValue)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />-Klasse.</summary>
      <param name="source">Ein <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> Objekt, das zur Initialisierung der neuen Instanz verwendet wird. </param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />-Klasse.</summary>
      <param name="value">Eine Zeichenfolge, die zur Initialisierung der neuen Instanz verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene Objekt und das aktuelle <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Parameters">
      <summary>Ruft die Übertragungs-Codierungs-Parameter ab.</summary>
      <returns>Gibt <see cref="T:System.Collections.Generic.ICollection`1" /> zurück.Die Übertragungscodierungsparameter.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Headerwerts für die Übertragungscodierung darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist kein ungültige Information für einen Übertragungscodierungs-Headerwert.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Value">
      <summary>Ruft den Übertragungscodierungswert ab.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Übertragungscodierungswert.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue">
      <summary>Stellt einen Accept-Encoding-Headerwert mit optionalem Qualitätsfaktor dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />-Klasse.</summary>
      <param name="value">Eine Zeichenfolge, die zur Initialisierung der neuen Instanz verwendet wird.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />-Klasse.</summary>
      <param name="value">Eine Zeichenfolge, die zur Initialisierung der neuen Instanz verwendet wird.</param>
      <param name="quality">Ein Wert für den Qualitätsfaktor.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Übertragungscodierungswerts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist ein ungültige Übertragungscodierung mit Qualitätsheaderwertinformationen.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Quality">
      <summary>Ruft den Qualitätsfaktor von <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> ab.</summary>
      <returns>Gibt <see cref="T:System.Double" /> zurück.Der Qualitätsfaktor von <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingWithQualityHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ViaHeaderValue">
      <summary>Stellt den Wert des Via-Headers dar.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Klasse.</summary>
      <param name="protocolVersion">Die Protokollversion des empfangenen Protokolls.</param>
      <param name="receivedBy">Der Host und der Port, von denen die Anforderung oder Antwort empfangen wurde.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Klasse.</summary>
      <param name="protocolVersion">Die Protokollversion des empfangenen Protokolls.</param>
      <param name="receivedBy">Der Host und der Port, von denen die Anforderung oder Antwort empfangen wurde.</param>
      <param name="protocolName">Der Protokollname des empfangenen Protokolls.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Klasse.</summary>
      <param name="protocolVersion">Die Protokollversion des empfangenen Protokolls.</param>
      <param name="receivedBy">Der Host und der Port, von denen die Anforderung oder Antwort empfangen wurde.</param>
      <param name="protocolName">Der Protokollname des empfangenen Protokolls.</param>
      <param name="comment">Das Befehlsfeld, das verwendet wird, um die Software des empfangenen Proxys oder Gateways zu identifizieren.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.Comment">
      <summary>Ruft das Befehlsfeld ab, das verwendet wird, um die Software des empfangenen Proxys oder Gateways zu identifizieren.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Das Befehlsfeld, das verwendet wird, um die Software des empfangenen Proxys oder Gateways zu identifizieren.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" />-Objekt und das aktuelle <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Gibt einen Hashcode für das aktuelle Objekt zurück.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Instanz.</summary>
      <returns>Gibt <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> zurück.Eine <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Instanz.</returns>
      <param name="input">Eine Zeichenfolge, die Informationen des Via-Headerwerts darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist keine gültige Information für einen Via-Headerwert.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolName">
      <summary>Ruft den Protokollnamen des empfangenen Protokolls ab.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Protokollname.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolVersion">
      <summary>Ruft die Protokollversion des empfangenen Protokolls ab.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Die Protokollversion.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ReceivedBy">
      <summary>Ruft den Host und Port ab, von der die Anforderung oder Antwort empfangen wurden.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Host und der Port, von denen die Anforderung oder Antwort empfangen wurde.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.TryParse(System.String,System.Net.Http.Headers.ViaHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
    <member name="T:System.Net.Http.Headers.WarningHeaderValue">
      <summary>Stellt einen Warnwert dar, der vom Warn-Header verwendet wird.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />-Klasse.</summary>
      <param name="code">Der spezifische Warncode.</param>
      <param name="agent">Der Host, der die Warnung angehängt hat.</param>
      <param name="text">Eine Zeichenfolge in Anführungszeichen, die den Warnungs-Text enthält.</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String,System.DateTimeOffset)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />-Klasse.</summary>
      <param name="code">Der spezifische Warncode.</param>
      <param name="agent">Der Host, der die Warnung angehängt hat.</param>
      <param name="text">Eine Zeichenfolge in Anführungszeichen, die den Warnungs-Text enthält.</param>
      <param name="date">Der Datum/Uhrzeit-Stempel der Warnung.</param>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Agent">
      <summary>Ruft den Host ab, der die Warnung angehängt hat.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Der Host, der die Warnung angehängt hat.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Code">
      <summary>Ruft den spezifischen Warncode ab.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Der spezifische Warncode.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Date">
      <summary>Ruft den Datum-/Uhrzeit-Stempel der Warnung ab.</summary>
      <returns>Gibt <see cref="T:System.DateTimeOffset" /> zurück.Der Datum/Uhrzeit-Stempel der Warnung.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Equals(System.Object)">
      <summary>Bestimmt, ob das angegebene <see cref="T:System.Object" /> und das aktuelle <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />-Objekt gleich sind.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn das angegebene <see cref="T:System.Object" /> und das aktuelle Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Das Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.GetHashCode">
      <summary>Fungiert als Hashfunktion für ein <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />-Objekt.</summary>
      <returns>Gibt <see cref="T:System.Int32" /> zurück.Ein Hashcode für das aktuelle Objekt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Parse(System.String)">
      <summary>Konvertiert eine Zeichenfolge in eine <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />-Instanz.</summary>
      <returns>Gibt eine <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />-Instanz zurück.</returns>
      <param name="input">Eine Zeichenfolge, die Authentifizierungsheader-Wertinformationen darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> ist ein null-Verweis.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> ist keine gültige Wertinformation für einen Authentifizierungsheader.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Text">
      <summary>Ruft eine Zeichenfolge in Anführungszeichen ab, die den Warnungstext enthält.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge in Anführungszeichen, die den Warnungs-Text enthält.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die das aktuelle <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />-Objekt darstellt.</summary>
      <returns>Gibt <see cref="T:System.String" /> zurück.Eine Zeichenfolge, die das aktuelle Objekt darstellt.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.TryParse(System.String,System.Net.Http.Headers.WarningHeaderValue@)">
      <summary>Bestimmt, ob es sich bei einer Zeichenfolge um gültige <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />-Informationen handelt.</summary>
      <returns>Gibt <see cref="T:System.Boolean" /> zurück.true, wenn <paramref name="input" /> gültige <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />-Informationen sind; andernfalls false.</returns>
      <param name="input">Die zu validierende Zeichenfolge.</param>
      <param name="parsedValue">Die <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />-Version der Zeichenfolge.</param>
    </member>
  </members>
</doc>