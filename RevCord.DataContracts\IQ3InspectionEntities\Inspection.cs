﻿using RevCord.DataContracts.InquireEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3InspectionEntities
{
    public class Inspection
    {
        public long Id { get; set; }
        public int InspectionTemplateId { get; set; }
        public string EventId { get; set; }
        public int InspectorId { get; set; }
        public string Comments { get; set; }
        public string Remarks { get; set; }
        public bool IsPassFailRequired { get; set; }
        public bool IsPassed { get; set; }
        public string FailReason { get; set; }
        public string InspectionStatus { get; set; }
        public DateTime InspectionDate { get; set; }
        public string Title { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
        public bool IsDeleted { get; set; }

        public string InspectorName { get; set; }
        public string Description { get; set; }

        public int NoOfSections { get; set; }
        public int NoOfMarkers { get; set; }
        public string StartTime { get; set; }
        public string Duration { get; set; }
        public string EventNotes { get; set; }
        public string GPS { get; set; }
        public bool HasPhotoMarker { get; set; }
        public string PhotoFileNameCSV { get; set; }
        public string SelectedPhotoFileName { get; set; }

        public Object IQ3Asset { get; set; }

        public List<PreInspectionData> PreInspectionDataList { get; set; }
        public List<Section> Sections { get; set; }
        public InspectionReportData InspectionReportData { get; set; }
    }
}