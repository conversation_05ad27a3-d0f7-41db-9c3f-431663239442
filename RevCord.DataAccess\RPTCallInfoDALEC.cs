﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.SqlClient;
using RevCord.DataContracts.ReportEntities;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.VoiceRecEntities;
using System.Configuration;
using RevCord.Util;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;

namespace RevCord.DataAccess
{
    public class RPTCallInfoDALEC
    {
        //private static int CMD_TIMEOUT = ConfigurationManager.AppSettings["commandTimeout"];
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);

        public List<RPTCallInfo> GetSearchResults(ReportRequest reportRequest, Recorder recorder)
        {

            SqlConnection con = reportRequest.TenantId > 0 ? DALHelper.GetConnection(reportRequest.TenantId) : new SqlConnection(recorder.ConnectionString);
            //con.ConnectionTimeout = 100;
            //SqlConnection con = new SqlConnection(recorder.ConnectionString + "Connection Timeout=90;");
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);
            cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", reportRequest.IsOnlyIQ3ModeEnabled);
            cmd.Parameters.AddWithValue("@JOINString", reportRequest.JOINString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);

            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;

            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSearchResults", 0));

                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"]),
                                     recorder.Id,
                                     recorder.Name)
                                 { }
                                     ).ToList();
                //).OrderBy(c => c.CallDate).ToList();

                //List<RPTCallInfo> target = dt.AsEnumerable()
                //                    .Select(row => new RPTCallInfo
                //                    {
                //                        Key = row["Key"].ToString(),
                //                        //Key = row.Field<long?>(0).GetValueOrDefault(), // column 0's type is Nullable<long>
                //                        Year = Convert.ToInt32(row["Year"]),
                //                        Month = Convert.ToInt32(row["Month"]),
                //                        Day = Convert.ToInt32(row["Day"]),
                //                        Count = Convert.ToInt64(row["Count"]),
                //                        Total = Convert.ToInt64(row["Total"]),
                //                        Avg = Convert.ToInt64(row["Avg"]),
                //                        RecorderId = recorder.Id,
                //                        RecoderName = recorder.Name
                //                        //DayOfWeek = String.IsNullOrEmpty(row.Field<string>(1)) ? "not found": row.Field<string>(1),
                //                    }).ToList();

            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }

        public List<RPTCallInfoDetail> GetDetailSearchResults(ReportRequest reportRequest, Recorder recorder)
        {
            SqlConnection con = reportRequest.TenantId > 0 ? DALHelper.GetConnection(reportRequest.TenantId) : new SqlConnection(recorder.ConnectionString); //DALHelper.GetConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_CALLDETAILS_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            //cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            //cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            //cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            //cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            //cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            //cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);

            DataTable dtCallInfoDetails = new DataTable();
            List<RPTCallInfoDetail> detailResults = null;

            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetDetailSearchResults", 0));
                dataAdapter.Fill(dtCallInfoDetails);
                detailResults = (from DataRow row in dtCallInfoDetails.Rows
                                 select new RPTCallInfoDetail(
                                     row["Key"].ToString(),
                                     row["CallID"].ToString(),
                                     row["UserName"].ToString(),
                                     row["GroupName"].ToString(),
                                     row["ChannelName"].ToString(),
                                     row["Ext"].ToString(),
                                     row["StartTime"].ToString(),
                                     Convert.ToInt64(row["Duration"]),
                                     (DayOfWeek)Enum.Parse(typeof(DayOfWeek), (Convert.ToInt32(row["Day"]) - 1).ToString()),
                                     recorder.Id, recorder.Name
                                     )
                                 { }
                                     ).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            detailResults.TrimExcess();
            return detailResults;
        }


        public ReportResponse GetCallSearchResults(ReportRequest reportRequest, Recorder recorder)
        {
            SqlConnection con = reportRequest.TenantId > 0 ? DALHelper.GetConnection(reportRequest.TenantId) : new SqlConnection(recorder.ConnectionString);//= DALHelper.GetConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);
            cmd.Parameters.AddWithValue("@getCallDetails", reportRequest.GetDetailReportData);
            cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", reportRequest.IsOnlyIQ3ModeEnabled);
            cmd.Parameters.AddWithValue("@JOINString", reportRequest.JOINString);
            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataSet dsCalls = new DataSet();
            List<RPTCallInfo> searchResults = null;
            List<RPTCallInfoDetail> detailResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetCallSearchResults", 0));
                dataAdapter.Fill(dsCalls);
                searchResults = (from DataRow row in dsCalls.Tables[0].Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"]),
                                     recorder.Id,
                                     recorder.Name
                                     )
                                 { }
                                     ).ToList();
                //).OrderBy(c => c.CallDate).ToList();
                if (reportRequest.GetDetailReportData)
                {
                    detailResults = (from DataRow row in dsCalls.Tables[1].Rows
                                     select new RPTCallInfoDetail(
                                         row["Key"].ToString(),
                                         row["CallID"].ToString(),
                                         row["UserName"].ToString(),
                                         row["GroupName"].ToString(),
                                         row["ChannelName"].ToString(),
                                         row["Ext"].ToString(),
                                         row["StartTime"].ToString(),
                                         Convert.ToInt64(row["Duration"]))
                                     { }
                                         ).ToList();
                }
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess(); detailResults.TrimExcess();
            return new ReportResponse { CallInfos = searchResults, CallInfoDetails = detailResults };
        }



        public List<RPTCallInfo> GetSearchResultsMonthDayOfWeek(ReportRequest reportRequest, Recorder recorder)
        {
            SqlConnection con = reportRequest.TenantId > 0 ? DALHelper.GetConnection(reportRequest.TenantId) : new SqlConnection(recorder.ConnectionString);//= DALHelper.GetConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);
            cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", reportRequest.IsOnlyIQ3ModeEnabled);
            cmd.Parameters.AddWithValue("@JOINString", reportRequest.JOINString);
            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> monthWeekDays = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSearchResultsMonthDayOfWeek", 0));

                dataAdapter.Fill(dt);
                monthWeekDays = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     (DayOfWeek)Enum.Parse(typeof(DayOfWeek), (Convert.ToInt32(row["Day"]) - 1).ToString()),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"]),
                                     recorder.Id,
                                     recorder.Name
                                     )
                                 { }
                                     ).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            monthWeekDays.TrimExcess();
            return monthWeekDays;
        }



        public List<RPTCallInfo> GetSearchResultsDayOfWeek(ReportRequest reportRequest, Recorder recorder)
        {
            SqlConnection con = reportRequest.TenantId > 0 ? DALHelper.GetConnection(reportRequest.TenantId) : new SqlConnection(recorder.ConnectionString);//= DALHelper.GetConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);
            cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", reportRequest.IsOnlyIQ3ModeEnabled);
            cmd.Parameters.AddWithValue("@JOINString", reportRequest.JOINString);
            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSearchResultsDayOfWeek", 0));

                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo
                                 {
                                     Key = row["Key"].ToString(),
                                     DayOfWeek = (DayOfWeek)Enum.Parse(typeof(DayOfWeek), (Convert.ToInt32(row["Day"]) - 1).ToString()),
                                     Count = Convert.ToInt64(row["Count"]),
                                     Total = Convert.ToInt64(row["Total"]),
                                     Avg = Convert.ToInt64(row["Avg"]),
                                     RecorderId = recorder.Id,
                                     RecoderName = recorder.Name
                                 }).ToList();

            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }


        public List<RPTCallInfo> GetSearchResultsHour(ReportRequest reportRequest, Recorder recorder)
        {
            SqlConnection con = reportRequest.TenantId > 0 ? DALHelper.GetConnection(reportRequest.TenantId) : new SqlConnection(recorder.ConnectionString);
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_HOUR_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);
            cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", reportRequest.IsOnlyIQ3ModeEnabled);
            cmd.Parameters.AddWithValue("@JOINString", reportRequest.JOINString);
            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSearchResultsHour", 0));

                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt32(row["CallHour"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"]),
                                     recorder.Id,
                                     recorder.Name
                                     )
                                 { }
                                     ).ToList();
                //).OrderBy(c => c.CallDate).ToList();

            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }

        public List<RPTCallInfo> GetCallAuditSearchResults(ReportRequest reportRequest, Recorder recorder)
        {
            SqlConnection con = reportRequest.TenantId > 0 ? DALHelper.GetConnection(reportRequest.TenantId) : new SqlConnection(recorder.ConnectionString);
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.CallAuditReport.CALL_AUDIT_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetCallAuditSearchResults", 0));

                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     recorder.Id,
                                     recorder.Name
                                     )
                                 { }
                                     ).ToList();
                //).OrderBy(c => c.CallDate).ToList();

            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }

        public List<RPTCallInfo> GetCallsNotAuditedSearchResults(ReportRequest reportRequest, Recorder recorder)
        {
            SqlConnection con = reportRequest.TenantId > 0 ? DALHelper.GetConnection(reportRequest.TenantId) : new SqlConnection(recorder.ConnectionString);
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.CallAuditReport.CALL_AUDIT_NOT_AUDITED_CALLS_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetCallsNotAuditedSearchResults", 0));

                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     recorder.Id,
                                     recorder.Name
                                     )
                                 { }
                                     ).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }

        #region 911 PBX
        public List<RPTCallInfo> GetSearchResults911(ReportRequest reportRequest, Recorder recorder)
        {
            SqlConnection con = reportRequest.TenantId > 0 ? DALHelper.GetConnection(reportRequest.TenantId) : new SqlConnection(recorder.ConnectionString);
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_911PBX_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", reportRequest.QueryParameter.SelectKey);
            cmd.Parameters.AddWithValue("@selectYear", reportRequest.QueryParameter.SelectYear);
            cmd.Parameters.AddWithValue("@selectMonth", reportRequest.QueryParameter.SelectMonth);
            cmd.Parameters.AddWithValue("@selectDay", reportRequest.QueryParameter.SelectDay);
            cmd.Parameters.AddWithValue("@FromDate", reportRequest.QueryParameter.FromDate);
            cmd.Parameters.AddWithValue("@ToDate", reportRequest.QueryParameter.ToDate);
            cmd.Parameters.AddWithValue("@TimeRange", reportRequest.QueryParameter.TimeRange);
            cmd.Parameters.AddWithValue("@Duration", reportRequest.QueryParameter.Duration);
            cmd.Parameters.AddWithValue("@groupKey", reportRequest.QueryParameter.GroupKey);
            cmd.Parameters.AddWithValue("@groupYear", reportRequest.QueryParameter.GroupYear);
            cmd.Parameters.AddWithValue("@groupMonth", reportRequest.QueryParameter.GroupMonth);
            cmd.Parameters.AddWithValue("@groupDay", reportRequest.QueryParameter.GroupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", reportRequest.QueryParameter.OptionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);

            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;

            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Report, "GetSearchResults911", 0));

                dataAdapter.Fill(dt);
                /* START :: Fill some static data */
                //searchResults = new List<RPTCallInfo>();
                //searchResults.Add(new RPTCallInfo("1001 : ROOT: CH1001", Convert.ToInt32(2020), Convert.ToInt32(08), Convert.ToInt32(15), Convert.ToInt64(100), Convert.ToInt64(200), Convert.ToInt64(10), recorder.Id, recorder.Name, Convert.ToInt32(5), Convert.ToInt32(10), Convert.ToInt32(15), Convert.ToInt64(30000), Convert.ToInt64(120000), Convert.ToInt64(4000)));
                //searchResults.Add(new RPTCallInfo("1002 : ROOT: CH1002", Convert.ToInt32(2020), Convert.ToInt32(08), Convert.ToInt32(16), Convert.ToInt64(50), Convert.ToInt64(100), Convert.ToInt64(5), recorder.Id, recorder.Name, Convert.ToInt32(3), Convert.ToInt32(5), Convert.ToInt32(8), Convert.ToInt64(20000), Convert.ToInt64(100000), Convert.ToInt64(3000)));
                //return searchResults;
                /* END :: Fill some static data */

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"]),
                                     recorder.Id,
                                     recorder.Name,
                                     Convert.ToInt32(row["NoOfRings"]),
                                     Convert.ToInt32(row["TotalTransferredCount"]),
                                     Convert.ToInt32(row["TotalAbandonedCount"]),
                                     Convert.ToInt64(row["TotalRingTime"]),
                                     Convert.ToInt64(row["TotalTalkTime"]),
                                     Convert.ToInt64(row["TotalHoldTime"]))
                                 { }
                                     ).ToList();

            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }

        public List<RPTCallInfo> GetCallTransferResults911(ReportRequest reportRequest, Recorder recorder)
        {
            return null;
        }

        #endregion
    }
}
