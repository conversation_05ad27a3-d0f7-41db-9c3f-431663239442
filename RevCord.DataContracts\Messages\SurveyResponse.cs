﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.SurveyEntities;

namespace RevCord.DataContracts.Response
{
    public class SurveyResponse
    {
        #region Properties

        /// <summary>
        /// Determines whether the Transaction is successful or not.
        /// </summary>
        public bool FlagStatus { get; set; }



        #endregion


        #region Associations

        public Survey Survey { get; set; }
        public List<Survey> Surveys { get; set; }
        public List<SurveySection> SurveySections  { get; set; }
        public Question Question { get; set; }
        public List<Question> Questions { get; set; }

        #endregion



    }
}
