﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;

namespace RevCord.Util
{
    public class RPTQueryHelper
    {
        private static readonly char _separator = ',';
        private static readonly string _groupDefault = "AND(CI.CallID IS NOT NULL)";
        private static readonly string _groupStart = "AND(CI.GroupNum IN ";
        private static readonly string _openParenthesis = "(";
        private static readonly string _closeParenthesis = ")";
        private static readonly string _orCaluseFormat = "{0}{1}{2}{3}{4}";
        private static readonly string _orCaluseGroup = "OR(CI.GroupNum=";
        private static readonly string _orCaluseExt = " AND CI.Ext='";
        private static readonly string _groupNegitive = "-9999";

        public static string BuildOptionString(string groupNumbers, string extensionGroups, string extensions)
        {
            StringBuilder sb = new StringBuilder();
            if (!String.IsNullOrEmpty(groupNumbers) || !String.IsNullOrEmpty(extensionGroups) || !String.IsNullOrEmpty(extensions))
            {
                sb.Append(_groupStart);

                if (!String.IsNullOrEmpty(groupNumbers) && String.IsNullOrEmpty(extensionGroups) && String.IsNullOrEmpty(extensions))
                {
                    sb.Append(_openParenthesis);
                    sb.Append(groupNumbers);
                    sb.Append(_closeParenthesis);
                }

                if (String.IsNullOrEmpty(groupNumbers) && !String.IsNullOrEmpty(extensionGroups) && !String.IsNullOrEmpty(extensions))
                {
                    sb.Append(_openParenthesis);
                    sb.Append(_groupNegitive);
                    sb.Append(_closeParenthesis);
                    IEnumerable<KeyValuePair<string, string>> collection = RPTQueryHelper.MergeKVP(RPTQueryHelper.getKeywords(extensionGroups), RPTQueryHelper.getKeywords(extensions));
                    foreach (KeyValuePair<string, string> kvp in collection)
                        sb.Append(String.Format(_orCaluseFormat, _orCaluseGroup, kvp.Key, _orCaluseExt, kvp.Value + "'", _closeParenthesis));
                }
                if (!String.IsNullOrEmpty(groupNumbers) && !String.IsNullOrEmpty(extensionGroups) && !String.IsNullOrEmpty(extensions))
                {
                    sb.Append(_openParenthesis);
                    sb.Append(groupNumbers);
                    sb.Append(_closeParenthesis);

                    List<string> groups = RPTQueryHelper.getKeywords(groupNumbers);
                    List<KeyValuePair<string, string>> extensionGroupsKVP = RPTQueryHelper.MergeKVP(RPTQueryHelper.getKeywords(extensionGroups), RPTQueryHelper.getKeywords(extensions)).ToList();
                    foreach (var g in groups)
                        extensionGroupsKVP.RemoveAll(x => x.Key.Equals(g));
                    foreach (KeyValuePair<string, string> kvp in extensionGroupsKVP)
                        sb.Append(String.Format(_orCaluseFormat, _orCaluseGroup, kvp.Key, _orCaluseExt, kvp.Value + "'", _closeParenthesis));
                    //sb.Append(closeParenthesis);
                }
                sb.Append(_closeParenthesis);
            }
            else
                sb.Append(_groupDefault);//sb.Append(QueryConstants.WHERE_CLAUSE_ANIALI);
            return sb.ToString();
        }

        



        static public List<string> getKeywords(string sentence)
        {
            if(!String.IsNullOrEmpty(sentence))
                return sentence.Split(RPTQueryHelper._separator).ToList();
            return null;
            //List<string> uniqueWords = sentence.Split(QueryHelper._separator).ToList();
            //return uniqueWords;
        }
        public static Dictionary<TKey, TValue> Merge<TKey, TValue>(IEnumerable<TKey> keys, IEnumerable<TValue> values)
        {

            var dic = keys.Zip(values, (k, v) => new { k, v })
                  .ToDictionary(x => x.k, x => x.v);
            return dic;
        }

        //public static List<KeyValuePair<TKey, TValue>> MergeKVP<TKey, TValue>(IEnumerable<TKey> keys, IEnumerable<TValue> values)
        //{
        //    List<KeyValuePair<TKey, TValue>> kvp = new List<KeyValuePair<TKey, TValue>>();
        //    kvp.Add(new KeyValuePair<TKey,TValue>(
        //    var dic = keys.Zip(values, (k, v) => new { k, v })
        //          .ToDictionary(x => x.k, x => x.v);

        //    //var dicS = keys.Select((k, i) => new { k, v = values[i] })
        //    //  .ToDictionary(x => x.k, x => x.v);

        //    return dic;

        //}

        public static IEnumerable<KeyValuePair<TKey, TValue>> MergeKVP<TKey, TValue>(IEnumerable<TKey> keys, IEnumerable<TValue> values)
        {
            var dic = keys.Zip(values, (k, v) => new { k, v });

            foreach (var key in dic)
                yield return new KeyValuePair<TKey, TValue>(key.k, key.v);
        }

        

    }
}
