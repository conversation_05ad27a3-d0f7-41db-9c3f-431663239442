﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.Request;
using RevCord.DataAccess;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.MessageBase;
using RevCord.Util;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using System.Web.Script.Serialization;

namespace RevCord.BusinessLogic
{
    public class UserManagementManager
    {
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress; //"127.0.0.1";
        private static readonly int _passwordExpiryInDays = AppSettingsUtil.GetInt("passwordExpiryInDays", 90);
        private static readonly int _passwordReminderInDays = AppSettingsUtil.GetInt("passwordReminderInDays", 3);

        #region Global Groups

        public UserManagementResponse CreateGroup(UserManagementRequest umRequest)
        {
            try
            {
                var dal = new UserManagementDAL(umRequest.TenantId);
                UserManagementResponse sResponse = new UserManagementResponse();
                sResponse.GlobalGroups = dal.CreateGroup(umRequest.GlobalGroup);
                sResponse.FlagStatus = (umRequest.GlobalGroup.Id != -1);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CreateGroup", umRequest.TenantId, "CreateGroup function has been called successfully."));
                return sResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetGropus", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetGropus", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse InqCreateGroup(UserManagementRequest umRequest)
        {
            try
            {
                var dal = new UserManagementDAL(umRequest.TenantId);
                UserManagementResponse sResponse = new UserManagementResponse();
                sResponse.GlobalGroups = dal.InqCreateGroup(umRequest.GlobalGroup);
                sResponse.FlagStatus = (umRequest.GlobalGroup.Id != -1);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "InqCreateGroup", umRequest.TenantId, "InqCreateGroup function has been called successfully."));
                return sResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "InqCreateGroup", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "InqCreateGroup", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }
        public UserManagementResponse DeleteGlobalGroup(UserManagementRequest umRequest)
        {

            try
            {
                var dal = new UserManagementDAL(umRequest.TenantId);
                UserManagementResponse sResponse = new UserManagementResponse();
                sResponse.GlobalGroups = dal.DeleteteGroup(umRequest.GlobalGroup.Id);
                sResponse.FlagStatus = (umRequest.GlobalGroup.Id != -1);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "DeleteGlobalGroup", umRequest.TenantId, "DeleteGlobalGroup function has been called successfully. Id = " + umRequest.GlobalGroup.Id));
                return sResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "DeleteGlobalGroup", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "DeleteGlobalGroup", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }

        public UserManagementResponse UpdateGroup(UserManagementRequest umRequest)
        {
            try
            {
                var dal = new UserManagementDAL(umRequest.TenantId);
                UserManagementResponse sResponse = new UserManagementResponse();
                sResponse.GlobalGroups = dal.UpdateGroup(umRequest.GlobalGroup);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateGroup", umRequest.TenantId, "UpdateGroup function has been called successfully. Id = " + umRequest.GlobalGroup.Id));
                sResponse.FlagStatus = (umRequest.GlobalGroup.Id != -1);
                return sResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateGroup", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateGroup", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse DeleteteGroup(UserManagementRequest umRequest)
        {
            try
            {
                var dal = new UserManagementDAL(umRequest.TenantId);
                UserManagementResponse sResponse = new UserManagementResponse();
                sResponse.GlobalGroups = dal.DeleteteGroup(umRequest.GlobalGroup.Id);
                sResponse.FlagStatus = (umRequest.GlobalGroup.Id != -1);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "DeleteteGroup", umRequest.TenantId, "DeleteteGroup function has been called successfully. Id = " + umRequest.GlobalGroup.Id));
                return sResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "DeleteteGroup", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "DeleteteGroup", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<GlobalGroup> GetGroups(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetGroups", tenantId, "GetGroups function has been called successfully. "));
                return new UserManagementDAL(tenantId).GetGropus();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetGropus", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetGropus", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        #region Application Users

        public UserManagementResponse DeleteAppUser(UserManagementRequest umRequest)
        {
            throw new NotImplementedException();
        }

        public UserManagementResponse GetAppUsers(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAppUsers", tenantId, "GetAppUsers function has been called successfully. "));
                return new UserManagementDAL(tenantId).GetAppUsers();

            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAppUsers", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAppUsers", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse GetAppUsers_User_Manager_Simple_User_Rights(int UserID, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAppUsers_User_Manager_Simple_User_Rights", tenantId, "GetAppUsers_User_Manager_Simple_User_Rights function has been called successfully. userId = " + UserID));
                return new UserManagementDAL(tenantId).GetAppUsers_User_Manager_Simple_User_Rights(UserID);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAppUsers_User_Manager_Simple_User_Rights", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAppUsers_User_Manager_Simple_User_Rights", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetEnterpriseUsers(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAppUsers_User_Manager_Simple_User_Rights", tenantId, "GetAppUsers_User_Manager_Simple_User_Rights function has been called successfully. "));
                return new UserManagementDAL(tenantId).GetEnterpriseUsers();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetEnterpriseUsers", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetEnterpriseUsers", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetUsersWithoutExtension(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetUsersWithoutExtension", tenantId, "GetUsersWithoutExtension function has been called successfully. "));
                return new UserManagementDAL(tenantId).GetUsersWithoutExtension();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetUsersWithoutExtension", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetUsersWithoutExtension", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse GetAllUsersAsAgents(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAllUsersAsAgents", tenantId, "GetAllUsersAsAgents function has been called successfully. "));
                return new UserManagementResponse { Users = new UserManagementDAL(tenantId).GetAllUsersAsAgents() };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAllUsersAsAgents", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAllUsersAsAgents", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetAllUsersWithPermission(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAllUsersWithPermission", tenantId, "GetAllUsersWithPermission function has been called successfully. "));
                return new UserManagementResponse { Users = new UserManagementDAL(tenantId).GetAllUsersWithPermission() };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAllUsersWithPermission", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAllUsersWithPermission", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetAllAppUsers(int tenantId, int userTypeId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAllAppUsers", tenantId, "GetAllAppUsers function has been called successfully. "));
                return new UserManagementResponse { Users = new UserManagementDAL(tenantId).GetAllAppUsers(userTypeId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAllAppUsers", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAllAppUsers", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetAgentsFromRecorder(Recorder recorder, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAgentsFromRecorder", tenantId, "GetAgentsFromRecorder function has been called successfully. recorderId =   " + recorder.Id));

                if (recorder.IsPrimary)
                    return new UserManagementResponse { Users = new UserManagerDALEC(tenantId).GetAllUsersFromRecorder(recorder) };
                else
                {
                    // Alternative Approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return new UserManagementResponse { Users = entClient.GetUsers(recorder).ToList() };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAgentsFromRecorder", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAgentsFromRecorder", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse GetAgentsFromAllRecorders(List<Recorder> recorders, int tenantId)
        {
            try
            {
                List<User> users = new List<User>();
                foreach (var recorder in recorders)
                {
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAgentsFromAllRecorders", tenantId, "GetAgentsFromAllRecorders function has been called successfully. recorderId =   " + recorder.Id));
                    bool bExceptionThrown = false;
                    try
                    {
                        if (recorder.IsPrimary)
                            users.AddRange(new UserManagerDALEC(tenantId).GetAllUsersFromRecorder(recorder));
                        else
                        {
                            // Alternative approach using service based call.
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                            users.AddRange(entClient.GetUsers(recorder).ToList());
                        }
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAgentsFromAllRecorders", tenantId, "GetAgentsFromAllRecorders function has been called successfully. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name));
                    }
                    catch (SqlException sqle)
                    {
                        Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAgentsFromAllRecorders", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                        bExceptionThrown = true;
                    }
                    catch (Exception ex)
                    {
                        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAgentsFromAllRecorders", tenantId, "An error has occurred while fetching agent's data from Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                        bExceptionThrown = true;
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new UserManagementResponse { Users = users };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAgentsFromAllRecorders", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAgentsFromAllRecorders", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse GetUserData(int tenantId)
        {
            List<UserInfoLite> liteUsers = new List<UserInfoLite>();
            try
            {
                liteUsers = new UserManagementDAL(tenantId).GetUserData();
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAgentsFromAllRecorders", tenantId, "GetUserData(). Lite user data fetched successfully. liteUsers = " + new JavaScriptSerializer().Serialize(liteUsers)));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetUserData", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetUserData", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
            }
            return new UserManagementResponse { LiteUsers = liteUsers };
        }
        public UserManagementResponse GetUserDataFromAllRecorders(List<Recorder> recorders, int tenantId)
        {
            List<UserInfoLite> liteUsers = new List<UserInfoLite>();
            foreach (var recorder in recorders)
            {
                bool bExceptionThrown = false;
                try
                {
                    if (recorder.IsPrimary)
                        liteUsers.AddRange(new UserManagerDALEC(tenantId).GetUserData(recorder));
                    else
                    {
                        // Alternative approach using service based call.
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        liteUsers.AddRange(entClient.GetUserData(recorder).ToList());
                    }
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetUserDataFromAllRecorders", tenantId, "GetUserDataFromAllRecorders().Lite user data fetched successfully from recorder.Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name + " liteUsers = " + new JavaScriptSerializer().Serialize(liteUsers)));
                }
                catch (SqlException sqle)
                {
                    bExceptionThrown = true;
                    Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetUserDataFromAllRecorders", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                }
                catch (Exception ex)
                {
                    bExceptionThrown = true;
                    Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetUserDataFromAllRecorders", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                }
                if (bExceptionThrown)
                    continue;
            }
            return new UserManagementResponse { LiteUsers = liteUsers };
        }


        #region User Account

        public UserManagementResponse CreateAppUserAccount(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CreateAppUserAccount", umRequest.TenantId, "CreateAppUserAccount function has been called successfully."));
                if (umRequest.User.IsIwbUser && umRequest.User.UserType == 8)
                {
                    umRequest.User.WelderStencilNumber = StringExtension.GenerateStencilNumber(umRequest.User.DOB.GetValueOrDefault(), umRequest.User.SocialSecurityNumber);
                }
                UserManagementResponse umResponse = new UserManagementResponse { Users = new UserManagementDAL(umRequest.TenantId).CreateAppUserAccount(umRequest.User, umRequest.IsOnlyIQ3ModeEnabled), FlagStatus = true, };
                return umResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CreateAppUserAccount", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CreateAppUserAccount", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse UpdateAppUserAccount(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateAppUserAccount", umRequest.TenantId, "UpdateAppUserAccount function has been called successfully."));
                UserManagementResponse umResponse = new UserManagementResponse { Users = new UserManagementDAL(umRequest.TenantId).UpdateAppUserAccount(umRequest.User), FlagStatus = true, };
                return umResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateAppUserAccount", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateAppUserAccount", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse UpdateAppUserAccount(Recorder recorder, UserManagementRequest umRequest, bool updateUserPwd)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateAppUserAccount", umRequest.TenantId, "UpdateAppUserAccount function has been called successfully. recorder.Id = " + recorder.Id));
                if (recorder.IsPrimary)
                    return new UserManagementResponse { Users = new UserManagementDAL(umRequest.TenantId).UpdateAppUserAccount(recorder, umRequest.User, updateUserPwd), FlagStatus = true, };
                else
                {
                    // Alternative approach using service based call.
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return new UserManagementResponse { Users = entClient.UpdateAppUserAccount(recorder, umRequest.User).ToList(), FlagStatus = true, };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateAppUserAccount", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateAppUserAccount", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse DeleteAppUserAccount(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "DeleteAppUserAccount", umRequest.TenantId, "DeleteAppUserAccount function has been called successfully. "));
                return new UserManagementDAL(umRequest.TenantId).DeleteAppUserAccount(umRequest.User.UserNum);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "DeleteAppUserAccount", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "DeleteAppUserAccount", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse DeleteAppUserAccount(Recorder recorder, UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "DeleteAppUserAccount", umRequest.TenantId, "DeleteAppUserAccount function has been called successfully. recorder.Id =  " + recorder.Id));
                if (recorder.IsPrimary)
                    return new UserManagementDAL(umRequest.TenantId).DeleteAppUserAccount(recorder, umRequest.User.UserNum, umRequest.User.RevSyncServerUserNum);
                else
                {
                    // Alternative approach using service based call.
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return entClient.DeleteAppUserAccount(recorder, umRequest.User.UserNum);
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "DeleteAppUserAccount(Recorder recorder, UserManagementRequest umRequest)", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "DeleteAppUserAccount(Recorder recorder, UserManagementRequest umRequest)", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public int PermanentRemoveAppUserAccount(int userNum, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "PermanentRemoveAppUserAccount", tenantId, "PermanentRemoveAppUserAccount function has been called successfully. "));
                return new UserManagementDAL(tenantId).PermanentRemoveAppUserAccount(userNum);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "PermanentRemoveAppUserAccount", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "PermanentRemoveAppUserAccount", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public int UpdateQBUserInfo(int userNum, int qbUserNum, bool existsOnQB, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateQBUserInfo", tenantId, "UpdateQBUserInfo function has been called successfully. "));
                return new UserManagementDAL(tenantId).UpdateQBUserInfo(userNum, qbUserNum, existsOnQB);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateQBUserInfo", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateQBUserInfo", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool CheckUserAlreadyExistsAndActive(string email, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CheckUserAlreadyExistsAndActive", tenantId, "CheckUserAlreadyExistsAndActive function has been called successfully. email = " + email));
                return new UserManagementDAL(tenantId).CheckUserAlreadyExistsAndActive(email);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CheckUserAlreadyExistsAndActive", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CheckUserAlreadyExistsAndActive", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool IsInviteeActiveAndLiveMonitorPermitted(string email, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "IsInviteeActiveAndLiveMonitorPermitted", tenantId, "IsInviteeActiveAndLiveMonitorPermitted function has been called successfully. email = " + email));
                return new UserManagementDAL(tenantId).IsInviteeActiveAndLiveMonitorPermitted(email);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "IsInviteeActiveAndLiveMonitorPermitted", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "IsInviteeActiveAndLiveMonitorPermitted", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse RecoverEnterpriseUserAccount(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "RecoverEnterpriseUserAccount", umRequest.TenantId, "RecoverEnterpriseUserAccount function has been called successfully. "));
                int rowsAffected = new UserManagementDAL(umRequest.TenantId).RecoverEnterpriseUserAccount(umRequest.UserIdTree);
                return new UserManagementResponse
                {
                    FlagStatus = rowsAffected > 0 ? true : false
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "RecoverEnterpriseUserAccount", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "RecoverEnterpriseUserAccount", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse RecoverAppUserAccount(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "RecoverAppUserAccount", umRequest.TenantId, "RecoverAppUserAccount function has been called successfully. "));
                return new UserManagementDAL(umRequest.TenantId).RecoverAppUserAccount(umRequest.UserIdTree);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "RecoverAppUserAccount", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "RecoverAppUserAccount", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse RecoverAppUserAccount(List<Recorder> recorders, UserManagementRequest umRequest)
        {
            try
            {
                List<RecUser> recUserIds = umRequest.RecUserIds;
                int rowsAffected = 0;
                foreach (var rec in recorders)
                {
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "RecoverAppUserAccount", umRequest.TenantId, "RecoverAppUserAccount function has been called successfully. recorder.Id =  " + rec.Id));
                    //string callIds = "";
                    string csvUserds = string.Format("'{0}'", string.Join("','", recUserIds.Where(r => r.RecId == rec.Id).Select(i => i.UserId.Replace("'", "''"))));

                    if (rec.IsPrimary)
                    {
                        if (!string.IsNullOrEmpty(csvUserds) && !csvUserds.Equals("''"))
                            rowsAffected += new UserManagementDAL(umRequest.TenantId).RecoverAppUserAccount(rec, csvUserds);
                    }
                    else
                    {
                        // Alternative approach using service based call.
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        if (!string.IsNullOrEmpty(csvUserds) && !csvUserds.Equals("''"))
                            rowsAffected += entClient.RecoverAppUserAccount(rec, csvUserds);
                    }
                }
                return new UserManagementResponse
                {
                    FlagStatus = rowsAffected > 0 ? true : false
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "RecoverAppUserAccount Recorders", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "RecoverAppUserAccount Recorder", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetAppUserAccount(long uId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAppUserAccount", tenantId, "GetAppUserAccount function has been called successfully."));
                return new UserManagementDAL(tenantId).GetAppUserAccount(uId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAppUserAccount", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAppUserAccount", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse GetAppUserAccount(Recorder recorder, long uId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAppUserAccount", tenantId, "GetAppUserAccount function has been called successfully. recorder.Id =  " + recorder.Id));
                if (recorder.IsPrimary)
                    return new UserManagementDAL(tenantId).GetAppUserAccount(recorder, uId);
                else
                {
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return entClient.GetAppUserAccount(recorder, uId);
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAppUserAccount", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAppUserAccount", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        #region User Profile

        public UserManagementResponse CreateAppUserProfile(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CreateAppUserProfile", umRequest.TenantId, "CreateAppUserProfile function has been called successfully."));
                return new UserManagementResponse { AppUsers = new UserManagementDAL(umRequest.TenantId).CreateAppUserProfile(umRequest.AppUser), FlagStatus = true, };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CreateAppUserProfile", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CreateAppUserProfile", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse UpdateAppUserProfile(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateAppUserProfile", umRequest.TenantId, "UpdateAppUserProfile function has been called successfully."));
                return new UserManagementResponse { AppUsers = new UserManagementDAL(umRequest.TenantId).UpdateAppUserProfile(umRequest.AppUser), FlagStatus = true, };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateAppUserProfile", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateAppUserProfile", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetAppUserProfile(long uId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAppUserProfile", tenantId, "GetAppUserProfile function has been called successfully. uId = " + uId));
                return new UserManagementDAL(tenantId).GetAppUserProfile(uId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAppUserProfile", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAppUserProfile", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion
        #region Inquire Group Management
        public UserManagementResponse InquireDeleteGroup(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "InquireDeleteGroup", umRequest.TenantId, "InquireDeleteGroup function has been called successfully."));
                var dal = new UserManagementDAL(umRequest.TenantId);
                UserManagementResponse sResponse = new UserManagementResponse();
                sResponse.GlobalGroups = dal.InquireDeleteGroup(umRequest.GlobalGroup.Id);
                sResponse.FlagStatus = (umRequest.GlobalGroup.Id != -1);
                return sResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "InquireDeleteGroup", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "InquireDeleteGroup", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse SaveUserGroup(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "SaveUserGroup", umRequest.TenantId, "SaveUserGroup function has been called successfully."));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).SaveUserGroup(umRequest.User.UserGroup) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "SaveUserGroup", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "SaveUserGroup", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse SaveUserGroup(Recorder recorder, UserManagementRequest umRequest)
        {
            UserManagementResponse userManagementResponse = null;
            try
            {
                userManagementResponse = new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).SaveUserGroup(recorder, umRequest.User.UserGroup) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "SaveUserGroup", umRequest.TenantId, "SaveUserGroup() has been exeuted successfully. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "SaveUserGroup", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "SaveUserGroup", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return userManagementResponse;
        }

        public UserManagementResponse EnableDisableInquireUser(UserManagementRequest umRequest)
        {
            try
            {
                int channelType = 0;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "EnableDisableInquireUser", umRequest.TenantId, "EnableDisableInquireUser() has been exeuted successfully. "));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).EnableDisableInquireUser(umRequest.User, out channelType), ChannelType = channelType };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "EnableDisableInquireUser", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "EnableDisableInquireUser", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse EnableDisableAvrisView(UserManagementRequest umRequest)
        {
            return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).EnableDisableAvrisView(umRequest.User) };
        }

        public UserManagementResponse EnableDisableIQ3View(UserManagementRequest umRequest)
        {
            return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).EnableDisableIQ3View(umRequest.User) };
        }

        public UserManagementResponse EnableDisableRevCell(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "EnableDisableRevCell", umRequest.TenantId, "EnableDisableRevCell() has been exeuted successfully. "));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).EnableDisableRevCell(umRequest.User) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "EnableDisableRevCell", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "EnableDisableRevCell", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse ManageRevcellPermission(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "ManageRevcellPermission", umRequest.TenantId, "ManageRevcellPermission() has been exeuted successfully. "));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).ManageRevcellPermission(umRequest.User) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "ManageRevcellPermission", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "ManageRevcellPermission", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool HasRevcellForCurrentServer(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "HasRevcellForCurrentServer", umRequest.TenantId, "HasRevcellForCurrentServer() has been exeuted successfully. Email = " + umRequest.User.UserEmail));
                return new UserManagementDAL(umRequest.TenantId).HasRevcellForCurrentServer(umRequest.User.UserEmail);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "HasRevcellForCurrentServer", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "HasRevcellForCurrentServer", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool HasActiveRevcellLicense(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "HasActiveRevcellLicense", umRequest.TenantId, "HasActiveRevcellLicense() has been exeuted successfully. Email = " + umRequest.User.UserEmail));
                return new UserManagementDAL(umRequest.TenantId).HasActiveRevcellLicense(umRequest.User.UserEmail);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "HasActiveRevcellLicense", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "HasActiveRevcellLicense", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool HasActiveIQ3License(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "HasActiveIQ3License", umRequest.TenantId, "HasActiveIQ3License() has been exeuted successfully. Email = " + umRequest.User.UserEmail));
                return new UserManagementDAL(umRequest.TenantId).HasActiveIQ3License(umRequest.User.UserEmail);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "HasActiveIQ3License", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "HasActiveIQ3License", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse ManageIQ3Permission(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "ManageIQ3Permission", umRequest.TenantId, "ManageIQ3Permission() has been exeuted successfully. "));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).ManageIQ3Permission(umRequest.User) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "ManageIQ3Permission", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "ManageIQ3Permission", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool IsDeviceUser(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "IsDeviceUser", umRequest.TenantId, "IsDeviceUser() has been exeuted successfully. Email = " + umRequest.User.UserEmail));
                return new UserManagementDAL(umRequest.TenantId).IsDeviceUser(umRequest.User.UserEmail);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "IsDeviceUser", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "IsDeviceUser", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetUserExtensionInfos(UserManagementRequest umRequest)
        {
            try
            {
                bool bExceptionThrown = false;

                List<UserExtensionInfo> userExtensionInfos = new List<UserExtensionInfo>();
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetUserExtensionInfos", umRequest.TenantId, "GetUserExtensionInfos has been exeuted successfully"));
                foreach (var recorder in umRequest.Recorders)
                {
                    try
                    {
                        bExceptionThrown = false;
                        if (recorder.IsPrimary)
                            userExtensionInfos.AddRange(new UserManagementDAL(umRequest.TenantId).GetUserExtensionInfos(recorder.Id));
                        else
                        {
                            // Alternative Approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                            userExtensionInfos.AddRange(entClient.GetUserExtensionInfos(recorder.Id).ToList());
                        }
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetUserExtensionInfos", umRequest.TenantId, "An error has occurred while fetching user extensions data from Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new UserManagementResponse { UserExtensionInfos = userExtensionInfos };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetUserExtensionInfos", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetUserExtensionInfos", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public UserManagementResponse InqGetUsers(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "InqGetUsers", umRequest.TenantId, "InqGetUsers has been exeuted successfully"));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).InqSaveGroupUser(umRequest.User.UserGroup) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "InqGetUsers", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "InqGetUsers", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse InqSaveGroupUser(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "InqSaveGroupUser", umRequest.TenantId, "InqSaveGroupUser has been exeuted successfully"));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).InqSaveGroupUser(umRequest.User.UserGroup) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "InqSaveGroupUser", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "InqSaveGroupUser", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse DeleteUserGroup(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "DeleteUserGroup", umRequest.TenantId, "DeleteUserGroup has been exeuted successfully"));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).DeleteUserGroup(umRequest.User.UserGroup) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "DeleteUserGroup", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "DeleteUserGroup", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse DeleteUserGroup(Recorder recorder, UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "DeleteUserGroup", umRequest.TenantId, "DeleteUserGroup has been exeuted successfully"));
                UserManagementResponse userManagementResponse = null;
                // Alternative approach using service based call.
                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                userManagementResponse = new UserManagementResponse { FlagStatus = entClient.DeleteUserGroup(recorder, umRequest.User.UserGroup) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAgentsFromAllRecorders", umRequest.TenantId, "User group/permissions have been deleted successfully.  UserId = " + umRequest.User.UserNum + " , Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name));

                return userManagementResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "DeleteUserGroup (recorder)", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "DeleteUserGroup (recorder)", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse InqDeleteUserGroup(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "InqDeleteUserGroup", umRequest.TenantId, "InqDeleteUserGroup has been exeuted successfully"));

                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).InqDeleteGroupUser(umRequest.User.UserGroup) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "InqDeleteUserGroup", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "InqDeleteUserGroup", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse AssignUnassignGroup(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "AssignUnassignGroup", umRequest.TenantId, "AssignUnassignGroup has been exeuted successfully"));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).SaveAssignedGroup(umRequest.User.UserNum, umRequest.User.GroupNum) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "AssignUnassignGroup", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "AssignUnassignGroup", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse AssignUnassignGroup(Recorder recorder, UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "AssignUnassignGroup", umRequest.TenantId, "AssignUnassignGroup has been exeuted successfully. RecorderId = " + recorder.Id));
                if (recorder.IsPrimary)
                    return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).SaveAssignedGroup(recorder, umRequest.User.UserNum, umRequest.User.GroupNum, umRequest.User.RevSyncServerUserNum) };
                else
                {
                    // Alternative approach using service based call.
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return new UserManagementResponse { FlagStatus = entClient.SaveAssignedGroup(recorder, umRequest.User.UserNum, umRequest.User.GroupNum) };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "AssignUnassignGroup(recorder)", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "AssignUnassignGroup(recorder)", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion
        #endregion

        #region TreeView

        public UserManagementResponse GetTreeView(UserManagementRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetTreeView", umReq.TenantId, "GetTreeView has been exeuted successfully. "));
                var GroupCategories = new List<GroupCategory>();
                //  GroupCategories.Add(new GroupCategory { Id = 1, Name = "Audio", ImageName = "audio_list.png" }); //Commented by arivu for Evaluation report treeview 
                GroupCategories.Add(new GroupCategory { Id = 1, Name = "Audio", ImageName = "" });
                GroupCategories.Add(new GroupCategory { Id = 2, Name = "Video", ImageName = "video_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 3, Name = "Text", ImageName = "text_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 4, Name = "Social", ImageName = "social_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 5, Name = "Email", ImageName = "email_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 6, Name = "Screens", ImageName = "screens_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 7, Name = "Inquire", ImageName = "inquire_list.png" });
                return new UserManagementResponse
                {
                    TreeViewDataDTOs = new UserManagementDAL(umReq.TenantId).GetTreeView(umReq.UserNum, umReq.UserIdTree, umReq.AuthNum, umReq.AuthType, umReq.Type, umReq.GetOnlyAudioChannels, umReq.SelectType),
                    GroupCategories = GroupCategories
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetTreeView", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetTreeView", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetTreeViewFromRecorder(Recorder recorder, UserManagementRequest umReq)
        {
            try
            {
                List<TreeViewDataDTO> treeViewDataDTOs = null;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetTreeViewFromRecorder", umReq.TenantId, "GetTreeViewFromRecorder has been exeuted successfully. "));

                var GroupCategories = new List<GroupCategory>();
                GroupCategories.Add(new GroupCategory { Id = 1, Name = "Audio", ImageName = "audio_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 2, Name = "Video", ImageName = "video_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 3, Name = "Text", ImageName = "text_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 4, Name = "Social", ImageName = "social_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 5, Name = "Email", ImageName = "email_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 6, Name = "Screens", ImageName = "screens_list.png" });
                GroupCategories.Add(new GroupCategory { Id = 7, Name = "Inquire", ImageName = "inquire_list.png" });
                if (umReq.IsEnterpriseTree)                     // Note : This code section is specific to Enterprise User Tree
                {
                    int adminUserNum = 1000;
                    int adminUserSelectType = 0;

                    if (recorder.IsPrimary)
                        treeViewDataDTOs = new UserManagementDAL(umReq.TenantId).GetTreeViewFromRecorder(recorder, umReq.UserNum, umReq.UserIdTree, umReq.AuthNum, umReq.AuthType, umReq.Type, umReq.GetOnlyAudioChannels, umReq.SelectType);
                    else
                    {
                        // Alternative approach using service based call.
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        treeViewDataDTOs = entClient.GetTreeViewFromRecorder(recorder, adminUserNum, umReq.UserIdTree, umReq.AuthNum, umReq.AuthType, umReq.Type, umReq.GetOnlyAudioChannels, adminUserSelectType).ToList();
                        // Filtration for building tree according to enterprise rights.
                        List<string> recNodes = new List<string>();
                        List<TreeViewDataDTO> modifiedTreeviewDataDTOs = new List<TreeViewDataDTO>();
                        char splitter = (umReq.AuthNum == 5 || umReq.AuthNum == 7) ? 'A' : 'E';       // TODO   Remoe the hard-coded splitter value once team india switch from channel to agent based reporting. A bug, already discussed with Vasanth.
                        recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(umReq.UserNum, umReq.AuthNum, recorder);
                        modifiedTreeviewDataDTOs = treeViewDataDTOs.FindAll(p => p.IsGroup == true);
                        foreach (var recNode in recNodes)
                        {
                            if (treeViewDataDTOs.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                modifiedTreeviewDataDTOs.Add(treeViewDataDTOs.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                        }
                        treeViewDataDTOs = modifiedTreeviewDataDTOs;
                    }
                }
                else
                {
                    if (recorder.IsPrimary)
                        treeViewDataDTOs = new UserManagementDAL(umReq.TenantId).GetTreeViewFromRecorder(recorder, umReq.UserNum, umReq.UserIdTree, umReq.AuthNum, umReq.AuthType, umReq.Type, umReq.GetOnlyAudioChannels, umReq.SelectType);
                    else
                    {
                        int adminUserNum = 1000;
                        int adminUserSelectType = 0;
                        // Alternative approach using service based call.
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        treeViewDataDTOs = entClient.GetTreeViewFromRecorder(recorder, adminUserNum, umReq.UserIdTree, umReq.AuthNum, umReq.AuthType, umReq.Type, umReq.GetOnlyAudioChannels, adminUserSelectType).ToList();
                    }
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetTreeViewFromRecorder", umReq.TenantId, "GetTreeViewFromRecorder has been exeuted successfully. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name));
                return new UserManagementResponse
                {
                    TreeViewDataDTOs = treeViewDataDTOs,
                    GroupCategories = GroupCategories
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetTreeViewFromRecorder", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetTreeViewFromRecorder", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        public User GetRevcordSupportCredentials(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetRevcordSupportCredentials", umRequest.TenantId, "GetRevcordSupportCredentials has been exeuted successfully. "));
                return new UserManagementDAL(umRequest.TenantId).GetRevcordSupportCredentials();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetRevcordSupportCredentials", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetRevcordSupportCredentials", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetMyInformation(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetMyInformation", umRequest.TenantId, "GetMyInformation has been exeuted successfully. "));

                List<Recorder> recorders = null;
                bool isChainDBConfig = false;
                bool isInquireEnabled = false;
                bool isECEnabled = false;
                bool isEnterpriseRecorder = false;
                List<int> sttEnabledChannels = null;
                bool isUserLocked = false;
                bool isUserExists = false;
                bool isUserAgreedToLicense = false;
                return new UserManagementResponse
                {
                    LoginUser = new UserManagementDAL(umRequest.TenantId).GetMyInformation(umRequest.User.UserID, umRequest.User.UserPW, umRequest.User.IsADUser, umRequest.TenantId, umRequest.ForcePasswordChange, umRequest.IsRoleBasedAccessEnabled, out recorders, out isChainDBConfig, out isInquireEnabled, out isECEnabled, out isEnterpriseRecorder, out sttEnabledChannels, out isUserLocked, out isUserExists, out isUserAgreedToLicense),
                    Recorders = recorders,
                    IsChainDBsConfigured = isChainDBConfig,
                    IsInquire = isInquireEnabled,
                    IsECEnabled = isECEnabled,
                    IsEnterpriseRecorder = isEnterpriseRecorder,
                    STTEnabledChannels = sttEnabledChannels,
                    IsUserLocked = isUserLocked,
                    IsUserExists = isUserExists,
                    IsUserAgreedToLicense = isUserAgreedToLicense,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetMyInformation", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetMyInformation", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetRecorderInfo(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetRecorderInfo", tenantId, "GetRecorderInfo has been exeuted successfully. "));
                return new UserManagementResponse { Recorders = new UserManagementDAL(tenantId).GetRecorderInfo() };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetRecorderInfo", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetRecorderInfo", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse SaveUserImage(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "SaveUserImage", umRequest.TenantId, "SaveUserImage has been exeuted successfully. "));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).SaveUserImage(umRequest.UserNum, umRequest.User.UserPic, umRequest.RevSyncServerUserNum) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "SaveUserImage", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "SaveUserImage", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse UpdateUserImage(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateUserImage", umRequest.TenantId, "UpdateUserImage has been exeuted successfully. "));
                int rowsAffected = new UserManagementDAL(umRequest.TenantId).RemoveUserImage(umRequest.UserId, umRequest.RevSyncServerUserNum);
                return new UserManagementResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateUserImage", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateUserImage", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool UpdateRootNode(int nodeId, string nodeText, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateRootNode", tenantId, "UpdateRootNode has been exeuted successfully. "));
                return new UserManagementDAL(tenantId).UpdateRootNode(nodeId, nodeText);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateRootNode", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateRootNode", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region Invitation
        public Invitation GetInvitationById(int invitationId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetInvitationById", tenantId, "GetInvitationById has been exeuted successfully. "));
                return new UserManagementDAL(tenantId).GetInvitationById(invitationId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetInvitationById", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetInvitationById", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetInvitationsByWhereClause(UserManagementRequest request)//InquireCriteria criteria
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetInvitationsByWhereClause", request.TenantId, "GetInvitationsByWhereClause has been exeuted successfully. "));

                StringBuilder sbWhereClause = new StringBuilder();
                //sbWhereClause.Append(" 1 = 1 ");
                InvitationCriteria criteria = request.Criteria;
                #region ------- Search Criteria -------

                if (criteria != null)
                {
                    if (criteria.SentByUserId > 0)
                    {
                        sbWhereClause.Append(" AND ");
                        sbWhereClause.Append(" (SentBy = " + criteria.SentByUserId + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (!string.IsNullOrEmpty(criteria.InvitationCode))
                    {
                        sbWhereClause.Append(" AND (InviteCode = " + criteria.InvitationCode + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.InvitationStatuses != null)
                    {
                        sbWhereClause.Append(" AND (StatusId IN ( ");
                        foreach (var status in criteria.InvitationStatuses)
                        {
                            sbWhereClause.AppendFormat("{0},", (int)status);
                        }
                        sbWhereClause.RemoveLast(",");
                        sbWhereClause.Append(" ) ");
                        sbWhereClause.Append(" ) ");
                        sbWhereClause.AppendLine();
                    }

                    sbWhereClause.AppendLine();
                }
                #endregion

                System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());

                return new UserManagementResponse
                {
                    Invitations = new UserManagementDAL(request.TenantId).GetInvitationsByWhereClause(sbWhereClause.ToString()),
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetInvitationsByWhereClause", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetInvitationsByWhereClause", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool CheckAlreadyInvited(string email, InvitationStatus status, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CheckAlreadyInvited", tenantId, "CheckAlreadyInvited has been exeuted successfully. "));
                return new UserManagementDAL(tenantId).CheckAlreadyInvited(email, status);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CheckAlreadyInvited", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CheckAlreadyInvited", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse CreateInvitation(UserManagementRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CreateInvitation", request.TenantId, "CreateInvitation has been exeuted successfully. "));
                switch (request.PersistType)
                {
                    case PersistType.Insert:
                        int lastId = new UserManagementDAL(request.TenantId).InsertInvitation(request.Invitation);
                        //request.Invitation.Id = lastId;
                        return new UserManagementResponse
                        {
                            Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                            InvitationId = lastId,
                            Invitation = request.Invitation,
                            Message = string.Format("Invitation has been saved successfully against invitee email:{0}.", request.Invitation.SentToEmail)
                        };
                    //case PersistType.Update:
                    //    int updatedId = InquireDAL.UpdateInvitation(request.Invitation, DateTime.Now, request.UserId);
                    //    return new InquireResponse
                    //    {
                    //        Acknowledge = updatedId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    //        InvitationId = updatedId,
                    //        Message = string.Format("Invitation has been updated successfully against invitationId:{0}.", request.Invitation.Id)
                    //    };
                    default:
                        return null;
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CreateInvitation", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CreateInvitation", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse InsertEventDispatchInfo(UserManagementRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "InsertEventDispatchInfo", request.TenantId, "InsertEventDispatchInfo has been exeuted successfully. "));
                var eventDispatcherInfo = request.EventDispatcherInfo;
                int lastId = new UserManagementDAL(request.TenantId).InsertEventDispatchInfo(eventDispatcherInfo.Ext, eventDispatcherInfo.UserNum, eventDispatcherInfo.Identity, eventDispatcherInfo.EventId, eventDispatcherInfo.CallerInvitationLink);
                return new UserManagementResponse
                {
                    Acknowledge = lastId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    InvitationId = lastId,
                    Invitation = request.Invitation,
                    Message = string.Format("Event dispatcher information has been saved successfully against email: {0}.", eventDispatcherInfo.Identity)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "InsertEventDispatchInfo", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "InsertEventDispatchInfo", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool ChangeInvitationStatus(int invitationId, InvitationStatus status, int? userId, DateTime? modifiedDate, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "ChangeInvitationStatus", tenantId, "ChangeInvitationStatus has been exeuted successfully. "));
                return new UserManagementDAL(tenantId).ChangeInvitationStatus(invitationId, status, userId, modifiedDate) > 0;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "ChangeInvitationStatus", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "ChangeInvitationStatus", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool UpdateInvitationURL(int invitationId, string invitationURL, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateInvitationURL", tenantId, "UpdateInvitationURL has been exeuted successfully. "));
                return new UserManagementDAL(tenantId).UpdateInvitationURL(invitationId, invitationURL) > 0;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateInvitationURL", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateInvitationURL", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool CancelInvitation(int invitationId, int userId, DateTime modifiedDate, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CancelInvitation", tenantId, "CancelInvitation has been exeuted successfully. userId = " + userId + " - invitationId = " + invitationId));
                return new UserManagementDAL(tenantId).CancelInvitation(invitationId, InvitationStatus.Revoked, userId, modifiedDate) > 0;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CancelInvitation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CancelInvitation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse AcceptInvitation(UserManagementRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "AcceptInvitation", request.TenantId, "AcceptInvitation has been exeuted successfully. userId = " + request.UserId + " - invitationId = " + request.InvitationId));
                int lastUserId = new UserManagementDAL(request.TenantId).AcceptInvitation(request.User, request.InvitationId, InvitationStatus.Accepted, request.ModifiedDate, request.IsOnlyIQ3ModeEnabled);
                return new UserManagementResponse
                {
                    Acknowledge = lastUserId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    InvitationId = request.InvitationId,
                    UserId = lastUserId,
                    Message = string.Format("User has been saved successfully against invitation Id:{0} and Invitee Email:{1}.", request.InvitationId, request.User.UserID)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "AcceptInvitation", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "AcceptInvitation", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool RejectInvitation(int invitationId, DateTime modifiedDate, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "RejectInvitation", tenantId, "RejectInvitation has been exeuted successfully.  invitationId = " + invitationId));
                return new UserManagementDAL(tenantId).RejectInvitation(invitationId, InvitationStatus.Rejected, modifiedDate) > 0;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "RejectInvitation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "RejectInvitation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool CheckUniqueId(string email, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CheckUniqueId", tenantId, "CheckUniqueId has been exeuted successfully.  email = " + email));
                return new UserManagementDAL(tenantId).CheckUniqueId(email);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CheckUniqueId", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CheckUniqueId", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Custom Marker
        public UserManagementResponse AddCustomMarker(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "AddCustomMarker", umRequest.TenantId, "AddCustomMarker has been exeuted successfully. "));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).AddCustomMarker(umRequest.cmData) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "AddCustomMarker", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "AddCustomMarker", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion
        #region MonitorPlaye
        public UserManagementResponse GetLPSettingsInfo(int UserNum, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetLPSettingsInfo", tenantId, "GetLPSettingsInfo has been exeuted successfully. "));
                var users = new UserManagementDAL(tenantId).GetLPSettingsInfo(UserNum);
                return new UserManagementResponse { Users = users };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetLPSettingsInfo", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetLPSettingsInfo", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool saveMonitorSettings(int UId, bool multiChannel, bool continuousPlay, int tenantId)
        {
            try
            {
                bool saved = false;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "saveMonitorSettings", tenantId, "saveMonitorSettings has been exeuted successfully. uid = " + UId + " - multiChannel =  " + multiChannel + " - continuousPlay = " + continuousPlay));
                return saved = new UserManagementDAL(tenantId).saveMonitorSettings(UId, multiChannel, continuousPlay);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "saveMonitorSettings", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "saveMonitorSettings", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion
        public UserManagementResponse GetInviteeUser(UserManagementRequest userManagementRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetInviteeUser", userManagementRequest.TenantId, "GetInviteeUser has been exeuted successfully. userManagementRequest.UserId = " + userManagementRequest.UserId));
                var users = new UserManagementDAL(userManagementRequest.TenantId).GetInviteeUser(userManagementRequest.User.UserID);
                return new UserManagementResponse { Users = users };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetInviteeUser", userManagementRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetInviteeUser", userManagementRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse UpdateEmailPassword(UserManagementRequest userManagementRequest, string userid)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateEmailPassword", userManagementRequest.TenantId, "UpdateEmailPassword has been exeuted successfully. userId = " + userid));
                var users = new UserManagementDAL(userManagementRequest.TenantId).UpdateEmailPassword(userManagementRequest.User.UserID, userManagementRequest.User.UserPW, userid);
                return new UserManagementResponse { Users = users };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateEmailPassword", userManagementRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateEmailPassword", userManagementRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetAllUsers(UserManagementRequest userManagementRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAllUsers", userManagementRequest.TenantId, "GetAllUsers has been exeuted successfully."));

                int userType = 0;
                if (userManagementRequest.User != null)
                {
                    userType = userManagementRequest.User.UserType;
                }
                var users = new UserManagementDAL(userManagementRequest.TenantId).GetAllUsers(userType);
                return new UserManagementResponse { Users = users };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAllUsers", userManagementRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAllUsers", userManagementRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetAllUsersOthers(UserManagementRequest userManagementRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAllUsersOthers", userManagementRequest.TenantId, "GetAllUsersOthers has been exeuted successfully."));
                var users = new UserManagementDAL(userManagementRequest.TenantId).GetAllUsersOthers(userManagementRequest.UserId);
                return new UserManagementResponse { Users = users };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAllUsersOthers", userManagementRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAllUsersOthers", userManagementRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region Event Invitation
        public UserManagementResponse CreateEventSpecificUserAccount(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CreateEventSpecificUserAccount", umRequest.TenantId, "CreateEventSpecificUserAccount has been exeuted successfully."));
                return new UserManagementResponse { User = new UserManagementDAL(umRequest.TenantId).CreateEventSpecificUserAccount(umRequest.User), FlagStatus = true, };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CreateEventSpecificUserAccount", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CreateEventSpecificUserAccount", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse CreateEventSpecificSimpleUserAccount(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CreateEventSpecificSimpleUserAccount", umRequest.TenantId, "CreateEventSpecificSimpleUserAccount has been exeuted successfully."));
                return new UserManagementResponse { User = new UserManagementDAL(umRequest.TenantId).CreateEventSpecificSimpleUserAccount(umRequest.User), FlagStatus = true, };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CreateEventSpecificSimpleUserAccount", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CreateEventSpecificSimpleUserAccount", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse UpdateEventSpecificSimpleRights(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateEventSpecificSimpleRights", umRequest.TenantId, "UpdateEventSpecificSimpleRights has been exeuted successfully."));
                return new UserManagementResponse { User = new UserManagementDAL(umRequest.TenantId).UpdateEventSpecificSimpleRights(umRequest.User), FlagStatus = true, };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateEventSpecificSimpleRights", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateEventSpecificSimpleRights", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse AddEventInvitation(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "AddEventInvitation", umRequest.TenantId, "AddEventInvitation has been exeuted successfully."));
                return new UserManagementResponse { EventInvitation = new UserManagementDAL(umRequest.TenantId).AddEventInvitation(umRequest.EventInvitation) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "AddEventInvitation", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "AddEventInvitation", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse GetInvitedActiveEventIdsByUserNum(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetInvitedActiveEventIdsByUserNum", umRequest.TenantId, "GetInvitedActiveEventIdsByUserNum has been exeuted successfully."));
                return new UserManagementResponse { EventInvitation = new EventInvitation { ActiveInvitedEventIds = new UserManagementDAL(umRequest.TenantId).GetInvitedActiveEventIdsByUserNum(umRequest.EventInvitation.UserNum) } };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetInvitedActiveEventIdsByUserNum", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetInvitedActiveEventIdsByUserNum", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse GetUserByEmailId(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetUserByEmailId", umRequest.TenantId, "GetUserByEmailId has been exeuted successfully."));
                return new UserManagementResponse { User = new UserManagementDAL(umRequest.TenantId).GetUserByEmailId(umRequest.User.UserID) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetUserByEmailId", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetUserByEmailId", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool CheckValidEventSpecificUser(string email, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CheckValidEventSpecificUser", tenantId, "CheckValidEventSpecificUser has been exeuted successfully."));
                return new UserManagementDAL(tenantId).CheckValidEventSpecificUser(email);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CheckValidEventSpecificUser", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CheckValidEventSpecificUser", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool GrantLiveMonitorPermission(int userNum, string userPermissions, int extension, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GrantLiveMonitorPermission", tenantId, "GrantLiveMonitorPermission has been exeuted successfully."));
                return new UserManagementDAL(tenantId).GrantLiveMonitorPermission(userNum, userPermissions, extension);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GrantLiveMonitorPermission", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GrantLiveMonitorPermission", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool EnableUserAccount(int userNum, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "EnableUserAccount", tenantId, "EnableUserAccount has been exeuted successfully. userNum = " + userNum));
                return new UserManagementDAL(tenantId).EnableUserAccount(userNum);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "EnableUserAccount", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "EnableUserAccount", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public User EnableDisableInvitationPermission(int userNum, bool bInvitationEnabled, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "EnableDisableInvitationPermission", tenantId, "EnableDisableInvitationPermission has been exeuted successfully."));
                return new UserManagementDAL(tenantId).EnableDisableInvitationPermission(userNum, bInvitationEnabled);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "EnableDisableInvitationPermission", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "EnableDisableInvitationPermission", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool EnableDisableUsersetting(int userNum, bool bAutoUploadEnabled, bool bCustomAssetIdEnabled, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "EnableDisableUsersetting", tenantId, "EnableDisableUsersetting has been exeuted successfully."));
                return new UserManagementDAL(tenantId).EnableDisableUsersetting(userNum, bAutoUploadEnabled, bCustomAssetIdEnabled);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "EnableDisableUsersetting", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "EnableDisableUsersetting", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        public bool ValidateUserPassword(int userNum, string currentPassword, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "ValidateUserPassword", tenantId, "ValidateUserPassword has been exeuted successfully. userNum = " + userNum));
                return new UserManagementDAL(tenantId).ValidateUserPassword(userNum, currentPassword);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "ValidateUserPassword", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "ValidateUserPassword", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool ValidateUserPasswordByEmail(string email, string currentPassword, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "ValidateUserPasswordByEmail", tenantId, "ValidateUserPasswordByEmail has been exeuted successfully. email = " + email));
                return new UserManagementDAL(tenantId).ValidateUserPasswordByEmail(email, currentPassword);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "ValidateUserPasswordByEmail", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "ValidateUserPasswordByEmail", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public bool ValidateTempUserPassword(int userNum, string currentPassword, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "ValidateTempUserPassword", tenantId, "ValidateTempUserPassword has been exeuted successfully. userNum = " + userNum));
                return new UserManagementDAL(tenantId).ValidateTempUserPassword(userNum, currentPassword);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "ValidateTempUserPassword", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "ValidateTempUserPassword", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool UpdateUserPassword(int userNum, string newPassword, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateUserPassword", tenantId, "UpdateUserPassword has been exeuted successfully. userNum = " + userNum));
                return new UserManagementDAL(tenantId).UpdateUserPassword(userNum, newPassword);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateUserPassword", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateUserPassword", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool UpdateUserPasswordByEmail(string email, string newPassword, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "UpdateUserPasswordByEmail", tenantId, "UpdateUserPasswordByEmail has been exeuted successfully. email = " + email));
                return new UserManagementDAL(tenantId).UpdateUserPasswordByEmail(email, newPassword);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateUserPasswordByEmail", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateUserPasswordByEmail", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<string> GetPasswordHistoryByUser(int userId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetPasswordHistoryByUser", tenantId, "GetPasswordHistoryByUser has been exeuted successfully. userId = " + userId));
                return new UserManagementDAL(tenantId).GetPasswordHistoryByUser(userId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetPasswordHistoryByUser", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetPasswordHistoryByUser", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<string> GetPasswordHistoryByEmail(string email, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetPasswordHistoryByEmail", tenantId, "GetPasswordHistoryByEmail has been exeuted successfully. email = " + email));
                return new UserManagementDAL(tenantId).GetPasswordHistoryByEmail(email);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetPasswordHistoryByEmail", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetPasswordHistoryByEmail", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool LockUnlockUserAccount(string email, bool lockAccount, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "LockUnlockUserAccount", tenantId, "LockUnlockUserAccount has been exeuted successfully. email = " + email + " - lockAccount = " + lockAccount));
                return new UserManagementDAL(tenantId).LockUnlockUserAccount(email, lockAccount);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "LockUnlockUserAccount", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "LockUnlockUserAccount", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UserManagementResponse GetUsersAboutToExpirePassword(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetUsersAboutToExpirePassword", tenantId, "GetUsersAboutToExpirePassword has been exeuted successfully."));
                //return new UserManagementDAL(tenantId).GetUsersAboutToExpirePassword();
                var users = new UserManagementDAL(tenantId).GetUsersAboutToExpirePassword();
                List<User> aboutToExpirePwdUsers = null;
                foreach (var user in users)
                {
                    if (user.LastPasswordChanged.HasValue)
                    {
                        DateTime startDate = user.LastPasswordChanged.Value;
                        DateTime expiryDate = startDate.AddDays(_passwordExpiryInDays);

                        //var expiryDays = (expiryDate - startDate).TotalDays;
                        var expiryDays = expiryDate.Date.Subtract(DateTime.Now.Date).Days;

                        if (expiryDays == _passwordReminderInDays)
                        {
                            if (aboutToExpirePwdUsers == null) aboutToExpirePwdUsers = new List<User>();
                            aboutToExpirePwdUsers.Add(user);
                        }
                    }

                }

                return new UserManagementResponse { Users = aboutToExpirePwdUsers };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetUsersAboutToExpirePassword", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetUsersAboutToExpirePassword", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public bool UpdateUserTempPassword(int userNum, string newPassword, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetUsersAboutToExpirePassword", tenantId, "GetUsersAboutToExpirePassword has been exeuted successfully. userNum = " + userNum));
                return new UserManagementDAL(tenantId).UpdateUserTempPassword(userNum, newPassword);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "UpdateUserTempPassword", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "UpdateUserTempPassword", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool ResetUserPasswordToTemp(string userID, string tempPassword, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "ResetUserPasswordToTemp", tenantId, "ResetUserPasswordToTemp has been exeuted successfully. userID = " + userID));
                return new UserManagementDAL(tenantId).ResetUserPasswordToTemp(userID, tempPassword);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "ResetUserPasswordToTemp", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "ResetUserPasswordToTemp", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool IsRevcellEnable(string email, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "IsRevcellEnable", tenantId, "IsRevcellEnable has been exeuted successfully. email = " + email));
                return new UserManagementDAL(tenantId).IsRevcellEnable(email);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "IsRevcellEnable", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "IsRevcellEnable", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetAppUserAccess(string userEmail, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAppUserAccess", tenantId, "GetAppUserAccess has been exeuted successfully. userEmail = " + userEmail));
                return new UserManagementResponse { AppUserAccess = new UserManagementDAL(tenantId).GetAppUserAccess(userEmail) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAppUserAccess", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAppUserAccess", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool CopyMarkers(int copyTo, int copyOf, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CopyMarkers", tenantId, "CopyMarkers has been exeuted successfully. copyOf = " + copyOf + " copyTo = " + copyTo));
                return new UserManagementDAL(tenantId).CopyMarkers(copyTo, copyOf);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CopyMarkers", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CopyMarkers", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse FetchAllActiveUsersFromRecorder(Recorder recorder, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "FetchAllActiveUsersFromRecorder", tenantId, "FetchAllActiveUsers has been exeuted successfully."));
                if (recorder.IsPrimary)
                {
                    return new UserManagementResponse { Users = new UserManagementDAL(tenantId).FetchAllActiveUsers() };
                }
                else
                {
                    // Alternative Approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return new UserManagementResponse { Users = entClient.FetchAllActiveUsers().ToList() };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "FetchAllActiveUsersFromRecorder", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "FetchAllActiveUsersFromRecorder", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int UpdateCompactViewStatus(int userNum, bool isCompactView, int tenantId)
        {
            try
            {
                var rowsAffected = new UserManagementDAL(tenantId).UpdateCompactViewStatus(userNum, isCompactView);
                if (rowsAffected > 0)
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "UpdateCompactViewStatus", tenantId, "Compact View status has been updated successfully. UserNum = " + userNum + " IsCompactView = " + isCompactView));
                return rowsAffected;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "UpdateCompactViewStatus", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "UpdateCompactViewStatus", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool CopyTenantUserMarkers(int copyTo, int copyOf, int sourceTenantId, int destinationTenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "CopyTenantUserMarkers", destinationTenantId, "CopyTenantUserMarkers has been exeuted successfully. copyOf = " + copyOf + " copyTo = " + copyTo));
                return new UserManagementDAL(destinationTenantId).CopyTenantUserMarkers(copyTo, copyOf, sourceTenantId, destinationTenantId);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "CopyMarkers", destinationTenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "CopyMarkers", destinationTenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse AcceptLicenseAgreement(UserManagementRequest umRequest)
        {
            try
            {
                var res = new UserManagementDAL(umRequest.TenantId).AcceptLicenseAgreement(umRequest.UserLicenseAgreement.UserNum, umRequest.UserLicenseAgreement.UserName, umRequest.UserLicenseAgreement.UserEmail, umRequest.UserLicenseAgreement.IsLicenseAccepted, umRequest.UserLicenseAgreement.CreatedDate);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "AcceptLicenseAgreement", umRequest.TenantId, "GetUserExtensionInfos has been exeuted successfully"));
                return new UserManagementResponse { FlagStatus = res };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "AcceptLicenseAgreement", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "AcceptLicenseAgreement", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool AuthenticateUser(string email, string password, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "AuthenticateUser", tenantId, "AuthenticateUser has been exeuted successfully. "));

                return new UserManagementDAL(tenantId).AuthenticateUser(email, password);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "AuthenticateUser", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "AuthenticateUser", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetUserInformation(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetUserInformation", umRequest.TenantId, "GetMyInformation has been exeuted successfully. "));

                List<Recorder> recorders = null;
                bool isChainDBConfig = false;
                bool isInquireEnabled = false;
                bool isECEnabled = false;
                bool isEnterpriseRecorder = false;
                List<int> sttEnabledChannels = null;
                bool isUserLocked = false;
                bool isUserExists = false;
                bool isUserAgreedToLicense = false;
                return new UserManagementResponse
                {
                    LoginUser = new UserManagementDAL(umRequest.TenantId).GetUserInformation(umRequest.User.UserID, umRequest.User.IsADUser, umRequest.TenantId, umRequest.ForcePasswordChange, umRequest.IsRoleBasedAccessEnabled, out recorders, out isChainDBConfig, out isInquireEnabled, out isECEnabled, out isEnterpriseRecorder, out sttEnabledChannels, out isUserLocked, out isUserExists, out isUserAgreedToLicense),
                    Recorders = recorders,
                    IsChainDBsConfigured = isChainDBConfig,
                    IsInquire = isInquireEnabled,
                    IsECEnabled = isECEnabled,
                    IsEnterpriseRecorder = isEnterpriseRecorder,
                    STTEnabledChannels = sttEnabledChannels,
                    IsUserLocked = isUserLocked,
                    IsUserExists = isUserExists,
                    IsUserAgreedToLicense = isUserAgreedToLicense,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetUserInformation", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetUserInformation", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UserManagementResponse GetAvailableAutoReportRecipients(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "GetAvailableAutoReportRecipients", tenantId, "GetAvailableAutoReportRecipients function has been called successfully. "));
                return new UserManagementResponse { Users = new UserManagementDAL(tenantId).GetAvailableAutoReportRecipients() };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "GetAvailableAutoReportRecipients", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "GetAvailableAutoReportRecipients", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region IWB

        public UserManagementResponse IwbAddExtension(UserManagementRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManagement, "EnableDisableIwbUser", umRequest.TenantId, "EnableDisableIwbUser() has been exeuted successfully. "));
                return new UserManagementResponse { FlagStatus = new UserManagementDAL(umRequest.TenantId).IwbAddExtension(umRequest.User) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManagement, "EnableDisableIwbUser", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManagement, "EnableDisableIwbUser", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion
    }
}