﻿using RevCord.DataContracts.IQ3InspectionEntities;
using RevCord.DataContracts.MessageBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class InspectionRequest: RequestBase
    {
        public int TenantId { get; set; }
        public int TargetTenantId { get; set; }
        public int UserId { get; set; }
        public int InspectionTemplateId { get; set; }
        public int TemplateRevSyncServerID { get; set; }
        public string SelectedUsers { get; set; }
        public InspectionTemplate InspectionTemplate { get; set; }
        public List<PreInspection> PreInspections { get; set; }
        public Marker Marker { get; set; }
        public Section Section { get; set; }

        public PersistType PersistType { get; set; }

        public string AssignedMarkers { get; set; }
        public string UnassignedMarkers { get; set; }
        public int SectionId { get; set; }
        public int TemplateType { get; set; }
        public int DisableCopyMarker { get; set; }

        public string EventId { get; set; }
        public int MarkerId { get; set; }
        public int PreInspectionId { get; set; }
        public InspectionTitle _InspectionTitle { get; set; }
        public List<MarkerSection> MarkerSections { get; set; }
        public int GraphicMarkerId { get; set; }
        public int PlaylistId { get; set; }

        public bool ViewOnly { get; set; }

        public RVIMessage RVIMessage { get; set; }

        public IQ3InspectionParameterType InspectionParameterType { get; set; }
        public string StartDateTime { get; set; }
        public string EndDateTime { get; set; }

        public string SearchText { get; set; }
        public int CustomFieldId { get; set; }
        public string CustomFieldIds { get; set; }
        public PreInspection CustomField { get; set; }

        public int GroupId { get; set; }
        public PreInspectionGroup PreInspectionGroup { get; set; }
        public bool IncludePreInspections { get; set; }

        public AutoReportRecipient AutoReportRecipient { get; set; }
        public List<AutoReportRecipient> AutoReportRecipients { get; set; }

        public bool IsCommon { get; set; }
        public bool OnlyDefaultTemplates { get; set; }

    }
}
