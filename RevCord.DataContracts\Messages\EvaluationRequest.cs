﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.MessageBase;

namespace RevCord.DataContracts.Request
{
    public class EvaluationRequest : RequestBase
    {
        public int UserId { get; set; }
        public string CallEvaluationIds { get; set; }

        public string RevSyncCallEvaluationIds { get; set; }
        public string CallId { get; set; }
        public string CallIds { get; set; }
        public int AgentId { get; set; }
        public int EvaluationType { get; set; }
        public int MultiCallEvaluationId { get; set; }

        /************* Purpose: used for Paging ****************/
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        //public int TotalPages { get; set; }
        /************* Purpose: used for Paging ****************/
        public bool SharedRequired { get; set; }
        public int SurveyId { get; set; }
        public long CallEvaluationId { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime? ActionPerformedDate { get; set; }


        public List<EvaluationStatus> EvalStatuses { get; set; }
        public EvaluationStatus EvalStatus { get; set; }
        public EvalSearchCriteriaDTO SearchCriteria { get; set; }
        public EvalActionType ActionToPerform { get; set; }

        public String ShareWith { get; set; }
        public bool IsSharedEvaluatorRetains { get; set; }

        public string Comments { get; set; }

        public bool IsSegmented { get; set; }
        public string SegmentCallIds { get; set; }
        public int GroupId { get; set; }


        #region User Evaluation

        public string UserIds { get; set; }
        public int EvaluatorId { get; set; }
        public string EvaluationIds { get; set; }
        public char ControlState { get; set; }

        #endregion

        #region Evaluation EC
        public List<Recorder> Recorders { get; set; }
        public Recorder Recorder { get; set; }
        //public bool IsEnterpriseEvaluator { get; set; }
        public int ActionToPerformOnRecorder { get; set; }
        #endregion
    }
}