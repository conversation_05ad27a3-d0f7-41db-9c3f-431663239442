﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.ReportEntities
{
    public class RPTPBX911Call
    {
        public string Key { get; set; }
        public int Id { get; set; }
        public string CallId { get; set; }
        public CallStatus CallStatusId { get; set; }
        public int NoOfRings { get; set; }
        public int? TransferFromExt { get; set; }
        public int? TransferToExt { get; set; }
        public string TransferredBy { get; set; }
        public string TransferredTo { get; set; }
        public string RingTime { get; set; }
        public string CallDuration { get; set; }
        public string HoldTime { get; set; }
    }

    public class PBX911Data
    {
        public bool RingCount { get; set; }
        public bool Abandoned { get; set; }
        public bool Transferred { get; set; }
    }

    public class PBX911SampleData
    {
        public static List<RPTPBX911Call> CallList = new List<RPTPBX911Call>()
        {
            new RPTPBX911Call()
            {
                Key = "1001",
                Id = 1,
                CallId = "tempCallId",
                CallStatusId = CallStatus.Attended,
                NoOfRings = 1,
                RingTime = "00:00:04",
                CallDuration = "01:00:06",
            },
            new RPTPBX911Call()
            {
                Key = "1001",
                Id = 2,
                CallId = "tempCallId",
                CallStatusId = CallStatus.Attended,
                NoOfRings = 1,
                RingTime = "00:00:04",
                CallDuration = "01:00:06",
            },
            new RPTPBX911Call()
            {
                Key = "1001",
                Id = 3,
                CallId = "tempCallId",
                CallStatusId = CallStatus.Attended,
                NoOfRings = 1,
                RingTime = "00:00:04",
                CallDuration = "01:00:06",
            },
            new RPTPBX911Call()
            {
                Key = "1001",
                Id = 4,
                CallId = "tempCallId",
                CallStatusId = CallStatus.Attended,
                NoOfRings = 1,
                RingTime = "00:00:04",
                CallDuration = "01:00:06",
            },
        };
    }
}
