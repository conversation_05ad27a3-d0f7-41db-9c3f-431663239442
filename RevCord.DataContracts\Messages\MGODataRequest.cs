﻿using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.MGODataEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class MGODataRequest : RequestBase
    {
        public int Id { get; set; }
        public string EventId { get; set; }

        public MGOTempData TempData { get; set; }
        public MGOReportData ReportData { get; set; }
    }
}
