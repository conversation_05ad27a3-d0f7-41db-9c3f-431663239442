﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.InquireEntities;
using RevCord.DataContracts.Criteria;

namespace RevCord.DataContracts.Messages
{
    public class InquireRequest : RequestBase
    {
        public int InvitationId { get; set; }

        public InvitationStatus InvitationStatus { get; set; }

        public Invitation Invitation { get; set; }

        public InquireCriteria Criteria { get; set; }

        public RegisterUser RegisterUser { get; set; }

        public EventInvitationGroup EventInvitationGroup { get; set; }

        public DateTime? AcceptedDate { get; set; }


    }
}