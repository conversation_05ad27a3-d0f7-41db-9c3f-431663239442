<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="RevLogSvc" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://localhost:27336/RevLogSvc.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://localhost:27336/RevLogSvc.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://localhost:27336/RevLogSvc.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IRevLogSvc_AddSuccessAuditLog_InputMessage">
    <wsdl:part name="parameters" element="tns:AddSuccessAuditLog" />
  </wsdl:message>
  <wsdl:message name="IRevLogSvc_AddSuccessAuditLog_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddSuccessAuditLogResponse" />
  </wsdl:message>
  <wsdl:message name="IRevLogSvc_AddErrorAuditLog_InputMessage">
    <wsdl:part name="parameters" element="tns:AddErrorAuditLog" />
  </wsdl:message>
  <wsdl:message name="IRevLogSvc_AddErrorAuditLog_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddErrorAuditLogResponse" />
  </wsdl:message>
  <wsdl:message name="IRevLogSvc_AddExceptionAuditLog_InputMessage">
    <wsdl:part name="parameters" element="tns:AddExceptionAuditLog" />
  </wsdl:message>
  <wsdl:message name="IRevLogSvc_AddExceptionAuditLog_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddExceptionAuditLogResponse" />
  </wsdl:message>
  <wsdl:message name="IRevLogSvc_AddSQLExceptionAuditLog_InputMessage">
    <wsdl:part name="parameters" element="tns:AddSQLExceptionAuditLog" />
  </wsdl:message>
  <wsdl:message name="IRevLogSvc_AddSQLExceptionAuditLog_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddSQLExceptionAuditLogResponse" />
  </wsdl:message>
  <wsdl:portType name="IRevLogSvc">
    <wsdl:operation name="AddSuccessAuditLog">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevLogSvc/AddSuccessAuditLog" message="tns:IRevLogSvc_AddSuccessAuditLog_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevLogSvc/AddSuccessAuditLogResponse" message="tns:IRevLogSvc_AddSuccessAuditLog_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddErrorAuditLog">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevLogSvc/AddErrorAuditLog" message="tns:IRevLogSvc_AddErrorAuditLog_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevLogSvc/AddErrorAuditLogResponse" message="tns:IRevLogSvc_AddErrorAuditLog_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddExceptionAuditLog">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevLogSvc/AddExceptionAuditLog" message="tns:IRevLogSvc_AddExceptionAuditLog_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevLogSvc/AddExceptionAuditLogResponse" message="tns:IRevLogSvc_AddExceptionAuditLog_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddSQLExceptionAuditLog">
      <wsdl:input wsaw:Action="http://tempuri.org/IRevLogSvc/AddSQLExceptionAuditLog" message="tns:IRevLogSvc_AddSQLExceptionAuditLog_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IRevLogSvc/AddSQLExceptionAuditLogResponse" message="tns:IRevLogSvc_AddSQLExceptionAuditLog_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IRevLogSvc" type="tns:IRevLogSvc">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="AddSuccessAuditLog">
      <soap:operation soapAction="http://tempuri.org/IRevLogSvc/AddSuccessAuditLog" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddErrorAuditLog">
      <soap:operation soapAction="http://tempuri.org/IRevLogSvc/AddErrorAuditLog" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddExceptionAuditLog">
      <soap:operation soapAction="http://tempuri.org/IRevLogSvc/AddExceptionAuditLog" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddSQLExceptionAuditLog">
      <soap:operation soapAction="http://tempuri.org/IRevLogSvc/AddSQLExceptionAuditLog" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="RevLogSvc">
    <wsdl:port name="BasicHttpBinding_IRevLogSvc" binding="tns:BasicHttpBinding_IRevLogSvc">
      <soap:address location="http://localhost:27336/RevLogSvc.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>