﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.AuditEntities
{
    public class Audit
    {
        public int RowNo { get; set; }
        public int Id { get; set; }
        public int AppId { get; set; }
        public int UserId { get; set; }
        public int LogCategoryId { get; set; }
        public string LogCategory { get; set; }
        public string Originator { get; set; }
        public string FunctionName { get; set; }
        public string Query { get; set; }
        public string Message { get; set; }
        public int ExceptionCode { get; set; }
        public string StackTrace { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }
    }
}
