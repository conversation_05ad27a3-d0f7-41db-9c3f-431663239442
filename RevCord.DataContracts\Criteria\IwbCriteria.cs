﻿using RevCord.DataContracts.IWBEntities;
using System;
using System.Collections.Generic;

namespace RevCord.DataContracts.Criteria
{
    public class IwbCriteria
    {
        public int CreatedBy { get; set; }

        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string JobName { get; set; }
        public string JobWps { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public int WelderId { get; set; }
        public int OrganizationId { get; set; }
        public int OrganizationTypeId { get; set; }

        public List<IwbJobStatus> JobStatuses { get; set; }

        public string Name { get; set; }
        public string StencilNumber { get; set; }
        public List<IwbDocumentType> DocumentTypes { get; set; }
        public int DocumentTypeId { get; set; }
        public string Alias { get; set; }
        public string TypeCode { get; set; }
        public List<IwbTestStatus> TestStatuses { get; set; }

    }
}