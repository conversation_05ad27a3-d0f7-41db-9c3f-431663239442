﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Criteria
{
    public class InvitationCriteria
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public int SentByUserId { get; set; }
        
        public string InvitationCode { get; set; }

        public List<InvitationStatus> InvitationStatuses { get; set; }

        public Boolean IsDeviceUser { get; set; }
    }
}
