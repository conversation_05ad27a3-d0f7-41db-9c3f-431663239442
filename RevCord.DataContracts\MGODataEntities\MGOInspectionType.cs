﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.MGODataEntities
{
    public class MGOInspectionTypeMap
    {
        [JsonProperty("id")]
        public int ID { get; set; }

        [JsonProperty("inspectionTypeId")]
        public int InspectionTypeID { get; set; }
    }

    public class MGOInspectionType
    {
        [<PERSON>son<PERSON>roperty("inspectionTypeID")]
        public int InspectionTypeID { get; set; }

        [JsonProperty("inspectionType")]
        public string InspectionType { get; set; }

        [JsonProperty("categoryID")]
        public int CategoryID { get; set; }

        [JsonProperty("category")]
        public string Category { get; set; }

        [JsonProperty("projectTypeID")]
        public int ProjectTypeID { get; set; }
    }

    public class MGOInspectionOption
    {
        [JsonProperty("id")]
        public int ID { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("projectTypeID")]
        public int ProjectTypeID { get; set; }
    }

    public class MGOInspection
    {
        public List<MGOInspectionType> inspections { get; set; }
        public List<MGOInspectionOption> inspectionOptions { get; set; }
    }

    public class MGOInspectionTypeResponse
    {
        public MGOInspection data { get; set; }
        public string message { get; set; }
        public string status { get; set; }
    }
}
