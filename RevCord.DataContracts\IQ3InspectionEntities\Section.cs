﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3InspectionEntities
{
    public class Section
    {
        public int Id { get; set; }
        public int InspectionTemplateId { get; set; }
        public string Title { get; set; }
        public SectionType SectionType { get; set; }
        public SectionCategory SectionCategory { get; set; }
        public bool IsDefaultSection { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }
        public bool AllowCopySection { get; set; }
        public int NoOfMarkers { get; set; }

        public List<Marker> Markers { get; set; }
    }
}
