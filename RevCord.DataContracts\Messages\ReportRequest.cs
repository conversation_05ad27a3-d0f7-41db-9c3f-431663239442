﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.ReportEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.MessageBase;

namespace RevCord.DataContracts.Request
{
    public class ReportRequest : RequestBase
    {
        public ReportType ReportType { get; set; }
        public ReportSubType ReportSubType { get; set; }
        public StandardReportSubType StandardReportSubType { get; set; }

        public bool GetRingTime { get; set; }
        public bool GetAbandonedCount { get; set; }
        public bool GetTransferredCallCount { get; set; }

        public RPTQueryParameter QueryParameter { get; set; }

        public XAxisData XAxisData { get; set; }

        public GroupData GroupData { get; set; }

        public YAxisData YAxisData { get; set; }

        public bool PopulateEmptyDate { get; set; }

        public bool GetDetailReportData { get; set; }

        public bool IsEvalReportGroup { get; set; }

        public bool IsCustomReportDetails { get; set; }

        public string CustomReportSearchCriteria { get; set; }

        public int CustomReportColumnIndex { get; set; }

        public int UserType { get; set; }
        public int RecorderId { get; set; }

        public Recorder Recorder { get; set; }
        public List<Recorder> Recorders { get; set; }

        public ReportCriteria Criteria { get; set; }
        public CallAuditCriteria CallAuditCriteria { get; set; }
        public SavedReport SavedReport { get; set; }
        public bool IsStandardReport { get; set; }
        public bool IsSavedReport { get; set; }

        public List<int> Ids { get; set; }
        public SavedReportCriteria SavedReportCriteria { get; set; }

        public SharedReportInfo SharedReportInfo { get; set; }
        public int SharedReportInfoId { get; set; }
        public int ReportId { get; set; }

        public PeriodicDateType PeriodicDateType { get; set; }
        public bool IsOnlyIQ3ModeEnabled { get; set; }
        public bool IsCustomDataSearch { get; set; }
        public CustomSearchType CustomSearchType { get; set; }
        public string JOINString { get; set; }
    }
}