﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.AspNet.SignalR.Core</name>
  </assembly>
  <members>
    <member name="T:Microsoft.AspNet.SignalR.AuthorizeAttribute">
      <summary>Apply to Hubs and Hub methods to authorize client connections to Hubs and authorize client invocations of Hub methods.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.AuthorizeAttribute.#ctor"></member>
    <member name="F:Microsoft.AspNet.SignalR.AuthorizeAttribute._requireOutgoing"></member>
    <member name="M:Microsoft.AspNet.SignalR.AuthorizeAttribute.AuthorizeHubConnection(Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest)">
      <summary>Determines whether client is authorized to connect to <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" />.</summary>
      <returns>true if the caller is authorized to connect to the hub; otherwise, false.</returns>
      <param name="hubDescriptor">Description of the hub client is attempting to connect to.</param>
      <param name="request">The (re)connect request from the client.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.AuthorizeAttribute.AuthorizeHubMethodInvocation(Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext,System.Boolean)">
      <summary>Determines whether client is authorized to invoke the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> method.</summary>
      <returns>true if the caller is authorized to invoke the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> method; otherwise, false.</returns>
      <param name="hubIncomingInvokerContext">An <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext" /> providing details regarding the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> method invocation.</param>
      <param name="appliesToMethod">Indicates whether the interface instance is an attribute applied directly to a method.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.AuthorizeAttribute.RequireOutgoing">
      <summary>Set to false to apply authorization only to the invocations of any of the Hub's server-side methods. This property only affects attributes applied to the Hub class. This property cannot be read.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.AuthorizeAttribute.Roles">
      <summary>Gets or sets the user roles.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.AuthorizeAttribute.UserAuthorized(System.Security.Principal.IPrincipal)">
      <summary>When overridden, provides an entry point for custom authorization checks.            Called by <see cref="M:Microsoft.AspNet.SignalR.AuthorizeAttribute.AuthorizeHubConnection(Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest)" /> and <see cref="M:Microsoft.AspNet.SignalR.AuthorizeAttribute.AuthorizeHubMethodInvocation(Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext,System.Boolean)" />.</summary>
      <returns>true if the user is authorized, otherwise, false.</returns>
      <param name="user">The <see cref="T:System.Security.Principal.IPrincipal" /> for the client being authorized.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.AuthorizeAttribute.Users">
      <summary>Gets or sets the authorized users.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.ConnectionConfiguration"></member>
    <member name="M:Microsoft.AspNet.SignalR.ConnectionConfiguration.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.ConnectionConfiguration.EnableCrossDomain">
      <summary>  Determines if browsers can make cross domain requests to SignalR endpoints.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.ConnectionConfiguration.Resolver">
      <summary>The dependency resolver to use for the hub connection.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.IDependencyResolver" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.ConnectionExtensions"></member>
    <member name="M:Microsoft.AspNet.SignalR.ConnectionExtensions.Broadcast(Microsoft.AspNet.SignalR.IConnection,System.Object,System.String[])">
      <summary>Broadcasts a value to all connections, excluding the connection ids specified.</summary>
      <returns>A task that represents when the broadcast is complete.</returns>
      <param name="connection">The connection</param>
      <param name="value">The value to broadcast.</param>
      <param name="excludeConnectionIds">The list of connection ids to exclude</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.ConnectionExtensions.Send(Microsoft.AspNet.SignalR.IConnection,System.String,System.Object,System.String[])">
      <summary>Sends a message to all connections subscribed to the specified signal. An example of signal may be a specific connection id, or fully qualified group name (Use <see cref="T:Microsoft.AspNet.SignalR.IGroupManager" /> to manipulate groups).</summary>
      <returns>A task that represents when the broadcast is complete.</returns>
      <param name="connection">The connection</param>
      <param name="connectionId">The signal to send to.</param>
      <param name="value">value to publish.</param>
      <param name="excludeConnectionIds">The list of connection ids to exclude</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.ConnectionMessage">
      <summary>A message sent to one or more connections.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.ConnectionMessage.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AspNet.SignalR.ConnectionMessage" /> class.</summary>
      <param name="signal">The signal.</param>
      <param name="value">The payload of the message.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.ConnectionMessage.#ctor(System.String,System.Object,System.Collections.Generic.IList{System.String})">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AspNet.SignalR.ConnectionMessage" /> class.</summary>
      <param name="signal">The signal.</param>
      <param name="value">The payload of the message.</param>
      <param name="excludedSignals">The signals to exclude.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.ConnectionMessage.ExcludedSignals">
      <summary>Represents a list of signals that should be used to filter what connections receive this message.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.IList`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.ConnectionMessage.Signal">
      <summary>The signal to this message should be sent to. Connections subscribed to this signal will receive the message payload.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.ConnectionMessage.Value">
      <summary>The payload of the message.</summary>
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Cookie"></member>
    <member name="M:Microsoft.AspNet.SignalR.Cookie.#ctor(System.String,System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Cookie.#ctor(System.String,System.String,System.String,System.String)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Cookie.Domain">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Cookie.Name">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Cookie.Path">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Cookie.Value">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.DefaultDependencyResolver"></member>
    <member name="M:Microsoft.AspNet.SignalR.DefaultDependencyResolver.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.DefaultDependencyResolver.Dispose"></member>
    <member name="M:Microsoft.AspNet.SignalR.DefaultDependencyResolver.Dispose(System.Boolean)"></member>
    <member name="M:Microsoft.AspNet.SignalR.DefaultDependencyResolver.GetService(System.Type)">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.DefaultDependencyResolver.GetServices(System.Type)">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.DefaultDependencyResolver.Register(System.Type,System.Collections.Generic.IEnumerable{System.Func{System.Object}})"></member>
    <member name="M:Microsoft.AspNet.SignalR.DefaultDependencyResolver.Register(System.Type,System.Func{System.Object})"></member>
    <member name="T:Microsoft.AspNet.SignalR.DependencyResolverExtensions"></member>
    <member name="M:Microsoft.AspNet.SignalR.DependencyResolverExtensions.Resolve``1(Microsoft.AspNet.SignalR.IDependencyResolver)">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.DependencyResolverExtensions.Resolve(Microsoft.AspNet.SignalR.IDependencyResolver,System.Type)"></member>
    <member name="M:Microsoft.AspNet.SignalR.DependencyResolverExtensions.ResolveAll``1(Microsoft.AspNet.SignalR.IDependencyResolver)">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.DependencyResolverExtensions.ResolveAll(Microsoft.AspNet.SignalR.IDependencyResolver,System.Type)"></member>
    <member name="T:Microsoft.AspNet.SignalR.GlobalHost">
      <summary>Provides access to default host information.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.GlobalHost.Configuration">
      <summary>Gets the default <see cref="T:Microsoft.AspNet.SignalR.Configuration.IConfigurationManager" /></summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Configuration.IConfigurationManager" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.GlobalHost.ConnectionManager">
      <summary>Gets the default <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IConnectionManager" /></summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IConnectionManager" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.GlobalHost.DependencyResolver">
      <summary>Gets or sets the default <see cref="T:Microsoft.AspNet.SignalR.IDependencyResolver" />.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.IDependencyResolver" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.GlobalHost.HubPipeline">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.GroupManager">
      <summary>Manages groups for a connection.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.GroupManager.#ctor(Microsoft.AspNet.SignalR.IConnection,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AspNet.SignalR.GroupManager" /> class.</summary>
      <param name="connection">The <see cref="T:Microsoft.AspNet.SignalR.IConnection" /> this group resides on.</param>
      <param name="groupPrefix">The prefix for this group. Either a <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> name or <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> type name.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.GroupManager.Add(System.String,System.String)">
      <summary>Adds a connection to the specified group.</summary>
      <returns>A task that represents the connection id being added to the group.</returns>
      <param name="connectionId">The connection id to add to the group.</param>
      <param name="groupName">The name of the group</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.GroupManager.Remove(System.String,System.String)">
      <summary>Removes a connection from the specified group.</summary>
      <returns>A task that represents the connection id being removed from the group.</returns>
      <param name="connectionId">The connection id to remove from the group.</param>
      <param name="groupName">The name of the group</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.GroupManager.Send(System.String,System.Object,System.String[])">
      <summary>Sends a value to the specified group.</summary>
      <returns>A task that represents when send is complete.</returns>
      <param name="groupName">The name of the group.</param>
      <param name="value">The value to send.</param>
      <param name="excludeConnectionIds">The list of connection ids to exclude.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hub">
      <summary>Provides methods that communicate with SignalR connections that connected to a <see cref="T:Microsoft.AspNet.SignalR.Hub" />.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hub.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hub.Clients">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hub.Context">
      <summary>Provides information about the calling client.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubCallerContext" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hub.Dispose"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hub.Dispose(System.Boolean)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hub.Groups">
      <summary>The group manager for this hub instance.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.IGroupManager" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hub.OnConnected">
      <summary>Called when the connection connects to this hub instance.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hub.OnDisconnected">
      <summary>Called when a connection disconnects from this hub instance.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hub.OnReconnected">
      <summary>Called when the connection reconnects to this hub instance.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.HubConfiguration"></member>
    <member name="M:Microsoft.AspNet.SignalR.HubConfiguration.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.HubConfiguration.EnableDetailedErrors">
      <summary>Determines whether detailed exceptions thrown in Hub methods get reported back the invoking client. Defaults to false.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.HubConfiguration.EnableJavaScriptProxies">
      <summary>Determines whether JavaScript proxies for the server-side hubs should be auto generated at {Path}/hubs. Defaults to true.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.HubPipelineExtensions"></member>
    <member name="M:Microsoft.AspNet.SignalR.HubPipelineExtensions.RequireAuthentication(Microsoft.AspNet.SignalR.Hubs.IHubPipeline)">
      <summary>Requiring Authentication adds an <see cref="T:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule" /> to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> with <see cref="T:Microsoft.AspNet.SignalR.Hubs.IAuthorizeHubConnection" /> and <see cref="T:Microsoft.AspNet.SignalR.Hubs.IAuthorizeHubMethodInvocation" /> authorizers that will be applied globally to all hubs and hub methods. These authorizers require that the <see cref="T:System.Security.Principal.IPrincipal" />'s <see cref="T:System.Security.Principal.IIdentity" /> IsAuthenticated for any clients that invoke server-side hub methods or receive client-side hub method invocations.</summary>
      <param name="pipeline">The <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> to which the <see cref="T:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule" /> will be added.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.IConnection">
      <summary>A communication channel for a <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> and its connections.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IConnection.DefaultSignal">
      <summary>The main signal for this connection. This is the main signalr for a <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> .</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.IConnection.Send(Microsoft.AspNet.SignalR.ConnectionMessage)">
      <summary>Sends a message to connections subscribed to the signal.</summary>
      <returns>A task that returns when the message has be sent.</returns>
      <param name="message">The message to send.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.IConnectionGroupManager">
      <summary>Manages groups for a connection and allows sending messages to the group.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.IConnectionGroupManager.Send(System.String,System.Object,System.String[])">
      <summary>Sends a value to the specified group.</summary>
      <returns>A task that represents when send is complete.</returns>
      <param name="groupName">The name of the group.</param>
      <param name="value">The value to send.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.IDependencyResolver"></member>
    <member name="M:Microsoft.AspNet.SignalR.IDependencyResolver.GetService(System.Type)">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.IDependencyResolver.GetServices(System.Type)">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.IDependencyResolver.Register(System.Type,System.Collections.Generic.IEnumerable{System.Func{System.Object}})"></member>
    <member name="M:Microsoft.AspNet.SignalR.IDependencyResolver.Register(System.Type,System.Func{System.Object})"></member>
    <member name="T:Microsoft.AspNet.SignalR.IGroupManager">
      <summary>Manages groups for a connection.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.IGroupManager.Add(System.String,System.String)">
      <summary>Adds a connection to the specified group.</summary>
      <returns>A task that represents the connection id being added to the group.</returns>
      <param name="connectionId">The connection id to add to the group.</param>
      <param name="groupName">The name of the group</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.IGroupManager.Remove(System.String,System.String)">
      <summary>Removes a connection from the specified group.</summary>
      <returns>A task that represents the connection id being removed from the group.</returns>
      <param name="connectionId">The connection id to remove from the group.</param>
      <param name="groupName">The name of the group</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.IHubContext">
      <summary>Provides access to information about a <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" />.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IHubContext.Clients">
      <summary>Encapsulates all information about a SignalR connection for an <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" />.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubConnectionContext" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IHubContext.Groups">
      <summary>Gets the <see cref="T:Microsoft.AspNet.SignalR.IGroupManager" /> the hub.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.IGroupManager" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.IPersistentConnectionContext">
      <summary>Provides access to information about a <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IPersistentConnectionContext.Connection">
      <summary>Gets the <see cref="T:Microsoft.AspNet.SignalR.IConnection" /> for the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> .</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IPersistentConnectionContext.Groups">
      <summary>Gets the <see cref="T:Microsoft.AspNet.SignalR.IConnectionGroupManager" /> for the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> .</summary>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.IRequest">
      <summary>Represents a SignalR request</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IRequest.Cookies">
      <summary>Gets the cookies for this request.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IRequest.Form">
      <summary>Gets the form for this request.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IRequest.Headers">
      <summary>Gets the headers for this request.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IRequest.Items">
      <summary>Gets state for the current HTTP request.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IRequest.QueryString">
      <summary>Gets the query string for this request.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IRequest.Url">
      <summary>Gets the url for this request.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.IRequest.User">
      <summary>Gets security information for the current HTTP request.</summary>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.PersistentConnection">
      <summary>Represents a connection between client and server.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.PersistentConnection.AckHandler">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IAckHandler" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.Authorize(Microsoft.AspNet.SignalR.IRequest)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
      <param name="request">The <see cref="T:Microsoft.AspNet.SignalR.IRequest" /> for the current connection.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.AuthorizeRequest(Microsoft.AspNet.SignalR.IRequest)">
      <summary>Called before every request and gives a user authorization to access the request.</summary>
      <returns>A Boolean value that represents if the request is authorized.</returns>
      <param name="request">The <see cref="T:Microsoft.AspNet.SignalR.IRequest" /> for the current connection.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.PersistentConnection.Connection">
      <summary>Gets the <see cref="T:Microsoft.AspNet.SignalR.IConnection" /> for the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> .</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.IConnection" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.PersistentConnection.Counters">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.GetSignals(System.String)">
      <summary>Returns the signals used in the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> .</summary>
      <returns>The signals used for this <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> .</returns>
      <param name="connectionId">The ID of the incoming connection.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.PersistentConnection.Groups">
      <summary>Gets the <see cref="T:Microsoft.AspNet.SignalR.IConnectionGroupManager" /> for the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> .</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.IConnectionGroupManager" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.Initialize(Microsoft.AspNet.SignalR.IDependencyResolver,Microsoft.AspNet.SignalR.Hosting.HostContext)"></member>
    <member name="P:Microsoft.AspNet.SignalR.PersistentConnection.JsonSerializer">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Json.IJsonSerializer" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.PersistentConnection.MessageBus">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Messaging.IMessageBus" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.OnConnected(Microsoft.AspNet.SignalR.IRequest,System.String)">
      <summary>   Called when a new connection is made.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that completes when the connect operation is complete.</returns>
      <param name="request">The <see cref="T:Microsoft.AspNet.SignalR.IRequest" /> for the current connection.</param>
      <param name="connectionId">The ID of the reconnecting client.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.OnDisconnected(Microsoft.AspNet.SignalR.IRequest,System.String)">
      <summary>  Called when a connection disconnects.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that completes when the disconnect operation is complete.</returns>
      <param name="request">The <see cref="T:Microsoft.AspNet.SignalR.IRequest" /> for the current connection.</param>
      <param name="connectionId">The ID of the disconnected client.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.OnReceived(Microsoft.AspNet.SignalR.IRequest,System.String,System.String)">
      <summary>Called when data is received from a connection.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that completes when the receive operation is complete.</returns>
      <param name="request">The <see cref="T:Microsoft.AspNet.SignalR.IRequest" /> for the current connection.</param>
      <param name="connectionId">The ID of the connection sending the data.</param>
      <param name="data">The payload sent to the connection.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.OnReconnected(Microsoft.AspNet.SignalR.IRequest,System.String)">
      <summary>Called when a connection reconnects after a timeout.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that completes when the re-connect operation is complete.</returns>
      <param name="request">The <see cref="T:Microsoft.AspNet.SignalR.IRequest" /> for the current connection.</param>
      <param name="connectionId">The ID of the reconnecting client.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.OnRejoiningGroups(Microsoft.AspNet.SignalR.IRequest,System.Collections.Generic.IList{System.String},System.String)">
      <summary>Called when a connection reconnects after a timeout to determine which groups should be rejoined.</summary>
      <returns>A collection of group names that should be joined on reconnect.</returns>
      <param name="request">The <see cref="T:Microsoft.AspNet.SignalR.IRequest" /> for the current connection.</param>
      <param name="groups">The groups the calling connection claims to be part of.</param>
      <param name="connectionId">The ID of the reconnecting client.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.PersistentConnection.ProcessRequest(Microsoft.AspNet.SignalR.Hosting.HostContext)">
      <summary>Handles all requests for a <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that completes when the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> pipeline is complete.</returns>
      <param name="context">The <see cref="T:Microsoft.AspNet.SignalR.Hosting.HostContext" /> for the current request.</param>
      <exception cref="T:System.InvalidOperationException">Thrown if the connection wasn't initialized. Thrown if the transport wasn't specified. Thrown if the connection ID wasn't specified.</exception>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.PersistentConnection.ProtectedData">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IProtectedData" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.PersistentConnection.Trace">
      <returns>Returns <see cref="T:System.Diagnostics.TraceSource" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.PersistentConnection.TraceManager">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Tracing.ITraceManager" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.PersistentConnection.Transport">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Transports.ITransport" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Configuration.DefaultConfigurationManager"></member>
    <member name="M:Microsoft.AspNet.SignalR.Configuration.DefaultConfigurationManager.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Configuration.DefaultConfigurationManager.ConnectionTimeout"></member>
    <member name="P:Microsoft.AspNet.SignalR.Configuration.DefaultConfigurationManager.DefaultMessageBufferSize"></member>
    <member name="P:Microsoft.AspNet.SignalR.Configuration.DefaultConfigurationManager.DisconnectTimeout"></member>
    <member name="P:Microsoft.AspNet.SignalR.Configuration.DefaultConfigurationManager.KeepAlive"></member>
    <member name="T:Microsoft.AspNet.SignalR.Configuration.IConfigurationManager">
      <summary>Provides access to server configuration.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Configuration.IConfigurationManager.ConnectionTimeout">
      <summary>Gets or sets a <see cref="T:System.TimeSpan" /> representing the amount of time to leave a connection open before timing out.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Configuration.IConfigurationManager.DefaultMessageBufferSize">
      <summary>Gets of sets the number of messages to buffer for a specific signal.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Configuration.IConfigurationManager.DisconnectTimeout">
      <summary>Gets or sets a <see cref="T:System.TimeSpan" /> representing the amount of time to wait after a connection goes away before raising the disconnect event.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Configuration.IConfigurationManager.KeepAlive">
      <summary>Gets or sets a <see cref="T:System.TimeSpan" /> representing the amount of time between send keep alive messages. If enabled, this value must be at least two seconds. Set to null to disable.</summary>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hosting.HostConstants"></member>
    <member name="F:Microsoft.AspNet.SignalR.Hosting.HostConstants.DebugMode"></member>
    <member name="F:Microsoft.AspNet.SignalR.Hosting.HostConstants.InstanceName"></member>
    <member name="F:Microsoft.AspNet.SignalR.Hosting.HostConstants.ShutdownToken"></member>
    <member name="F:Microsoft.AspNet.SignalR.Hosting.HostConstants.SupportsWebSockets"></member>
    <member name="F:Microsoft.AspNet.SignalR.Hosting.HostConstants.WebSocketServerUrl"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hosting.HostContext"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.HostContext.#ctor(Microsoft.AspNet.SignalR.IRequest,Microsoft.AspNet.SignalR.Hosting.IResponse)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hosting.HostContext.Items"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hosting.HostContext.Request"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hosting.HostContext.Response"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hosting.HostContextExtensions"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.HostContextExtensions.GetValue``1(Microsoft.AspNet.SignalR.Hosting.HostContext,System.String)">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.HostContextExtensions.HostShutdownToken(Microsoft.AspNet.SignalR.Hosting.HostContext)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.HostContextExtensions.InstanceName(Microsoft.AspNet.SignalR.Hosting.HostContext)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.HostContextExtensions.IsDebuggingEnabled(Microsoft.AspNet.SignalR.Hosting.HostContext)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.HostContextExtensions.SupportsWebSockets(Microsoft.AspNet.SignalR.Hosting.HostContext)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.HostContextExtensions.WebSocketServerUrl(Microsoft.AspNet.SignalR.Hosting.HostContext)"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hosting.HostDependencyResolverExtensions"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.HostDependencyResolverExtensions.InitializeHost(Microsoft.AspNet.SignalR.IDependencyResolver,System.String,System.Threading.CancellationToken)"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hosting.IResponse">
      <summary>Represents a connection to the client.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hosting.IResponse.CancellationToken">
      <summary>Gets a cancellation token that represents the client's lifetime.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hosting.IResponse.ContentType">
      <summary>Gets or sets the content type of the response.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.IResponse.End">
      <summary>Closes the connection to the client.</summary>
      <returns>A task that represents when the connection is closed.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.IResponse.Flush">
      <summary>Flushes the buffered response to the client.</summary>
      <returns>A task that represents when the data has been flushed.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.IResponse.Write(System.ArraySegment{System.Byte})">
      <summary>Writes buffered data.</summary>
      <param name="data">The data to write to the buffer.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hosting.IWebSocket">
      <summary>Represents a web socket.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hosting.IWebSocket.OnClose">
      <summary>Invoked when the websocket gracefully closes</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hosting.IWebSocket.OnError">
      <summary>Invoked when there is an error</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hosting.IWebSocket.OnMessage">
      <summary>Invoked when data is sent over the websocket</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.IWebSocket.Send(System.String)">
      <summary>Sends data over the websocket.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that represents the send is complete.</returns>
      <param name="value">The value to send.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hosting.IWebSocketRequest"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.IWebSocketRequest.AcceptWebSocketRequest(System.Func{Microsoft.AspNet.SignalR.Hosting.IWebSocket,System.Threading.Tasks.Task})"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hosting.PersistentConnectionFactory">
      <summary>Responsible for creating <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> instances.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.PersistentConnectionFactory.#ctor(Microsoft.AspNet.SignalR.IDependencyResolver)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.AspNet.SignalR.Hosting.PersistentConnectionFactory" /> class.</summary>
      <param name="resolver">The dependency resolver to use for when creating the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> .</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.PersistentConnectionFactory.CreateInstance(System.Type)">
      <summary>Creates an instance of the specified type using the dependency resolver or the type's default constructor.</summary>
      <returns>An instance of a <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> .</returns>
      <param name="connectionType">The type of <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" /> to create.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hosting.ResponseExtensions">
      <summary>Extension methods for <see cref="T:Microsoft.AspNet.SignalR.Hosting.IResponse" /> .</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.ResponseExtensions.AsStream(Microsoft.AspNet.SignalR.Hosting.IResponse)">
      <summary>Wraps the underlying <see cref="T:Microsoft.AspNet.SignalR.Hosting.IResponse" /> object as a stream</summary>
      <returns>A stream the wraps the response</returns>
      <param name="response">The <see cref="T:Microsoft.AspNet.SignalR.Hosting.IResponse" /></param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hosting.ResponseExtensions.End(Microsoft.AspNet.SignalR.Hosting.IResponse,System.String)">
      <summary>Closes the connection to a client with optional data.</summary>
      <returns>A task that represents when the connection is closed.</returns>
      <param name="response">The <see cref="T:Microsoft.AspNet.SignalR.Hosting.IResponse" /> .</param>
      <param name="data">The data to write to the connection.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule">
      <summary>Applies authorization attributes from the <see cref="T:Microsoft.AspNet.SignalR.Hub" /> class to determine whether to allow clients to receive messages sent from the hub.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule.#ctor(Microsoft.AspNet.SignalR.Hubs.IAuthorizeHubConnection,Microsoft.AspNet.SignalR.Hubs.IAuthorizeHubMethodInvocation)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule.BuildAuthorizeConnect(System.Func{Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest,System.Boolean})">
      <summary>Wraps a function to be called before a client subscribes to signals belonging to the hub described by the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" />. By default, the <see cref="T:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule" /> will look for attributes on the hub to help determine if the client is authorized to subscribe to method invocations for the described hub. The function returns true if the client is authorized to subscribe to client-side hub method invocations; false, otherwise.</summary>
      <returns>A wrapped function that dictates whether or not the client is authorized to connect to the described Hub.</returns>
      <param name="authorizeConnect">A function that dictates whether or not the client is authorized to connect to the described Hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule.BuildIncoming(System.Func{Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext,System.Threading.Tasks.Task{System.Object}})">
      <summary>Wraps a function that invokes a server-side hub method. Even if a client has not been authorized to connect to a hub, it will still be authorized to invoke server-side methods on that hub unless it is prevented in <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildIncoming(System.Func{Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext,System.Threading.Tasks.Task{System.Object}})" /> by not executing the invoke parameter.</summary>
      <returns>A wrapped function that invokes a server-side hub method.</returns>
      <param name="invoke">A function that invokes a server-side hub method.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation">
      <summary>A description of a client-side hub method invocation.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation.Args">
      <summary>The argument list the client-side hub method will be called with.</summary>
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation.Hub">
      <summary>The name of the hub that the method being invoked belongs to.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation.Method">
      <summary>The name of the client-side hub method to be invoked.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation.State">
      <summary>A key-value store representing the hub state on the server that has changed since the last time the hub state was sent to the client.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation.Target">
      <summary>The signal that clients receiving this invocation are subscribed to.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.ClientProxy">
      <summary>Represents a server side proxy for the client side hub.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ClientProxy.#ctor(System.Func{System.String,Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation,System.Collections.Generic.IList{System.String},System.Threading.Tasks.Task},System.String,System.Collections.Generic.IList{System.String})"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ClientProxy.Invoke(System.String,System.Object[])">
      <summary>Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNet.SignalR.Hubs.ClientProxy" /> instance.</summary>
      <returns>A task that represents when the data has been sent to the client.</returns>
      <param name="method">The name of the method to invoke.</param>
      <param name="args">The arguments to pass to the client.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ClientProxy.TryInvokeMember(System.Dynamic.InvokeMemberBinder,System.Object[],System.Object@)">
      <summary>Executes the <see cref="M:Microsoft.AspNet.SignalR.Hubs.ClientProxy.Invoke(System.String,System.Object[])" /> method.</summary>
      <returns>true always.</returns>
      <param name="binder">The information that contains binding semantic and details of the client side hub operation.</param>
      <param name="args">The arguments that are passed to the object member during the invoke operation.</param>
      <param name="result">When method returns, contains the result of the member invocation.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.ConnectionIdProxy"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ConnectionIdProxy.#ctor(System.Func{System.String,Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation,System.Collections.Generic.IList{System.String},System.Threading.Tasks.Task},System.String,System.String,System.String[])"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.DefaultAssemblyLocator"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultAssemblyLocator.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultAssemblyLocator.GetAssemblies">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.DefaultHubActivator"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultHubActivator.#ctor(Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultHubActivator.Create(Microsoft.AspNet.SignalR.Hubs.HubDescriptor)">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.DefaultHubManager"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultHubManager.#ctor(Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultHubManager.GetHub(System.String)">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultHubManager.GetHubMethod(System.String,System.String,System.Collections.Generic.IList{Microsoft.AspNet.SignalR.Json.IJsonValue})"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultHubManager.GetHubMethods(System.String,System.Func{Microsoft.AspNet.SignalR.Hubs.MethodDescriptor,System.Boolean})">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultHubManager.GetHubs(System.Func{Microsoft.AspNet.SignalR.Hubs.HubDescriptor,System.Boolean})">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultHubManager.ResolveHub(System.String)">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultHubManager.ResolveHubs">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.DefaultJavaScriptProxyGenerator"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultJavaScriptProxyGenerator.#ctor(Microsoft.AspNet.SignalR.Hubs.IHubManager,Microsoft.AspNet.SignalR.Hubs.IJavaScriptMinifier)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultJavaScriptProxyGenerator.#ctor(Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultJavaScriptProxyGenerator.GenerateProxy(System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultJavaScriptProxyGenerator.GenerateProxy(System.String,System.Boolean)">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.DefaultParameterResolver"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultParameterResolver.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultParameterResolver.ResolveMethodParameters(Microsoft.AspNet.SignalR.Hubs.MethodDescriptor,System.Collections.Generic.IList{Microsoft.AspNet.SignalR.Json.IJsonValue})">
      <summary>Resolves method parameter values based on provided objects.</summary>
      <returns>The array of parameter values.</returns>
      <param name="method">The method descriptor.</param>
      <param name="values">The list of values to resolve parameter values from.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.DefaultParameterResolver.ResolveParameter(Microsoft.AspNet.SignalR.Hubs.ParameterDescriptor,Microsoft.AspNet.SignalR.Json.IJsonValue)">
      <summary>Resolves a parameter value based on the provided object.</summary>
      <returns>The parameter value.</returns>
      <param name="descriptor">The parameter descriptor.</param>
      <param name="value">The value to resolve the parameter value from.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.Descriptor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.Descriptor.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.Descriptor.Name">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.Descriptor.NameSpecified">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.EmptyJavaScriptProxyGenerator"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.EmptyJavaScriptProxyGenerator.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.EmptyJavaScriptProxyGenerator.GenerateProxy(System.String)"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.GroupProxy"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.GroupProxy.#ctor(System.Func{System.String,Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation,System.Collections.Generic.IList{System.String},System.Threading.Tasks.Task},System.String,System.String,System.Collections.Generic.IList{System.String})"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.HubCallerContext"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubCallerContext.#ctor(Microsoft.AspNet.SignalR.IRequest,System.String)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubCallerContext.ConnectionId">
      <summary>Gets the connection ID of the calling client.</summary>
      <returns>The connection ID of the calling client.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubCallerContext.Headers">
      <summary>Gets the headers for the request.</summary>
      <returns>The headers for the request.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubCallerContext.QueryString">
      <summary>Gets the query string for the request.</summary>
      <returns>The query string for the request.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubCallerContext.Request">
      <summary>Gets the <see cref="T:Microsoft.AspNet.SignalR.IRequest" /> for the current HTTP request.</summary>
      <returns>The <see cref="T:Microsoft.AspNet.SignalR.IRequest" /> for the current HTTP request.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubCallerContext.RequestCookies">
      <summary>Gets the cookies for the request.</summary>
      <returns>The cookies for the request.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubCallerContext.User">
      <summary>Gets the <see cref="T:System.Security.Principal.IPrincipal" /> for the request.</summary>
      <returns>The <see cref="T:System.Security.Principal.IPrincipal" /> for the request.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext">
      <summary>Encapsulates all information about an individual SignalR connection for an <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> .</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext.#ctor(Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker,Microsoft.AspNet.SignalR.IConnection,System.String,System.String,Microsoft.AspNet.SignalR.Hubs.StateChangeTracker)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext.All">
      <summary>All connected clients.</summary>
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext.AllExcept(System.String[])">
      <summary>Returns a dynamic representation of all clients except the calling client ones specified.</summary>
      <returns>A dynamic representation of all clients except the calling client ones specified.</returns>
      <param name="excludeConnectionIds">A list of connection ids to exclude.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext.Caller">
      <summary>Represents the calling client.</summary>
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext.Client(System.String)">
      <summary>Returns a dynamic representation of the connection with the specified connection ID.</summary>
      <returns>A dynamic representation of the specified client.</returns>
      <param name="connectionId">The connection id</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext.Group(System.String,System.String[])">
      <summary>Returns a dynamic representation of the specified group.</summary>
      <returns>A dynamic representation of the specified group.</returns>
      <param name="groupName">The name of the group</param>
      <param name="excludeConnectionIds">A list of connection ids to exclude.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext.Others">
      <summary>All connected clients except the calling client.</summary>
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext.OthersInGroup(System.String)">
      <summary>Returns a dynamic representation of all clients in a group except the calling client.</summary>
      <returns>A dynamic representation of all clients in a group except the calling client.</returns>
      <param name="groupName">The name of the group</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor">
      <summary>Holds information about a single hub.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDescriptor.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDescriptor.CreateQualifiedName(System.String)">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubDescriptor.HubType">
      <summary>Hub type.</summary>
      <returns>Returns <see cref="T:System.Type" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher">
      <summary>Handles all communication over the hubs persistent connection.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.#ctor(Microsoft.AspNet.SignalR.HubConfiguration)">
      <summary>Initializes an instance of the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> class.</summary>
      <param name="configuration">Configuration settings determining whether to enable JS proxies and provide clients with detailed hub errors.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.AuthorizeRequest(Microsoft.AspNet.SignalR.IRequest)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.GetSignals(System.String)">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.Initialize(Microsoft.AspNet.SignalR.IDependencyResolver,Microsoft.AspNet.SignalR.Hosting.HostContext)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.OnConnected(Microsoft.AspNet.SignalR.IRequest,System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.OnDisconnected(Microsoft.AspNet.SignalR.IRequest,System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.OnReceived(Microsoft.AspNet.SignalR.IRequest,System.String,System.String)">
      <summary>Processes the hub's incoming method calls.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.OnReconnected(Microsoft.AspNet.SignalR.IRequest,System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.OnRejoiningGroups(Microsoft.AspNet.SignalR.IRequest,System.Collections.Generic.IList{System.String},System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.ProcessRequest(Microsoft.AspNet.SignalR.Hosting.HostContext)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubDispatcher.Trace">
      <returns>Returns <see cref="T:System.Diagnostics.TraceSource" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.HubManagerExtensions"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubManagerExtensions.EnsureHub(Microsoft.AspNet.SignalR.Hubs.IHubManager,System.String,Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter[])">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubManagerExtensions.GetHubMethods(Microsoft.AspNet.SignalR.Hubs.IHubManager,System.String)">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubManagerExtensions.GetHubs(Microsoft.AspNet.SignalR.Hubs.IHubManager)">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.HubMethodNameAttribute"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubMethodNameAttribute.#ctor(System.String)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubMethodNameAttribute.MethodName">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.HubNameAttribute"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubNameAttribute.#ctor(System.String)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubNameAttribute.HubName">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule">
      <summary>Common base class to simplify the implementation of IHubPipelineModules. A module can intercept and customize various stages of hub processing such as connecting, reconnecting, disconnecting, invoking server-side hub methods, invoking client-side hub methods, authorizing hub clients and rejoining hub groups. A module can be activated by calling <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHubPipeline.AddModule(Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule)" /> . The combined modules added to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are invoked via the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker" /> interface.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.BuildAuthorizeConnect(System.Func{Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest,System.Boolean})">
      <summary>Wraps a function to be called before a client subscribes to signals belonging to the hub described by the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" /> . By default, the <see cref="T:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule" /> will look for attributes on the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> to help determine if the client is authorized to subscribe to method invocations for the described hub. The function returns true if the client is authorized to subscribe to client-side hub method invocations; false, otherwise.</summary>
      <returns>A wrapped function that dictates whether or not the client is authorized to connect to the described Hub.</returns>
      <param name="authorizeConnect">A function that dictates whether or not the client is authorized to connect to the described Hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.BuildConnect(System.Func{Microsoft.AspNet.SignalR.Hubs.IHub,System.Threading.Tasks.Task})">
      <summary>Wraps a function that is called when a client connects to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> for each <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client connects to. By default, this results in the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> 's OnConnected method being invoked.</summary>
      <returns>A wrapped function to be called when a client connects to a hub.</returns>
      <param name="connect">A function to be called when a client connects to a hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.BuildDisconnect(System.Func{Microsoft.AspNet.SignalR.Hubs.IHub,System.Threading.Tasks.Task})">
      <summary>Wraps a function that is called when a client disconnects from the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> for each <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client was connected to. By default, this results in the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> 's OnDisconnected method being invoked.</summary>
      <returns>A wrapped function to be called when a client disconnects from a hub.</returns>
      <param name="disconnect">A function to be called when a client disconnects from a hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.BuildIncoming(System.Func{Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext,System.Threading.Tasks.Task{System.Object}})">
      <summary>Wraps a function that invokes a server-side hub method. Even if a client has not been authorized to connect to a hub, it will still be authorized to invoke server-side methods on that hub unless it is prevented in <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildIncoming(System.Func{Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext,System.Threading.Tasks.Task{System.Object}})" /> by not executing the invoke parameter.</summary>
      <returns>A wrapped function that invokes a server-side hub method.</returns>
      <param name="invoke">A function that invokes a server-side hub method.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.BuildOutgoing(System.Func{Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext,System.Threading.Tasks.Task})">
      <summary>Wraps a function that invokes a client-side hub method.</summary>
      <returns>A wrapped function that invokes a client-side hub method.</returns>
      <param name="send">A function that invokes a client-side hub method.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.BuildReconnect(System.Func{Microsoft.AspNet.SignalR.Hubs.IHub,System.Threading.Tasks.Task})">
      <summary>Wraps a function that is called when a client reconnects to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> for each <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client connects to. By default, this results in the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> 's OnReconnected method being invoked.</summary>
      <returns>A wrapped function to be called when a client reconnects to a hub.</returns>
      <param name="reconnect">A function to be called when a client reconnects to a hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.BuildRejoiningGroups(System.Func{Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest,System.Collections.Generic.IList{System.String},System.Collections.Generic.IList{System.String}})">
      <summary>Wraps a function that determines which of the groups belonging to the hub described by the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" /> the client should be allowed to rejoin. By default, clients that are reconnecting to the server will be removed from all groups they may have previously been a member of, because untrusted clients may claim to be a member of groups they were never authorized to join.</summary>
      <returns>A wrapped function that determines which groups the client should be allowed to rejoin.</returns>
      <param name="rejoiningGroups">A function that determines which groups the client should be allowed to rejoin.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnAfterConnect(Microsoft.AspNet.SignalR.Hubs.IHub)">
      <summary>This method is called after the connect components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are executed and after <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnConnected" /> is executed, if at all.</summary>
      <param name="hub">The hub the client has connected to.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnAfterDisconnect(Microsoft.AspNet.SignalR.Hubs.IHub)">
      <summary>This method is called after the disconnect components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are executed and after <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnDisconnected" /> is executed, if at all.</summary>
      <param name="hub">The hub the client has disconnected from.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnAfterIncoming(System.Object,Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext)">
      <summary>This method is called after the incoming components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> and the server-side hub method have completed execution.</summary>
      <returns>The possibly new or updated return value of the server-side hub method</returns>
      <param name="result">The return value of the server-side hub method</param>
      <param name="context">A description of the server-side hub method invocation.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnAfterOutgoing(Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext)">
      <summary>This method is called after the outgoing components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are executed. This does not mean that all the clients have received the hub method invocation, but it does indicate a hub invocation message has successfully been published to a message bus.</summary>
      <param name="context">A description of the client-side hub method invocation.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnAfterReconnect(Microsoft.AspNet.SignalR.Hubs.IHub)">
      <summary>This method is called after the reconnect components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are executed and after <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnReconnected" /> is executed, if at all.</summary>
      <param name="hub">The hub the client has reconnected to.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnBeforeAuthorizeConnect(Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest)">
      <summary>This method is called before the AuthorizeConnect components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are executed. If this returns false, then those later-added modules will not run and the client will not be allowed to subscribe to client-side invocations of methods belonging to the hub defined by the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" /> .</summary>
      <returns>true, if the client is authorized to connect to the hub, false otherwise.</returns>
      <param name="hubDescriptor">A description of the hub the client is trying to subscribe to.</param>
      <param name="request">The connect request of the client trying to subscribe to the hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnBeforeConnect(Microsoft.AspNet.SignalR.Hubs.IHub)">
      <summary>This method is called before the connect components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are executed. If this returns false, then those later-added modules and the <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnConnected" /> method will not be run.</summary>
      <returns>true, if the connect components of later added modules and the <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnConnected" /> method should be executed; false, otherwise.</returns>
      <param name="hub">The hub the client has connected to.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnBeforeDisconnect(Microsoft.AspNet.SignalR.Hubs.IHub)">
      <summary>This method is called before the disconnect components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are executed. If this returns false, then those later-added modules and the <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnDisconnected" /> method will not be run.</summary>
      <returns>true, if the disconnect components of later added modules and the <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnDisconnected" /> method should be executed; false, otherwise.</returns>
      <param name="hub">The hub the client has disconnected from.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnBeforeIncoming(Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext)">
      <summary>This method is called before the incoming components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are executed. If this returns false, then those later-added modules and the server-side hub method invocation will not be executed. Even if a client has not been authorized to connect to a hub, it will still be authorized to invoke server-side methods on that hub unless it is prevented in <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildIncoming(System.Func{Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext,System.Threading.Tasks.Task{System.Object}})" /> by not executing the invoke parameter or prevented in <see cref="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnBeforeIncoming(Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext)" /> by returning false.</summary>
      <returns>true, if the incoming components of later added modules and the server-side hub method invocation should be executed; false, otherwise.</returns>
      <param name="context">A description of the server-side hub method invocation.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnBeforeOutgoing(Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext)">
      <summary>This method is called before the outgoing components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are executed. If this returns false, then those later-added modules and the client-side hub method invocation(s) will not be executed.</summary>
      <returns>true, if the outgoing components of later added modules and the client-side hub method invocation(s) should be executed; false, otherwise.</returns>
      <param name="context">A description of the client-side hub method invocation.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnBeforeReconnect(Microsoft.AspNet.SignalR.Hubs.IHub)">
      <summary>This method is called before the reconnect components of any modules added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are executed. If this returns false, then those later-added modules and the <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnReconnected" /> method will not be run.</summary>
      <returns>true, if the reconnect components of later added modules and the <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnReconnected" /> method should be executed; false, otherwise.</returns>
      <param name="hub">The hub the client has reconnected to.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubPipelineModule.OnIncomingError(System.Exception,Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext)">
      <summary>This is called when an uncaught exception is thrown by a server-side hub method or the incoming component of a module added later to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> . Observing the exception using this method will not prevent it from bubbling up to other modules.</summary>
      <param name="ex">The exception that was thrown during the server-side invocation.</param>
      <param name="context">A description of the server-side hub method invocation.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.HubRequest"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubRequest.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubRequest.Hub">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubRequest.Id">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubRequest.Method">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubRequest.ParameterValues">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.IJsonValue" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubRequest.State">
      <returns>Returns <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.HubResponse">
      <summary>The response returned from an incoming hub request.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.HubResponse.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubResponse.Error">
      <summary>The exception that occurs as a result of invoking the hub method.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubResponse.Id">
      <summary>The id of the operation.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubResponse.Result">
      <summary>The result of the invocation.</summary>
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubResponse.StackTrace">
      <summary>The stack trace of the exception that occurs as a result of invoking the hub method.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.HubResponse.State">
      <summary>The changes made the round tripped state.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IAssemblyLocator"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IAssemblyLocator.GetAssemblies">
      <returns>Returns <see cref="T:System.Collections.Generic.IList`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IAuthorizeHubConnection">
      <summary>Interface to be implemented by <see cref="T:System.Attribute" /> s that can authorize client to connect to a <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> .</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IAuthorizeHubConnection.AuthorizeHubConnection(Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest)">
      <summary>Given a <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubCallerContext" /> , determine whether client is authorized to connect to <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> .</summary>
      <returns>true if the caller is authorized to connect to the hub; otherwise, false.</returns>
      <param name="hubDescriptor">Description of the hub client is attempting to connect to.</param>
      <param name="request">The connection request from the client.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IAuthorizeHubMethodInvocation">
      <summary>Interface to be implemented by <see cref="T:System.Attribute" />s that can authorize the invocation of <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> methods.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IAuthorizeHubMethodInvocation.AuthorizeHubMethodInvocation(Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext,System.Boolean)">
      <summary>Given a <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext" /> , determine whether client is authorized to invoke the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> method.</summary>
      <returns>true if the caller is authorized to invoke the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> method; otherwise, false.</returns>
      <param name="hubIncomingInvokerContext">An <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext" /> providing details regarding the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> method invocation.</param>
      <param name="appliesToMethod">Indicates whether the interface instance is an attribute applied directly to a method.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IClientProxy">
      <summary>A server side proxy for the client side hub.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IClientProxy.Invoke(System.String,System.Object[])">
      <summary>Invokes a method on the connection(s) represented by the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IClientProxy" /> instance.</summary>
      <returns>A task that represents when the data has been sent to the client.</returns>
      <param name="method">The name of the method to invoke</param>
      <param name="args">The arguments to pass to the client</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHub"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHub.Clients">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubConnectionContext" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHub.Context">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubCallerContext" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHub.Groups">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.IGroupManager" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnConnected">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnDisconnected">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHub.OnReconnected">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHubActivator"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubActivator.Create(Microsoft.AspNet.SignalR.Hubs.HubDescriptor)">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHubConnectionContext">
      <summary>Encapsulates all information about a SignalR connection for an <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> .</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHubConnectionContext.All">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubConnectionContext.AllExcept(System.String[])">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubConnectionContext.Client(System.String)">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubConnectionContext.Group(System.String,System.String[])">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHubDescriptorProvider">
      <summary>Describes hub descriptor provider, which provides information about available hubs.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubDescriptorProvider.GetHubs">
      <summary>Retrieves all available hubs.</summary>
      <returns>Collection of hub descriptors.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubDescriptorProvider.TryGetHub(System.String,Microsoft.AspNet.SignalR.Hubs.HubDescriptor@)">
      <summary>Tries to retrieve hub with a given name.</summary>
      <returns>True, if hub has been found</returns>
      <param name="hubName">Name of the hub.</param>
      <param name="descriptor">Retrieved descriptor object.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext">
      <summary>A description of a server-side hub method invocation originating from a client.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext.Args">
      <summary>The arguments to be passed to the invoked method.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext.Hub">
      <summary>A hub instance that contains the invoked method as a member.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext.MethodDescriptor">
      <summary>A description of the method being invoked by the client.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext.StateTracker">
      <summary>A key-value store representing the hub state on the client at the time of the invocation.</summary>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHubManager">
      <summary>Describes a hub manager - main point in the whole hub and method lookup process.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubManager.GetHub(System.String)">
      <summary>Retrieves a single hub descriptor.</summary>
      <returns>Hub descriptor, if found. Null, otherwise.</returns>
      <param name="hubName">Name of the hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubManager.GetHubMethod(System.String,System.String,System.Collections.Generic.IList{Microsoft.AspNet.SignalR.Json.IJsonValue})">
      <summary>Retrieves a method with a given name on a given hub.</summary>
      <returns>Descriptor of the method, if found. Null otherwise.</returns>
      <param name="hubName">Name of the hub.</param>
      <param name="method">Name of the method to find.</param>
      <param name="parameters">Method parameters to match.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubManager.GetHubMethods(System.String,System.Func{Microsoft.AspNet.SignalR.Hubs.MethodDescriptor,System.Boolean})">
      <summary>Gets all methods available to call on a given hub.</summary>
      <returns>List of available methods.</returns>
      <param name="hubName">Name of the hub,</param>
      <param name="predicate">Optional predicate for filtering results.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubManager.GetHubs(System.Func{Microsoft.AspNet.SignalR.Hubs.HubDescriptor,System.Boolean})">
      <summary>Retrieves all available hubs matching the given predicate.</summary>
      <returns>List of hub descriptors.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubManager.ResolveHub(System.String)">
      <summary>Resolves a given hub name to a concrete object.</summary>
      <returns>Hub implementation instance, if found. Null otherwise.</returns>
      <param name="hubName">Name of the hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubManager.ResolveHubs">
      <summary>Resolves all available hubs to their concrete objects.</summary>
      <returns>List of hub instances.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext">
      <summary>A description of a client-side hub method invocation originating from the server.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext.Connection">
      <summary>The <see cref="T:Microsoft.AspNet.SignalR.IConnection" /> , if any, corresponding to the client that invoked the server-side hub method that is invoking the client-side hub method.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext.ExcludedSignals">
      <summary>The signals (ConnectionId, hub type name or hub type name + "." + group name) belonging to clients that should not receive the method invocation regardless of the <see cref="P:Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext.Signal" /> .</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext.Invocation">
      <summary>A description of the method call to be made on the client.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext.Signal">
      <summary>The signal (ConnectionId, hub type name or hub type name + "." + group name) belonging to clients that receive the method invocation.</summary>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline">
      <summary>A collection of modules that can intercept and customize various stages of hub processing such as connecting, reconnecting, disconnecting, invoking server-side hub methods, invoking client-side hub methods, authorizing hub clients and rejoining hub groups.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipeline.AddModule(Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule)">
      <summary>Adds an <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule" /> to the hub pipeline. Modules added to the pipeline first will wrap modules that are added to the pipeline later. All modules must be added to the pipeline before any methods on the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker" /> are invoked.</summary>
      <returns>The <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> itself with the newly added module allowing <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHubPipeline.AddModule(Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule)" /> calls to be chained. This method mutates the pipeline it is invoked on so it is not necessary to store its result.</returns>
      <param name="pipelineModule">A module that may intercept and customize various stages of hub processing such as connecting, reconnecting, disconnecting, invoking server-side hub methods, invoking client-side hub methods, authorizing hub clients and rejoining hub groups.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker">
      <summary>Implementations of this interface are responsible for executing operation required to complete various stages hub processing such as connecting, reconnecting, disconnecting, invoking server-side hub methods, invoking client-side hub methods, authorizing hub clients and rejoining hub groups.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker.AuthorizeConnect(Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest)">
      <summary>To be called before a client subscribes to signals belonging to the hub described by the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" /> . By default, the <see cref="T:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule" /> will look for attributes on the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> to help determine if the client is authorized to subscribe to method invocations for the described hub.</summary>
      <returns>true, if the client is authorized to subscribe to client-side hub method invocations; false, otherwise.</returns>
      <param name="hubDescriptor">A description of the hub the client is attempting to connect to.</param>
      <param name="request">The connect request being made by the client which should include the client's <see cref="T:System.Security.Principal.IPrincipal" /> User.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker.Connect(Microsoft.AspNet.SignalR.Hubs.IHub)">
      <summary>To be called when a client connects to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> for each <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client connects to. By default, this results in the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> 's OnConnected method being invoked.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="hub">A <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client is connected to.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker.Disconnect(Microsoft.AspNet.SignalR.Hubs.IHub)">
      <summary>To be called when a client disconnects from the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> for each <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client was connected to. By default, this results in the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> 's OnDisconnected method being invoked.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="hub">A <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client was disconnected from.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker.Invoke(Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext)">
      <summary>Invokes a server-side hub method.</summary>
      <returns>An asynchronous operation giving the return value of the server-side hub method invocation.</returns>
      <param name="context">A description of the server-side hub method invocation.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker.Reconnect(Microsoft.AspNet.SignalR.Hubs.IHub)">
      <summary>To be called when a client reconnects to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> for each <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client connects to. By default, this results in the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> 's OnReconnected method being invoked.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="hub">A <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client is reconnected to.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker.RejoiningGroups(Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest,System.Collections.Generic.IList{System.String})">
      <summary>This method determines which of the groups belonging to the hub described by the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" /> the client should be allowed to rejoin. By default, clients that are reconnecting to the server will be removed from all groups they may have previously been a member of, because untrusted clients may claim to be a member of groups they were never authorized to join.</summary>
      <returns>A list of groups the client is allowed to rejoin.</returns>
      <param name="hubDescriptor">A description of the hub for which the client is attempting to rejoin groups.</param>
      <param name="request">The reconnect request being made by the client that is attempting to rejoin groups.</param>
      <param name="groups">The list of groups belonging to the relevant hub that the client claims to have been a member of before the reconnect.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker.Send(Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext)">
      <summary>Invokes a client-side hub method.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="context">A description of the client-side hub method invocation.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule">
      <summary>An <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule" /> can intercept and customize various stages of hub processing such as connecting, reconnecting, disconnecting, invoking server-side hub methods, invoking client-side hub methods, authorizing hub clients and rejoining hub groups. Modules can be be activated by calling <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHubPipeline.AddModule(Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule)" /> . The combined modules added to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipeline" /> are invoked via the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHubPipelineInvoker" /> interface.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildAuthorizeConnect(System.Func{Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest,System.Boolean})">
      <summary>Wraps a function to be called before a client subscribes to signals belonging to the hub described by the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" /> . By default, the <see cref="T:Microsoft.AspNet.SignalR.Hubs.AuthorizeModule" /> will look for attributes on the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> to help determine if the client is authorized to subscribe to method invocations for the described hub. The function returns true if the client is authorized to subscribe to client-side hub method invocations; false, otherwise.</summary>
      <returns>A wrapped function that dictates whether or not the client is authorized to connect to the described Hub.</returns>
      <param name="authorizeConnect">A function that dictates whether or not the client is authorized to connect to the described Hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildConnect(System.Func{Microsoft.AspNet.SignalR.Hubs.IHub,System.Threading.Tasks.Task})">
      <summary>Wraps a function that is called when a client connects to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> for each <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client connects to. By default, this results in the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> 's OnConnected method being invoked.</summary>
      <returns>A wrapped function to be called when a client connects to a hub.</returns>
      <param name="connect">A function to be called when a client connects to a hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildDisconnect(System.Func{Microsoft.AspNet.SignalR.Hubs.IHub,System.Threading.Tasks.Task})">
      <summary>Wraps a function that is called when a client disconnects from the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> for each <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client was connected to. By default, this results in the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> 's OnDisconnected method being invoked.</summary>
      <returns>A wrapped function to be called when a client disconnects from a hub.</returns>
      <param name="disconnect">A function to be called when a client disconnects from a hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildIncoming(System.Func{Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext,System.Threading.Tasks.Task{System.Object}})">
      <summary>Wraps a function that invokes a server-side hub method. Even if a client has not been authorized to connect to a hub, it will still be authorized to invoke server-side methods on that hub unless it is prevented in <see cref="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildIncoming(System.Func{Microsoft.AspNet.SignalR.Hubs.IHubIncomingInvokerContext,System.Threading.Tasks.Task{System.Object}})" /> by not executing the invoke parameter.</summary>
      <returns>A wrapped function that invokes a server-side hub method.</returns>
      <param name="invoke">A function that invokes a server-side hub method.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildOutgoing(System.Func{Microsoft.AspNet.SignalR.Hubs.IHubOutgoingInvokerContext,System.Threading.Tasks.Task})">
      <summary>Wraps a function that invokes a client-side hub method.</summary>
      <returns>A wrapped function that invokes a client-side hub method.</returns>
      <param name="send">A function that invokes a client-side hub method.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildReconnect(System.Func{Microsoft.AspNet.SignalR.Hubs.IHub,System.Threading.Tasks.Task})">
      <summary>Wraps a function that is called when a client reconnects to the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> for each <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> the client connects to. By default, this results in the <see cref="T:Microsoft.AspNet.SignalR.Hubs.IHub" /> 's OnReconnected method being invoked.</summary>
      <returns>A wrapped function to be called when a client reconnects to a hub.</returns>
      <param name="reconnect">A function to be called when a client reconnects to a hub.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubPipelineModule.BuildRejoiningGroups(System.Func{Microsoft.AspNet.SignalR.Hubs.HubDescriptor,Microsoft.AspNet.SignalR.IRequest,System.Collections.Generic.IList{System.String},System.Collections.Generic.IList{System.String}})">
      <summary>Wraps a function that determines which of the groups belonging to the hub described by the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" /> the client should be allowed to rejoin. By default, clients that are reconnecting to the server will be removed from all groups they may have previously been a member of, because untrusted clients may claim to be a member of groups they were never authorized to join.</summary>
      <returns>A wrapped function that determines which groups the client should be allowed to rejoin.</returns>
      <param name="rejoiningGroups">A function that determines which groups the client should be allowed to rejoin.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IHubRequestParser">
      <summary>Handles parsing incoming requests through the <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDispatcher" /> .</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IHubRequestParser.Parse(System.String)">
      <summary>Parses the incoming hub payload into a <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubRequest" /> .</summary>
      <returns>The resulting <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubRequest" /> .</returns>
      <param name="data">The raw hub payload.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IJavaScriptMinifier"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IJavaScriptMinifier.Minify(System.String)">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IJavaScriptProxyGenerator"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IJavaScriptProxyGenerator.GenerateProxy(System.String)"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IMethodDescriptorProvider">
      <summary>Describes a hub method provider that builds a collection of available methods on a given hub.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IMethodDescriptorProvider.GetMethods(Microsoft.AspNet.SignalR.Hubs.HubDescriptor)">
      <summary>Retrieve all methods on a given hub.</summary>
      <returns>Available methods.</returns>
      <param name="hub">Hub descriptor object.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IMethodDescriptorProvider.TryGetMethod(Microsoft.AspNet.SignalR.Hubs.HubDescriptor,System.String,Microsoft.AspNet.SignalR.Hubs.MethodDescriptor@,System.Collections.Generic.IList{Microsoft.AspNet.SignalR.Json.IJsonValue})">
      <summary>Tries to retrieve a method.</summary>
      <returns>True, if a method has been found.</returns>
      <param name="hub">Hub descriptor object</param>
      <param name="method">Name of the method.</param>
      <param name="descriptor">Descriptor of the method, if found. Null otherwise.</param>
      <param name="parameters">Method parameters to match.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.IParameterResolver">
      <summary>Describes a parameter resolver for resolving parameter-matching values based on provided information.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.IParameterResolver.ResolveMethodParameters(Microsoft.AspNet.SignalR.Hubs.MethodDescriptor,System.Collections.Generic.IList{Microsoft.AspNet.SignalR.Json.IJsonValue})">
      <summary>Resolves method parameter values based on provided objects.</summary>
      <returns>Array of parameter values.</returns>
      <param name="method">Method descriptor.</param>
      <param name="values">List of values to resolve parameter values from.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.MethodDescriptor">
      <summary>Holds information about a single hub method.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.MethodDescriptor.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.MethodDescriptor.Attributes">
      <summary>Attributes attached to this method.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.MethodDescriptor.Hub">
      <summary>Hub descriptor object, target to this method.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.HubDescriptor" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.MethodDescriptor.Invoker">
      <summary>Method invocation delegate. Takes a target hub and an array of invocation arguments as it's arguments.</summary>
      <returns>Returns <see cref="T:System.Func`3" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.MethodDescriptor.Parameters">
      <summary>Available method parameters.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.IList`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.MethodDescriptor.ReturnType">
      <summary>The return type of this method.</summary>
      <returns>Returns <see cref="T:System.Type" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.MethodExtensions"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.MethodExtensions.Matches(Microsoft.AspNet.SignalR.Hubs.MethodDescriptor,System.Collections.Generic.IList{Microsoft.AspNet.SignalR.Json.IJsonValue})"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.NotAuthorizedException"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.NotAuthorizedException.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.NotAuthorizedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.NotAuthorizedException.#ctor(System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.NotAuthorizedException.#ctor(System.String,System.Exception)"></member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.NullJavaScriptMinifier"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.NullJavaScriptMinifier.#ctor"></member>
    <member name="F:Microsoft.AspNet.SignalR.Hubs.NullJavaScriptMinifier.Instance"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.NullJavaScriptMinifier.Minify(System.String)">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.ParameterDescriptor">
      <summary>Holds information about a single hub method parameter.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ParameterDescriptor.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.ParameterDescriptor.Name">
      <summary>Parameter name.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.ParameterDescriptor.ParameterType">
      <summary>Parameter type.</summary>
      <returns>Returns <see cref="T:System.Type" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.ReflectedHubDescriptorProvider"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ReflectedHubDescriptorProvider.#ctor(Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ReflectedHubDescriptorProvider.BuildHubsCache">
      <returns>Returns <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ReflectedHubDescriptorProvider.GetHubs">
      <returns>Returns <see cref="T:System.Collections.Generic.IList`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ReflectedHubDescriptorProvider.TryGetHub(System.String,Microsoft.AspNet.SignalR.Hubs.HubDescriptor@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.ReflectedMethodDescriptorProvider"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ReflectedMethodDescriptorProvider.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ReflectedMethodDescriptorProvider.GetMethods(Microsoft.AspNet.SignalR.Hubs.HubDescriptor)">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ReflectedMethodDescriptorProvider.TryGetMethod(Microsoft.AspNet.SignalR.Hubs.HubDescriptor,System.String,Microsoft.AspNet.SignalR.Hubs.MethodDescriptor@,System.Collections.Generic.IList{Microsoft.AspNet.SignalR.Json.IJsonValue})">
      <summary>Searches the specified <paramref name="hub" /> for the specified <paramref name="method" />.</summary>
      <returns>true if the method matching the name/parameter set is found on the hub, otherwise false.</returns>
      <param name="hub">Hub to search for the specified <paramref name="method" /> on.</param>
      <param name="method">The method name to search for.</param>
      <param name="descriptor">If successful, the <see cref="T:Microsoft.AspNet.SignalR.Hubs.MethodDescriptor" /> that was resolved.</param>
      <param name="parameters">The set of parameters that will be used to help locate a specific overload of the specified <paramref name="method" />.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.ReflectionHelper"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ReflectionHelper.GetAttributeValue``2(System.Reflection.ICustomAttributeProvider,System.Func{``0,``1})">
      <returns>Returns <see cref="{0}" />.</returns>
      <typeparam name="TAttribute"></typeparam>
      <typeparam name="TResult"></typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.ReflectionHelper.GetExportedHubMethods(System.Type)">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.SignalProxy"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.SignalProxy.#ctor(System.Func{System.String,Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation,System.Collections.Generic.IList{System.String},System.Threading.Tasks.Task},System.String,System.String,System.String,System.Collections.Generic.IList{System.String})"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.SignalProxy.GetInvocationData(System.String,System.Object[])">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.SignalProxy.HubName">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.SignalProxy.Invoke(System.String,System.Object[])">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.SignalProxy.Send">
      <returns>Returns <see cref="T:System.Func`4" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.SignalProxy.Signal">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.SignalProxy.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.SignalProxy.TryInvokeMember(System.Dynamic.InvokeMemberBinder,System.Object[],System.Object@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.StateChangeTracker">
      <summary>A change tracking dictionary.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.StateChangeTracker.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.StateChangeTracker.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object})"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.StateChangeTracker.GetChanges">
      <returns>Returns <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Hubs.StateChangeTracker.Item(System.String)">
      <returns>Returns <see cref="T:System.Object" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Hubs.StatefulSignalProxy"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.StatefulSignalProxy.#ctor(System.Func{System.String,Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation,System.Collections.Generic.IList{System.String},System.Threading.Tasks.Task},System.String,System.String,System.String,Microsoft.AspNet.SignalR.Hubs.StateChangeTracker)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.StatefulSignalProxy.GetInvocationData(System.String,System.Object[])">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hubs.ClientHubInvocation" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.StatefulSignalProxy.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Hubs.StatefulSignalProxy.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.Connection"></member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.Connection.#ctor(Microsoft.AspNet.SignalR.Messaging.IMessageBus,Microsoft.AspNet.SignalR.Json.IJsonSerializer,System.String,System.String,System.Collections.Generic.IList{System.String},System.Collections.Generic.IList{System.String},Microsoft.AspNet.SignalR.Tracing.ITraceManager,Microsoft.AspNet.SignalR.Infrastructure.IAckHandler,Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager,Microsoft.AspNet.SignalR.Infrastructure.IProtectedData)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.Connection.DefaultSignal">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="E:Microsoft.AspNet.SignalR.Infrastructure.Connection.EventKeyAdded"></member>
    <member name="E:Microsoft.AspNet.SignalR.Infrastructure.Connection.EventKeyRemoved"></member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.Connection.GetCursor">
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.Connection.Identity">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.Connection.Microsoft#AspNet#SignalR#Messaging#ISubscriber#EventKeys">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.Connection.Receive(System.String,System.Func{Microsoft.AspNet.SignalR.Transports.PersistentResponse,System.Threading.Tasks.Task{System.Boolean}},System.Int32)">
      <returns>Returns <see cref="T:System.IDisposable" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.Connection.Receive(System.String,System.Threading.CancellationToken,System.Int32)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.Connection.Send(Microsoft.AspNet.SignalR.ConnectionMessage)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.ConnectionManager">
      <summary>Default <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IConnectionManager" /> implementation.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.ConnectionManager.#ctor(Microsoft.AspNet.SignalR.IDependencyResolver)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.ConnectionManager" /> class.</summary>
      <param name="resolver">The <see cref="T:Microsoft.AspNet.SignalR.IDependencyResolver" />.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.ConnectionManager.GetConnection(System.Type)">
      <summary>Returns a <see cref="T:Microsoft.AspNet.SignalR.IPersistentConnectionContext" /> for the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</summary>
      <returns>A <see cref="T:Microsoft.AspNet.SignalR.IPersistentConnectionContext" /> for the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</returns>
      <param name="type">Type of the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.ConnectionManager.GetConnectionContext``1">
      <summary>Returns a <see cref="T:Microsoft.AspNet.SignalR.IPersistentConnectionContext" /> for the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</summary>
      <returns>A <see cref="T:Microsoft.AspNet.SignalR.IPersistentConnectionContext" /> for the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</returns>
      <typeparam name="T">Type of the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.ConnectionManager.GetHubContext``1">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.ConnectionManager.GetHubContext(System.String)"></member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.DefaultProtectedData"></member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.DefaultProtectedData.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.DefaultProtectedData.Protect(System.String,System.String)">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.DefaultProtectedData.Unprotect(System.String,System.String)">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.IAckHandler"></member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IAckHandler.CreateAck(System.String)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IAckHandler.TriggerAck(System.String)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.IConnectionManager">
      <summary>Provides access to hubs and persistent connections references.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IConnectionManager.GetConnectionContext``1">
      <summary>Returns a <see cref="T:Microsoft.AspNet.SignalR.IPersistentConnectionContext" /> for the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</summary>
      <returns>A <see cref="T:Microsoft.AspNet.SignalR.IPersistentConnectionContext" /> for the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</returns>
      <typeparam name="T">Type of the <see cref="T:Microsoft.AspNet.SignalR.PersistentConnection" />.</typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IConnectionManager.GetHubContext``1">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IConnectionManager.GetHubContext(System.String)"></member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter"></member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter.Close"></member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter.Decrement">
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter.Increment">
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter.IncrementBy(System.Int64)">
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter.NextSample"></member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter.RawValue">
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter.RemoveInstance"></member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager">
      <summary>Provides access to performance counters.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ConnectionMessagesReceivedPerSec">
      <summary>Gets the performance counter representing the number of messages received by connections (server to client) per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ConnectionMessagesReceivedTotal">
      <summary>Gets the performance counter representing the total number of messages received by connections (server to client) since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ConnectionMessagesSentPerSec">
      <summary>Gets the performance counter representing the number of messages sent by connections (client to server) per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ConnectionMessagesSentTotal">
      <summary>Gets the performance counter representing the total number of messages received by connections (server to client) since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ConnectionsConnected">
      <summary>Gets the performance counter representing the total number of connection Connect events since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ConnectionsCurrent">
      <summary>Gets the performance counter representing the number of connections currently connected.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ConnectionsDisconnected">
      <summary>Gets the performance counter representing the total number of connection Disconnect events since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ConnectionsReconnected">
      <summary>Gets the performance counter representing the total number of connection Reconnect events since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ErrorsAllPerSec">
      <summary>Gets the performance counter representing the number of all errors processed per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ErrorsAllTotal">
      <summary>Gets the performance counter representing the total number of all errors processed since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ErrorsHubInvocationPerSec">
      <summary>Gets the performance counter representing the number of hub invocation errors per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ErrorsHubInvocationTotal">
      <summary>Gets the performance counter representing the total number of hub invocation errors processed since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ErrorsHubResolutionPerSec">
      <summary>Gets the performance counter representing the number of hub resolution errors per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ErrorsHubResolutionTotal">
      <summary>Gets the performance counter representing the total number of hub resolution errors processed since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ErrorsTransportPerSec">
      <summary>Gets the performance counter representing the number of transport errors per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.ErrorsTransportTotal">
      <summary>Gets the performance counter representing the total number of transport errors processed since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.Initialize(System.String,System.Threading.CancellationToken)">
      <summary>Initializes the performance counters.</summary>
      <param name="instanceName">The host instance name.</param>
      <param name="hostShutdownToken">The CancellationToken representing the host shutdown.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.MessageBusAllocatedWorkers">
      <summary>Gets the performance counter representing the number of workers allocated to deliver messages in the message bus.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.MessageBusBusyWorkers">
      <summary>Gets the performance counter representing the number of workers currently busy delivering messages in the message bus.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.MessageBusMessagesPublishedPerSec">
      <summary>Gets the performance counter representing the number of messages published to the message bus per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.MessageBusMessagesPublishedTotal">
      <summary>Gets the performance counter representing the total number of messages published to the message bus since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.MessageBusSubscribersCurrent">
      <summary>Gets the performance counter representing the current number of subscribers to the message bus.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.MessageBusSubscribersPerSec">
      <summary>Gets the performance counter representing the number of new subscribers to the message bus per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.MessageBusSubscribersTotal">
      <summary>Gets the performance counter representing the total number of subscribers to the message bus since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager.MessageBusTopicsCurrent">
      <summary>Gets the performance counter representing the current number of topics in the message bus.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.IProtectedData"></member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IProtectedData.Protect(System.String,System.String)">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IProtectedData.Unprotect(System.String,System.String)">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.IServerIdManager">
      <summary>Generates a server id</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.IServerIdManager.ServerId">
      <summary>The id of the server.</summary>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier"></member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.Minify(System.String)">
      <summary>Minifies a string in a way that can be reversed by this instance of <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier" />.</summary>
      <returns>A minified representation of the <paramref name="value" /> without the following characters:,|\</returns>
      <param name="value">The string to be minified.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.RemoveUnminified(System.String)">
      <summary>A call to this function indicates that any future attempt to unminify strings that were previously minified from <paramref name="value" /> may be met with a null return value. This provides an opportunity normalize any internal data structures that reference <paramref name="value" />.</summary>
      <param name="value">The string that may have previously have been minified.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.Unminify(System.String)">
      <summary>Reverses a <see cref="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.Minify(System.String)" /> call that was executed at least once previously on this instance of <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier" /> without any subsequent calls to <see cref="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.RemoveUnminified(System.String)" /> sharing the same argument as the <see cref="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.Minify(System.String)" /> call that returned <paramref name="value" />.</summary>
      <returns>The argument of all previous calls to <see cref="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.Minify(System.String)" /> that returned <paramref name="value" />. If every call to <see cref="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.Minify(System.String)" /> on this instance of <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier" /> has never returned <paramref name="value" /> or if the most recent call to <see cref="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.Minify(System.String)" /> that did return <paramref name="value" /> was followed by a call to <see cref="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.RemoveUnminified(System.String)" /> sharing the same argument, <see cref="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.Unminify(System.String)" /> may return null but must not throw.</returns>
      <param name="value">A minified string that was returned by a previous call to <see cref="M:Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier.Minify(System.String)" />.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager">
      <summary>Manages performance counters using Windows performance counters.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.#ctor">
      <summary>Creates a new instance.</summary>
    </member>
    <member name="F:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.CategoryName">
      <summary>The performance counter category name for SignalR counters.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ConnectionMessagesReceivedPerSec">
      <summary>Gets the performance counter representing the number of messages received by connections (server to client) per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ConnectionMessagesReceivedTotal">
      <summary>Gets the performance counter representing the total number of messages received by connections (server to client) since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ConnectionMessagesSentPerSec">
      <summary>Gets the performance counter representing the number of messages sent by connections (client to server) per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ConnectionMessagesSentTotal">
      <summary>Gets the performance counter representing the total number of messages sent by connections (client to server) since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ConnectionsConnected">
      <summary>Gets the performance counter representing the total number of connection Connect events since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ConnectionsCurrent">
      <summary>Gets the performance counter representing the number of connections currently connected.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ConnectionsDisconnected">
      <summary>Gets the performance counter representing the total number of connection Disconnect events since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ConnectionsReconnected">
      <summary>Gets the performance counter representing the total number of connection Reconnect events since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ErrorsAllPerSec">
      <summary>Gets the performance counter representing the number of all errors processed per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ErrorsAllTotal">
      <summary>Gets the performance counter representing the total number of all errors processed since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ErrorsHubInvocationPerSec">
      <summary>Gets the performance counter representing the number of hub invocation errors per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ErrorsHubInvocationTotal">
      <summary>Gets the performance counter representing the total number of hub invocation errors processed since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ErrorsHubResolutionPerSec">
      <summary>Gets the performance counter representing the number of hub resolution errors per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ErrorsHubResolutionTotal">
      <summary>Gets the performance counter representing the total number of hub resolution errors processed since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ErrorsTransportPerSec">
      <summary>Gets the performance counter representing the number of transport errors per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.ErrorsTransportTotal">
      <summary>Gets the performance counter representing the total number of transport errors processed since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.Initialize(System.String,System.Threading.CancellationToken)">
      <summary>Initializes the performance counters.</summary>
      <param name="instanceName">The host instance name.</param>
      <param name="hostShutdownToken">The CancellationToken representing the host shutdown.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.MessageBusAllocatedWorkers">
      <summary>Gets the performance counter representing the number of workers allocated to deliver messages in the message bus.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.MessageBusBusyWorkers">
      <summary>Gets the performance counter representing the number of workers currently busy delivering messages in the message bus.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.MessageBusMessagesPublishedPerSec">
      <summary>Gets the performance counter representing the number of messages published to the message bus per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.MessageBusMessagesPublishedTotal">
      <summary>Gets the performance counter representing the total number of messages published to the message bus since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.MessageBusSubscribersCurrent">
      <summary>Gets the performance counter representing the current number of subscribers to the message bus.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.MessageBusSubscribersPerSec">
      <summary>Gets the performance counter representing the number of new subscribers to the message bus per second.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.MessageBusSubscribersTotal">
      <summary>Gets the performance counter representing the total number of subscribers to the message bus since the application was started.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.PerformanceCounterManager.MessageBusTopicsCurrent">
      <summary>Gets the performance counter representing the current number of topics in the message bus.</summary>
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounter" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.Purposes"></member>
    <member name="F:Microsoft.AspNet.SignalR.Infrastructure.Purposes.ConnectionToken"></member>
    <member name="F:Microsoft.AspNet.SignalR.Infrastructure.Purposes.Groups"></member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.ServerCommandType"></member>
    <member name="F:Microsoft.AspNet.SignalR.Infrastructure.ServerCommandType.RemoveConnection">
      <summary />
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Infrastructure.ServerIdManager">
      <summary>Default <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IServerIdManager" /> implementation.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Infrastructure.ServerIdManager.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Infrastructure.ServerIdManager.ServerId">
      <summary>The id of the server.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Json.IJsonSerializer">
      <summary>Used to serialize and deserialize outgoing/incoming data.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.IJsonSerializer.Parse(System.String,System.Type)">
      <summary>Deserializes the JSON to a .NET object.</summary>
      <returns>The deserialized object from the JSON string.</returns>
      <param name="json">The JSON to deserialize.</param>
      <param name="targetType">The <see cref="T:System.Type" /> of object being deserialized.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.IJsonSerializer.Serialize(System.Object,System.IO.TextWriter)">
      <summary>Serializes the specified object to a <see cref="T:System.IO.TextWriter" />.</summary>
      <param name="value">The object to serialize.</param>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to serialize the object to.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Json.IJsonValue">
      <summary>Represents a JSON value.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.IJsonValue.CanConvertTo(System.Type)">
      <summary>Converts the parameter value to the specified <see cref="T:System.Type" />.</summary>
      <returns>The converted parameter value.</returns>
      <param name="type">The <see cref="T:System.Type" /> to convert the parameter to.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.IJsonValue.ConvertTo(System.Type)">
      <summary>Converts the parameter value to the specified <see cref="T:System.Type" />.</summary>
      <returns>The converted parameter value.</returns>
      <param name="type">The <see cref="T:System.Type" /> to convert the parameter to.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Json.IJsonWritable">
      <summary>Implementations handle their own serialization to JSON.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.IJsonWritable.WriteJson(System.IO.TextWriter)">
      <summary>Serializes itself to JSON via a <see cref="T:System.IO.TextWriter" />.</summary>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> that receives the JSON serialized object.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Json.JsonNetSerializer">
      <summary>Default <see cref="T:Microsoft.AspNet.SignalR.Json.IJsonSerializer" /> implementation over Json.NET.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.JsonNetSerializer.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Json.JsonNetSerializer.#ctor(Newtonsoft.Json.JsonSerializerSettings)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Json.JsonNetSerializer.Parse(System.String,System.Type)">
      <summary>Deserializes the JSON to a .NET object.</summary>
      <returns>The deserialized object from the JSON string.</returns>
      <param name="json">The JSON to deserialize.</param>
      <param name="targetType">The <see cref="T:System.Type" /> of object being deserialized</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.JsonNetSerializer.Serialize(System.Object,System.IO.TextWriter)">
      <summary>Serializes the specified object to a <see cref="T:System.IO.TextWriter" />.</summary>
      <param name="value">The object to serialize.</param>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to serialize the object to.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Json.JsonSerializerExtensions">
      <summary>Extensions for <see cref="T:Microsoft.AspNet.SignalR.Json.IJsonSerializer" />.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.JsonSerializerExtensions.Parse``1(Microsoft.AspNet.SignalR.Json.IJsonSerializer,System.String)">
      <summary>Deserializes the JSON to a .NET object.</summary>
      <returns>The deserialized object from the JSON string.</returns>
      <param name="serializer">The serializer.</param>
      <param name="json">The JSON to deserialize.</param>
      <typeparam name="T">The <see cref="T:System.Type" /> of object being deserialized.</typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.JsonSerializerExtensions.Stringify(Microsoft.AspNet.SignalR.Json.IJsonSerializer,System.Object)">
      <summary>Serializes the specified object to a JSON string.</summary>
      <returns>A JSON string representation of the object.</returns>
      <param name="serializer">The serializer.</param>
      <param name="value">The object to serialize.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Json.JsonUtility">
      <summary>Helper class for common JSON operations.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.JsonUtility.CamelCase(System.String)">
      <summary>Converts the specified name to camel case.</summary>
      <returns>A camel cased version of the specified name.</returns>
      <param name="name">The name to convert.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Json.JsonUtility.CreateJsonpCallback(System.String,System.String)">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Json.JsonUtility.JavaScriptMimeType"></member>
    <member name="P:Microsoft.AspNet.SignalR.Json.JsonUtility.JsonMimeType"></member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.Command"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Command.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Command.CommandType">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Messaging.CommandType" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Command.Id">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Command.Value">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Command.WaitForAck">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.CommandType"></member>
    <member name="F:Microsoft.AspNet.SignalR.Messaging.CommandType.AddToGroup">
      <summary />
    </member>
    <member name="F:Microsoft.AspNet.SignalR.Messaging.CommandType.RemoveFromGroup">
      <summary />
    </member>
    <member name="F:Microsoft.AspNet.SignalR.Messaging.CommandType.Disconnect">
      <summary />
    </member>
    <member name="F:Microsoft.AspNet.SignalR.Messaging.CommandType.Abort">
      <summary />
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.IMessageBus"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.IMessageBus.Publish(Microsoft.AspNet.SignalR.Messaging.Message)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.IMessageBus.Subscribe(Microsoft.AspNet.SignalR.Messaging.ISubscriber,System.String,System.Func{Microsoft.AspNet.SignalR.Messaging.MessageResult,System.Threading.Tasks.Task{System.Boolean}},System.Int32)">
      <returns>Returns <see cref="T:System.IDisposable" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.IndexedDictionary`2">
      <typeparam name="TKey"></typeparam>
      <typeparam name="TValue"></typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.IndexedDictionary`2.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.IndexedDictionary`2.Add(`0,`1)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.IndexedDictionary`2.Item(`0)">
      <returns>Returns <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.IndexedDictionary`2.MaxKey">
      <returns>Returns <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.IndexedDictionary`2.MinKey">
      <returns>Returns <see cref="T:System.Collections.Generic.LinkedListNode`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.ISubscriber"></member>
    <member name="E:Microsoft.AspNet.SignalR.Messaging.ISubscriber.EventKeyAdded"></member>
    <member name="E:Microsoft.AspNet.SignalR.Messaging.ISubscriber.EventKeyRemoved"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.ISubscriber.EventKeys">
      <returns>Returns <see cref="T:System.Collections.Generic.IEnumerable`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.ISubscriber.GetCursor">
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.ISubscriber.Identity">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.ISubscription"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.ISubscription.Identity">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ISubscription.SetQueued">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ISubscription.UnsetQueued">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ISubscription.Work">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.LocalEventKeyInfo"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.LocalEventKeyInfo.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.LocalEventKeyInfo.Count">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.LocalEventKeyInfo.MinLocal">
      <returns>Returns <see cref="T:System.UInt64" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.LocalEventKeyInfo.Store">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Messaging.MessageStore`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.Message"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Message.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Message.#ctor(System.String,System.String,System.String)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Message.CommandId">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Message.Filter">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Message.IsAck">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Message.IsCommand">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Message.Key">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Message.Source">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Message.Value">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Message.WaitForAck">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.MessageBroker">
      <summary>This class is the main coordinator. It schedules work to be done for a particular subscription and has an algorithm for choosing a number of workers (thread pool threads), to handle the scheduled work.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBroker.#ctor(Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBroker.#ctor(Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager,System.Int32,System.Int32)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageBroker.AllocatedWorkers">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageBroker.BusyWorkers">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBroker.Dispose"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBroker.Dispose(System.Boolean)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBroker.Schedule(Microsoft.AspNet.SignalR.Messaging.ISubscription)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageBroker.Trace">
      <returns>Returns <see cref="T:System.Diagnostics.TraceSource" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.MessageBus"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBus.#ctor(Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBus.#ctor(Microsoft.AspNet.SignalR.Infrastructure.IStringMinifier,Microsoft.AspNet.SignalR.Tracing.ITraceManager,Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager,Microsoft.AspNet.SignalR.Configuration.IConfigurationManager,System.Int32)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageBus.AllocatedWorkers">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageBus.BusyWorkers">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageBus.Counters">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBus.CreateSubscription(Microsoft.AspNet.SignalR.Messaging.ISubscriber,System.String,System.Func{Microsoft.AspNet.SignalR.Messaging.MessageResult,System.Threading.Tasks.Task{System.Boolean}},System.Int32)">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Messaging.Subscription" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBus.CreateTopic(System.String)">
      <summary>Creates a topic for the specified key.</summary>
      <returns>A <see cref="T:Microsoft.AspNet.SignalR.Messaging.Topic" /> for the specified key.</returns>
      <param name="key">The key to create the topic for.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBus.Dispose"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBus.Dispose(System.Boolean)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBus.Publish(Microsoft.AspNet.SignalR.Messaging.Message)">
      <summary>Publishes a new message to the specified event on the bus.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="message">The message to publish.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBus.Save(Microsoft.AspNet.SignalR.Messaging.Message)">
      <returns>Returns <see cref="T:System.UInt64" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBus.ScheduleEvent(System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBus.Subscribe(Microsoft.AspNet.SignalR.Messaging.ISubscriber,System.String,System.Func{Microsoft.AspNet.SignalR.Messaging.MessageResult,System.Threading.Tasks.Task{System.Boolean}},System.Int32)">
      <returns>Returns <see cref="T:System.IDisposable" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageBus.Topics">
      <returns>Returns <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.MessageBusExtensions"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBusExtensions.Enumerate(System.Collections.Generic.IList{System.ArraySegment{Microsoft.AspNet.SignalR.Messaging.Message}},System.Action{Microsoft.AspNet.SignalR.Messaging.Message})"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBusExtensions.Enumerate(System.Collections.Generic.IList{System.ArraySegment{Microsoft.AspNet.SignalR.Messaging.Message}},System.Func{Microsoft.AspNet.SignalR.Messaging.Message,System.Boolean},System.Action{Microsoft.AspNet.SignalR.Messaging.Message})"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageBusExtensions.Publish(Microsoft.AspNet.SignalR.Messaging.IMessageBus,System.String,System.String,System.String)"></member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.MessageResult"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageResult.#ctor(System.Boolean)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageResult.#ctor(System.Collections.Generic.IList{System.ArraySegment{Microsoft.AspNet.SignalR.Messaging.Message}},System.Int32)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageResult.Messages">
      <summary>Gets an <see cref="T:IList{Message}" /> associated with the result.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.IList`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageResult.Terminal">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="F:Microsoft.AspNet.SignalR.Messaging.MessageResult.TerminalMessage"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageResult.TotalCount">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.MessageStore`1">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageStore`1.#ctor(System.UInt32)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageStore`1.#ctor(System.UInt32,System.UInt32)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageStore`1.Add(`0)">
      <returns>Returns <see cref="T:System.UInt64" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageStore`1.GetMessageCount">
      <returns>Returns <see cref="T:System.UInt64" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageStore`1.GetMessages(System.UInt64,System.Int32)">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Messaging.MessageStoreResult`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.MessageStoreResult`1">
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.MessageStoreResult`1.#ctor(System.UInt64,System.ArraySegment{`0},System.Boolean)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageStoreResult`1.FirstMessageId">
      <returns>Returns <see cref="T:System.UInt64" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageStoreResult`1.HasMoreData">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.MessageStoreResult`1.Messages">
      <returns>Returns <see cref="T:System.ArraySegment`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.ScaleoutMapping"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ScaleoutMapping.#ctor(System.Collections.Generic.IDictionary{System.String,Microsoft.AspNet.SignalR.Messaging.LocalEventKeyInfo})"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.ScaleoutMapping.EventKeyMappings">
      <returns>Returns <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.ScaleoutMessageBus">
      <summary>Sends messages to the backplane.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ScaleoutMessageBus.#ctor(Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ScaleoutMessageBus.CreateSubscription(Microsoft.AspNet.SignalR.Messaging.ISubscriber,System.String,System.Func{Microsoft.AspNet.SignalR.Messaging.MessageResult,System.Threading.Tasks.Task{System.Boolean}},System.Int32)">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Messaging.Subscription" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ScaleoutMessageBus.OnReceived(System.String,System.UInt64,System.Collections.Generic.IList{Microsoft.AspNet.SignalR.Messaging.Message})">
      <summary>Invoked when a payload is received from the backplane. There should only be one active call at any time.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
      <param name="streamId">The ID of the stream.</param>
      <param name="id">The ID of the payload within that stream.</param>
      <param name="messages">List of messages associated.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ScaleoutMessageBus.Publish(Microsoft.AspNet.SignalR.Messaging.Message)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ScaleoutMessageBus.Send(System.Collections.Generic.IList{Microsoft.AspNet.SignalR.Messaging.Message})">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.ScaleoutSubscription"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ScaleoutSubscription.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String},System.String,System.Collections.Concurrent.ConcurrentDictionary{System.String,Microsoft.AspNet.SignalR.Messaging.IndexedDictionary{System.UInt64,Microsoft.AspNet.SignalR.Messaging.ScaleoutMapping}},System.Func{Microsoft.AspNet.SignalR.Messaging.MessageResult,System.Threading.Tasks.Task{System.Boolean}},System.Int32,Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ScaleoutSubscription.BeforeInvoke(System.Object)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ScaleoutSubscription.GetCursor">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.ScaleoutSubscription.PerformWork(System.Collections.Generic.IList{System.ArraySegment{Microsoft.AspNet.SignalR.Messaging.Message}},System.Int32@,System.Object@)"></member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.Subscription"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String},System.Func{Microsoft.AspNet.SignalR.Messaging.MessageResult,System.Threading.Tasks.Task{System.Boolean}},System.Int32,Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.AddEvent(System.String,Microsoft.AspNet.SignalR.Messaging.Topic)">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.BeforeInvoke(System.Object)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.Dispose"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.Dispose(System.Boolean)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Subscription.DisposedCallback">
      <returns>Returns <see cref="T:System.Action" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Subscription.EventKeys">
      <returns>Returns <see cref="T:System.Collections.Generic.HashSet`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.GetCursor">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Subscription.Identity">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.Invoke(Microsoft.AspNet.SignalR.Messaging.MessageResult)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Subscription.MaxMessages">
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.PerformWork(System.Collections.Generic.IList{System.ArraySegment{Microsoft.AspNet.SignalR.Messaging.Message}},System.Int32@,System.Object@)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.RemoveEvent(System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.SetEventTopic(System.String,Microsoft.AspNet.SignalR.Messaging.Topic)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.SetQueued">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.UnsetQueued">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Subscription.Work">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.Topic"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Topic.#ctor(System.UInt32,System.TimeSpan)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Topic.AddSubscription(Microsoft.AspNet.SignalR.Messaging.ISubscription)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Topic.IsExpired">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Topic.LastUsed">
      <returns>Returns <see cref="T:System.DateTime" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.Topic.RemoveSubscription(Microsoft.AspNet.SignalR.Messaging.ISubscription)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Topic.Store">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Messaging.MessageStore`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Topic.SubscriptionLock">
      <returns>Returns <see cref="T:System.Threading.ReaderWriterLockSlim" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.Topic.Subscriptions">
      <returns>Returns <see cref="T:System.Collections.Generic.IList`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Messaging.TopicLookup"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.TopicLookup.#ctor"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.TopicLookup.Clear"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.TopicLookup.ContainsKey(System.String)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.TopicLookup.Count"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.TopicLookup.GetEnumerator"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.TopicLookup.GetOrAdd(System.String,System.Func{System.String,Microsoft.AspNet.SignalR.Messaging.Topic})"></member>
    <member name="P:Microsoft.AspNet.SignalR.Messaging.TopicLookup.Item(System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.TopicLookup.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.TopicLookup.TryGetValue(System.String,Microsoft.AspNet.SignalR.Messaging.Topic@)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Messaging.TopicLookup.TryRemove(System.String)"></member>
    <member name="T:Microsoft.AspNet.SignalR.Tracing.ITraceManager"></member>
    <member name="P:Microsoft.AspNet.SignalR.Tracing.ITraceManager.Item(System.String)">
      <returns>Returns <see cref="T:System.Diagnostics.TraceSource" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Tracing.ITraceManager.Switch">
      <returns>Returns <see cref="T:System.Diagnostics.SourceSwitch" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Tracing.TraceManager"></member>
    <member name="M:Microsoft.AspNet.SignalR.Tracing.TraceManager.#ctor"></member>
    <member name="P:Microsoft.AspNet.SignalR.Tracing.TraceManager.Item(System.String)">
      <returns>Returns <see cref="T:System.Diagnostics.TraceSource" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Tracing.TraceManager.Switch">
      <returns>Returns <see cref="T:System.Diagnostics.SourceSwitch" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.ForeverFrameTransport"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverFrameTransport.#ctor(Microsoft.AspNet.SignalR.Hosting.HostContext,Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverFrameTransport.InitializeResponse(Microsoft.AspNet.SignalR.Transports.ITransportConnection)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverFrameTransport.KeepAlive">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ForeverFrameTransport.OutputWriter">
      <returns>Returns <see cref="T:System.IO.TextWriter" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverFrameTransport.Send(Microsoft.AspNet.SignalR.Transports.PersistentResponse)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.ForeverTransport"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.#ctor(Microsoft.AspNet.SignalR.Hosting.HostContext,Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.#ctor(Microsoft.AspNet.SignalR.Hosting.HostContext,Microsoft.AspNet.SignalR.Json.IJsonSerializer,Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat,Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager,Microsoft.AspNet.SignalR.Tracing.ITraceManager)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ForeverTransport.Connected">
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.EnqueueOperation(System.Func{System.Threading.Tasks.Task})">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.InitializePersistentState"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.InitializeResponse(Microsoft.AspNet.SignalR.Transports.ITransportConnection)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ForeverTransport.JsonSerializer">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Json.IJsonSerializer" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ForeverTransport.LastMessageId">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.OnSending(System.String)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.OnSendingResponse(Microsoft.AspNet.SignalR.Transports.PersistentResponse)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.ProcessRequest(Microsoft.AspNet.SignalR.Transports.ITransportConnection)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.ProcessRequestCore(Microsoft.AspNet.SignalR.Transports.ITransportConnection)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ForeverTransport.Received">
      <returns>Returns <see cref="T:System.Func`2" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ForeverTransport.Reconnected">
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.ReleaseRequest"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.Send(Microsoft.AspNet.SignalR.Transports.PersistentResponse)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ForeverTransport.Send(System.Object)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ForeverTransport.TransportConnected">
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.ITrackingConnection">
      <summary>Represents a connection that can be tracked by an <see cref="T:Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat" /> .</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.CancellationToken">
      <summary>Gets a cancellation token that represents the connection’s lifetime.</summary>
      <returns>Returns <see cref="T:System.Threading.CancellationToken" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.ConnectionId">
      <summary>Gets the ID of the connection.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.Disconnect">
      <summary>Causes the connection to disconnect.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.DisconnectThreshold">
      <summary>Gets a value indicating the amount of time to wait after the connection dies before firing the disconnecting the connection.</summary>
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.End">
      <summary>Kills the connection.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.IsAlive">
      <summary>Gets a value that represents if the connection is alive.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.IsTimedOut">
      <summary>Gets a value that represents if the connection is timed out.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.KeepAlive">
      <summary>Sends a keep alive ping over the connection.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.ReleaseRequest">
      <summary>Releases the http request associated with the connection (if any).</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.SupportsKeepAlive">
      <summary>Gets a value that represents if the connection supports keep alive.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.Timeout">
      <summary>Causes the connection to timeout.</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITrackingConnection.Url">
      <summary>Gets the uri of the connection.</summary>
      <returns>Returns <see cref="T:System.Uri" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.ITransport">
      <summary>Represents a transport that communicates</summary>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITransport.Connected">
      <summary>Gets or sets a callback that is invoked when the initial connection connects to the transport.</summary>
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITransport.ConnectionId">
      <summary>Gets or sets the connection ID for the transport.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITransport.Disconnected">
      <summary>Gets or sets a callback that is invoked when the transport disconnects.</summary>
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransport.ProcessRequest(Microsoft.AspNet.SignalR.Transports.ITransportConnection)">
      <summary>Processes the specified <see cref="T:Microsoft.AspNet.SignalR.Transports.ITransportConnection" /> for this transport.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that completes when the transport has finished processing the connection.</returns>
      <param name="connection">The <see cref="T:Microsoft.AspNet.SignalR.Transports.ITransportConnection" /> to process.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITransport.Received">
      <summary>Gets or sets a callback that is invoked when the transport receives data.</summary>
      <returns>Returns <see cref="T:System.Func`2" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITransport.Reconnected">
      <summary>Gets or sets a callback that is invoked when the transport reconnects.</summary>
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransport.Send(System.Object)">
      <summary>Sends data over the transport.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> that completes when the send is complete.</returns>
      <param name="value">The value to be sent.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.ITransport.TransportConnected">
      <summary>Gets or sets a callback that is invoked when the transport connects.</summary>
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.ITransportConnection"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransportConnection.Receive(System.String,System.Func{Microsoft.AspNet.SignalR.Transports.PersistentResponse,System.Threading.Tasks.Task{System.Boolean}},System.Int32)">
      <returns>Returns <see cref="T:System.IDisposable" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransportConnection.Receive(System.String,System.Threading.CancellationToken,System.Int32)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransportConnection.Send(Microsoft.AspNet.SignalR.ConnectionMessage)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat">
      <summary>Manages tracking the state of connections.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat.AddConnection(Microsoft.AspNet.SignalR.Transports.ITrackingConnection)">
      <summary>Adds a new connection to the list of tracked connections.</summary>
      <param name="connection">The connection to be added.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat.GetConnections">
      <summary>Gets a list of connections being tracked.</summary>
      <returns>A list of connections.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat.MarkConnection(Microsoft.AspNet.SignalR.Transports.ITrackingConnection)">
      <summary>Marks an existing connection as active.</summary>
      <param name="connection">The connection to mark.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat.RemoveConnection(Microsoft.AspNet.SignalR.Transports.ITrackingConnection)">
      <summary>Removes a connection from the list of tracked connections.</summary>
      <param name="connection">The connection to remove.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.ITransportManager">
      <summary>Manages the transports for connections.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransportManager.GetTransport(Microsoft.AspNet.SignalR.Hosting.HostContext)">
      <summary>Gets the specified transport for the specified <see cref="T:Microsoft.AspNet.SignalR.Hosting.HostContext" />.</summary>
      <returns>The <see cref="T:Microsoft.AspNet.SignalR.Transports.ITransport" /> for the specified <see cref="T:Microsoft.AspNet.SignalR.Hosting.HostContext" />.</returns>
      <param name="hostContext">The <see cref="T:Microsoft.AspNet.SignalR.Hosting.HostContext" /> for the current request.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ITransportManager.SupportsTransport(System.String)">
      <summary>Determines whether the specified transport is supported.</summary>
      <returns>True if the transport is supported, otherwise False.</returns>
      <param name="transportName">The name of the transport to test.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.LongPollingTransport"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.#ctor(Microsoft.AspNet.SignalR.Hosting.HostContext,Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.#ctor(Microsoft.AspNet.SignalR.Hosting.HostContext,Microsoft.AspNet.SignalR.Json.IJsonSerializer,Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat,Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager,Microsoft.AspNet.SignalR.Tracing.ITraceManager)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.Connected">
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.DisconnectThreshold">
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.IsConnectRequest">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.LongPollDelay">
      <summary>Gets or sets the number of milliseconds to tell the browser to wait before reestablishing a long poll connection after data is sent from the server. Defaults to 0.</summary>
      <returns>Returns <see cref="T:System.Int64" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.ProcessRequest(Microsoft.AspNet.SignalR.Transports.ITransportConnection)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.Received">
      <returns>Returns <see cref="T:System.Func`2" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.Reconnected">
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.Send(Microsoft.AspNet.SignalR.Transports.PersistentResponse)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.Send(System.Object)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.SupportsKeepAlive">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.LongPollingTransport.TransportConnected">
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.PersistentResponse">
      <summary>Represents a response to a connection.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.PersistentResponse.#ctor">
      <summary>Creates a new instance of <see cref="T:Microsoft.AspNet.SignalR.Transports.PersistentResponse" />.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.PersistentResponse.#ctor(System.Func{Microsoft.AspNet.SignalR.Messaging.Message,System.Boolean})">
      <summary>Creates a new instance of <see cref="T:Microsoft.AspNet.SignalR.Transports.PersistentResponse" />.</summary>
      <param name="exclude">A filter that determines whether messages should be written to the client.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.PersistentResponse.Aborted">
      <summary>Gets or sets a value that indicates whether the connection was forcibly closed.</summary>
      <returns>true if the connection was forcibly closed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.PersistentResponse.Disconnect">
      <summary>Gets or sets a value that indicates whether the connection receives a disconnect command.</summary>
      <returns>true if the connection receives a disconnect command; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.PersistentResponse.ExcludeFilter">
      <summary>Gets or sets a filter that determines whether messages should be written to the client.</summary>
      <returns>A filter that determines whether messages should be written to the client.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.PersistentResponse.GroupsToken">
      <summary>   Signed token representing the list of groups. Updates on change.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.PersistentResponse.LongPollDelay">
      <summary>Gets or sets the time the long polling client should wait before reestablishing a connection if no data is received.</summary>
      <returns>The time the long polling client should wait before reestablishing a connection if no data is received.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.PersistentResponse.MessageId">
      <summary>Gets or sets the id of the last message in the connection received.</summary>
      <returns>The id of the last message in the connection received.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.PersistentResponse.Messages">
      <summary>Gets or sets the list of messages to be sent to the receiving connection.</summary>
      <returns>The list of messages to be sent to the receiving connection.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.PersistentResponse.Microsoft#AspNet#SignalR#Json#IJsonWritable#WriteJson(System.IO.TextWriter)">
      <summary>Serializes only the necessary components of the <see cref="T:Microsoft.AspNet.SignalR.Transports.PersistentResponse" /> to JSON using Json.NET’s JsonTextWriter to improve performance.</summary>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> that receives the JSON serialization.</param>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.PersistentResponse.TimedOut">
      <summary>Gets or sets a value that indicates whether the connection timed out.</summary>
      <returns>true if the connection timed out; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.PersistentResponse.TotalCount">
      <summary>Gets or sets the total count of the messages sent the receiving connection.</summary>
      <returns>The total count of the messages sent the receiving connection.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.ServerSentEventsTransport"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ServerSentEventsTransport.#ctor(Microsoft.AspNet.SignalR.Hosting.HostContext,Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ServerSentEventsTransport.InitializeResponse(Microsoft.AspNet.SignalR.Transports.ITransportConnection)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ServerSentEventsTransport.KeepAlive">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.ServerSentEventsTransport.Send(Microsoft.AspNet.SignalR.Transports.PersistentResponse)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.#ctor(Microsoft.AspNet.SignalR.Hosting.HostContext,Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat,Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager,Microsoft.AspNet.SignalR.Tracing.ITraceManager)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.CancellationToken">
      <returns>Returns <see cref="T:System.Threading.CancellationToken" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.Completed">
      <returns>Returns <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.CompleteRequest"></member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.Connection">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Transports.ITransportConnection" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.ConnectionEndToken">
      <returns>Returns <see cref="T:System.Threading.CancellationToken" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.ConnectionId">
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.Context">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Hosting.HostContext" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.Disconnect">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.Disconnected">
      <returns>Returns <see cref="T:System.Func`1" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.DisconnectThreshold">
      <returns>Returns <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.End"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.EnqueueOperation(System.Func{System.Threading.Tasks.Task})">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.Heartbeat">
      <returns>Returns <see cref="T:Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.IncrementErrorCounters(System.Exception)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.InitializePersistentState"></member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.IsAbortRequest">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.IsAlive"></member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.IsConnectRequest">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.IsTimedOut">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.KeepAlive">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.Microsoft#AspNet#SignalR#Transports#ITrackingConnection#ReleaseRequest"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.OnDisconnect">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.OutputWriter">
      <returns>Returns <see cref="T:System.IO.TextWriter" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.ReleaseRequest"></member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.SupportsKeepAlive">
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.Timeout"></member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.Trace">
      <returns>Returns <see cref="T:System.Diagnostics.TraceSource" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.TransportDisconnectBase.Url">
      <returns>Returns <see cref="T:System.Uri" />.</returns>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.TransportHeartbeat">
      <summary>Default implementation of <see cref="T:Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat" /> .</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportHeartbeat.#ctor(Microsoft.AspNet.SignalR.IDependencyResolver)">
      <summary>Initializes and instance of the <see cref="T:Microsoft.AspNet.SignalR.Transports.TransportHeartbeat" /> class.</summary>
      <param name="resolver">The <see cref="T:Microsoft.AspNet.SignalR.IDependencyResolver" /> .</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportHeartbeat.AddConnection(Microsoft.AspNet.SignalR.Transports.ITrackingConnection)">
      <summary>Adds a new connection to the list of tracked connections.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.</returns>
      <param name="connection">The connection to be added.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportHeartbeat.Dispose"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportHeartbeat.Dispose(System.Boolean)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportHeartbeat.GetConnections">
      <summary>Gets a list of connections being tracked.</summary>
      <returns>A list of connections.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportHeartbeat.MarkConnection(Microsoft.AspNet.SignalR.Transports.ITrackingConnection)">
      <summary>Marks an existing connection as active.</summary>
      <param name="connection">The connection to mark.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportHeartbeat.RemoveConnection(Microsoft.AspNet.SignalR.Transports.ITrackingConnection)">
      <summary>Removes a connection from the list of tracked connections.</summary>
      <param name="connection">The connection to remove.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.TransportManager">
      <summary>The default <see cref="T:Microsoft.AspNet.SignalR.Transports.ITransportManager" /> implementation.</summary>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportManager.#ctor(Microsoft.AspNet.SignalR.IDependencyResolver)">
      <summary>Initializes a new instance of <see cref="T:Microsoft.AspNet.SignalR.Transports.TransportManager" /> class.</summary>
      <param name="resolver">The default <see cref="T:Microsoft.AspNet.SignalR.IDependencyResolver" /> .</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportManager.GetTransport(Microsoft.AspNet.SignalR.Hosting.HostContext)">
      <summary>Gets the specified transport for the specified <see cref="T:Microsoft.AspNet.SignalR.Hosting.HostContext" />.</summary>
      <returns>The <see cref="T:Microsoft.AspNet.SignalR.Transports.ITransport" /> for the specified <see cref="T:Microsoft.AspNet.SignalR.Hosting.HostContext" />.</returns>
      <param name="hostContext">The <see cref="T:Microsoft.AspNet.SignalR.Hosting.HostContext" /> for the current request.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportManager.Register(System.String,System.Func{Microsoft.AspNet.SignalR.Hosting.HostContext,Microsoft.AspNet.SignalR.Transports.ITransport})">
      <summary>Adds a new transport to the list of supported transports.</summary>
      <param name="transportName">The specified transport.</param>
      <param name="transportFactory">The factory method for the specified transport.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportManager.Remove(System.String)">
      <summary>Removes a transport from the list of supported transports.</summary>
      <param name="transportName">The specified transport.</param>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.TransportManager.SupportsTransport(System.String)">
      <summary>Determines whether the specified transport is supported.</summary>
      <returns>True if the transport is supported, otherwise False.</returns>
      <param name="transportName">The name of the transport to test.</param>
    </member>
    <member name="T:Microsoft.AspNet.SignalR.Transports.WebSocketTransport"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.WebSocketTransport.#ctor(Microsoft.AspNet.SignalR.Hosting.HostContext,Microsoft.AspNet.SignalR.IDependencyResolver)"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.WebSocketTransport.#ctor(Microsoft.AspNet.SignalR.Hosting.HostContext,Microsoft.AspNet.SignalR.Json.IJsonSerializer,Microsoft.AspNet.SignalR.Transports.ITransportHeartbeat,Microsoft.AspNet.SignalR.Infrastructure.IPerformanceCounterManager,Microsoft.AspNet.SignalR.Tracing.ITraceManager)"></member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.WebSocketTransport.CancellationToken">
      <returns>Returns <see cref="T:System.Threading.CancellationToken" />.</returns>
    </member>
    <member name="P:Microsoft.AspNet.SignalR.Transports.WebSocketTransport.IsAlive"></member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.WebSocketTransport.KeepAlive">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.WebSocketTransport.ProcessRequest(Microsoft.AspNet.SignalR.Transports.ITransportConnection)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.WebSocketTransport.Send(Microsoft.AspNet.SignalR.Transports.PersistentResponse)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="M:Microsoft.AspNet.SignalR.Transports.WebSocketTransport.Send(System.Object)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
  </members>
</doc>