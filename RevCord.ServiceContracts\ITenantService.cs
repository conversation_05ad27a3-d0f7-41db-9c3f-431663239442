﻿using RevCord.DataContracts.Messages;
using RevCord.DataContracts.TenantEntities;
using RevCord.DataContracts.ViewModelEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.ServiceContracts
{
    public interface ITenantService
    {
        TenantResponse GetColumnModel( int tenantId, int userNum, bool isOnlyIQ3ModeEnabled);
        TenantResponse GetColumnModelByWhereClause(TenantRequest request);
        bool ResetUserColumnModel(TenantRequest request);
        TenantResponse UpdateColumnModel(TenantRequest request);

        TenantResponse GetConfigurations(int tenantId);
        TenantResponse GetConfigurationsByWhereClause(TenantRequest request);
        TenantResponse UpdateConfigurations(TenantRequest request);

        bool AuthenticateEmailFromLocalDB(string email);
        bool AuthenticateEmail(string email, out int tenantId);
        bool AuthenticateEmail(string email, out int tenantId, out string connection);
        string GetDatabaseConnection(int tenantId);

        List<Tenant> AuthenticateEmail(string email);
        List<Tenant> GetAllTenants();
        List<TenantUser> FetchIQ3TenantUsers(int tenantId);
        List<CustomMarkersData> FetchIQ3TenantUserMarkers(int tenantId, int userNum);
        TenantResponse ReorderColumns(TenantRequest request);
        TenantResponse GetTenantInformation(int tenantId, out int noOfADUsers);
    }
}
