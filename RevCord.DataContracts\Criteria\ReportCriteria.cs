﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.Criteria
{
    public class ReportCriteria
    {

        public List<RecorderGroupExtension> RecorderAudioGroupExtensions { get; set; }
        public List<RecorderGroupExtension> RecorderInquireGroupExtensions { get; set; }
        public List<RecorderGroupExtension> RecorderMdGroupExtensions { get; set; }
        //int userId, DateTime startDate, DateTime endDate, int pageSize, int pageIndex, int tenantId
        public int UserId { get; set; }
        public string UserEmail { get; set; }
        public bool IsAdmin { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int PageSize { get; set; }
        public int PageIndex { get; set; }
        public int TenantId { get; set; }
        public TenantAppType TenantAppType { get; set; }
        public bool IsOpenAccessPageCall { get; set; }
    }

    public class RecorderGroupExtension
    {
        public int RecorderId { get; set; }
        public CategoryGroupExtension AudioGroupExtension { get; set; }
        public CategoryGroupExtension InquireGroupExtension { get; set; } //arivu
        public CategoryGroupExtension MdGroupExtension { get; set; } //arivu

    }
}
