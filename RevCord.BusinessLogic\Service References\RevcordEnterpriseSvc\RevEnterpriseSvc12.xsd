<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd6" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.MessageBase" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" />
  <xs:complexType name="CallInfoSearchCriteriaDTO">
    <xs:sequence>
      <xs:element minOccurs="0" name="AdvanceSearchData" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Criteria" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DurationStr" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="EndDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="EndDuration" type="ser:duration" />
      <xs:element minOccurs="0" name="EndTime" type="ser:duration" />
      <xs:element minOccurs="0" name="HasShiftRest" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsPercentage" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRandom" type="xs:boolean" />
      <xs:element minOccurs="0" name="LoginTime" type="xs:dateTime" />
      <xs:element minOccurs="0" name="NoOfRandomCalls" type="xs:int" />
      <xs:element minOccurs="0" name="SearchRest" type="xs:int" />
      <xs:element minOccurs="0" name="SearchType" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="StartDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="StartDuration" type="ser:duration" />
      <xs:element minOccurs="0" name="StartTime" type="ser:duration" />
      <xs:element minOccurs="0" name="StartTimeString" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallInfoSearchCriteriaDTO" nillable="true" type="tns:CallInfoSearchCriteriaDTO" />
  <xs:complexType name="DALMediaResponse">
    <xs:sequence>
      <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.MessageBase" minOccurs="0" name="Acknowledge" type="q1:AcknowledgeType" />
      <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="ListOfMedias" nillable="true" type="q2:ArrayOfMediaInfo" />
      <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ProcessingTime" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TotalPages" type="xs:int" />
      <xs:element minOccurs="0" name="TotalRecords" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DALMediaResponse" nillable="true" type="tns:DALMediaResponse" />
  <xs:complexType name="ArrayOfTreeViewDataDTO">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="TreeViewDataDTO" nillable="true" type="tns:TreeViewDataDTO" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfTreeViewDataDTO" nillable="true" type="tns:ArrayOfTreeViewDataDTO" />
  <xs:complexType name="TreeViewDataDTO">
    <xs:sequence>
      <xs:element name="_x003C_Depth_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_IsGroup_x003E_k__BackingField" type="xs:boolean" />
      <xs:element name="_x003C_MenuType_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_NodeCaption_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_NodeId_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_Param1_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_Param2_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_Param3_x003E_k__BackingField" nillable="true" type="xs:string" />
      <xs:element name="_x003C_ParentNodeId_x003E_k__BackingField" type="xs:int" />
      <xs:element name="_x003C_ViewType_x003E_k__BackingField" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TreeViewDataDTO" nillable="true" type="tns:TreeViewDataDTO" />
  <xs:complexType name="EnterpriseGroupRightDTO">
    <xs:sequence>
      <xs:element minOccurs="0" name="EnterpriseRecorderGroups" nillable="true" type="tns:ArrayOfEnterpriseRecorderGroup" />
      <xs:element minOccurs="0" name="Permission" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EnterpriseGroupRightDTO" nillable="true" type="tns:EnterpriseGroupRightDTO" />
  <xs:complexType name="ArrayOfEnterpriseRecorderGroup">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EnterpriseRecorderGroup" nillable="true" type="tns:EnterpriseRecorderGroup" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEnterpriseRecorderGroup" nillable="true" type="tns:ArrayOfEnterpriseRecorderGroup" />
  <xs:complexType name="EnterpriseRecorderGroup">
    <xs:sequence>
      <xs:element minOccurs="0" name="GroupId" type="xs:int" />
      <xs:element minOccurs="0" name="IsActive" type="xs:boolean" />
      <xs:element minOccurs="0" name="RecId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EnterpriseRecorderGroup" nillable="true" type="tns:EnterpriseRecorderGroup" />
  <xs:complexType name="ArrayOfEnterpriseNodeDTO">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EnterpriseNodeDTO" nillable="true" type="tns:EnterpriseNodeDTO" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEnterpriseNodeDTO" nillable="true" type="tns:ArrayOfEnterpriseNodeDTO" />
  <xs:complexType name="EnterpriseNodeDTO">
    <xs:sequence>
      <xs:element minOccurs="0" name="DbStatus" type="xs:boolean" />
      <xs:element minOccurs="0" name="ModuleBit" type="tns:ModuleBit" />
      <xs:element minOccurs="0" name="NodeId" type="xs:int" />
      <xs:element minOccurs="0" name="RecId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EnterpriseNodeDTO" nillable="true" type="tns:EnterpriseNodeDTO" />
  <xs:simpleType name="ModuleBit">
    <xs:annotation>
      <xs:appinfo>
        <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
      </xs:appinfo>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="Unknown" />
      <xs:enumeration value="Setup" />
      <xs:enumeration value="IRLite" />
      <xs:enumeration value="Monitor" />
      <xs:enumeration value="Search" />
      <xs:enumeration value="Evaluation" />
      <xs:enumeration value="Dashboard" />
      <xs:enumeration value="QAEvaluationReports" />
      <xs:enumeration value="AdvancedReports" />
      <xs:enumeration value="IRFull" />
      <xs:enumeration value="SaveNEmail" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="ModuleBit" nillable="true" type="tns:ModuleBit" />
  <xs:complexType name="ArrayOfCallEvaluationDTO">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="CallEvaluationDTO" nillable="true" type="tns:CallEvaluationDTO" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfCallEvaluationDTO" nillable="true" type="tns:ArrayOfCallEvaluationDTO" />
  <xs:complexType name="CallEvaluationDTO">
    <xs:sequence>
      <xs:element minOccurs="0" name="AnsweredQuestions" type="xs:int" />
      <xs:element minOccurs="0" name="AppUserId" type="xs:int" />
      <xs:element minOccurs="0" name="AssociatedAgent" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="AssociatedAgentCode" type="xs:int" />
      <xs:element minOccurs="0" name="AssociatedAgentEmail" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BookmarkXML" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallEvaluationCode" type="xs:int" />
      <xs:element minOccurs="0" name="CallEvaluationId" type="xs:int" />
      <xs:element minOccurs="0" name="CallEvaluationRevSyncId" type="xs:int" />
      <xs:element minOccurs="0" name="CallID" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallType" type="xs:int" />
      <xs:element minOccurs="0" name="CallType_inq" type="xs:int" />
      <xs:element minOccurs="0" name="Channel" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CompletedDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="CreatedBy" type="xs:int" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Duration" type="xs:int" />
      <xs:element minOccurs="0" name="EvaluatedScore" type="xs:float" />
      <xs:element minOccurs="0" name="EvaluationType" type="xs:int" />
      <xs:element minOccurs="0" name="Evaluator" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="EvaluatorComments" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="EvaluatorEmail" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ExtName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Feedback" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FileName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GroupName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GroupNum" type="xs:int" />
      <xs:element minOccurs="0" name="IsAgentAssociated" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsPictureEvent" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRevCell" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRevView" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsShared" type="xs:boolean" />
      <xs:element minOccurs="0" name="ModifiedBy" type="xs:int" />
      <xs:element minOccurs="0" name="ModifiedDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="RecIP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecId" type="xs:int" />
      <xs:element minOccurs="0" name="RecName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevSyncSurveyId" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="RevViewAgentName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevViewFileName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevViewPhoneNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RevViewStartTime" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RowNo" type="xs:int" />
      <xs:element minOccurs="0" name="ScreenFileNames" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SharedWith" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="StartTime" nillable="true" type="xs:string" />
      <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="Status" type="q3:EvaluationStatus" />
      <xs:element minOccurs="0" name="StatusId" type="xs:short" />
      <xs:element minOccurs="0" name="SurveyId" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="SurveyName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TotalQuestions" type="xs:int" />
      <xs:element minOccurs="0" name="TotalScore" type="xs:float" />
      <xs:element minOccurs="0" name="UserEmail" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserNum" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallEvaluationDTO" nillable="true" type="tns:CallEvaluationDTO" />
  <xs:complexType name="QADTO">
    <xs:sequence>
      <xs:element minOccurs="0" name="AnsweredQuestions" type="xs:int" />
      <xs:element minOccurs="0" name="EvaluatedScore" type="xs:float" />
      <xs:element minOccurs="0" name="TotalQuestions" type="xs:int" />
      <xs:element minOccurs="0" name="TotalScore" type="xs:float" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="QADTO" nillable="true" type="tns:QADTO" />
</xs:schema>