﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.EMREntities
{
    public class Patient
    {
        public int Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public Gender Gender { get; set; }
        public DateTime? DOB { get; set; }
        public string Height { get; set; }
        public string Weight { get; set; }
        public string BloodGroup { get; set; }
        public string SocialSecurityNumber { get; set; }
        public string TelephoneNo { get; set; }
        public string CellNo { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string ZipCode { get; set; }
        public string Comments { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public long? LastModifiedBy { get; set; }
        public bool IsDeleted { get; set; }

        public string FullName { get { return string.Format("{0} {1}", this.FirstName, this.LastName); } }
        public string GenderString { get { return this.Gender.ToString(); } }
    }
}
