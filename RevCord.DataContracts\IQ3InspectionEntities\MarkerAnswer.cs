﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3InspectionEntities
{
    public class MarkerAnswer
    {
        public long Id { get; set; }
        public long InspectionTemplateId { get; set; }
        public long MarkerId { get; set; }
        public long MarkerOptionId { get; set; }
        public string AnswerText { get; set; }
        public bool IsSelected { get; set; }
        public string PhotoFileName { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public bool IsDeleted { get; set; }
    }
}
