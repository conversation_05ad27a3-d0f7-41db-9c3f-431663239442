﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeFileHandle">
      <summary>Представляет класс-оболочку для дескриптора файла. </summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeFileHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" />. </summary>
      <param name="preexistingHandle">Объект <see cref="T:System.IntPtr" />, представляющий ранее существующий дескриптор для использования.</param>
      <param name="ownsHandle">Значение true, чтобы наверняка освободить дескриптор на стадии завершения; в противном случае — значение false (не рекомендуется).</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeFileHandle.IsInvalid"></member>
    <member name="T:System.IO.Directory">
      <summary>Предоставляет статические методы для создания, перемещения и перечисления в каталогах и вложенных каталогах.Этот класс не наследуется.Чтобы просмотреть исходный код .NET Framework для этого типа, см. ссылки на источник.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>Создает все каталоги и подкаталоги по указанному пути, если они еще не существуют.</summary>
      <returns>Объект, представляющий каталог по указанному пути.Этот объект возвращается вне зависимости от того, существует ли уже каталог по указанному пути.</returns>
      <param name="path">Каталог, который необходимо создать. </param>
      <exception cref="T:System.IO.IOException">Каталог, заданный параметром <paramref name="path" />, представляет собой файл.-или-Имя сети неизвестно.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или-<paramref name="path" /> начинается с двоеточия или содержит только двоеточие (:).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> содержит двоеточие (:), которое не является частью метки диска ("C:\").</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>Удаляет пустой каталог по заданному пути.</summary>
      <param name="path">Имя пустого каталога, который необходимо удалить.Этот каталог должен поддерживать запись и быть пустым.</param>
      <exception cref="T:System.IO.IOException">Файл с этим именем уже существует в местоположении, указанном параметром <paramref name="path" />.-или-Каталог является текущим рабочим каталогом приложения.-или-Каталог, заданный параметром <paramref name="path" />, не пуст.-или-Каталог доступен только для чтения или содержит доступный только для чтения файл.-или-Каталог используется другим процессом.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> не существует или не может быть найден.-или-Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>Удаляет заданный каталог и, при наличии соответствующей инструкции, все подкаталоги и файлы в нем. </summary>
      <param name="path">Имя каталога, который необходимо удалить. </param>
      <param name="recursive">Значение true позволяет удалить каталоги, подкаталоги и файлы по заданному <paramref name="path" />, в противном случае — значение false. </param>
      <exception cref="T:System.IO.IOException">Файл с этим именем уже существует в местоположении, указанном параметром <paramref name="path" />.-или-Каталог, заданный параметром <paramref name="path" />, доступен только для чтения или же для параметра <paramref name="recursive" /> задано значение false, а каталог, заданный параметром <paramref name="path" />, не является пустым. -или-Каталог является текущим рабочим каталогом приложения. -или-Каталог содержит файл, доступный только для чтения.-или-Каталог используется другим процессом.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> не существует или не может быть найден.-или-Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>Возвращает перечисляемую коллекцию имен каталогов, расположенных по указанному пути.</summary>
      <returns>Перечислимая коллекция полных имен (включая пути) для каталогов в каталоге заданном <paramref name="path" />.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path " />является пустой строкой, содержит только пробелы или содержит недопустимые символы.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим, возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>Возвращает перечисляемую коллекцию имен каталогов, соответствующих шаблону поиска по указанному пути.</summary>
      <returns>Перечисляемая коллекция полных имен (включая пути) для каталогов в каталоге, указанном в <paramref name="path" /> и которые соответствуют указанному шаблону поиска.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которую необходимо сравнивать с именами каталогов, расположенных по пути <paramref name="path" />.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path " />является пустой строкой, содержит только пробелы или содержит недопустимые символы.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или-<paramref name="searchPattern" /> не содержит допустимого шаблона.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.-или-<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим, возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Возвращает перечисляемую коллекцию имен каталогов, соответствующих шаблону поиска по указанному пути. Возможно, поиск ведется также и в подкаталогах.</summary>
      <returns>Перечисляемая коллекция полных имен (включая пути) для каталогов в каталоге, указанном в <paramref name="path" /> и которые соответствуют указанному шаблону и параметру поиска.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которую необходимо сравнивать с именами каталогов, расположенных по пути <paramref name="path" />.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах.Значение по умолчанию — <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path " />является пустой строкой, содержит только пробелы или содержит недопустимые символы.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или-<paramref name="searchPattern" /> не содержит допустимого шаблона.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.-или-<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="searchOption" /> не является допустимым значением <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим, возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>Возвращает перечисляемую коллекцию имен файлов, расположенных по указанному пути.</summary>
      <returns>Перечислимая коллекция полных имен (включая пути) для файлов в каталоге заданном <paramref name="path" />.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path " />является пустой строкой, содержит только пробелы или содержит недопустимые символы.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим, возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>Возвращает перечисляемую коллекцию имен файлов, соответствующих шаблону поиска по указанному пути.</summary>
      <returns>Перечисляемая коллекция полных имен (включая пути) для файлов в каталоге, указанном в <paramref name="path" /> и которые соответствуют указанному шаблону поиска.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которую необходимо сравнивать с именами файлов, на которые указывает <paramref name="path" />.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path " />является пустой строкой, содержит только пробелы или содержит недопустимые символы.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или-<paramref name="searchPattern" /> не содержит допустимого шаблона.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.-или-<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим, возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Возвращает перечисляемую коллекцию имен файлов, соответствующих шаблону поиска по указанному пути. Возможно, поиск ведется также и в подкаталогах.</summary>
      <returns>Перечисляемая коллекция полных имен (включая пути) для файлов в каталоге, указанном в <paramref name="path" /> и которые соответствуют указанному шаблону и параметру поиска.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которую необходимо сравнивать с именами файлов, на которые указывает <paramref name="path" />.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах.Значение по умолчанию — <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path " />является пустой строкой, содержит только пробелы или содержит недопустимые символы.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или-<paramref name="searchPattern" /> не содержит допустимого шаблона.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.-или-<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="searchOption" /> не является допустимым значением <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим, возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>Возвращает перечисляемую коллекцию имен файлов и имен каталогов по указанному пути. </summary>
      <returns>Перечисляемая коллекция записей файловой системы в каталоге, заданном параметром <paramref name="path" />.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path " />является пустой строкой, содержит только пробелы или содержит недопустимые символы.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим, возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>Возвращает перечисляемую коллекцию имен файлов и имен каталогов по указанному пути, соответствующих шаблону поиска.</summary>
      <returns>Перечисляемая коллекция записей файловой системы в каталоге, заданном параметром <paramref name="path" />, который соответствует шаблону поиска.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с записями файловой системы, расположенными по пути в параметре <paramref name="path" />.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path " />является пустой строкой, содержит только пробелы или содержит недопустимые символы.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или-<paramref name="searchPattern" /> не содержит допустимого шаблона.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.-или-<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим, возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Возвращает перечисляемую коллекцию записей файловой системы, соответствующих шаблону поиска по указанному пути. Возможно, поиск ведется также и в подкаталогах.</summary>
      <returns>Перечисляемая коллекция записей файловой системы в каталоге, заданном параметром <paramref name="path" />, который соответствует шаблону и параметру поиска.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с записями файловой системы, расположенными по пути в параметре <paramref name="path" />.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах.Значение по умолчанию — <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path " />является пустой строкой, содержит только пробелы или содержит недопустимые символы.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или-<paramref name="searchPattern" /> не содержит допустимого шаблона.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.-или-<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="searchOption" /> не является допустимым значением <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим, возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>Определяет, указывает ли заданный путь на существующий каталог на диске.</summary>
      <returns>Значение true, если <paramref name="path" /> ссылается на существующий каталог; значение false, если каталог не существует или если при попытке определить, существует ли указанный файл, произошла ошибка.</returns>
      <param name="path">Проверяемый путь. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>Получает дату и время создания каталога.</summary>
      <returns>Структура, для которой заданы дата и время создания указанного каталога.Значение представляется в формате местного времени.</returns>
      <param name="path">Путь к каталогу. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>Получает время и дату создания каталога в формате всемирного координированного времени (UTC).</summary>
      <returns>Структура, для которой заданы дата и время создания указанного каталога.Значение выражено в формате всемирного координированного времени (UTC).</returns>
      <param name="path">Путь к каталогу. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>Получает текущий рабочий каталог приложения.</summary>
      <returns>Строка, содержащая путь к текущему рабочему каталогу, не оканчивающаяся обратной косой чертой (\).</returns>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">Операционная система Windows CE не поддерживает функцию текущего каталога.Этот метод доступен в .NET Compact Framework, но в настоящий момент не поддерживается.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>Возвращает имена подкаталогов (включая пути) в указанном каталоге.</summary>
      <returns>Массив полных имен (включая пути) подкаталогов по указанному пути или пустой массив, если каталоги не найдены.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>Возвращает имена подкаталогов (включая пути) в указанном каталоге, соответствующих указанному шаблону поиска.</summary>
      <returns>Массив полных имен (включая пути) подкаталогов в указанном каталоге, которые соответствуют указанному шаблону поиска, или пустой массив, если каталоги не найдены.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами подкаталогов, расположенных по пути в параметре <paramref name="path" />.Этот параметр может содержать сочетание допустимых литеральных и подстановочных символов (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или- <paramref name="searchPattern" /> не содержит допустимого шаблона. </exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="path" /> или <paramref name="searchPattern" /> — null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Возвращает имена подкаталогов (включая пути) в указанном каталоге, соответствующих указанному шаблону поиска, и при необходимости ведет поиск в подкаталогах.</summary>
      <returns>Массив полных имен (включая пути) подкаталогов, соответствующих указанным критериям, или пустой массив, если каталоги не найдены.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами подкаталогов, расположенных по пути в параметре <paramref name="path" />.Этот параметр может содержать сочетание допустимых литеральных и подстановочных символов (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или- <paramref name="searchPattern" /> не содержит допустимого шаблона. </exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="path" /> или <paramref name="searchPattern" /> — null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="searchOption" /> не является допустимым значением <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>Возвращает для заданного пути сведения о томе и корневом каталоге по отдельности или сразу.</summary>
      <returns>Строка, в которой содержатся сведения о томе, корневом каталоге или одновременно сведения и о томе, и о корневом каталоге для заданного пути.</returns>
      <param name="path">Путь к файлу или каталогу. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>Возвращает имена файлов (с указанием пути к ним) в указанном каталоге.</summary>
      <returns>Массив полных имен (включая пути) файлов в указанном каталоге или пустой массив, если файлы не найдены.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.-или-Произошла ошибка сети. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь не найден или не является допустимым (например, соответствует неподключенному диску). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>Возвращает имена файлов (включая пути) из указанного каталога, отвечающие условиям заданного шаблона поиска.</summary>
      <returns>Массив полных имен (включая пути) файлов в указанном каталоге, которые соответствуют указанному шаблону поиска, или пустой массив, если файлы не найдены.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которую необходимо сравнивать с именами файлов, на которые указывает <paramref name="path" />.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.-или-Произошла ошибка сети. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или- <paramref name="searchPattern" /> не содержит допустимого шаблона. </exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="path" /> или <paramref name="searchPattern" /> — null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь не найден или не является допустимым (например, соответствует неподключенному диску). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Возвращает имена файлов (включая пути) в заданном каталоге, отвечающие условиям шаблона поиска, используя значение, которое определяет, выполнять ли поиск в подкаталогах.</summary>
      <returns>Массив полных имен (включая пути) файлов в указанном каталоге, которые соответствуют указанному шаблону и параметру поиска, или пустой массив, если файлы не найдены.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которую необходимо сравнивать с именами файлов, на которые указывает <paramref name="path" />.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или- <paramref name="searchPattern" /> не содержит допустимого шаблона.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="path" /> или <paramref name="searchpattern" /> — null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="searchOption" /> не является допустимым значением <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь не найден или не является допустимым (например, соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.-или-Произошла ошибка сети. </exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>Возвращает имена всех файлов и подкаталогов по указанному пути.</summary>
      <returns>Массив имен файлов и подкаталогов в указанном каталоге или пустой массив, если файлы или подкаталоги не найдены.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>Возвращает массив имен файлов и имен каталогов по указанному пути, соответствующих шаблону поиска.</summary>
      <returns>Массив имен файлов и имен каталогов, соответствующих указанным критериям поиска, или пустой массив, если файлы или каталоги не найдены.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами файла и каталогов, расположенных по пути в параметре <paramref name="path" />.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или- <paramref name="searchPattern" /> не содержит допустимого шаблона. </exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="path" /> или <paramref name="searchPattern" /> — null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Возвращает массив всех имен файлов и каталогов по указанному пути, соответствующих шаблону поиска, и при необходимости ведет поиск в подкаталогах.</summary>
      <returns>Массив имен файлов и имен каталогов, соответствующих указанным критериям поиска, или пустой массив, если файлы или каталоги не найдены.</returns>
      <param name="path">Относительный или абсолютный путь к каталогу для поиска.В этой строке не учитывается регистр знаков.</param>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами файлов и каталогов, расположенных по пути в параметре <paramref name="path" />.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах.Значение по умолчанию — <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path " />является пустой строкой, содержит только пробелы или содержит недопустимые символы.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.-или-<paramref name="searchPattern" /> не содержит допустимого шаблона.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null.-или-<paramref name="searchPattern" />is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="searchOption" /> не является допустимым значением <see cref="T:System.IO.SearchOption" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим, возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> является именем файла.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>Возвращает время и дату последнего обращения к указанному файлу или каталогу.</summary>
      <returns>Структура, для которой заданы дата и время последнего доступа к указанному файлу или каталогу.Значение представляется в формате местного времени.</returns>
      <param name="path">Файл или каталог, информацию о дате и времени обращения к которому следует получить. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">Параметр <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>Возвращает дату и время последнего доступа к заданному файлу или каталогу в формате всемирного координированного времени (UTC).</summary>
      <returns>Структура, для которой заданы дата и время последнего доступа к указанному файлу или каталогу.Значение выражено в формате всемирного координированного времени (UTC).</returns>
      <param name="path">Файл или каталог, информацию о дате и времени обращения к которому следует получить. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">Параметр <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>Возвращает время и дату последней операции записи в указанный файл или каталог.</summary>
      <returns>Структура, для которой заданы дата и время последней операции записи в указанный файл или каталог.Значение представляется в формате местного времени.</returns>
      <param name="path">Файл или каталог, дату и время изменения которого следует получить. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>Возвращает дату и время последней операции записи в заданный файл или каталог в формате всемирного координированного времени (UTC).</summary>
      <returns>Структура, для которой заданы дата и время последней операции записи в указанный файл или каталог.Значение выражено в формате всемирного координированного времени (UTC).</returns>
      <param name="path">Файл или каталог, дату и время изменения которого следует получить. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>Извлекает родительский каталог, на который указывает абсолютный или относительный путь.</summary>
      <returns>Родительский каталог или значение null, если параметр <paramref name="path" /> является корневым каталогом, в том числе корнем сервера UNC или именем совместно используемого ресурса.</returns>
      <param name="path">Путь, указывающий на родительский каталог, который необходимо извлечь. </param>
      <exception cref="T:System.IO.IOException">Каталог, заданный параметром <paramref name="path" />, доступен только для чтения. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь не найден. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>Перемещает файл или каталог со всем его содержимым в новое местоположение.</summary>
      <param name="sourceDirName">Путь к файлу или каталогу, который необходимо переместить. </param>
      <param name="destDirName">Путь к новому местоположению <paramref name="sourceDirName" />.Если <paramref name="sourceDirName" /> является файлом, то параметр <paramref name="destDirName" /> также должен быть именем файла.</param>
      <exception cref="T:System.IO.IOException">Предпринята попытка переместить каталог в другой том. -или- <paramref name="destDirName" /> уже существует. -или- Параметры <paramref name="sourceDirName" /> и <paramref name="destDirName" /> ссылаются на один и тот же файл или каталог. -или-Каталог или файл, в нем используется другим процессом.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="sourceDirName" /> или <paramref name="destDirName" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="sourceDirName" /> или <paramref name="destDirName" /> — null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, заданный параметром <paramref name="sourceDirName" />, является недопустимым (например, он соответствует неподключенному диску). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>Устанавливает дату и время создания заданного файла или каталога.</summary>
      <param name="path">Файл или каталог, для которого следует установить дату и время создания. </param>
      <param name="creationTime">Дата и время последней записи в файл или каталог.Значение представляется в формате местного времени.</param>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="creationTime" /> указывает на значение, выходящее за пределы разрешенных для операции значений даты и времени. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Устанавливает дату и время создания указанного файла или папки в формате всемирного координированного времени (UTC).</summary>
      <param name="path">Файл или каталог, для которого следует установить дату и время создания. </param>
      <param name="creationTimeUtc">Дата и время создания каталога или файла.Значение представляется в формате местного времени.</param>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="creationTime" /> указывает на значение, выходящее за пределы разрешенных для операции значений даты и времени. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>Устанавливает заданный каталог в качестве текущего рабочего каталога приложения.</summary>
      <param name="path">Путь, который должен быть назначен рабочему каталогу. </param>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего оператора отсутствует разрешение на доступ к неуправляемому коду. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Заданный каталог не найден.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Устанавливает время и дату последнего обращения к заданному файлу или каталогу.</summary>
      <param name="path">Файл или каталог, для которого необходимо установить дату и время доступа. </param>
      <param name="lastAccessTime">Объект, который содержит значение, которое необходимо присвоить дате и времени доступа <paramref name="path" />.Значение представляется в формате местного времени.</param>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="lastAccessTime" /> указывает на значение, выходящее за пределы разрешенных для операции значений даты и времени.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Устанавливает дату и время последнего доступа к заданному файлу или каталогу в формате всемирного координированного времени (UTC).</summary>
      <param name="path">Файл или каталог, для которого необходимо установить дату и время доступа. </param>
      <param name="lastAccessTimeUtc">Объект, который содержит значение, которое необходимо присвоить дате и времени доступа <paramref name="path" />.Значение выражено в формате всемирного координированного времени (UTC).</param>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="lastAccessTimeUtc" /> указывает на значение, выходящее за пределы разрешенных для операции значений даты и времени.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Устанавливает дату и время последней записи в файл или каталог.</summary>
      <param name="path">Путь к каталогу. </param>
      <param name="lastWriteTime">Дата и время последней записи в файл или каталог.Значение представляется в формате местного времени.</param>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="lastWriteTime" /> указывает на значение, выходящее за пределы разрешенных для операции значений даты и времени.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Устанавливает дату и время последней записи в заданный каталог в формате всемирного координированного времени (UTC).</summary>
      <param name="path">Путь к каталогу. </param>
      <param name="lastWriteTimeUtc">Дата и время последней записи в файл или каталог.Значение выражено в формате всемирного координированного времени (UTC).</param>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или содержит один или несколько недопустимых символов.Недопустимые символы можно запросить с помощью метода <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="lastWriteTimeUtc" /> указывает на значение, выходящее за пределы разрешенных для операции значений даты и времени.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>Предоставляет методы экземпляра класса для создания, перемещения и перечисления в каталогах и подкаталогах.Этот класс не наследуется.Чтобы просмотреть исходный код .NET Framework для этого типа, см.
                                Ссылки на источник.
                            </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса
                                <see cref="T:System.IO.DirectoryInfo" />класс по указанному пути.
                            </summary>
      <param name="path">Строка, указывающая путь, на котором требуется создать
                                    DirectoryInfo.
                                </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />является
                                        null.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> содержит такие недопустимые знаки, как ", &lt;, &gt; или |.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.Слишком длинный путь или имя файла.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>Создает каталог.</summary>
      <exception cref="T:System.IO.IOException">Каталог не может быть создан.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>Создает один или несколько подкаталогов по заданному пути.Указанный путь может быть указан относительно этого экземпляра
                            <see cref="T:System.IO.DirectoryInfo" />класс.
                        </summary>
      <returns>Последняя папка, указанная в
                                <paramref name="path" />.
                            </returns>
      <param name="path">Заданный путь.Этот путь не может указывать на другой том диска или иметь формат UNC.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />не указан допустимый путь к файлу или содержит недопустимый
                                        DirectoryInfoсимволы.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />является
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску.</exception>
      <exception cref="T:System.IO.IOException">Подкаталог не может быть создан.-или-Файл или каталог уже имеет имя, указанное в
                                        <paramref name="path" />.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.Слишком длинный путь или имя файла.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего оператора отсутствует разрешение на доступ к коду для создания каталога.-или-Вызывающий объект не имеет разрешения доступа к коду для чтения каталога, описанного возвращенного
                                    Объект <see cref="T:System.IO.DirectoryInfo" />.
                                Это может произойти, когда
                                    <paramref name="path" />параметр указывает на существующий каталог.
                                </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> содержит двоеточие (:), которое не является частью метки диска ("C:\").
                                    </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>Удаляет этот
                                <see cref="T:System.IO.DirectoryInfo" />Если он пуст.
                            </summary>
      <exception cref="T:System.UnauthorizedAccessException">Каталог содержит файл, доступный только для чтения.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Каталог, описанный в этом
                                        <see cref="T:System.IO.DirectoryInfo" />объект не существует или не найден.
                                    </exception>
      <exception cref="T:System.IO.IOException">Каталог не пуст.-или-Каталог является текущим рабочим каталогом приложения.-или-Для каталога имеется открытый дескриптор, используется операционная система Windows XP или более ранняя версия.Открытый дескриптор мог появиться в результате перечисления каталогов.Дополнительные сведения см. в разделе .
                                    Практическое руководство. Перечисление каталогов и файлов.
                                </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>Удаляет данный экземпляр
                                <see cref="T:System.IO.DirectoryInfo" />, определяя, нужно ли удалять файлы и подкаталоги.
                            </summary>
      <param name="recursive">trueЧтобы удалить этот каталог, его подкаталоги и все файлы; в противном случае —
                                    false.
                                </param>
      <exception cref="T:System.UnauthorizedAccessException">Каталог содержит файл, доступный только для чтения.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Каталог, описанный в этом
                                        <see cref="T:System.IO.DirectoryInfo" />объект не существует или не найден.
                                    </exception>
      <exception cref="T:System.IO.IOException">Каталог доступен только для чтения.-или-Каталог содержит файлы или вложенные папки и
                                        <paramref name="recursive" />является
                                        false.
                                    -или-Каталог является текущим рабочим каталогом приложения.-или-Для каталога или одного из его файлов имеется открытый дескриптор, используется операционная система Windows XP или более ранняя версия.Открытый дескриптор мог появиться в результате перечисления каталогов и файлов.Дополнительные сведения см. в разделе .
                                    Практическое руководство. Перечисление каталогов и файлов.
                                </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>Возвращает перечисляемую коллекцию сведений о каталогах в текущем каталоге.</summary>
      <returns>Перечисляемая коллекция каталогов в текущем каталоге.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        <see cref="T:System.IO.DirectoryInfo" />Недопустимый объект (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>Возвращает перечисляемую коллекцию сведений о каталогах, соответствующую указанному шаблону поиска.</summary>
      <returns>Перечисляемая коллекция каталогов, соответствующая
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами каталогов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        <see cref="T:System.IO.DirectoryInfo" />Недопустимый объект (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>Возвращает перечисляемую коллекцию сведений о каталогах, соответствующую указанному шаблону поиска и параметру поиска в подкаталогах.</summary>
      <returns>Перечисляемая коллекция каталогов, соответствующая
                                <paramref name="searchPattern" />и
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами каталогов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах.Значение по умолчанию
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />не является допустимым
                                        Значение <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        <see cref="T:System.IO.DirectoryInfo" />Недопустимый объект (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>Возвращает перечисляемую коллекцию сведений о файлах в текущем каталоге.</summary>
      <returns>Перечисляемая коллекция файлов в текущем каталоге.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        <see cref="T:System.IO.DirectoryInfo" />Недопустимый объект (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>Возвращает перечисляемую коллекцию сведений о файлах, соответствующую шаблону поиска.</summary>
      <returns>Перечисляемая коллекция файлов, соответствующая
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами файлов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        <see cref="T:System.IO.DirectoryInfo" />объект является недопустимым, (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>Возвращает перечисляемую коллекцию сведений о файлах, соответствующую указанному шаблону поиска и параметру поиска в подкаталогах.</summary>
      <returns>Перечисляемая коллекция файлов, соответствующая
                                <paramref name="searchPattern" />и
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами файлов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах.Значение по умолчанию
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />не является допустимым
                                        Значение <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        <see cref="T:System.IO.DirectoryInfo" />Недопустимый объект (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>Возвращает перечисляемую коллекцию сведений о файловой системе текущего каталога.</summary>
      <returns>Перечисляемая коллекция сведений о файловой системе текущего каталога.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        <see cref="T:System.IO.DirectoryInfo" />Недопустимый объект (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>Возвращает перечисляемую коллекцию сведений о файловой системе, соответствующую указанному шаблону поиска.</summary>
      <returns>Перечисляемая коллекция объектов сведения файловой системы, соответствующая
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами каталогов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        <see cref="T:System.IO.DirectoryInfo" />Недопустимый объект (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Возвращает перечисляемую коллекцию сведений о файловой системе, соответствующую указанному шаблону поиска и параметру поиска в подкаталогах.</summary>
      <returns>Перечисляемая коллекция объектов сведения файловой системы, соответствующая
                                <paramref name="searchPattern" />и
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами каталогов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах.Значение по умолчанию
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />не является допустимым
                                        Значение <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        <see cref="T:System.IO.DirectoryInfo" />Недопустимый объект (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>Получает значение, определяющее наличие каталога.</summary>
      <returns>trueЕсли папка существует; в противном случае —
                                false.
                            </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>Возвращает подкаталоги текущего каталога.</summary>
      <returns>Массив
                                Объекты <see cref="T:System.IO.DirectoryInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        <see cref="T:System.IO.DirectoryInfo" />объект является недопустимым, несопоставленные диска.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>Возвращает массив каталогов в текущем
                                <see cref="T:System.IO.DirectoryInfo" />соответствующие заданным критериям поиска.
                            </summary>
      <returns>Массив типа
                                DirectoryInfoсопоставления
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами каталогов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />содержит один или несколько недопустимых символов определяется
                                        Метод <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        DirectoryInfoНедопустимый объект (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>Возвращает массив каталогов в текущем
                                <see cref="T:System.IO.DirectoryInfo" />соответствующие заданным критериям поиска с использованием значения, чтобы определить, следует ли выполнять поиск в подкаталогах.
                            </summary>
      <returns>Массив типа
                                DirectoryInfoсопоставления
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами каталогов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />содержит один или несколько недопустимых символов определяется
                                        Метод <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />не является допустимым
                                        Значение <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, содержащийся в
                                        DirectoryInfoНедопустимый объект (например, это несопоставленные диску).
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>Возвращает список файлов текущего каталога.</summary>
      <returns>Массив типа
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь является недопустимым; возможно, он соответствует неподключенному диску.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>Возвращает список файлов текущего каталога, соответствующих заданному шаблону поиска.</summary>
      <returns>Массив типа
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами файлов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />содержит один или несколько недопустимых символов определяется
                                        Метод <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>Возвращает список файлов из текущего каталога, соответствующих заданному шаблону поиска, с использованием значения, которое позволяет определить, следует ли выполнять поиск в подкаталогах.</summary>
      <returns>Массив типа
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами файлов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />содержит один или несколько недопустимых символов определяется
                                        Метод <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />не является допустимым
                                        Значение <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>Возвращает массив строго типизированные
                                <see cref="T:System.IO.FileSystemInfo" />операции, представляющих все файлы и подкаталоги в каталоге.
                            </summary>
      <returns>Строго типизированный массив
                                <see cref="T:System.IO.FileSystemInfo" />операции.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь недопустим (например, он соответствует неподключенному диску).</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>Извлекает массив строго типизированных
                                <see cref="T:System.IO.FileSystemInfo" />объекты, представляющих файлы и подкаталоги, соответствующие заданным критериям поиска.
                            </summary>
      <returns>Строго типизированный массив
                                FileSystemInfoобъекты, соответствующие критериям поиска.
                            </returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами каталогов и файлов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />содержит один или несколько недопустимых символов определяется
                                        Метод <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Получает массив
                                <see cref="T:System.IO.FileSystemInfo" />объектов, представляющих файлы и подкаталоги, соответствующие заданным критериям поиска.
                            </summary>
      <returns>Массив элементов файловой системы, удовлетворяющих критериям поиска.</returns>
      <param name="searchPattern">Строка поиска, которая будет сравниваться с именами каталогов и файлов.Этот параметр может содержать сочетание допустимого литерального пути и подстановочных символов (* и ?) (см. раздел "Примечания"), но не поддерживает регулярные выражения.Шаблон по умолчанию, возвращающий все файлы, — "*".</param>
      <param name="searchOption">Одно из значений перечисления, определяющее, следует ли выполнять поиск только в текущем каталоге или также во всех его подкаталогах.Значение по умолчанию
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />содержит один или несколько недопустимых символов определяется
                                        Метод <see cref="M:System.IO.Path.GetInvalidPathChars" />.
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />является
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />не является допустимым
                                        Значение <see cref="T:System.IO.SearchOption" />.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>Перемещение
                                <see cref="T:System.IO.DirectoryInfo" />экземпляр и его содержимое по новому пути.
                            </summary>
      <param name="destDirName">Имя и путь к местоположению, в которое необходимо переместить указанный каталог.Место назначения не должно находиться на другом томе устройства или в каталоге с идентичным именем.Оно должно представлять собой существующий каталог, в который перемещаемый каталог будет добавлен в виде подкаталога.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" />является
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" /> является пустой строкой ("").
                                    </exception>
      <exception cref="T:System.IO.IOException">Предпринята попытка переместить каталог в другой том.-или-<paramref name="destDirName" /> уже существует.
                                    -или-Отсутствует авторизация для доступа по этому пути.-или-Каталог перемещается, и каталог назначения имеет то же самое имя.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Не удалось найти каталог назначения.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>Возвращает имя этого
                                <see cref="T:System.IO.DirectoryInfo" />экземпляр.
                            </summary>
      <returns>Имя каталога.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>Получает родительский каталог заданного подкаталога.</summary>
      <returns>Родительский каталог или
                                nullЕсли путь имеет значение null или если путь к файлу указывает на корневой каталог (например, "C:", "\" или * «\\server\share»).
                            </returns>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>Получает корневую часть каталога.</summary>
      <returns>Объект, представляющий корень каталога.</returns>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>Возвращает исходный путь, переданный пользователем.</summary>
      <returns>Возвращает исходный путь, переданный пользователем.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.File">
      <summary>Предоставляет статические методы для создания, копирования, удаления, перемещения и открытия одного файла, а также помогает при создании объектов <see cref="T:System.IO.FileStream" />.Чтобы просмотреть исходный код .NET Framework для этого типа, см. Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Добавляет строки в файл, затем закрывает файл.Если указанный файл не существует, этот метод создает файл, записывает в него указанные строки и затем закрывает файл.</summary>
      <param name="path">Файл, в который добавляются строки.Если файл не существует, он создается.</param>
      <param name="contents">Строки, добавляемые в файл.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы либо один или несколько недопустимых символов, определенных методом <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">Либо<paramref name=" path " />или <paramref name="contents" /> — null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> недействителен (например, каталог не существует или он соответствует неподключенному диску).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный параметром <paramref name="path" />, не найден.</exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина параметра <paramref name="path" /> превышает определенное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат.</exception>
      <exception cref="T:System.Security.SecurityException">Вызывающий объект не имеет разрешения на запись в файл.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="path" /> определяет файл, доступный только для чтения.-или-Эта операция не поддерживается на текущей платформе.-или-<paramref name="path" /> является каталогом.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Добавляет строки в файл, используя заданную кодировку, затем закрывает файл.Если указанный файл не существует, этот метод создает файл, записывает в него указанные строки и затем закрывает файл.</summary>
      <param name="path">Файл, в который добавляются строки.Если файл не существует, он создается.</param>
      <param name="contents">Строки, добавляемые в файл.</param>
      <param name="encoding">Кодировка символов, которую нужно использовать.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы либо один или несколько недопустимых символов, определенных методом <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра<paramref name=" path" />, <paramref name="contents" /> или <paramref name="encoding" /> — null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> недействителен (например, каталог не существует или он соответствует неподключенному диску).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный параметром <paramref name="path" />, не найден.</exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина параметра <paramref name="path" /> превышает определенное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="path" /> определяет файл, доступный только для чтения.-или-Эта операция не поддерживается на текущей платформе.-или-<paramref name="path" /> является каталогом.-или-У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>Открывает файл, добавляет в него указанную строку и затем закрывает файл.Если файл не существует, этот метод создает файл, записывает в него указанную строку и затем закрывает файл.</summary>
      <param name="path">Файл, в который нужно добавить заданную строку. </param>
      <param name="contents">Строка, которую нужно добавить в файл. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недействителен (например, каталог не существует или он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Добавляет указанную строку в файл, создавая файл, если он не существует.</summary>
      <param name="path">Файл, в который нужно добавить заданную строку. </param>
      <param name="contents">Строка, которую нужно добавить в файл. </param>
      <param name="encoding">Кодировка символов, которую нужно использовать. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недействителен (например, каталог не существует или он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>Создает объект <see cref="T:System.IO.StreamWriter" />, добавляющий текст с кодировкой UTF-8 в существующий файл, или в новый файл, если указанный файл не существует.</summary>
      <returns>Модуль записи потока, который добавляет текст в кодировке UTF-8 в указанный файл или новый файл.</returns>
      <param name="path">Путь к файлу, в который производится добавление. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недействителен (например, каталог не существует или он соответствует неподключенному диску). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>Копирует существующий файл в новый файл.Перезапись файла с тем же именем не разрешена.</summary>
      <param name="sourceFileName">Копируемый файл. </param>
      <param name="destFileName">Имя конечного файла.Это не может быть имя каталога или имя существующего файла.</param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="sourceFileName" /> или <paramref name="destFileName" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />.-или- Параметр <paramref name="sourceFileName" /> или <paramref name="destFileName" /> описывает каталог. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="sourceFileName" /> или <paramref name="destFileName" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, указанный в <paramref name="sourceFileName" /> или <paramref name="destFileName" />, недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл <paramref name="sourceFileName" /> не найден. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> уже существует.-или- Произошла ошибка ввода-вывода. </exception>
      <exception cref="T:System.NotSupportedException">Параметр <paramref name="sourceFileName" /> или <paramref name="destFileName" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>Копирует существующий файл в новый файл.Перезапись файла с тем же именем разрешена.</summary>
      <param name="sourceFileName">Копируемый файл. </param>
      <param name="destFileName">Имя конечного файла.Это не может быть имя каталога.</param>
      <param name="overwrite">true, если конечный файл можно перезаписать; в противном случае — false. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. -или-Свойство <paramref name="destFileName" /> доступно только для чтения.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="sourceFileName" /> или <paramref name="destFileName" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />.-или- Параметр <paramref name="sourceFileName" /> или <paramref name="destFileName" /> описывает каталог. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="sourceFileName" /> или <paramref name="destFileName" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, указанный в <paramref name="sourceFileName" /> или <paramref name="destFileName" />, недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл <paramref name="sourceFileName" /> не найден. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> существует, а <paramref name="overwrite" /> равно false.-или- Произошла ошибка ввода-вывода. </exception>
      <exception cref="T:System.NotSupportedException">Параметр <paramref name="sourceFileName" /> или <paramref name="destFileName" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>Создает или перезаписывает файл в указанном пути.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" />, обеспечивающий доступ для чтения и записи к файлу, указанному в <paramref name="path" />.</returns>
      <param name="path">Путь и имя создаваемого файла. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.-или- <paramref name="path" /> указывает файл, разрешенный только для чтения. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При создании файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>Создает или перезаписывает указанный файл.</summary>
      <returns>
        <see cref="T:System.IO.FileStream" /> с заданным размером буфера, который обеспечивает доступ для чтения и записи к файлу, указанному в <paramref name="path" />.</returns>
      <param name="path">Имя файла. </param>
      <param name="bufferSize">Число байтов, буферизируемых при чтении и записи в данный файл. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.-или- <paramref name="path" /> указывает файл, разрешенный только для чтения. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При создании файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>Создает или перезаписывает указанный файл, определяя размер буфера и значение <see cref="T:System.IO.FileOptions" />, которое описывает, как создавать или перезаписывать файл.</summary>
      <returns>Новый файл с заданным размером буфера.</returns>
      <param name="path">Имя файла. </param>
      <param name="bufferSize">Число байтов, буферизируемых при чтении и записи в данный файл. </param>
      <param name="options">Одно из значений <see cref="T:System.IO.FileOptions" />, которое описывает, как создавать или перезаписывать файл.</param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.-или- <paramref name="path" /> указывает файл, разрешенный только для чтения. -или-<see cref="F:System.IO.FileOptions.Encrypted" /> указывается для <paramref name="options" />, и шифрование фалов не поддерживается на текущей платформе.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При создании файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.-или- <paramref name="path" /> указывает файл, разрешенный только для чтения. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.-или- <paramref name="path" /> указывает файл, разрешенный только для чтения. </exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>Создается или открывается файл для записи текста в кодировке UTF-8.</summary>
      <returns>
        <see cref="T:System.IO.StreamWriter" />, выполняющий запись в указанный файл в кодировке UTF-8.</returns>
      <param name="path">Файл, который нужно открыть для записи. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>Удаляет указанный файл. </summary>
      <param name="path">Имя файла, предназначенного для удаления.Знаки подстановки не поддерживаются.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">Заданный файл используется. -или-Для файла имеется открытый дескриптор, используется операционная система Windows XP или более ранняя версия.Открытый дескриптор мог появиться в результате перечисления каталогов и файлов.Для получения дополнительной информации см. Практическое руководство. Перечисление каталогов и файлов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.-или- Файл является исполняемым файлом, который уже используется.-или- <paramref name="path" /> является каталогом.-или- Параметр <paramref name="path" /> определяет файл, разрешенный только для чтения. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>Определяет, существует ли заданный файл.</summary>
      <returns>Значение true, если вызывающий оператор имеет требуемые разрешения и <paramref name="path" /> содержит имя существующего файла; в противном случае — false.Этот метод также возвращает false, если <paramref name="path" /> — null, недействительный путь или строка нулевой длины.Если у вызывающего оператора нет достаточных полномочий на чтение заданного файла, исключения не создаются, а данный метод возвращает false вне зависимости от существования <paramref name="path" />.</returns>
      <param name="path">Проверяемый файл. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>Получает значение <see cref="T:System.IO.FileAttributes" /> для файла в пути.</summary>
      <returns>Значение <see cref="T:System.IO.FileAttributes" /> для файла в пути.</returns>
      <param name="path">Путь к файлу. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы или недопустимые символы. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> представляет файл и является недопустимым, возможно, он соответствует неподключенному диску или файл не найден. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> представляет каталог и является недопустимым, возможно, он соответствует неподключенному диску или каталог не найден.</exception>
      <exception cref="T:System.IO.IOException">Этот файл используется другим процессом.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>Возвращает дату и время создания заданного файла или каталога.</summary>
      <returns>Структура <see cref="T:System.DateTime" />, для которой заданы дата и время создания указанного файла или каталога.Значение представляется в формате местного времени.</returns>
      <param name="path">Файл или каталог, для которого получены сведения о дате и времени создания. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>Возвращает дату и время создания заданного файла или каталога в формате общего скоординированного времени (UTC).</summary>
      <returns>Структура <see cref="T:System.DateTime" />, для которой заданы дата и время создания указанного файла или каталога.Значение выражено в формате всемирного координированного времени (UTC).</returns>
      <param name="path">Файл или каталог, для которого получены сведения о дате и времени создания. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>Возвращает время и дату последнего обращения к указанному файлу или каталогу.</summary>
      <returns>Структура <see cref="T:System.DateTime" />, для которой заданы дата и время последнего доступа к указанному файлу или каталогу.Значение представляется в формате местного времени.</returns>
      <param name="path">Файл или каталог, информацию о дате и времени обращения к которому следует получить. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>Возвращает дату и время последнего доступа к заданному файлу или каталогу в формате всемирного координированного времени (UTC).</summary>
      <returns>Структура <see cref="T:System.DateTime" />, для которой заданы дата и время последнего доступа к указанному файлу или каталогу.Значение выражено в формате всемирного координированного времени (UTC).</returns>
      <param name="path">Файл или каталог, информацию о дате и времени обращения к которому следует получить. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>Возвращает время и дату последней операции записи в указанный файл или каталог.</summary>
      <returns>Структура <see cref="T:System.DateTime" />, для которой заданы дата и время последней операции записи в указанный файл или каталог.Значение представляется в формате местного времени.</returns>
      <param name="path">Файл или каталог, для которого должны быть получены сведения о дате и времени записи. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>Возвращает дату и время последней операции записи в заданный файл или каталог в формате всемирного координированного времени (UTC).</summary>
      <returns>Структура <see cref="T:System.DateTime" />, для которой заданы дата и время последней операции записи в указанный файл или каталог.Значение выражено в формате всемирного координированного времени (UTC).</returns>
      <param name="path">Файл или каталог, для которого должны быть получены сведения о дате и времени записи. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>Перемещает заданный файл в новое местоположение и разрешает переименование файла.</summary>
      <param name="sourceFileName">Имя перемещаемого файла.Может содержать относительный или абсолютный путь.</param>
      <param name="destFileName">Новый путь к файлу и его имя.</param>
      <exception cref="T:System.IO.IOException">Конечный файл уже существует.-или-Файл <paramref name="sourceFileName" /> не найден. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="sourceFileName" /> или <paramref name="destFileName" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> или <paramref name="destFileName" /> является пустой строкой, содержит только пробелы или включает недопустимые знаки, как определено в <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, указанный в <paramref name="sourceFileName" /> или <paramref name="destFileName" />, недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.NotSupportedException">Параметр <paramref name="sourceFileName" /> или <paramref name="destFileName" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>Открывает объект <see cref="T:System.IO.FileStream" /> по указанному пути с доступом для чтения и записи.</summary>
      <returns>Поток <see cref="T:System.IO.FileStream" />, открытый в указанном режиме и по указанному пути с доступом для чтения и записи и без предоставления общего доступа.</returns>
      <param name="path">Открываемый файл. </param>
      <param name="mode">Значение <see cref="T:System.IO.FileMode" /> указывает, нужно ли создавать файл, если он не существует, и определяет, будет ли содержимое существующих файлов сохранено или перезаписано. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. -или-<paramref name="mode" /> имеет значение <see cref="F:System.IO.FileMode.Create" />, а указанный файл является скрытым.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> задает недопустимое значение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный в <paramref name="path" />, не найден. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Открывает <see cref="T:System.IO.FileStream" /> в заданном пути с заданным режимом и доступом.</summary>
      <returns>Объект с монопольным доступом <see cref="T:System.IO.FileStream" />, обеспечивающий доступ к указанному файлу с заданным режимом и доступом.</returns>
      <param name="path">Открываемый файл. </param>
      <param name="mode">Значение <see cref="T:System.IO.FileMode" /> указывает, нужно ли создавать файл, если он не существует, и определяет, будет ли содержимое существующих файлов сохранено или перезаписано. </param>
      <param name="access">Значение <see cref="T:System.IO.FileAccess" />, описывающее операции, которые можно выполнять с файлом. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />.-или- <paramref name="access" /> определяет Read и <paramref name="mode" /> определяет Create, CreateNew, Truncate или Append. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения, а параметр <paramref name="access" /> не равенRead.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. -или-<paramref name="mode" /> имеет значение <see cref="F:System.IO.FileMode.Create" />, а указанный файл является скрытым.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> или <paramref name="access" /> задает недопустимое значение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный в <paramref name="path" />, не найден. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Открывает <see cref="T:System.IO.FileStream" /> в заданном пути, с заданным режимом доступа для чтения, записи или чтения и записи и с заданным параметром совместного использования.</summary>
      <returns>Поток <see cref="T:System.IO.FileStream" /> по указанному пути с указанным режимом доступа для чтения, записи или чтения и записи и с указанным параметром совместного доступа.</returns>
      <param name="path">Открываемый файл. </param>
      <param name="mode">Значение <see cref="T:System.IO.FileMode" /> указывает, нужно ли создавать файл, если он не существует, и определяет, будет ли содержимое существующих файлов сохранено или перезаписано. </param>
      <param name="access">Значение <see cref="T:System.IO.FileAccess" />, описывающее операции, которые можно выполнять с файлом. </param>
      <param name="share">Значение <see cref="T:System.IO.FileShare" />, задающее тип доступа к файлу других потоков. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />.-или- <paramref name="access" /> определяет Read и <paramref name="mode" /> определяет Create, CreateNew, Truncate или Append. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения, а параметр <paramref name="access" /> не равенRead.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. -или-<paramref name="mode" /> имеет значение <see cref="F:System.IO.FileMode.Create" />, а указанный файл является скрытым.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />, <paramref name="access" /> или <paramref name="share" /> задает недопустимое значение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный в <paramref name="path" />, не найден. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>Открывает для чтения существующий файл.</summary>
      <returns>Доступный только для чтения <see cref="T:System.IO.FileStream" /> в заданном пути.</returns>
      <param name="path">Файл, открываемый для чтения. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный в <paramref name="path" />, не найден. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>Открывает для чтения существующий файл, содержащий текст в кодировке UTF-8.</summary>
      <returns>
        <see cref="T:System.IO.StreamReader" /> в заданном пути.</returns>
      <param name="path">Файл, открываемый для чтения. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный в <paramref name="path" />, не найден. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>Открывает существующий файл или создает новый файл для записи.</summary>
      <returns>Объект с монопольным доступом <see cref="T:System.IO.FileStream" /> в заданном пути с доступом <see cref="F:System.IO.FileAccess.Write" />.</returns>
      <param name="path">Файл, который нужно открыть для записи. </param>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение.-или- <paramref name="path" /> определяет файл или каталог, разрешенный только для чтения. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>Открывает двоичный файл, считывает содержимое файла в массив байтов и затем закрывает файл.</summary>
      <returns>Массив байтов с содержимым файла.</returns>
      <param name="path">Файл, открываемый для чтения. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный в <paramref name="path" />, не найден. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>Открывает текстовый файл, считывает все строки файла и затем закрывает файл.</summary>
      <returns>Массив строк, содержащий все строки файла.</returns>
      <param name="path">Файл, открываемый для чтения. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный в <paramref name="path" />, не найден. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>Открывает файл, считывает все строки файла с заданной кодировкой и затем закрывает файл.</summary>
      <returns>Массив строк, содержащий все строки файла.</returns>
      <param name="path">Файл, открываемый для чтения. </param>
      <param name="encoding">Кодировка, примененная к содержимому файла. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный в <paramref name="path" />, не найден. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>Открывает текстовый файл, считывает все строки файла и затем закрывает файл.</summary>
      <returns>Строка, содержащая все строки файла.</returns>
      <param name="path">Файл, открываемый для чтения. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный в <paramref name="path" />, не найден. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>Открывает файл, считывает все строки файла с заданной кодировкой и затем закрывает файл.</summary>
      <returns>Строка, содержащая все строки файла.</returns>
      <param name="path">Файл, открываемый для чтения. </param>
      <param name="encoding">Кодировка, примененная к содержимому файла. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный в <paramref name="path" />, не найден. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>Считывает строки файла.</summary>
      <returns>Все строки файла или строки, которые являются результатом запроса.</returns>
      <param name="path">Файл, который нужно прочитать.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержит только пробелы или содержит один или несколько недопустимых символов определяется <see cref="M:System.IO.Path.GetInvalidPathChars" /> метод.</exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный параметром <paramref name="path" />, не найден.</exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина параметра <paramref name="path" /> превышает определенное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="path" /> определяет файл, доступный только для чтения.-или-Эта операция не поддерживается на текущей платформе.-или-<paramref name="path" /> является каталогом.-или-У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>Считывает строки файла с заданной кодировкой.</summary>
      <returns>Все строки файла или строки, которые являются результатом запроса.</returns>
      <param name="path">Файл, который нужно прочитать.</param>
      <param name="encoding">Кодировка, примененная к содержимому файла. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой, содержит только пробелы либо один или несколько недопустимых символов, определенных методом <see cref="M:System.IO.Path.GetInvalidPathChars" />.</exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный параметром <paramref name="path" />, не найден.</exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина параметра <paramref name="path" /> превышает определенное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="path" /> определяет файл, доступный только для чтения.-или-Эта операция не поддерживается на текущей платформе.-или-<paramref name="path" /> является каталогом.-или-У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>Устанавливает заданные атрибуты <see cref="T:System.IO.FileAttributes" /> файла по заданному пути.</summary>
      <param name="path">Путь к файлу. </param>
      <param name="fileAttributes">Побитовое сочетание значений перечисления. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> является пустой строкой, содержит только пробелы, содержит недопустимые знаки или недействительные атрибуты файла. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.FileNotFoundException">Не удается найти файл.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>Устанавливает дату и время создания файла.</summary>
      <param name="path">Файл, для которого задаются сведения о дате и времени создания. </param>
      <param name="creationTime">Объект <see cref="T:System.DateTime" />, содержащий значение, которое должно быть задано для даты и времени создания <paramref name="path" />.Значение представляется в формате местного времени.</param>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.IOException">При выполнении операции возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> задает значение, находящееся вне диапазона дат, времени или обеих переменных, разрешенного для этой операции. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Устанавливает дату и время создания файла, представленные в формате общего скоординированного времени (UTC).</summary>
      <param name="path">Файл, для которого задаются сведения о дате и времени создания. </param>
      <param name="creationTimeUtc">Объект <see cref="T:System.DateTime" />, содержащий значение, которое должно быть задано для даты и времени создания <paramref name="path" />.Значение выражено в формате всемирного координированного времени (UTC).</param>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.IOException">При выполнении операции возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> задает значение, находящееся вне диапазона дат, времени или обеих переменных, разрешенного для этой операции. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Устанавливаются дата и время последнего доступа к заданному файлу.</summary>
      <param name="path">Файл, для которого устанавливаются сведения о дате и времени доступа. </param>
      <param name="lastAccessTime">Объект <see cref="T:System.DateTime" />, содержащий значение, которое должно быть задано для даты и времени последнего доступа к <paramref name="path" />.Значение представляется в формате местного времени.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="lastAccessTime" /> указывает на значение, выходящее за пределы разрешенных для операции значений даты и времени.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Устанавливает дату и время последнего доступа к заданному файлу в формате всемирного координированного времени (UTC).</summary>
      <param name="path">Файл, для которого устанавливаются сведения о дате и времени доступа. </param>
      <param name="lastAccessTimeUtc">Объект <see cref="T:System.DateTime" />, содержащий значение, которое должно быть задано для даты и времени последнего доступа к <paramref name="path" />.Значение выражено в формате всемирного координированного времени (UTC).</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="lastAccessTimeUtc" /> указывает на значение, выходящее за пределы разрешенных для операции значений даты и времени.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Устанавливаются дата и время последней операции записи в заданный файл.</summary>
      <param name="path">Файл, для которого устанавливаются сведения о дате и времени. </param>
      <param name="lastWriteTime">Объект <see cref="T:System.DateTime" />, содержащий значение, которое должно быть задано для даты и времени последней записи <paramref name="path" />.Значение представляется в формате местного времени.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="lastWriteTime" /> указывает на значение, выходящее за пределы разрешенных для операции значений даты и времени.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Устанавливает дату и время последней операции записи в заданный файл в формате всемирного координированного времени (UTC).</summary>
      <param name="path">Файл, для которого устанавливаются сведения о дате и времени. </param>
      <param name="lastWriteTimeUtc">Объект <see cref="T:System.DateTime" />, содержащий значение, которое должно быть задано для даты и времени последней записи <paramref name="path" />.Значение выражено в формате всемирного координированного времени (UTC).</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Указанный путь не найден. </exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="lastWriteTimeUtc" /> указывает на значение, выходящее за пределы разрешенных для операции значений даты и времени.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>Создает новый файл, записывает в него указанный массив байтов и затем закрывает файл.Если целевой файл уже существует, он будет переопределен.</summary>
      <param name="path">Файл, в который осуществляется запись. </param>
      <param name="bytes">Байты, которые нужно записать в файл. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> принимает значение null, или массив байтов пуст. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Создает новый файл, записывает в него коллекцию строк, затем закрывает файл.</summary>
      <param name="path">Файл, в который осуществляется запись.</param>
      <param name="contents">Строки, записываемые в файл.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержит только пробелы или содержит один или несколько недопустимых символов определяется <see cref="M:System.IO.Path.GetInvalidPathChars" /> метод.</exception>
      <exception cref="T:System.ArgumentNullException">Либо<paramref name=" path " />или <paramref name="contents" /> — null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина параметра <paramref name="path" /> превышает определенное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="path" /> определяет файл, доступный только для чтения.-или-Эта операция не поддерживается на текущей платформе.-или-<paramref name="path" /> является каталогом.-или-У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Создает новый файл, используя указанную кодировку, записывает коллекцию строк в этот файл, затем закрывает файл.</summary>
      <param name="path">Файл, в который осуществляется запись.</param>
      <param name="contents">Строки, записываемые в файл.</param>
      <param name="encoding">Кодировка символов, которую нужно использовать.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержит только пробелы или содержит один или несколько недопустимых символов определяется <see cref="M:System.IO.Path.GetInvalidPathChars" /> метод.</exception>
      <exception cref="T:System.ArgumentNullException">Либо<paramref name=" path" />,<paramref name=" contents" />, или <paramref name="encoding" /> — null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="path" /> недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина параметра <paramref name="path" /> превышает определенное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="path" /> определяет файл, доступный только для чтения.-или-Эта операция не поддерживается на текущей платформе.-или-<paramref name="path" /> является каталогом.-или-У вызывающего объекта отсутствует необходимое разрешение.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>Создает новый файл, записывает в него указанную строку и затем закрывает файл.Если целевой файл уже существует, он будет переопределен.</summary>
      <param name="path">Файл, в который осуществляется запись. </param>
      <param name="contents">Строка, которую нужно записать в файл. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="path" /> имеет значение null, или параметр <paramref name="contents" /> пуст.  </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Создает новый файл, записывает указанную строку в этот файл, используя заданную кодировку, и затем закрывает файл.Если целевой файл уже существует, он будет переопределен.</summary>
      <param name="path">Файл, в который осуществляется запись. </param>
      <param name="contents">Строка, которую нужно записать в файл. </param>
      <param name="encoding">Кодировка, которую необходимо применить к строке.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="path" /> имеет значение null, или параметр <paramref name="contents" /> пуст. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> указывает файл, разрешенный только для чтения.-или- Эта операция не поддерживается на текущей платформе.-или- <paramref name="path" /> определяет каталог.-или- У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> имеет недопустимый формат. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>Предоставляет свойства и методы экземпляра для создания, копирования, удаления, перемещения и открытия файлов, а также позволяет создавать объекты <see cref="T:System.IO.FileStream" />.Этот класс не наследуется.Чтобы просмотреть исходный код .NET Framework для этого типа, см. Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>Выполняет инициализацию нового экземпляра класса <see cref="T:System.IO.FileInfo" />, который служит оболочкой для пути файла.</summary>
      <param name="fileName">Полное имя нового файла или относительное имя файла.Путь не должен заканчиваться символом разделителя каталогов.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="fileName" /> имеет значение null. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Имя файла является пустой строкой, содержит только пробелы или содержит недопустимые символы. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Отказано в доступе к <paramref name="fileName" />. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">Параметр <paramref name="fileName" /> содержит двоеточие (:) в середине строки. </exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>Создает <see cref="T:System.IO.StreamWriter" />, который добавляет текст в файл, представленный этим экземпляром <see cref="T:System.IO.FileInfo" />..</summary>
      <returns>Новый объект StreamWriter.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>Копирует существующий файл в новый файл и запрещает перезапись существующего файла.</summary>
      <returns>Новый файл с полным именем.</returns>
      <param name="destFileName">Имя нового файла, в который будет выполняться копирование. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="destFileName" /> является пустой строкой, содержит только пробелы или недопустимые символы. </exception>
      <exception cref="T:System.IO.IOException">Возникла ошибка, или файл назначения уже существует. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="destFileName" /> имеет значение null. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Передан путь к каталогу, или же файл перемещается на другой диск. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Каталог, заданный параметром <paramref name="destFileName" />, не существует.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">Значение <paramref name="destFileName" /> содержит двоеточие (:), но не задает букву тома. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>Копирует существующий файл в новый файл и разрешает перезапись существующего файла.</summary>
      <returns>Новый файл или перезапись существующего файла, если для параметра <paramref name="overwrite" /> задано значение true.Если файл существует и для параметра <paramref name="overwrite" /> задано значение false, создается исключение <see cref="T:System.IO.IOException" />.</returns>
      <param name="destFileName">Имя нового файла, в который будет выполняться копирование. </param>
      <param name="overwrite">Значение true позволяет разрешить перезапись существующего файла; в противном случае — значение false. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="destFileName" /> является пустой строкой, содержит только пробелы или недопустимые символы. </exception>
      <exception cref="T:System.IO.IOException">Возникла ошибка, или файл назначения уже существует, тогда как для параметра <paramref name="overwrite" /> задано значение false. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="destFileName" /> имеет значение null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Каталог, заданный параметром <paramref name="destFileName" />, не существует.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Передан путь к каталогу, или же файл перемещается на другой диск. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">Параметр <paramref name="destFileName" /> содержит двоеточие (:) в середине строки. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>Создает файл.</summary>
      <returns>Новый файл.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>Создает <see cref="T:System.IO.StreamWriter" />, который записывает новый текстовый файл.</summary>
      <returns>Новый объект StreamWriter.</returns>
      <exception cref="T:System.UnauthorizedAccessException">Имя файла представляет собой каталог. </exception>
      <exception cref="T:System.IO.IOException">Диск доступен только для чтения. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>Удаляет файл без возможности восстановления.</summary>
      <exception cref="T:System.IO.IOException">Файл назначения открыт или размещен в памяти на компьютере под управлением Microsoft Windows NT.-или-Для файла имеется открытый дескриптор, используется операционная система Windows XP или более ранняя версия.Открытый дескриптор мог появиться в результате перечисления каталогов и файлов.Для получения дополнительной информации см. Практическое руководство. Перечисление каталогов и файлов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Путь представляет собой каталог. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>Получает экземпляр родительского каталога.</summary>
      <returns>Объект <see cref="T:System.IO.DirectoryInfo" />, представляющий родительский каталог данного файла.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>Получает строку, представляющую полный путь к каталогу.</summary>
      <returns>Строка, представляющая полный путь к каталогу.</returns>
      <exception cref="T:System.ArgumentNullException">В качестве имени каталога передано значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Полный путь содержит 260 или более символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>Получает значение, показывающее, существует ли файл.</summary>
      <returns>Значение true, если файл существует; значение false, если файл не существует или если это каталог.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>Возвращает или задает значение, позволяющее определить, является ли текущий файл доступным только для чтения.</summary>
      <returns>Значение true, если текущий файл доступен только для чтения; в противном случае — значение false.</returns>
      <exception cref="T:System.IO.FileNotFoundException">Файл, описанный текущим объектом <see cref="T:System.IO.FileInfo" />, не найден.</exception>
      <exception cref="T:System.IO.IOException">При открытии файла возникла ошибка ввода-вывода.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Эта операция не поддерживается на текущей платформе.-или- У вызывающего объекта отсутствует необходимое разрешение.</exception>
      <exception cref="T:System.ArgumentException">Пользователь не имеет разрешения на запись, но попытался задать для этого свойства значение false.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>Получает размер текущего файла в байтах.</summary>
      <returns>Размер текущего файла в байтах.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> не может обновить состояние файла или каталога. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл не существует.-или- Свойство Length вызывается для каталога. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>Перемещает заданный файл в новое местоположение и разрешает переименование файла.</summary>
      <param name="destFileName">Путь, указывающий на местоположение, в которое необходимо переместить файл; в этом же пути можно задать и другое имя файла. </param>
      <exception cref="T:System.IO.IOException">Ошибка ввода-вывода, например, файл назначения уже существует или устройство назначения не готово. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="destFileName" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="destFileName" /> является пустой строкой, содержит только пробелы или недопустимые символы. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="destFileName" /> доступен только для чтения, или это каталог. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл не найден. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.NotSupportedException">Параметр <paramref name="destFileName" /> содержит двоеточие (:) в середине строки. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>Возвращает имя файла.</summary>
      <returns>Имя файла</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>Открывает файл в заданном режиме.</summary>
      <returns>Файл открыт в заданном режиме с доступом для чтения и записи и без предоставления общего доступа.</returns>
      <param name="mode">Константа <see cref="T:System.IO.FileMode" /> задает режим (например Open или Append), в котором необходимо открыть файл. </param>
      <exception cref="T:System.IO.FileNotFoundException">Файл не найден. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Файл доступен только для чтения, или это каталог. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">Файл уже открыт. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>Открывает файл в заданном режиме с доступом для чтения или записи, или и для чтения, и для записи.</summary>
      <returns>Объект <see cref="T:System.IO.FileStream" /> открыт в заданном режиме, с заданными правами и без предоставления общего доступа.</returns>
      <param name="mode">Константа <see cref="T:System.IO.FileMode" /> задает режим (например Open или Append), в котором необходимо открыть файл. </param>
      <param name="access">Константа <see cref="T:System.IO.FileAccess" /> задает доступ к открываемому файлу: Read, Write или ReadWrite. </param>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл не найден. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="path" /> доступен только для чтения, или это каталог. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">Файл уже открыт. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Открывает файл в заданном режиме с доступом для чтения, записи или и для чтения, и для записи и с заданным параметром совместного доступа.</summary>
      <returns>Объект <see cref="T:System.IO.FileStream" /> открыт в заданном режиме, с заданными правами и параметром общего доступа.</returns>
      <param name="mode">Константа <see cref="T:System.IO.FileMode" /> задает режим (например Open или Append), в котором необходимо открыть файл. </param>
      <param name="access">Константа <see cref="T:System.IO.FileAccess" /> задает доступ к открываемому файлу: Read, Write или ReadWrite. </param>
      <param name="share">Константа <see cref="T:System.IO.FileShare" /> задает тип доступа к файлу для других объектов FileStream. </param>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл не найден. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="path" /> доступен только для чтения, или это каталог. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">Файл уже открыт. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>Создает доступный только для чтения поток <see cref="T:System.IO.FileStream" />.</summary>
      <returns>Новый объект <see cref="T:System.IO.FileStream" />, доступный только для чтения.</returns>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="path" /> доступен только для чтения, или это каталог. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.IOException">Файл уже открыт. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>Создает поток <see cref="T:System.IO.StreamReader" /> с кодировкой UTF-8, который считывает данные из существующего текстового файла.</summary>
      <returns>Новый объект StreamReader с кодировкой UTF8.</returns>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл не найден. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="path" /> доступен только для чтения, или это каталог. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>Создает доступный только для чтения поток <see cref="T:System.IO.FileStream" />.</summary>
      <returns>Объект <see cref="T:System.IO.FileStream" />, доступный только для записи, не предназначенный для совместного использования и служащий для нового или существующего файла.</returns>
      <exception cref="T:System.UnauthorizedAccessException">Указанный путь при создании экземпляра объекта <see cref="T:System.IO.FileInfo" /> доступен только для чтения или является каталогом.  </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Путь, заданный при создании экземпляра объекта <see cref="T:System.IO.FileInfo" />, является недопустимым; возможно, он соответствует неподключенному диску. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>Возвращает путь в виде строки.</summary>
      <returns>Строка, содержащая путь.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileOptions">
      <summary>Представляет дополнительные параметры для создания объекта <see cref="T:System.IO.FileStream" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileOptions.Asynchronous">
      <summary>Указывает, что файл может использоваться для асинхронного чтения и записи. </summary>
    </member>
    <member name="F:System.IO.FileOptions.DeleteOnClose">
      <summary>Указывает, что файл автоматически удаляется, если он не будет больше использоваться.</summary>
    </member>
    <member name="F:System.IO.FileOptions.Encrypted">
      <summary>Указывает, что файл является зашифрованным и может быть расшифрован только с использованием той же учетной записи пользователя, которая применялась для шифрования.</summary>
    </member>
    <member name="F:System.IO.FileOptions.None">
      <summary>Указывает, что при создании объекта <see cref="T:System.IO.FileStream" /> не должны использоваться дополнительные параметры.</summary>
    </member>
    <member name="F:System.IO.FileOptions.RandomAccess">
      <summary>Указывается, что доступ к файлу осуществляется произвольно.Система может использовать это в качестве подсказки для оптимизации кэширования файла.</summary>
    </member>
    <member name="F:System.IO.FileOptions.SequentialScan">
      <summary>Указывает, что доступ к файлу осуществляется последовательно от начала к концу.Система может использовать это в качестве подсказки для оптимизации кэширования файла.Если в приложении указатель позиции в файле перемещается для произвольного доступа, оптимального кэширования можно не достигнуть, однако правильная работа гарантируется.</summary>
    </member>
    <member name="F:System.IO.FileOptions.WriteThrough">
      <summary>Указывает, что запись в системе должна выполняться через любой промежуточный кэш и переходить напрямую на диск.</summary>
    </member>
    <member name="T:System.IO.FileStream">
      <summary>Предоставляет <see cref="T:System.IO.Stream" /> в файле, поддерживая синхронные и асинхронные операции чтения и записи.Чтобы просмотреть исходный код .NET Framework для этого типа, см. Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.IO.FileStream" /> для заданного дескриптора файла с указанными разрешениями на чтение и запись. </summary>
      <param name="handle">Дескриптор для файла, являющегося текущим объектом FileStream, будет инкапсулирован. </param>
      <param name="access">Константа, которая задает свойства <see cref="P:System.IO.FileStream.CanRead" /> и <see cref="P:System.IO.FileStream.CanWrite" /> объекта FileStream. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="access" /> не является полем <see cref="T:System.IO.FileAccess" />. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода, например ошибка диска.-или-Поток закрыт. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Запрошенный параметр <paramref name="access" /> не разрешен операционной системой для заданного дескриптора файла, например, когда параметр <paramref name="access" /> равен Write или ReadWrite, а дескриптор файла установлен на доступ только для чтения. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.IO.FileStream" /> для заданного дескриптора файла, используя указанные разрешения на чтение и запись и размер буфера.</summary>
      <param name="handle">Дескриптор для файла, являющегося текущим объектом FileStream, будет инкапсулирован. </param>
      <param name="access">Константа <see cref="T:System.IO.FileAccess" />, которая задает свойства <see cref="P:System.IO.FileStream.CanRead" /> и <see cref="P:System.IO.FileStream.CanWrite" /> объекта FileStream. </param>
      <param name="bufferSize">Положительное значение <see cref="T:System.Int32" />, большее 0, определяющее размер буфера.Размер буфера по умолчанию — 4096.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="handle" /> является недействительным дескриптором.-или-Параметр <paramref name="handle" /> является синхронным дескриптором, и он был использован асинхронно. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="bufferSize" /> является отрицательным. </exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода, например ошибка диска.-или-Поток закрыт.  </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Запрошенный параметр <paramref name="access" /> не разрешен операционной системой для заданного дескриптора файла, например, когда параметр <paramref name="access" /> равен Write или ReadWrite, а дескриптор файла установлен на доступ только для чтения. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.IO.FileStream" /> для заданного дескриптора файла, используя указанные разрешения на чтение и запись, размер буфера и синхронное или асинхронное состояние.</summary>
      <param name="handle">Дескриптор файла для файла, который будет инкапсулироваться с помощью объекта FileStream. </param>
      <param name="access">Константа, которая задает свойства <see cref="P:System.IO.FileStream.CanRead" /> и <see cref="P:System.IO.FileStream.CanWrite" /> объекта FileStream. </param>
      <param name="bufferSize">Положительное значение <see cref="T:System.Int32" />, большее 0, определяющее размер буфера.Размер буфера по умолчанию — 4096.</param>
      <param name="isAsync">Значение true, если этот дескриптор был открыт асинхронно (т. е. в режиме перекрывающегося ввода-вывода); в противном случае — значение false. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="handle" /> является недействительным дескриптором.-или-Параметр <paramref name="handle" /> является синхронным дескриптором, и он был использован асинхронно. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="bufferSize" /> является отрицательным. </exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода, например ошибка диска.-или-Поток закрыт.  </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Запрошенный параметр <paramref name="access" /> не разрешен операционной системой для заданного дескриптора файла, например, когда параметр <paramref name="access" /> равен Write или ReadWrite, а дескриптор файла установлен на доступ только для чтения. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.IO.FileStream" /> указанным путем и режимом создания.</summary>
      <param name="path">Абсолютный или относительный путь к файлу, который будет инкапсулироваться объектом FileStream. </param>
      <param name="mode">Константа, определяющая способ открытия или создания файла. </param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой (""), содержит только пробелы или содержит один или несколько недопустимых знаков. -или-<paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде без NTFS.</exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Невозможно найти файл, например, когда <paramref name="mode" /> содержит FileMode.Truncate или FileMode.Open, а файл, заданный в <paramref name="path" />, не существует.Файл должен уже существовать в этих режимах.</exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода, например, задано FileMode.CreateNew, когда файл, указанный в <paramref name="path" />, уже существует.-или-Поток закрыт. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="mode" /> содержит недопустимое значение. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.IO.FileStream" /> заданным путем, режимом создания и разрешением на чтение и запись.</summary>
      <param name="path">Абсолютный или относительный путь к файлу, который будет инкапсулироваться объектом FileStream. </param>
      <param name="mode">Константа, определяющая способ открытия или создания файла. </param>
      <param name="access">Константа, определяющая способ доступа к файлу объекта FileStream.Также определяет значения, возвращаемые свойствами <see cref="P:System.IO.FileStream.CanRead" /> и <see cref="P:System.IO.FileStream.CanWrite" /> объекта FileStream.Свойство <see cref="P:System.IO.FileStream.CanSeek" /> имеет значение true, если параметр <paramref name="path" /> задает файл на диске.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой (""), содержит только пробелы или содержит один или несколько недопустимых знаков. -или-<paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде без NTFS.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Невозможно найти файл, например, когда <paramref name="mode" /> содержит FileMode.Truncate или FileMode.Open, а файл, заданный в <paramref name="path" />, не существует.Файл должен уже существовать в этих режимах.</exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода, например, задано FileMode.CreateNew, когда файл, указанный в <paramref name="path" />, уже существует. -или-Поток закрыт.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Запрошенный параметр <paramref name="access" /> не разрешен операционной системой для заданного <paramref name="path" />, например, когда параметр <paramref name="access" /> равен Write или ReadWrite, а файл или каталог установлены на доступ только для чтения. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="mode" /> содержит недопустимое значение. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.IO.FileStream" /> заданным путем, режимом создания, разрешением на чтение и запись и разрешением на совместное использование.</summary>
      <param name="path">Абсолютный или относительный путь к файлу, который будет инкапсулироваться объектом FileStream. </param>
      <param name="mode">Константа, определяющая способ открытия или создания файла. </param>
      <param name="access">Константа, определяющая способ доступа к файлу объекта FileStream.Также определяет значения, возвращаемые свойствами <see cref="P:System.IO.FileStream.CanRead" /> и <see cref="P:System.IO.FileStream.CanWrite" /> объекта FileStream.Свойство <see cref="P:System.IO.FileStream.CanSeek" /> имеет значение true, если параметр <paramref name="path" /> задает файл на диске.</param>
      <param name="share">Константа, определяющая способ совместного использования файла процессами. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой (""), содержит только пробелы или содержит один или несколько недопустимых знаков. -или-<paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде без NTFS.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Невозможно найти файл, например, когда <paramref name="mode" /> содержит FileMode.Truncate или FileMode.Open, а файл, заданный в <paramref name="path" />, не существует.Файл должен уже существовать в этих режимах.</exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода, например, задано FileMode.CreateNew, когда файл, указанный в <paramref name="path" />, уже существует. -или-Компьютер работает под управлением операционной системы Windows 98 или Windows 98 Second Edition, и для параметра <paramref name="share" /> задано значение FileShare.Delete.-или-Поток закрыт.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Запрошенный параметр <paramref name="access" /> не разрешен операционной системой для заданного <paramref name="path" />, например, когда параметр <paramref name="access" /> равен Write или ReadWrite, а файл или каталог установлены на доступ только для чтения. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="mode" /> содержит недопустимое значение. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.IO.FileStream" /> заданным путем, режимом создания, разрешениями на чтение и запись и на совместное использование, а также размером буфера.</summary>
      <param name="path">Абсолютный или относительный путь к файлу, который будет инкапсулироваться объектом FileStream. </param>
      <param name="mode">Константа, определяющая способ открытия или создания файла. </param>
      <param name="access">Константа, определяющая способ доступа к файлу объекта FileStream.Также определяет значения, возвращаемые свойствами <see cref="P:System.IO.FileStream.CanRead" /> и <see cref="P:System.IO.FileStream.CanWrite" /> объекта FileStream.Свойство <see cref="P:System.IO.FileStream.CanSeek" /> имеет значение true, если параметр <paramref name="path" /> задает файл на диске.</param>
      <param name="share">Константа, определяющая способ совместного использования файла процессами. </param>
      <param name="bufferSize">Положительное значение <see cref="T:System.Int32" />, большее 0, определяющее размер буфера.Размер буфера по умолчанию — 4096.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой (""), содержит только пробелы или содержит один или несколько недопустимых знаков. -или-<paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде без NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="bufferSize" /> имеет отрицательное значение или равен нулю.-или- <paramref name="mode" />, <paramref name="access" /> или <paramref name="share" /> содержит недопустимое значение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Невозможно найти файл, например, когда <paramref name="mode" /> содержит FileMode.Truncate или FileMode.Open, а файл, заданный в <paramref name="path" />, не существует.Файл должен уже существовать в этих режимах.</exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода, например, задано FileMode.CreateNew, когда файл, указанный в <paramref name="path" />, уже существует. -или-Компьютер работает под управлением операционной системы Windows 98 или Windows 98 Second Edition, и для параметра <paramref name="share" /> задано значение FileShare.Delete.-или-Поток закрыт.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Запрошенный параметр <paramref name="access" /> не разрешен операционной системой для заданного <paramref name="path" />, например, когда параметр <paramref name="access" /> равен Write или ReadWrite, а файл или каталог установлены на доступ только для чтения. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.IO.FileStream" /> заданным путем, режимом создания, разрешениями на чтение и запись и совместное использование, размером буфера и синхронным или асинхронным состоянием.</summary>
      <param name="path">Абсолютный или относительный путь к файлу, который будет инкапсулироваться объектом FileStream. </param>
      <param name="mode">Константа, определяющая способ открытия или создания файла. </param>
      <param name="access">Константа, определяющая способ доступа к файлу объекта FileStream.Также определяет значения, возвращаемые свойствами <see cref="P:System.IO.FileStream.CanRead" /> и <see cref="P:System.IO.FileStream.CanWrite" /> объекта FileStream.Свойство <see cref="P:System.IO.FileStream.CanSeek" /> имеет значение true, если параметр <paramref name="path" /> задает файл на диске.</param>
      <param name="share">Константа, определяющая способ совместного использования файла процессами. </param>
      <param name="bufferSize">Положительное значение <see cref="T:System.Int32" />, большее 0, определяющее размер буфера.Размер буфера по умолчанию: 4096.</param>
      <param name="useAsync">Указывает, использовать ли асинхронный ввод-вывод или синхронный ввод-вывод.Однако обратите внимание, что базовая операционная система может не поддерживать асинхронный ввод-вывод, поэтому, когда задается значение true, дескриптор может быть открыт синхронно в зависимости от платформы.При асинхронном открытии методы <see cref="M:System.IO.FileStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> и <see cref="M:System.IO.FileStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> выполняются лучше при чтении или записи больших объемов, но они могут работать намного медленнее при чтении или записи маленьких объемов данных.Если приложение разработано таким образом, чтобы получить преимущества асинхронного ввода-вывода, установите для параметра <paramref name="useAsync" /> значение true.При корректном использовании асинхронного ввода-вывода быстродействие приложения может возрасти вплоть до 10 раз, но использование такого режима ввода-вывода без переработки приложения может во столько же раз ухудшить быстродействие.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой (""), содержит только пробелы или содержит один или несколько недопустимых знаков. -или-<paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде без NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="bufferSize" /> имеет отрицательное значение или равен нулю.-или- <paramref name="mode" />, <paramref name="access" /> или <paramref name="share" /> содержит недопустимое значение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Невозможно найти файл, например, когда <paramref name="mode" /> содержит FileMode.Truncate или FileMode.Open, а файл, заданный в <paramref name="path" />, не существует.Файл должен уже существовать в этих режимах.</exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода, например, задано FileMode.CreateNew, когда файл, указанный в <paramref name="path" />, уже существует.-или- Компьютер работает под управлением операционной системы Windows 98 или Windows 98 Second Edition, и для параметра <paramref name="share" /> задано значение FileShare.Delete.-или-Поток закрыт.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Запрошенный параметр <paramref name="access" /> не разрешен операционной системой для заданного <paramref name="path" />, например, когда параметр <paramref name="access" /> равен Write или ReadWrite, а файл или каталог установлены на доступ только для чтения. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.IO.FileOptions)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.IO.FileStream" /> заданным путем, режимом создания, разрешениями на чтение, запись и совместное использование, доступом для других FileStreams к этому же файлу, размером буфера и дополнительными параметрами файла.</summary>
      <param name="path">Абсолютный или относительный путь к файлу, который будет инкапсулироваться объектом FileStream. </param>
      <param name="mode">Константа, определяющая способ открытия или создания файла. </param>
      <param name="access">Константа, определяющая способ доступа к файлу объекта FileStream.Также определяет значения, возвращаемые свойствами <see cref="P:System.IO.FileStream.CanRead" /> и <see cref="P:System.IO.FileStream.CanWrite" /> объекта FileStream.Свойство <see cref="P:System.IO.FileStream.CanSeek" /> имеет значение true, если параметр <paramref name="path" /> задает файл на диске.</param>
      <param name="share">Константа, определяющая способ совместного использования файла процессами. </param>
      <param name="bufferSize">Положительное значение <see cref="T:System.Int32" />, большее 0, определяющее размер буфера.Размер буфера по умолчанию — 4096.</param>
      <param name="options">Значение, задающее дополнительные параметры файла.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="path" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="path" /> является пустой строкой (""), содержит только пробелы или содержит один или несколько недопустимых знаков. -или-<paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде NTFS.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> ссылается на устройство, которое не является файловым, например "con:", "com1:", "lpt1:" и т. д.в среде без NTFS.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Параметр <paramref name="bufferSize" /> имеет отрицательное значение или равен нулю.-или- <paramref name="mode" />, <paramref name="access" /> или <paramref name="share" /> содержит недопустимое значение. </exception>
      <exception cref="T:System.IO.FileNotFoundException">Невозможно найти файл, например, когда <paramref name="mode" /> содержит FileMode.Truncate или FileMode.Open, а файл, заданный в <paramref name="path" />, не существует.Файл должен уже существовать в этих режимах.</exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода, например, задано FileMode.CreateNew, когда файл, указанный в <paramref name="path" />, уже существует.-или-Поток закрыт.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим; возможно, он соответствует неподключенному диску. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Запрошенный параметр <paramref name="access" /> не разрешен операционной системой для заданного <paramref name="path" />, например, когда параметр <paramref name="access" /> равен Write или ReadWrite, а файл или каталог установлены на доступ только для чтения. -или-<see cref="F:System.IO.FileOptions.Encrypted" /> задается для <paramref name="options" />, но шифрование файлов не поддерживается на текущей платформе.</exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
    </member>
    <member name="P:System.IO.FileStream.CanRead">
      <summary>Возвращает значение, определяющее в текущем потоке наличие поддержки операций чтения.</summary>
      <returns>Значение true, если в потоке поддерживаются операции чтения; значение false, если поток закрыт или открыт только для записи.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanSeek">
      <summary>Возвращает значение, определяющее в текущем потоке наличие поддержки операций поиска.</summary>
      <returns>Значение true, если поток поддерживает поиск; значение false, если поток закрыт или объект FileStream был сконструирован из дескриптора операционной системы, такого как канал или вывод на консоль.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanWrite">
      <summary>Возвращает значение, определяющее в текущем потоке наличие поддержки операций записи.</summary>
      <returns>Значение true, если поток поддерживает операции записи; значение false, если поток закрыт или открыт только для чтения.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.IO.FileStream" />, а при необходимости освобождает также управляемые ресурсы.</summary>
      <param name="disposing">Значение true позволяет освободить как управляемые, так и неуправляемые ресурсы; значение false освобождает только неуправляемые ресурсы. </param>
    </member>
    <member name="M:System.IO.FileStream.Finalize">
      <summary>Гарантирует, что ресурсы освобождены и выполнены другие операции очистки, когда сборщик мусора восстанавливает FileStream.</summary>
    </member>
    <member name="M:System.IO.FileStream.Flush">
      <summary>Очищает буферы для этого потока и вызывает запись всех буферизованных данных в файл.</summary>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода. </exception>
      <exception cref="T:System.ObjectDisposedException">Поток закрыт. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Flush(System.Boolean)">
      <summary>Очищает буферы для этого потока и вызывает запись всех буферизованных данных в файл, а также очищает все буферы промежуточных файлов.</summary>
      <param name="flushToDisk">Значение true для записи на диск  буферов всех промежуточных файлов; в противном случае — значение false. </param>
    </member>
    <member name="M:System.IO.FileStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Асинхронно очищает все буферы данного потока, вызывает запись буферизованных данных в базовое устройство и отслеживает запросы отмены. </summary>
      <returns>Задача, представляющая асинхронную операцию очистки. </returns>
      <param name="cancellationToken">Токен для отслеживания запросов отмены.</param>
      <exception cref="T:System.ObjectDisposedException">Поток был ликвидирован.</exception>
    </member>
    <member name="P:System.IO.FileStream.IsAsync">
      <summary>Возвращает значение, определяющее, как был открыт объект FileStream: синхронно или асинхронно.</summary>
      <returns>Значение true, если FileStream был открыт асинхронно, в противном случае — значение false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Length">
      <summary>Возвращает длину потока в байтах.</summary>
      <returns>Длинное значение, представляющее длину потока в байтах.</returns>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.FileStream.CanSeek" /> для этого потока имеет значение false. </exception>
      <exception cref="T:System.IO.IOException">Возникла ошибка ввода-вывода, например файл закрыт. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Name">
      <summary>Возвращает имя объекта FileStream, которое было передано в конструктор.</summary>
      <returns>Строка, содержащая имя объекта FileStream.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileStream.Position">
      <summary>Возвращает или задает текущую позицию этого потока.</summary>
      <returns>Текущая позиция потока.</returns>
      <exception cref="T:System.NotSupportedException">Этот поток не поддерживает поиск. </exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода. -или-Для позиции было задано большое значение за пределами конца потока в Windows 98 и более ранних версиях.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Попытка установить положение в отрицательное значение. </exception>
      <exception cref="T:System.IO.EndOfStreamException">Попытка поиска за пределами потока, который не поддерживает этот метод. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Выполняет чтение блока байтов из потока и запись данных в заданный буфер.</summary>
      <returns>Общее количество байтов, считанных в буфер.Оно может быть меньше запрошенного числа байтов, если в настоящее время не имеется нужного количества байтов, или же равно нулю, если достигнут конец потока.</returns>
      <param name="array">При возврате этот метод содержит указанный массив байтов, в котором значения в диапазоне от <paramref name="offset" /> до (<paramref name="offset" /> + <paramref name="count" /> - 1<paramref name=")" />) заменены байтами, считанными из текущего источника. </param>
      <param name="offset">Смещение в байтах в массиве <paramref name="array" />, в который будут помещены считанные байты. </param>
      <param name="count">Максимальное число байтов, предназначенных для чтения. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="offset" /> или <paramref name="count" /> является отрицательным. </exception>
      <exception cref="T:System.NotSupportedException">Поток не поддерживает чтение. </exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> и <paramref name="count" /> определяют неверный диапазон в <paramref name="array" />. </exception>
      <exception cref="T:System.ObjectDisposedException">Методы были вызваны после закрытия потока. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Асинхронно считывает последовательность байтов из текущего потока, перемещает позицию в потоке на число считанных байтов и отслеживает запросы отмены.</summary>
      <returns>Задача, представляющая асинхронную операцию чтения.Значение параметра <paramref name="TResult" /> содержит общее число байтов, считанных в буфер.Значение результата может быть меньше запрошенного числа байтов, если число доступных в данный момент байтов меньше запрошенного числа, или результат может быть равен 0 (нулю), если был достигнут конец потока.</returns>
      <param name="buffer">Буфер, в который записываются данные.</param>
      <param name="offset">Смещение байтов в <paramref name="buffer" />, с которого начинается запись данных из потока.</param>
      <param name="count">Максимальное число байтов, предназначенных для чтения.</param>
      <param name="cancellationToken">Токен для отслеживания запросов отмены.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="buffer" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="offset" /> или <paramref name="count" /> является отрицательным.</exception>
      <exception cref="T:System.ArgumentException">Сумма значений параметров <paramref name="offset" /> и <paramref name="count" /> больше длины буфера.</exception>
      <exception cref="T:System.NotSupportedException">Поток не поддерживает чтение.</exception>
      <exception cref="T:System.ObjectDisposedException">Поток был ликвидирован.</exception>
      <exception cref="T:System.InvalidOperationException">Поток в настоящее время используется предыдущей операцией чтения. </exception>
    </member>
    <member name="M:System.IO.FileStream.ReadByte">
      <summary>Считывает байт из файла и перемещает положение чтения на один байт.</summary>
      <returns>Байт приводится к типу <see cref="T:System.Int32" /> или -1, если достигнут конец потока.</returns>
      <exception cref="T:System.NotSupportedException">Текущий поток не поддерживает чтение. </exception>
      <exception cref="T:System.ObjectDisposedException">Текущий поток закрыт. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.SafeFileHandle">
      <summary>Возвращает объект <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" />, представляющий дескриптор файла операционной системы для файла, инкапсулируемого текущим объектом <see cref="T:System.IO.FileStream" />.</summary>
      <returns>Объект, представляющий дескриптор файла операционной системы для файла, инкапсулируемого текущим объектом <see cref="T:System.IO.FileStream" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Устанавливает текущее положение этого потока на заданное значение.</summary>
      <returns>Новая позиция в потоке.</returns>
      <param name="offset">Указатель относительно начальной точки <paramref name="origin" />, от которой начинается поиск. </param>
      <param name="origin">Задает начальную, конечную или текущую позицию как опорную точку для <paramref name="offset" />, используя значение типа <see cref="T:System.IO.SeekOrigin" />. </param>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода. </exception>
      <exception cref="T:System.NotSupportedException">Данный поток не поддерживает поиск, например, если FileStream сконструирован из канала или выхода консоли. </exception>
      <exception cref="T:System.ArgumentException">Попытка поиска выполняется до начала потока. </exception>
      <exception cref="T:System.ObjectDisposedException">Методы были вызваны после закрытия потока. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.SetLength(System.Int64)">
      <summary>Устанавливает длину этого потока на заданное значение.</summary>
      <param name="value">Новая длина потока. </param>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода. </exception>
      <exception cref="T:System.NotSupportedException">Этот поток не поддерживает ни поиск, ни запись. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Сделана попытка установить значение параметра <paramref name="value" /> меньше 0. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Записывает блок байтов в файловый поток.</summary>
      <param name="array">Буфер, содержащий данные для записи в поток.</param>
      <param name="offset">Смещение байтов (начиная с нуля) в объекте <paramref name="array" />, с которого начинается копирование байтов в поток. </param>
      <param name="count">Максимальное число байтов для записи. </param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="array" /> имеет значение null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> и <paramref name="count" /> определяют неверный диапазон в <paramref name="array" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="offset" /> или <paramref name="count" /> является отрицательным. </exception>
      <exception cref="T:System.IO.IOException">Произошла ошибка ввода-вывода. -или-Возможно, другой поток вызвал неожиданное изменение в положении дескриптора файла операционной системы. </exception>
      <exception cref="T:System.ObjectDisposedException">Поток закрыт. </exception>
      <exception cref="T:System.NotSupportedException">Текущий экземпляр потока не поддерживает запись. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Асинхронно записывает последовательность байтов в текущий поток, перемещает текущую позицию внутри потока на число записанных байтов и отслеживает запросы отмены. </summary>
      <returns>Задача, представляющая асинхронную операцию записи.</returns>
      <param name="buffer">Буфер, из которого записываются данные. </param>
      <param name="offset">Смещение байтов (начиная с нуля) в объекте <paramref name="buffer" />, с которого начинается копирование байтов в поток.</param>
      <param name="count">Максимальное число байтов для записи.</param>
      <param name="cancellationToken">Токен для отслеживания запросов отмены.</param>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="buffer" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="offset" /> или <paramref name="count" /> является отрицательным.</exception>
      <exception cref="T:System.ArgumentException">Сумма значений параметров <paramref name="offset" /> и <paramref name="count" /> больше длины буфера.</exception>
      <exception cref="T:System.NotSupportedException">Поток не поддерживает запись.</exception>
      <exception cref="T:System.ObjectDisposedException">Поток был ликвидирован.</exception>
      <exception cref="T:System.InvalidOperationException">Поток в настоящее время используется предыдущей операцией записи. </exception>
    </member>
    <member name="M:System.IO.FileStream.WriteByte(System.Byte)">
      <summary>Запись байта в текущую позицию в потоке файла.</summary>
      <param name="value">Байт, который необходимо записать в поток. </param>
      <exception cref="T:System.ObjectDisposedException">Поток закрыт. </exception>
      <exception cref="T:System.NotSupportedException">Поток не поддерживает запись. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>Предоставляет базовый класс для объектов <see cref="T:System.IO.FileInfo" /> и <see cref="T:System.IO.DirectoryInfo" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.IO.FileSystemInfo" />.</summary>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>Получает или задает атрибуты для текущего файла или каталога.</summary>
      <returns>Атрибуты <see cref="T:System.IO.FileAttributes" /> текущего объекта <see cref="T:System.IO.FileSystemInfo" />.</returns>
      <exception cref="T:System.IO.FileNotFoundException">Заданный файл не существует. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <exception cref="T:System.ArgumentException">Вызывающий оператор пытается установить недействительный атрибут файла. -или-Пользователь пытается задать значение атрибута, но не имеет разрешения на запись.</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> не может инициализировать данные. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>Получает или задает время создания текущего файла или каталога.</summary>
      <returns>Дата и временя создания текущего объекта <see cref="T:System.IO.FileSystemInfo" />.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> не может инициализировать данные. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Вызывающий объект пытается задать недействительное время создания.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>Получает или задает время создания текущего файла или каталога в формате UTC.</summary>
      <returns>Дата и время создания текущего объекта <see cref="T:System.IO.FileSystemInfo" /> в формате UTC.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> не может инициализировать данные. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Вызывающий объект пытается задать недействительное время доступа.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>Удаляет файл или каталог.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.IO.IOException">Для файла или каталога имеется открытый дескриптор, используется операционная система Windows XP или более ранняя версия.Открытый дескриптор мог появиться в результате перечисления каталогов и файлов.Для получения дополнительной информации см. Практическое руководство. Перечисление каталогов и файлов.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>Получает значение, показывающее, существует ли данный файл или каталог.</summary>
      <returns>Значение true, если файл или каталог существует; в противном случае — значение false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>Получает строку, содержащую расширение файла.</summary>
      <returns>Строка, содержащая расширение <see cref="T:System.IO.FileSystemInfo" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>Получает полный путь к каталогу или файлу.</summary>
      <returns>Строка, содержащая полный путь.</returns>
      <exception cref="T:System.IO.PathTooLongException">Полный путь и имя файла содержат 260 или более символов.</exception>
      <exception cref="T:System.Security.SecurityException">У вызывающего объекта отсутствует необходимое разрешение. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>Представляет полный путь к каталогу или файлу.</summary>
      <exception cref="T:System.IO.PathTooLongException">Полный путь содержит 260 или более символов.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>Получает или задает время последнего доступа к текущему файлу или каталогу.</summary>
      <returns>Время последнего доступа к текущему файлу или каталогу.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> не может инициализировать данные. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Вызывающий объект пытается задать недействительное время доступа</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>Получает или задает дату и время последнего доступа к заданному файлу или каталогу в формате UTC.</summary>
      <returns>Время последнего доступа к текущему файлу или каталогу в формате UTC.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> не может инициализировать данные. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Вызывающий объект пытается задать недействительное время доступа.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>Получает или задает время последней операции записи в текущий файл или каталог.</summary>
      <returns>Время последней операции записи в текущий файл.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> не может инициализировать данные. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Вызывающий объект пытается задать недействительное время записи.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>Получает или задает время последней операции записи в текущий файл или каталог в формате UTC.</summary>
      <returns>Время последней операции записи в текущий файл в формате UTC.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> не может инициализировать данные. </exception>
      <exception cref="T:System.PlatformNotSupportedException">Текущей операционной системой не является Windows NT или более поздняя версия.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Вызывающий объект пытается задать недействительное время записи.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>Для файлов — получает имя файла.Для каталогов — получает имя последнего каталога в иерархии, если таковая существует.В противном случае свойство Name получает имя данного каталога.</summary>
      <returns>Строка, представляющая собой имя родительского каталога, имя последнего каталога в иерархии или имя файла, включая расширение имени файла.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>Первоначально заданный пользователем относительный или абсолютный путь.</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>Обновляет состояние объекта.</summary>
      <exception cref="T:System.IO.IOException">Устройство, например дисковод, не готово. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>Указывает, следует ли выполнять поиск в текущем каталоге или в текущем каталоге и всех подкаталогах. </summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>Включает текущий каталог и все подкаталоги в операцию поиска.Этот параметр включает в поиск точки повторной обработки, такие как подключенные диски и символические ссылки.</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>Включает в операцию поиска только текущий каталог.</summary>
    </member>
  </members>
</doc>