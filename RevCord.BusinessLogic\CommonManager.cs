﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataAccess;
using System.IO;
using RevCord.Util;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.CommonEntities;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;

namespace RevCord.BusinessLogic
{
    public class CommonManager
    {
        public List<string> GetFieldValues(string FieldName, string TableName, string SearchCriteria
                                                    , string RoleId, string Criteria, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "GetFieldValues", tenantId, "GetFieldValues function has been called successfully."));
                return new CommonDAL(tenantId).GetFieldValues(FieldName, TableName, SearchCriteria, RoleId, Criteria);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "GetFieldValues", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "GetFieldValues", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public string GetLicenceInformation(int tenantId)
        {
            try
            {
                var licenseInfo = new CommonDAL(tenantId).GetLicenceInformation();
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "GetLicenceInformation", tenantId, "License information has been fetched successfully. licenseInfo = " + licenseInfo));
                return licenseInfo;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "GetLicenceInformation", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "GetLicenceInformation", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public string GetLicenceInfoAndAdInfo(out int noOfDomainUsers, int tenantId)
        {
            try
            {
                var licAndADInfo = new CommonDAL(tenantId).GetLicenceInfoAndAdInfo(out noOfDomainUsers);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "GetLicenceInfoAndAdInfo", tenantId, "License and AD information has been fetched successfully. licAndADInfo = " + licAndADInfo));
                return licAndADInfo;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "GetLicenceInfoAndAdInfo", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "GetLicenceInfoAndAdInfo", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region Custom Settings
        public bool ResetToDefaultIcons(string serverPhysicalPath, int tenantId)
        {
            try
            {
                var mediaIconsPath = serverPhysicalPath + @"\SystemUploadedFiles\MediaIcons";
                var defaultIconsPath = serverPhysicalPath + @"\SystemUploadedFiles\MediaIcons\DefaultIcons\";
                string[] sourceFiles = Directory.GetFiles(defaultIconsPath, "*.png").ToArray();

                foreach (var sourceFile in sourceFiles)
                {
                    string file = sourceFile.ToString();

                    string destination = mediaIconsPath + @"\Tenant_" + tenantId +@"\" + Path.GetFileName(file.ToString());
                    File.Copy(file, destination, true);
                }
                return true;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "ResetToDefaultIcons", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "ResetToDefaultIcons", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public RevcordConfiguration FetchConfiguration(int tenantId)
        {
            try
            {
                return new CommonDAL(tenantId).FetchConfiguration();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "FetchConfiguration", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "FetchConfiguration", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool UpdateConfiguration(int tenantId, RevcordConfiguration revcordConfiguration)
        {
            try
            {
                return new CommonDAL(tenantId).UpdateConfiguration(revcordConfiguration.NextGenRecorder);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "FetchConfiguration", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "FetchConfiguration", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region IP Camera Settings
        public List<IPCameraSetting> GetAllIPCameraSettings(CommonRequest commonRequest)
        {
            try
            {
                List<IPCameraSetting> ipCamSettings = new CommonDAL(commonRequest.TenantId).GetAllIPCameraSettings(commonRequest.IPCameraSetting.UserNum);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "GetAllIPCameraSettings", commonRequest.TenantId, "IP camera settings have been fetched successfully for the the user id = " + commonRequest.IPCameraSetting.UserNum));
                return ipCamSettings;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "GetAllIPCameraSettings", commonRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "GetAllIPCameraSettings", commonRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public IPCameraSetting GetIPCameraSettingsById(CommonRequest commonRequest)
        {
            try
            {
                IPCameraSetting ipCamSetting = new CommonDAL(commonRequest.TenantId).GetIPCameraSettingsById(commonRequest.IPCameraSetting.Id);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "GetIPCameraSettingsById", commonRequest.TenantId, "IP camera settings by Id have been fetched successfully. Id = " + commonRequest.IPCameraSetting.Id));
                return ipCamSetting;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "GetIPCameraSettingsById", commonRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "GetIPCameraSettingsById", commonRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int SaveIPCameraSettings(CommonRequest commonRequest)
        {
            try
            {
                IPCameraSetting ipCamSettings = commonRequest.IPCameraSetting;
                int rowsAffected = new CommonDAL(commonRequest.TenantId).SaveIPCameraSettings(ipCamSettings.UserNum, ipCamSettings.CompanyName, ipCamSettings.UserName, ipCamSettings.Password, ipCamSettings.Url);
                if (rowsAffected > 0)
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "SaveIPCameraSettings", commonRequest.TenantId, "IP camera settings have been saved successfully."));

                return rowsAffected;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "SaveIPCameraSettings", commonRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "SaveIPCameraSettings", commonRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int UpdateIPCameraSettings(CommonRequest commonRequest)
        {
            try
            {
                IPCameraSetting ipCamSettings = commonRequest.IPCameraSetting;
                int rowsAffected = new CommonDAL(commonRequest.TenantId).UpdateIPCameraSettings(ipCamSettings.Id, ipCamSettings.CompanyName, ipCamSettings.UserName, ipCamSettings.Password, ipCamSettings.Url);
                if (rowsAffected > 0)
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "UpdateIPCameraSettings", commonRequest.TenantId, "IP camera settings have been updated successfully."));

                return rowsAffected;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "UpdateIPCameraSettings", commonRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "UpdateIPCameraSettings", commonRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int DeleteIPCameraSettings(CommonRequest commonRequest)
        {
            try
            {
                int rowsAffected = new CommonDAL(commonRequest.TenantId).DeleteIPCameraSettings(commonRequest.IPCameraSetting.Id);
                if (rowsAffected > 0)
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "DeleteIPCameraSettings", commonRequest.TenantId, "IP camera settings have been deleted successfully."));

                return rowsAffected;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "DeleteIPCameraSettings", commonRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "DeleteIPCameraSettings", commonRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int UpdateIPCameraStatus(CommonRequest commonRequest)
        {
            try
            {
                IPCameraSetting ipCamSettings = commonRequest.IPCameraSetting;
                var rowsAffected = new CommonDAL(commonRequest.TenantId).UpdateIPCameraStatus(ipCamSettings.Id, ipCamSettings.IsEnable);
                if (rowsAffected > 0)
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "UpdateIPCameraStatus", commonRequest.TenantId, "IP camera status has been updated successfully. Id = " + ipCamSettings.Id + " status = " + ipCamSettings.IsEnable));
                return rowsAffected;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "UpdateIPCameraStatus", commonRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "UpdateIPCameraStatus", commonRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        
        public List<IPCamUserAssociation> GetUsersAssociatedWithIpCam(CommonRequest commonRequest)
        {
            try
            {
                IPCameraSetting ipCamSettings = commonRequest.IPCameraSetting;
                List <IPCamUserAssociation> _IPCamUserAssociation = new CommonDAL(commonRequest.TenantId).GetUsersAssociatedWithIpCam(ipCamSettings.Id);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "GetUsersAssociatedWithIpCam", commonRequest.TenantId, "User Fetched successfully. Id = " + ipCamSettings.Id + " status = " + ipCamSettings.IsEnable));
                return _IPCamUserAssociation;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "GetUsersAssociatedWithIpCam", commonRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "GetUsersAssociatedWithIpCam", commonRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public int saveUsersAssociatedWithIpCam(CommonRequest commonRequest)
        {
            try
            {
                IPCameraSetting ipCamSettings = commonRequest.IPCameraSetting;
                string UserAssociated = commonRequest.UserAssociated;
                var rowaffected = new CommonDAL(commonRequest.TenantId).saveUsersAssociatedWithIpCam(ipCamSettings.Id, commonRequest.UserAssociated);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Common, "GetUsersAssociatedWithIpCam", commonRequest.TenantId, "User Fetched successfully. Id = " + ipCamSettings.Id + " status = " + ipCamSettings.IsEnable));
                return rowaffected;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "GetUsersAssociatedWithIpCam", commonRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "GetUsersAssociatedWithIpCam", commonRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        
        #endregion
    }
}
