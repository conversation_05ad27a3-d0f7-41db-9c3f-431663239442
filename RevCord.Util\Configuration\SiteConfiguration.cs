﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace RevCord.Util.Configuration
{
    [XmlRoot("SiteConfiguration")]
    public class SiteConfiguration
    {
        [XmlElement("DALConnectionString")]
        public string DALConnectionString { get; set; }

        [XmlElement("RevLogDBConnectionString")]
        public string RevLogDBConnectionString { get; set; }

        [XmlElement("MasterDBConnectionString")]
        public string MasterDBConnectionString { get; set; }

        [XmlElement("RevSyncDALConnectionString")]
        public string RevSyncDALConnectionString { get; set; }

        [XmlElement("RevSyncMasterDALConnectionString")]
        public string RevSyncMasterDALConnectionString { get; set; }

        [XmlElement("SearchPageSize")]
        public int SearchPageSize { get; set; }

        [XmlElement("WebUrl")]
        public string WebUrl { get; set; }

        [XmlElement("HelperServiceAddress")]
        public string HelperServiceAddress { get; set; }

        [XmlElement("RevRecAddress")]
        public string RevRecAddress { get; set; }

        [XmlElement("MMSIQ3URL")]
        public string MMSIQ3URL { get; set; }

        [XmlElement("RevSyncURL")]
        public string RevSyncURL { get; set; }

        [XmlElement("RevSyncEnabled")]
        public bool RevSyncEnabled { get; set; }

        [XmlElement("RevSyncTenantID")]
        public int RevSyncTenantID { get; set; }

        [XmlElement("RevSyncISMT")]
        public bool RevSyncISMT { get; set; }

        [XmlElement("IsMTEnable")]
        public bool IsMTEnable { get; set; }

        [XmlElement("TenantID")]
        public int TenantID { get; set; }

        [XmlElement("RealTimeServer")]
        public string RealTimeServer { get; set; }

        [XmlElement("VodServer")]
        public string VodServer { get; set; }

        [XmlElement("IRStartupPlayer")]
        public string IRStartupPlayer { get; set; }

        [XmlElement("IRView")]
        public string IRView { get; set; }

        [XmlElement("FolderPathToZip")]
        public string FolderPathToZip { get; set; }

        [XmlElement("IsECEnabled")]
        public bool IsECEnabled { get; set; }

        [XmlElement("IsEnterpriseRecorder")]
        public bool IsEnterpriseRecorder { get; set; }

        [XmlElement("IsChainDBsConfigured")]
        public bool IsChainDBsConfigured { get; set; }

        [XmlElement("GoogleMapApiKey")]
        public string GoogleMapApiKey { get; set; }

        [XmlElement("PlaylistUploadRootFolder")]
        public string PlaylistUploadRootFolder { get; set; }

        [XmlElement("PlaylistUploadHttpRoot")]
        public string PlaylistUploadHttpRoot { get; set; }

        [XmlElement("PreviewImageMaxWidth")]
        public int PreviewImageMaxWidth { get; set; }

        [XmlElement("PreviewImageMaxHeight")]
        public int PreviewImageMaxHeight { get; set; }

        [XmlElement("Host")]
        public string Host { get; set; }

        [XmlElement("Port")]
        public int Port { get; set; }

        [XmlElement("EnableSsl")]
        public bool EnableSsl { get; set; }

        [XmlElement("UserName")]
        public string UserName { get; set; }

        [XmlElement("Password")]
        public string Password { get; set; }

        [XmlElement("TranscriptLength")]
        public int TranscriptLength { get; set; }

        [XmlElement("DVREnabled")]
        public bool DVREnabled { get; set; }

        [XmlElement("MaxExtensionCount")]
        public int MaxExtensionCount { get; set; }

        [XmlElement("AccountSid")]
        public string AccountSid { get; set; }

        [XmlElement("ApiSid")]
        public string ApiSid { get; set; }

        [XmlElement("ApiSecret")]
        public string ApiSecret { get; set; }

        [XmlElement("ChatServiceSid")]
        public string ChatServiceSid { get; set; }

        [XmlElement("TwilioSMSAccountSid")]
        public string TwilioSMSAccountSid { get; set; }

        [XmlElement("TwilioSMSAuthToken")]
        public string TwilioSMSAuthToken { get; set; }

        [XmlElement("TwilioSMSPhoneNo")]
        public string TwilioSMSPhoneNo { get; set; }

        [XmlElement("ConversationFilePath")]
        public string ConversationFilePath { get; set; }

        [XmlElement("IsRevcellEnabled")]
        public bool IsRevcellEnabled { get; set; }

        [XmlElement("RevcellProjectId")]
        public string RevcellProjectId { get; set; }

        [XmlElement("RevcellAuthToken")]
        public string RevcellAuthToken { get; set; }

        [XmlElement("IsActiveDirectory")]
        public bool IsActiveDirectory { get; set; }

        [XmlElement("PlivoAuthID")]
        public string PlivoAuthID { get; set; }

        [XmlElement("PlivoAuthToken")]
        public string PlivoAuthToken { get; set; }

        [XmlElement("PlivoEndpointAppId")]
        public string PlivoEndpointAppId { get; set; }

        [XmlElement("PlivoPhoneNumberAppId")]
        public string PlivoPhoneNumberAppId { get; set; }

        [XmlElement("PlivoDomain")]
        public string PlivoDomain { get; set; }

        [XmlElement("IsRevLogEnabled")]
        public bool IsRevLogEnabled { get; set; }

        [XmlElement("RevLogServiceURL")]
        public string RevLogServiceURL { get; set; }

        [XmlElement("RevViewServerURL")]
        public string RevViewServerURL { get; set; }

        [XmlElement("InspectionServerURL")]
        public string InspectionServerURL { get; set; }
        [XmlElement("RevMTAPIServerURL")]
        public string RevMTAPIServerURL { get; set; }

        [XmlElement("IsHostedOnLocalNetwork")]
        public bool IsHostedOnLocalNetwork { get; set; }

        [XmlElement("RapidSOSClientID")]
        public string RapidSOSClientID { get; set; }

        [XmlElement("RapidSOSClientSecret")]
        public string RapidSOSClientSecret { get; set; }

        [XmlElement("RapidSOSUserName")]
        public string RapidSOSUserName { get; set; }

        [XmlElement("RapidSOSPassword")]
        public string RapidSOSPassword { get; set; }

        [XmlElement("IsOnlyIQ3ModeEnabled")]
        public bool IsOnlyIQ3ModeEnabled { get; set; }

        [XmlElement("IsRoleBasedAccessEnabled")]
        public bool IsRoleBasedAccessEnabled { get; set; }

        [XmlElement("IsTeamsEnabled")]
        public bool IsTeamsEnabled { get; set; }

        [XmlElement("ShowMarketingLinks")]
        public bool ShowMarketingLinks { get; set; }
        
        [XmlElement("Is2FAEnabled")]
        public bool Is2FAEnabled { get; set; }

        [XmlElement("IsCustomGraphicMarkerEnabled")]
        public bool IsCustomGraphicMarkerEnabled { get; set; }

        [XmlElement("IsIWBModeEnabled")]
        public bool IsIWBModeEnabled { get; set; }

        [XmlElement("IsMTRModeEnabled")]
        public bool IsMTRModeEnabled { get; set; }

        [XmlElement("IsOnlyIWBModeEnabled")]
        public bool IsOnlyIWBModeEnabled { get; set; }

        

        [XmlElement("AISERVEROCRURL")]
        public string AISERVEROCRURL { get; set; }

    }
}
