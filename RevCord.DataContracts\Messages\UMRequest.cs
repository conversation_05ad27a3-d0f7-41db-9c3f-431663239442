﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.IQ3;
using RevCord.DataContracts.RoleManagement;

namespace RevCord.DataContracts.Messages
{
    public class UMRequest : RequestBase
    {
        public int UserId { get; set; }

        public GroupTreeCriteria TreeCriteria { get; set; }

        public UserType UserType { get; set; }

        public List<Recorder> Recorders { get; set; }

        public bool IsDemoMode { get; set; }
        public bool IsInquireView { get; set; }
        public bool IsTeamsEnabled { get; set; }
        public bool IsRevcellEnabled { get; set; }

        public UserActivity UserActivity { get; set; }

        public List<EnterpriseNodeDTO> EnterpriseUserNodes { get; set; }

        public EnterpriseGroupRightDTO EnterpriseGroupRight { get; set; }

        public User User { get; set; }
        public bool IsRoleBasedAccessEnabled { get; set; }
        public bool Is2FAEnabled { get; set; }
        public Role Role { get; set; }
        public CustomField CustomField { get; set; }
    }
}
