﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IWBEntities
{
    public class Job
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime CompletionDate { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public IwbJobStatus CurrentStatus { get; set; }


        public string CallId { get; set; }
        //public int WelderId { get; set; }
        //public int OrganizationId { get; set; }

        public int InitiatorId { get; set; }
        public int OwnerId { get; set; }
        public int ContractorId { get; set; }
        public int PerformerId { get; set; }//WelderId 

        public int WpsId { get; set; }
        public string WpsName { get; set; }

        //public IList<WPS> WPSs { get; set; }  // will be updated upon job completion

        public int NoOfPositions { get; set; }
        public int NoOfApplicants { get; set; }
        public int CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int? LastModifiedBy { get; set; }

        public string InitiatorName { get; set; }

        public string WPSData { get; set; } 
        public string Location { get; set; }
    }

    public class JobApplicant
    {
        public int Id { get; set; }
        public int JobId { get; set; }
        public string JobName { get; set; }

        public int ApplicantId { get; set; }
        public string ApplicantName { get; set; }
        public DateTime ApplyDate { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public IwbJobApplicationStatus CurrentStatus { get; set; }

        public UserManagement.User User { get; set; }

        public int CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int? LastModifiedBy { get; set; }


    }

}