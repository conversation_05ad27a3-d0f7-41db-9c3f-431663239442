<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ReportEntities" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd15" namespace="http://schemas.datacontract.org/2004/07/System" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" />
  <xs:complexType name="ArrayOfCallAuditReport">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="CallAuditReport" nillable="true" type="tns:CallAuditReport" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfCallAuditReport" nillable="true" type="tns:ArrayOfCallAuditReport" />
  <xs:complexType name="CallAuditReport">
    <xs:sequence>
      <xs:element minOccurs="0" name="AuditDateTime" type="xs:dateTime" />
      <xs:element minOccurs="0" name="AuditedBy" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallType" type="xs:int" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="DurationInMilliSeconds" type="xs:int" />
      <xs:element minOccurs="0" name="Ext" type="xs:int" />
      <xs:element minOccurs="0" name="ExtName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FileName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IPAddress" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IsDeleted" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsSaved" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsTagged" type="xs:boolean" />
      <xs:element minOccurs="0" name="NoOfAudits" type="xs:int" />
      <xs:element minOccurs="0" name="RowNo" type="xs:int" />
      <xs:element minOccurs="0" name="StartTime" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserNum" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallAuditReport" nillable="true" type="tns:CallAuditReport" />
  <xs:complexType name="ArrayOfRPTCallInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RPTCallInfo" nillable="true" type="tns:RPTCallInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRPTCallInfo" nillable="true" type="tns:ArrayOfRPTCallInfo" />
  <xs:complexType name="RPTCallInfo">
    <xs:sequence>
      <xs:element minOccurs="0" name="Avg" type="xs:long" />
      <xs:element minOccurs="0" name="CallDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="CallHour" type="xs:int" />
      <xs:element minOccurs="0" name="Count" type="xs:long" />
      <xs:element minOccurs="0" name="Day" type="xs:int" />
      <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/System" minOccurs="0" name="DayOfWeek" type="q1:DayOfWeek" />
      <xs:element minOccurs="0" name="DayOfWeekString" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Key" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Month" type="xs:int" />
      <xs:element minOccurs="0" name="RecoderName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecorderId" type="xs:int" />
      <xs:element minOccurs="0" name="Total" type="xs:long" />
      <xs:element minOccurs="0" name="TotalAbandonedCount" type="xs:int" />
      <xs:element minOccurs="0" name="TotalHoldTime" type="xs:long" />
      <xs:element minOccurs="0" name="TotalNoOfRings" type="xs:int" />
      <xs:element minOccurs="0" name="TotalRingTime" type="xs:long" />
      <xs:element minOccurs="0" name="TotalTalkTime" type="xs:long" />
      <xs:element minOccurs="0" name="TotalTransferredCount" type="xs:int" />
      <xs:element minOccurs="0" name="Year" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RPTCallInfo" nillable="true" type="tns:RPTCallInfo" />
  <xs:complexType name="ArrayOfRPTCallInfoDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RPTCallInfoDetail" nillable="true" type="tns:RPTCallInfoDetail" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRPTCallInfoDetail" nillable="true" type="tns:ArrayOfRPTCallInfoDetail" />
  <xs:complexType name="RPTCallInfoDetail">
    <xs:sequence>
      <xs:element minOccurs="0" name="CallDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="CallID" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallStatus" type="tns:CallStatus" />
      <xs:element minOccurs="0" name="ChannelName" nillable="true" type="xs:string" />
      <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/System" minOccurs="0" name="DayOfWeek" type="q2:DayOfWeek" />
      <xs:element minOccurs="0" name="Duration" type="xs:long" />
      <xs:element minOccurs="0" name="Extension" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GroupName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="HoldTime" type="xs:long" />
      <xs:element minOccurs="0" name="Key" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="NoOfRings" type="xs:int" />
      <xs:element minOccurs="0" name="RecoderName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecorderId" type="xs:int" />
      <xs:element minOccurs="0" name="RingTime" type="xs:long" />
      <xs:element minOccurs="0" name="TalkTime" type="xs:long" />
      <xs:element minOccurs="0" name="UserName" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RPTCallInfoDetail" nillable="true" type="tns:RPTCallInfoDetail" />
  <xs:simpleType name="CallStatus">
    <xs:annotation>
      <xs:appinfo>
        <ActualType Name="short" Namespace="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/2003/10/Serialization/" />
      </xs:appinfo>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:enumeration value="Attended">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">1</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Transferred">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">2</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
      <xs:enumeration value="Abandoned">
        <xs:annotation>
          <xs:appinfo>
            <EnumerationValue xmlns="http://schemas.microsoft.com/2003/10/Serialization/">3</EnumerationValue>
          </xs:appinfo>
        </xs:annotation>
      </xs:enumeration>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="CallStatus" nillable="true" type="tns:CallStatus" />
  <xs:complexType name="ArrayOfRPTEvaluation">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RPTEvaluation" nillable="true" type="tns:RPTEvaluation" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRPTEvaluation" nillable="true" type="tns:ArrayOfRPTEvaluation" />
  <xs:complexType name="RPTEvaluation">
    <xs:sequence>
      <xs:element minOccurs="0" name="AgentId" type="xs:int" />
      <xs:element minOccurs="0" name="AgentName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CallId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CompletedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="EvaluatedScore" type="xs:float" />
      <xs:element minOccurs="0" name="GroupName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GroupNum" type="xs:int" />
      <xs:element minOccurs="0" name="Id" type="xs:long" />
      <xs:element minOccurs="0" name="IsShared" type="xs:boolean" />
      <xs:element minOccurs="0" name="MaxScore" type="xs:float" />
      <xs:element minOccurs="0" name="QAnswer" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="QId" type="xs:long" />
      <xs:element minOccurs="0" name="QScore" type="xs:float" />
      <xs:element minOccurs="0" name="QTitle" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SectionId" type="xs:int" />
      <xs:element minOccurs="0" name="SectionTitle" nillable="true" type="xs:string" />
      <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="Status" type="q3:EvaluationStatus" />
      <xs:element minOccurs="0" name="StatusId" type="xs:short" />
      <xs:element minOccurs="0" name="SupervisorComments" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SupervisorId" type="xs:int" />
      <xs:element minOccurs="0" name="SupervisorName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="SurveyId" type="xs:int" />
      <xs:element minOccurs="0" name="SurveyName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TotalScore" type="xs:float" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RPTEvaluation" nillable="true" type="tns:RPTEvaluation" />
</xs:schema>