﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataContracts.MessageBase;

namespace RevCord.DataContracts.Request
{
    public class SurveyRequest : RequestBase
    {

        #region Properties
        
        public int SurveyId { get; set; }
        public int SectionId { get; set; }
        public string AssignedQuestions { get; set; }
        public string UnassignedQuestions { get; set; }

        #endregion

        #region Associations

        public Survey Survey { get; set; }
        public Question Question { get; set; }
        public List<Question> Questions { get; set; }

        #endregion

        

    }
}
