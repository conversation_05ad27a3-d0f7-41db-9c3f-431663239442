﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.ServiceContracts
{
    public interface IEvaluationService
    {
        EvaluationResponse GetPaged(EvaluationRequest evaluationRequest);
        //List<CallEvaluationDTO> GetPaged(int pageSize, int currentPageIndex, out int totalPages, List<EvaluationStatus> statuses, ActionToPerform actionToPerform);
        EvaluationResponse UpdateAndGetPaged(EvaluationRequest evaluationRequest);
        EvaluationResponse GetClosedEvaluationsByAgentId(int agentId, int tenantId);
        bool Delete(long id, int tenantId);
        //CallEvaluation CallEvaluationDetailsById(long id);
        EvaluationResponse CallEvaluationDetailsById(long id, int userId, int tenantId);

        CallEvaluation UpdateCallEvaluation(CallEvaluation callEvaluation, int tenantId);
        User UpdateAndGetAssociatedUser(int evalId, int agentId, int tenantId);

        //CallEvaluation InsertCallEvaluation(int surveyId, string callId, int userId, EvaluationStatus status);
        short InsertCallEvaluation(EvaluationRequest evalRequest);

        bool InsertEvaluations(EvaluationRequest evaluationRequest);
        EvaluationResponse GetEvaluationId(EvaluationRequest evalRequest);
        #region Evaluation Feedback
        bool InsertEvaluationFeedback(EvaluationRequest evaluationRequest);
        bool DeleteEvaluationFeedback(EvaluationRequest evaluationRequest);

        EvaluationResponse GetCallEvaluationFeedback(EvaluationRequest evaluationRequest);

        IList<CallInfo> GetSegmentedCalls(int tenantId, long evaluationId);

        #endregion

        #region User Evaluation

        EvaluationResponse SaveUserEvaluation(EvaluationRequest evalRequest);
        EvaluationResponse UserEvaluationDetailsById(long id, int userId, int tenantId);
        UserEvaluation UpdateUserEvaluation(UserEvaluation userEvaluation,  int tenantId);
        EvaluationResponse GetUserEvaluations(EvaluationRequest evalRequest);
        EvaluationResponse UpdateUserEvaluationStatus(EvaluationRequest evalRequest);

        #endregion

        #region Evaluation EC
        EvaluationResponse GetAllDrilldownChartsFromAllRecorders(EvaluationRequest evaluationRequest);
        EvaluationResponse GetEvaluationsFromAllRecorders(EvaluationRequest evaluationRequest);
        EvaluationResponse GetRecorderCallEvaluationDetailsById(EvaluationRequest evaluationRequest);
        CallEvaluation RecorderUpdateCallEvaluation(Recorder recorder, CallEvaluation callEvaluation, int tenantId);
        User UpdateAndGetAssociatedUserFromRecorder(Recorder recorder, int evalId, int agentId, int tenantId);
        short InsertCallEvaluationOnRecorder(Recorder recorder, EvaluationRequest evalRequest);
        bool InsertEnterpriseEvaluations(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail, Recorder recorder);
        short AddEnterpriseEvaluations(Recorder recorder, EvaluationRequest evalRequest, int evaluatorId, string evaluatorName, string EvaluatorEmail);
        EvaluationResponse GetEvaluationIdFromRecorder(EvaluationRequest evalRequest, Recorder recorder);
        bool PerformActionOnRecorder(Recorder recorder, string callSurveyIds, int action, int actionValue, int userId, DateTime? ActionDate, int tenantId);
        #endregion

        #region MyRegion
        EvaluationResponse GetMultiCallEvaluations(EvaluationRequest evaluationRequest);
        EvaluationResponse DeleteMultiCallEvaluation(EvaluationRequest evalReq);
        EvaluationResponse GetMultiCallEvaluationDetails(EvaluationRequest evaluationRequest);
        #endregion
    }
}
