﻿using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.ScheduleReportEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class ScheduleReportResponse
    {
        public int ScheduleReportId { get; set; }
        public AcknowledgeType Acknowledge { get; set; }
        public string Message { get; set; }
        public List<ScheduleReport> ScheduleReports { get; set; }
        public ScheduleReport ScheduleReport { get; set; }
        public List<string> ReportRecipients { get; set; }
    }
}
