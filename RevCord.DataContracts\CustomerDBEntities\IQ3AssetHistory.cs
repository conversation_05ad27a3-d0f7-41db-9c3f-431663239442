﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.CustomerDBEntities
{
    public class IQ3AssetHistory
    {
        public int Id { get; set; }
        public int UserNum { get; set; }
        public int AssetId { get; set; }
        public IQ3AssetHistoryEvent HistoryEvent { get; set; }
        public string Field { get; set; }
        public string FieldTitle { get; set; }
        public string ChangedFrom { get; set; }
        public string ChangedTo { get; set; }
        public DateTime CreatedOn { get; set; }

        public string UserName { get; set; }
    }

    public enum IQ3AssetHistoryEvent
    {
        [DescriptionAttribute("Asset Edit")]
        AssetEdit = 1,
        [DescriptionAttribute("Status Edit")]
        StatusEdit = 2,
    }
}
