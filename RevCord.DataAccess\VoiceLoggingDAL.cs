﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.Criteria;
using RevCord.Util;
using RevCord.DataContracts;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts.EvaluationEntities;
using Newtonsoft.Json;
using RevCord.DataContracts.IQ3;
using RevCord.DataContracts.TenantEntities;
using RevCord.DataContracts.IQ3InspectionEntities;
using RevCord.DataContracts.MGODataEntities;

namespace RevCord.DataAccess
{
    public class VoiceLoggingDAL
    {
        private int _tenantId;
        public VoiceLoggingDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        #region ------- New Calls -------

        //public List<CallInfo> SearchCallsPrimaryDB(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria)
        //public List<CallInfo> SearchCallsSecondaryDB(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria)
        //public List<CallInfo> SearchCallsSecondaryDBs(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria)

        #endregion

        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);

        #region ------- Calls -------

        public List<CallInfo> SearchCallsPrimaryDB(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria)
        {
            List<CallInfo> calls = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());

                    //cmd.Parameters.AddWithValue("@IsRandom", callCriteria.IsRandom);
                    //cmd.Parameters.AddWithValue("@IsPercentage", callCriteria.IsPercentage);
                    //cmd.Parameters.AddWithValue("@NoOfCalls", callCriteria.NoOfCalls);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);
                    cmd.Parameters.AddWithValue("@IsAgentBasedSearch", callCriteria.IsAgentBasedSearch);
                    cmd.Parameters.AddWithValue("@AgentName", callCriteria.AgentName);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    //cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " ").Replace("OR (  )", " "));
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  ) or", " ").Replace("OR (  )", " ").Replace("OR (  ) or", " "));//

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters.AddWithValue("@JOINString", callCriteria.JOINString);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsPrimaryDB", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> GetEventsByAssetId(int assetId, int pageSize, int pageIndex, out int totalPages, out long totalRecords)
        {
            List<CallInfo> calls = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.EVENTS_GET_BY_ASSET_ID;

                    cmd.Parameters.AddWithValue("@AssetId", assetId);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetEventsByAssetId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> SearchCallsPrimaryDBLite(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PRIMARY_DB_LITE;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);
                    cmd.Parameters.AddWithValue("@IsAgentBasedSearch", callCriteria.IsAgentBasedSearch);
                    cmd.Parameters.AddWithValue("@AgentName", callCriteria.AgentName);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  ) or", " ").Replace("OR (  )", " ").Replace("OR (  ) or", " "));//
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsPrimaryDBLite", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfosLite(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return calls;
        }

        public List<CallInfo> SearchCallsChainedDBs(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria)
        {
            List<CallInfo> calls = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_CHAIN_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.SearchType == SearchType.Global ? "" : callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.SearchType == SearchType.Global ? "" : callCriteria.EndDate.DateAsStringWithoutSlash());
                    //cmd.Parameters.AddWithValue("@BufferName", "[dbo].[Sher]");
                    //cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    //cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " "));

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;



                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsChainedDBs", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);

                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());

                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());

                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> SearchCallsChainedDBs(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria, int noOfCalls)
        {
            List<CallInfo> calls = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_RANDOM_CHAIN_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());

                    cmd.Parameters.AddWithValue("@IsPercentage", callCriteria.IsPercentage);
                    cmd.Parameters.AddWithValue("@NoOfCalls", callCriteria.NoOfCalls);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);
                    cmd.Parameters.AddWithValue("@IsAgentBasedSearch", callCriteria.IsAgentBasedSearch);
                    cmd.Parameters.AddWithValue("@AgentName", callCriteria.AgentName);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " "));

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsChainedDBs", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);

                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());

                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());

                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> SearchCallsForExportData(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria)
        {
            List<CallInfo> calls = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_FOR_EXPORT_DATA_IN_CHAIN_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.SearchType == SearchType.Global ? "" : callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.SearchType == SearchType.Global ? "" : callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " "));
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsForExportData", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallsForExportData(dr);
                    }
                    totalPages = 0;
                    totalRecords = calls.Count;
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }


        //Not sure if needed or not

        public List<CallInfo> SearchCallsChainedDBsLite(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria)
        {
            List<CallInfo> calls = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_CHAIN_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.SearchType == SearchType.Global ? "" : callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.SearchType == SearchType.Global ? "" : callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " "));

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsChainedDBsLite", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfosLite(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());

                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());

                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> SearchCallsByIdsInChainedDBs(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_CHAIN_DB;

                    cmd.Parameters.AddWithValue("@StartDate", "");
                    cmd.Parameters.AddWithValue("@EndDate", "");
                    cmd.Parameters.AddWithValue("@StartTime", "000000");
                    cmd.Parameters.AddWithValue("@EndTime", "235959");

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", false);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " "));


                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsByIdsInChainedDBs", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> SearchCallsByIdsInChainedDBs(string startDate, string endDate, int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_CHAIN_DB;

                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@StartTime", "000000");
                    cmd.Parameters.AddWithValue("@EndTime", "235959");

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", false);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " "));


                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsByIdsInChainedDBs", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        //Not sure if needed or not

        public List<CallInfo> SearchCallsByIdsInChainedDBsLite(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_CHAIN_DB;

                    cmd.Parameters.AddWithValue("@StartDate", "");
                    cmd.Parameters.AddWithValue("@EndDate", "");
                    cmd.Parameters.AddWithValue("@StartTime", "000000");
                    cmd.Parameters.AddWithValue("@EndTime", "235959");

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", false);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " "));


                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsByIdsInChainedDBsLite", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfosLite(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfoLite> SearchCallLitePrimaryDB(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria)
        {
            List<CallInfoLite> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " "));


                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallLitePrimaryDB", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (calls == null) calls = new List<CallInfoLite>();
                        while (dr.Read())
                        {
                            var call = new CallInfoLite
                            {
                                RowNo = (long)dr["RowNo"],
                                CallId = Convert.ToString(dr["CallID"]),
                                AgentId = Convert.ToInt32(dr["UserNum"]),
                                ChannelId = Convert.ToInt32(dr["Ext"]),
                                ChannelName = Convert.ToString(dr["ExtName"]),
                                StartTimeString = Convert.ToString(dr["StartTime"]),
                                DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]),
                                FileName = Convert.ToString(dr["FileName"]),
                                CallComments = Convert.ToString(dr["CALL_COMMENT"]),
                                BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                                //TagRule=Convert.ToString(dr["TagRule"]),
                            };

                            calls.Add(call);
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public CallInfoLite GetCallLiteFromPrimary(string callId)
        {
            //System.Diagnostics.Debug.WriteLine(callId);
            CallInfoLite call = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALL_GET_BY_ID_PRIMARY_DB;
                    cmd.Parameters.AddWithValue("@CallId", callId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetCallLiteFromPrimary", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //if (dr.HasRows)
                        if (dr.Read())
                        {
                            call = new CallInfoLite
                            {
                                RowNo = 1,
                                CallId = Convert.ToString(dr["CallID"]),
                                AgentId = Convert.ToInt32(dr["UserNum"]),
                                ChannelId = Convert.ToInt32(dr["Ext"]),
                                //ChannelName = Convert.ToString(dr["ExtName"]),
                                StartTimeString = Convert.ToString(dr["StartTime"]),
                                DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]),
                                FileName = Convert.ToString(dr["FileName"]),
                                CallComments = Convert.ToString(dr["CALL_COMMENT"]),
                                TagRule = Convert.ToString(dr["TagRule"]),

                                //BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                            };
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return call;
        }

        public List<CallInfo> SearchCallsPrimaryDB(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria, int noOfCalls)
        {
            List<CallInfo> calls = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_RANDOM_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());

                    cmd.Parameters.AddWithValue("@IsRandom", callCriteria.IsRandom);
                    cmd.Parameters.AddWithValue("@IsPercentage", callCriteria.IsPercentage);
                    cmd.Parameters.AddWithValue("@NoOfCalls", callCriteria.NoOfCalls);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);
                    cmd.Parameters.AddWithValue("@IsAgentBasedSearch", callCriteria.IsAgentBasedSearch);
                    cmd.Parameters.AddWithValue("@AgentName", callCriteria.AgentName);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SearchCallsPrimaryDB", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public List<CallInfo> GetCallsByEventId(int eventId)
        {
            List<CallInfo> calls = null;

            try
            {
                //_tenantId = tenantId;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_GET_BY_EVENT_ID;
                    cmd.Parameters.AddWithValue("@EventId", eventId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetCallsByEventId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public CallInfo GetEventByEventId(string eventId)
        {
            CallInfo eventInfo = null;

            try
            {
                //_tenantId = tenantId;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.EVENT_GET_BY_EVENT_ID;
                    cmd.Parameters.AddWithValue("@EventId", eventId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetEventByEventId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        eventInfo = ORMapper.MapCallInfo(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return eventInfo;
        }


        #endregion

        #region ------- Custom Fields -------

        public int UpdateCallsCustomFields(string callIds, int fieldType, string fieldText, int userId)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandTimeout = CMD_TIMEOUT;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.VoiceRec.CALLS_UPDATE_CUSTOM_FIELDS_CHAIN_DB;
                cmd.Parameters.AddWithValue("@Comment", fieldText);
                //cmd.Parameters.AddWithValue("@CallID", callIds.Split(',')[0]);
                //cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", callIds);
                cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));
                //cmd.Parameters.AddWithValue("@Ext", callInfo.CalledID);
                cmd.Parameters.AddWithValue("@Type", fieldType);

                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateCallsCustomFields", _tenantId));

                int rowsaffected = cmd.ExecuteNonQuery();

                if (SiteConfig.RevSyncEnabled)
                {
                    try
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("CallIds", Convert.ToString(callIds));
                        _dic.Add("FieldType", Convert.ToString(fieldType));
                        _dic.Add("FieldText", Convert.ToString(fieldText));
                        _dic.Add("UserId", Convert.ToString(userId));

                        DALHelper.RevsyncAPICall(_tenantId, "VoiceLoggingDAL", "UpdateCallsCustomFields", JsonConvert.SerializeObject(_dic));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                }
                else if (SiteConfig.IsMTEnable)
                {
                    Dictionary<string, string> _dic = new Dictionary<string, string>();
                    _dic.Add("CallIds", Convert.ToString(callIds));
                    _dic.Add("FieldType", Convert.ToString(fieldType));
                    _dic.Add("FieldText", Convert.ToString(fieldText));
                    _dic.Add("UserId", Convert.ToString(userId));

                    DALHelper.SendMessageToHub(_tenantId, "VoiceLoggingDAL", "UpdateCallsCustomFields", JsonConvert.SerializeObject(_dic));
                }
                return rowsaffected;
            }
        }

        public int UpdateCallCustomFields(CallInfo callInfo)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_CUSTOM_FIELDS_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", callInfo.CallId);
                    cmd.Parameters.AddWithValue("@Tag1", callInfo.Tag1);
                    cmd.Parameters.AddWithValue("@Tag2", callInfo.Tag2);
                    cmd.Parameters.AddWithValue("@Tag3", callInfo.Tag3);
                    cmd.Parameters.AddWithValue("@Tag4", callInfo.Tag4);
                    cmd.Parameters.AddWithValue("@CustName", callInfo.CustName);
                    cmd.Parameters.AddWithValue("@CallComments", callInfo.CallComments);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateCallCustomFields", _tenantId));

                    //return Convert.ToBoolean(cmd.ExecuteScalar());
                    int rowsaffected = cmd.ExecuteNonQuery();

                    if (SiteConfig.RevSyncEnabled)
                    {
                        try
                        {
                            DALHelper.RevsyncAPICall(_tenantId, "VoiceLoggingDAL", "UpdateCallCustomFields", JsonConvert.SerializeObject(callInfo));
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        DALHelper.SendMessageToHub(_tenantId, "VoiceLoggingDAL", "UpdateCallCustomFields", JsonConvert.SerializeObject(callInfo));
                    }

                    return rowsaffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateCallTag(string callIds, string TagcolorID)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CallTags.CALLS_CALLTAG_UPDATE;
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));
                    cmd.Parameters.AddWithValue("@TagcolorID", TagcolorID.Replace('#',' ').Trim());
                    //cmd.Parameters.AddWithValue("@Ext", callInfo.CalledID);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateCallRetention", _tenantId));
                    //string s = Convert.ToString( cmd.ExecuteScalar());
                    int rowsaffected = cmd.ExecuteNonQuery();

                    //if (SiteConfig.RevSyncEnabled)
                    //{
                    //    Dictionary<string, string> _dic = new Dictionary<string, string>();
                    //    _dic.Add("CallId", Convert.ToString(callId));
                    //    _dic.Add("RetainValue", Convert.ToString(retainValue));

                    //    DALHelper.RevsyncAPICall(_tenantId, "VoiceLoggingDAL", "UpdateCallRetention", JsonConvert.SerializeObject(_dic));
                    //}
                    //else if (SiteConfig.IsMTEnable)
                    //{
                    //    Dictionary<string, string> _dic = new Dictionary<string, string>();
                    //    _dic.Add("CallId", Convert.ToString(callId));
                    //    _dic.Add("RetainValue", Convert.ToString(retainValue));

                    //    DALHelper.SendMessageToHub(_tenantId, "VoiceLoggingDAL", "UpdateCallRetention", JsonConvert.SerializeObject(_dic));
                    //}
                    return rowsaffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateBookmarkFlags(string BookmarkID, string FlagColorID)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.BookmarkTags.BOOKMARK_BOOKMARKFLAGS_UPDATE;
                    cmd.Parameters.AddWithValue("@BookmarkID", Convert.ToInt32(BookmarkID));
                    cmd.Parameters.AddWithValue("@FlagColorID", FlagColorID.Replace('#', ' ').Trim());

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateCallRetention", _tenantId));
                    int rowsaffected = cmd.ExecuteNonQuery();

                    
                    return rowsaffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateCallRetention(string callId, bool retainValue)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_RETAIN_VALUE_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", callId);
                    cmd.Parameters.AddWithValue("@RetainValue", retainValue);
                    //cmd.Parameters.AddWithValue("@Ext", callInfo.CalledID);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateCallRetention", _tenantId));
                    //string s = Convert.ToString( cmd.ExecuteScalar());
                    int rowsaffected = cmd.ExecuteNonQuery();

                    if (SiteConfig.RevSyncEnabled)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("CallId", Convert.ToString(callId));
                        _dic.Add("RetainValue", Convert.ToString(retainValue));

                        DALHelper.RevsyncAPICall(_tenantId, "VoiceLoggingDAL", "UpdateCallRetention", JsonConvert.SerializeObject(_dic));
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("CallId", Convert.ToString(callId));
                        _dic.Add("RetainValue", Convert.ToString(retainValue));

                        DALHelper.SendMessageToHub(_tenantId, "VoiceLoggingDAL", "UpdateCallRetention", JsonConvert.SerializeObject(_dic));
                    }
                    return rowsaffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region ------- Bookmark -------

        public int InsertBookmark(Bookmark bookmark)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_INSERT;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertBookmark", _tenantId));

                    //cmd.Parameters.Add("@Id", SqlDbType.Int).Direction = ParameterDirection.Output;  
                    //bookmark.Id = Convert.ToInt32(cmd.ExecuteScalar());

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());

                    if (SiteConfig.RevSyncEnabled)
                    {
                        DALHelper.RevsyncAPICall(_tenantId, "VoiceLoggingDAL", "InsertBookmark", JsonConvert.SerializeObject(bookmark));
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        DALHelper.SendMessageToHub(_tenantId, "VoiceLoggingDAL", "InsertBookmark", JsonConvert.SerializeObject(bookmark));
                    }
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public int UpdateBookmark(Bookmark bookmark)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateBookmark", _tenantId));

                    //cmd.Parameters.Add("@Id", SqlDbType.Int).Direction = ParameterDirection.Output;  
                    //bookmark.Id = Convert.ToInt32(cmd.ExecuteScalar());
                    //cmd.ExecuteNonQuery();

                    int rowsaffected = cmd.ExecuteNonQuery();

                    if (SiteConfig.RevSyncEnabled)
                    {
                        DALHelper.RevsyncAPICall(_tenantId, "VoiceLoggingDAL", "UpdateBookmark", JsonConvert.SerializeObject(bookmark));
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        DALHelper.SendMessageToHub(_tenantId, "VoiceLoggingDAL", "UpdateBookmark", JsonConvert.SerializeObject(bookmark));
                    }

                    return rowsaffected;
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public List<string> InsertBookmarkAndGetByCallId(Bookmark bookmark, out int rowsAffected)
        {
            List<string> bookmarks = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_INSERT_N_GET;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertBookmarkAndGetByCallId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        rowsAffected = dr.RecordsAffected;
                        if (dr.HasRows)
                        {
                            dr.Read();
                            bookmarks = new List<string>();
                            //string BookmarkXML = Convert.ToString(dr[0]);
                            string BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                            string BookmarkCSV = "";
                            if (!String.IsNullOrEmpty(BookmarkXML))
                            {
                                BookmarkCSV = BookmarkXML.ConvertXmlStringToCsvString();
                            }
                            bookmarks.Add(BookmarkXML);
                            bookmarks.Add(BookmarkCSV);
                        }
                    }
                }


                if (SiteConfig.RevSyncEnabled)
                {
                    DALHelper.RevsyncAPICall(_tenantId, "VoiceLoggingDAL", "InsertBookmarkAndGetByCallId", JsonConvert.SerializeObject(bookmark));
                }
                else if (SiteConfig.IsMTEnable)
                {
                    DALHelper.SendMessageToHub(_tenantId, "VoiceLoggingDAL", "InsertBookmarkAndGetByCallId", JsonConvert.SerializeObject(bookmark));
                }
            }
            catch (Exception ex) { throw ex; }
            return bookmarks;
        }

        public List<string> UpdateBookmarkAndGetByCallId(Bookmark bookmark, out int rowsAffected)
        {
            List<string> bookmarks = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_UPDATE_N_GET;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateBookmarkAndGetByCallId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        rowsAffected = dr.RecordsAffected;
                        if (dr.HasRows)
                        {
                            dr.Read();
                            bookmarks = new List<string>();
                            //string BookmarkXML = Convert.ToString(dr[0]);
                            string BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                            string BookmarkCSV = "";
                            if (!String.IsNullOrEmpty(BookmarkXML))
                            {
                                BookmarkCSV = BookmarkXML.ConvertXmlStringToCsvString();
                            }
                            bookmarks.Add(BookmarkXML);
                            bookmarks.Add(BookmarkCSV);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return bookmarks;
        }


        #endregion

        #region ------- Transcription -------

        public int InsertTranscription(string callId, string transcript, float confidence, DateTime createdDate, int userId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALL_TRANSCRIPT_INSERT;

                    cmd.Parameters.AddWithValue("@callId", callId);
                    cmd.Parameters.AddWithValue("@transcript", transcript);
                    cmd.Parameters.AddWithValue("@confidence", confidence);
                    cmd.Parameters.AddWithValue("@isDeleted", false);
                    cmd.Parameters.AddWithValue("@createdDate", createdDate);
                    //cmd.Parameters.AddWithValue("@modifiedDate", modifiedDate);
                    cmd.Parameters.AddWithValue("@userId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertTranscription", _tenantId));

                    int lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastSavedId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateTranscription(int transcriptId, string callId, string transcript, float confidence, DateTime createdDate, int modifiedBy)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            {
                //UPDATE
                using (var cmdUpdate = conn.CreateCommand())
                {
                    cmdUpdate.CommandType = CommandType.Text;
                    cmdUpdate.CommandText = "UPDATE vrCallTranscription SET IsDeleted = @isDeleted, ModifiedBy = @modifiedBy, ModifiedDate = @modifiedDate WHERE Id = @transcriptId";
                    cmdUpdate.Parameters.AddWithValue("@transcriptId", transcriptId);
                    //cmdUpdate.CommandText = "UPDATE vrCallTranscription SET IsDeleted = @isDeleted, ModifiedBy = @modifiedBy, ModifiedDate = @modifiedDate WHERE CallId = @callId";
                    //cmdUpdate.Parameters.AddWithValue("@callId", callId);
                    cmdUpdate.Parameters.AddWithValue("@isDeleted", true);
                    cmdUpdate.Parameters.AddWithValue("@modifiedBy", modifiedBy);
                    cmdUpdate.Parameters.AddWithValue("@modifiedDate", createdDate);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmdUpdate), Originator.VoiceLogging, "UpdateTranscription", _tenantId));

                    cmdUpdate.ExecuteNonQuery();
                }
                //INSERT
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALL_TRANSCRIPT_INSERT;

                    cmd.Parameters.AddWithValue("@callId", callId);
                    cmd.Parameters.AddWithValue("@transcript", transcript);
                    cmd.Parameters.AddWithValue("@confidence", confidence);
                    cmd.Parameters.AddWithValue("@isDeleted", false);
                    cmd.Parameters.AddWithValue("@createdDate", createdDate);
                    cmd.Parameters.AddWithValue("@userId", modifiedBy);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateTranscription", _tenantId));
                    int lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastSavedId;
                }
            }
        }


        #endregion


        #region ------- Channels -------

        public List<MonitorChannel> GetChannelsToMonitor(string whereClause)
        {
            List<MonitorChannel> channels = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CHANNELS_MONITOR_GETBY_WHERECLAUSE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetChannelsToMonitor", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (channels == null) channels = new List<MonitorChannel>();
                        while (dr.Read())
                        {
                            var channel = new MonitorChannel
                            {
                                UserNum = Convert.ToInt32(dr["UserNum"]),
                                UserName = Convert.ToString(dr["UserName"]),
                                GroupNum = Convert.ToInt32(dr["GroupNum"]),
                                GroupName = Convert.ToString(dr["GroupName"]),
                                Ext = Convert.ToString(dr["Ext"]),
                                AgentIP = Convert.ToString(dr["AgentIP"]),
                                UserPW = Convert.ToString(dr["UserPW"]),
                                ROD = Convert.ToString(dr["ROD"]),
                                POD = Convert.ToString(dr["POD"]),
                                EOD = Convert.ToString(dr["EOD"]),
                                SOD = Convert.ToString(dr["SOD"]),
                                ExtName = Convert.ToString(dr["ExtName"]),
                                UserEmail = Convert.ToString(dr["UserEmail"]),
                                RecId = Convert.ToInt32(dr["RecId"]),
                                RecName = Convert.ToString(dr["RecName"]),
                                RecIP = Convert.ToString(dr["RecIP"]),
                                RecPort = Convert.ToInt16(dr["RecPort"]),
                                Channeltype = Convert.ToInt16(dr["Channeltype"]),
                                IsAvtecChannel = Convert.ToInt16(dr["IsAvtecChannel"]),
                                IsRevStreamChannel = Convert.ToInt16(dr["IsRevStreamChannel"]),
                                //IsRevViewChannel = Convert.ToInt16(dr["IsRevViewChannel"]),
                                RevStreamID = Convert.ToInt16(dr["RevStreamID"]),
                                Status = "Inactive",
                                IsRevView = Convert.ToBoolean(dr["IsRevView"])
                            };

                            channels.Add(channel);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return channels;
        }

        public List<Channel> GetAudioChannels()
        {
            List<Channel> channels = null;
            Channel channel = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.AUDIO_CHANNELS_GETLIST;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetAudioChannels", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (channels == null) channels = new List<Channel>();
                        while (dr.Read())
                        {
                            channel = new Channel();
                            channel.Ext = Convert.ToString(dr["Ext"]);
                            channel.RecID = Convert.ToInt32(dr["RecID"]);
                            channel.ChNum = Convert.ToInt32(dr["ChNum"]);
                            channel.ExtIP = Convert.ToString(dr["ExtIP"]);
                            channel.VoIP = Convert.ToString(dr["VoIP"]);
                            channel.Triger = Convert.ToInt32(dr["Triger"]);
                            channel.Min_Du = Convert.ToInt32(dr["Min_Du"]);
                            channel.Hold_Du = Convert.ToInt32(dr["Hold_Du"]);
                            channel.Max_Du = Convert.ToInt32(dr["Max_Du"]);
                            channel.Tr = Convert.ToInt32(dr["Tr"]);
                            channel.Tf = Convert.ToInt32(dr["Tf"]);
                            channel.Gain = Convert.ToInt32(dr["Gain"]);
                            channel.EncType = Convert.ToInt32(dr["EncType"]);
                            channel.Status = Convert.ToInt32(dr["Status"]);
                            //channel.Create_T = Convert.ToString(dr[""]);
                            //channel.Modify_T = Convert.ToString(dr[""]);
                            //channel.Delete_T = Convert.ToString(dr[""]);
                            channel.Descr = Convert.ToString(dr["Descr"]);
                            channel.ExtName = Convert.ToString(dr["ExtName"]);
                            channel.RODMAC = Convert.ToString(dr["RODMAC"]);
                            channel.CallerIDMod = Convert.ToInt32(dr["CallerIDMod"]);
                            channel.HangupDTRMVoltage = Convert.ToInt32(dr["HangupDTRMVoltage"]);
                            channel.LineHoldTime = Convert.ToInt32(dr["LineHoldTime"]);
                            channel.EnableAGC = Convert.ToInt32(dr["EnableAGC"]);
                            channel.OffHook = Convert.ToInt32(dr["OffHook"]);
                            channel.OffHookSub = Convert.ToString(dr["OffHookSub"]);
                            channel.AudioOn = Convert.ToInt32(dr["AudioOn"]);
                            channel.AudioOnSub = Convert.ToString(dr["AudioOnSub"]);
                            channel.FuncBtn = Convert.ToInt32(dr["FuncBtn"]);
                            channel.FuncBtnSub = Convert.ToString(dr["FuncBtnSub"]);
                            channel.OnHook = Convert.ToInt32(dr["OnHook"]);
                            channel.OnHookSub = Convert.ToString(dr["OnHookSub"]);
                            channel.AudioOff = Convert.ToInt32(dr["AudioOff"]);
                            channel.AudioOffSub = Convert.ToString(dr["AudioOffSub"]);
                            channel.ReleaseBtn = Convert.ToInt32(dr["ReleaseBtn"]);
                            channel.ReleaseBtnSub = Convert.ToString(dr["ReleaseBtnSub"]);
                            channel.GatewayId = Convert.ToString(dr["GatewayId"]);
                            //channel.Channeltype
                            //channel.IsRevCell
                            channel.RecName = Convert.ToString(dr["RecName"]);

                            channels.Add(channel);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return channels;
        }

        public Channel GetAudioChannel(int channelId)
        {
            Channel channel = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.AUDIO_CHANNEL_GETBYID;
                    cmd.Parameters.AddWithValue("@ChannelId", channelId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetAudioChannel", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            channel = new Channel();
                            channel.Ext = Convert.ToString(dr["Ext"]);
                            channel.RecID = Convert.ToInt32(dr["RecID"]);
                            channel.ChNum = Convert.ToInt32(dr["ChNum"]);
                            channel.ExtIP = Convert.ToString(dr["ExtIP"]);
                            channel.VoIP = Convert.ToString(dr["VoIP"]);
                            channel.Triger = Convert.ToInt32(dr["Triger"]);
                            channel.Min_Du = Convert.ToInt32(dr["Min_Du"]);
                            channel.Hold_Du = Convert.ToInt32(dr["Hold_Du"]);
                            channel.Max_Du = Convert.ToInt32(dr["Max_Du"]);
                            channel.Tr = Convert.ToInt32(dr["Tr"]);
                            channel.Tf = Convert.ToInt32(dr["Tf"]);
                            channel.Gain = Convert.ToInt32(dr["Gain"]);
                            channel.EncType = Convert.ToInt32(dr["EncType"]);
                            channel.Status = Convert.ToInt32(dr["Status"]);
                            //channel.Create_T = Convert.ToString(dr[""]);
                            //channel.Modify_T = Convert.ToString(dr[""]);
                            //channel.Delete_T = Convert.ToString(dr[""]);
                            channel.Descr = Convert.ToString(dr["Descr"]);
                            channel.ExtName = Convert.ToString(dr["ExtName"]);
                            channel.RODMAC = Convert.ToString(dr["RODMAC"]);
                            channel.CallerIDMod = Convert.ToInt32(dr["CallerIDMod"]);
                            channel.HangupDTRMVoltage = Convert.ToInt32(dr["HangupDTRMVoltage"]);
                            channel.LineHoldTime = Convert.ToInt32(dr["LineHoldTime"]);
                            channel.EnableAGC = Convert.ToInt32(dr["EnableAGC"]);
                            channel.OffHook = Convert.ToInt32(dr["OffHook"]);
                            channel.OffHookSub = Convert.ToString(dr["OffHookSub"]);
                            channel.AudioOn = Convert.ToInt32(dr["AudioOn"]);
                            channel.AudioOnSub = Convert.ToString(dr["AudioOnSub"]);
                            channel.FuncBtn = Convert.ToInt32(dr["FuncBtn"]);
                            channel.FuncBtnSub = Convert.ToString(dr["FuncBtnSub"]);
                            channel.OnHook = Convert.ToInt32(dr["OnHook"]);
                            channel.OnHookSub = Convert.ToString(dr["OnHookSub"]);
                            channel.AudioOff = Convert.ToInt32(dr["AudioOff"]);
                            channel.AudioOffSub = Convert.ToString(dr["AudioOffSub"]);
                            channel.ReleaseBtn = Convert.ToInt32(dr["ReleaseBtn"]);
                            channel.ReleaseBtnSub = Convert.ToString(dr["ReleaseBtnSub"]);
                            channel.GatewayId = Convert.ToString(dr["GatewayId"]);
                            //channel.Channeltype
                            //channel.IsRevCell
                            channel.RecName = Convert.ToString(dr["RecName"]);

                        }
                    }
                    channel.AvailableGateways = this.FetchAvailableTenantGateways();
                }
            }
            catch (Exception ex) { throw ex; }

            return channel;
        }

        public int DeleteAudioChannels(List<int> channelIds)
        {
            //var listIds = new List<int>() { 2154, 2155, 2156, 2157, 2158, 2159, 2160, 2161, 2162, 2164, 2165, 2166 };
            int rowsAffected = 0;
            try
            {
                //using (var conn = DALHelper.GetConnection(_tenantId))
                //using (var cmd = conn.CreateCommand())
                //{
                //    cmd.CommandType = CommandType.Text;
                //    cmd.CommandText = "DELETE FROM t_ExtInfo WHERE Ext IN (" + String.Join(",", listIds) + ")";
                //    conn.Open();
                //    rowsAffected = cmd.ExecuteNonQuery();
                //    return rowsAffected;
                //}

                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();

                    using (var tran = conn.BeginTransaction())
                    {
                        using (var cmd = new SqlCommand("DELETE FROM t_ExtInfo WHERE Ext IN (" + String.Join(",", channelIds) + ")", conn, tran))
                        {
                            rowsAffected = cmd.ExecuteNonQuery();

                            cmd.CommandText = "DELETE FROM t_Account WHERE Ext IN (" + String.Join(",", channelIds) + ")";
                            rowsAffected += cmd.ExecuteNonQuery();

                            cmd.CommandText = "DELETE FROM t_ExtGroup WHERE Ext IN (" + String.Join(",", channelIds) + ")";
                            rowsAffected += cmd.ExecuteNonQuery();
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetAudioChannel", _tenantId));

                        }
                        tran.Commit();
                    }
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }


        public int InsertAudioChannels(List<Channel> channels, int recorderId)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();

                    using (var tran = conn.BeginTransaction())
                    {
                        foreach (var channel in channels)
                        {
                            using (SqlCommand cmd = new SqlCommand(DBConstants.VoiceRec.AUDIO_CHANNEL_INSERT, conn, tran))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@Ext", channel.Ext);
                                cmd.Parameters.AddWithValue("@RecID", recorderId);
                                cmd.Parameters.AddWithValue("@ChNum", channel.ChNum);
                                cmd.Parameters.AddWithValue("@ExtIP", channel.ExtIP);
                                cmd.Parameters.AddWithValue("@VoIP", channel.VoIP);
                                cmd.Parameters.AddWithValue("@Triger", channel.Triger);
                                cmd.Parameters.AddWithValue("@Min_Du", channel.Min_Du);
                                cmd.Parameters.AddWithValue("@Hold_Du", channel.Hold_Du);
                                cmd.Parameters.AddWithValue("@Max_Du", channel.Max_Du);
                                cmd.Parameters.AddWithValue("@Tr", channel.Tr);
                                cmd.Parameters.AddWithValue("@Tf", channel.Tf);
                                cmd.Parameters.AddWithValue("@Gain", channel.Gain);
                                cmd.Parameters.AddWithValue("@EncType", channel.EncType);
                                cmd.Parameters.AddWithValue("@Status", channel.Status);
                                //cmd.Parameters.AddWithValue("@Create_T", channel.Create_T);
                                //cmd.Parameters.AddWithValue("@Modify_T", channel.Modify_T);
                                cmd.Parameters.AddWithValue("@Descr", channel.Descr);
                                cmd.Parameters.AddWithValue("@ExtName", channel.ExtName);
                                cmd.Parameters.AddWithValue("@RODMAC", channel.RODMAC);
                                cmd.Parameters.AddWithValue("@CallerIDMod", channel.CallerIDMod);
                                cmd.Parameters.AddWithValue("@HangupDTRMVoltage", channel.HangupDTRMVoltage);
                                cmd.Parameters.AddWithValue("@LineHoldTime", channel.LineHoldTime);
                                cmd.Parameters.AddWithValue("@EnableAGC", channel.EnableAGC);
                                cmd.Parameters.AddWithValue("@OffHook", channel.OffHook);
                                cmd.Parameters.AddWithValue("@OffHookSub", channel.OffHookSub);
                                cmd.Parameters.AddWithValue("@AudioOn", channel.AudioOn);
                                cmd.Parameters.AddWithValue("@AudioOnSub", channel.AudioOnSub);
                                cmd.Parameters.AddWithValue("@FuncBtn", channel.FuncBtn);
                                cmd.Parameters.AddWithValue("@FuncBtnSub", channel.FuncBtnSub);
                                cmd.Parameters.AddWithValue("@OnHook", channel.OnHook);
                                cmd.Parameters.AddWithValue("@OnHookSub", channel.OnHookSub);
                                cmd.Parameters.AddWithValue("@AudioOff", channel.AudioOn);
                                cmd.Parameters.AddWithValue("@AudioOffSub", channel.AudioOnSub);
                                cmd.Parameters.AddWithValue("@ReleaseBtn", channel.ReleaseBtn);
                                cmd.Parameters.AddWithValue("@ReleaseBtnSub", channel.ReleaseBtnSub);
                                cmd.Parameters.AddWithValue("@Channeltype", channel.Channeltype);
                                cmd.Parameters.AddWithValue("@IsRevCell", channel.IsRevCell);
                                cmd.Parameters.AddWithValue("@GatewayId", channel.GatewayId);
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertAudioChannels", _tenantId));
                                rowsAffected += cmd.ExecuteNonQuery();
                            }
                        }
                        tran.Commit();
                    }
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateAudioChannels(Channel channel, int recorderId)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.VoiceRec.AUDIO_CHANNEL_UPDATE;
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@Ext", channel.Ext);
                        cmd.Parameters.AddWithValue("@ExtIP", channel.ExtIP);
                        cmd.Parameters.AddWithValue("@VoIP", channel.VoIP);
                        cmd.Parameters.AddWithValue("@Triger", channel.Triger);
                        cmd.Parameters.AddWithValue("@Min_Du", channel.Min_Du);
                        cmd.Parameters.AddWithValue("@Hold_Du", channel.Hold_Du);
                        cmd.Parameters.AddWithValue("@Max_Du", channel.Max_Du);
                        cmd.Parameters.AddWithValue("@Descr", channel.Descr);
                        cmd.Parameters.AddWithValue("@ExtName", channel.ExtName);
                        cmd.Parameters.AddWithValue("@RODMAC", channel.RODMAC);
                        //cmd.Parameters.AddWithValue("@CallerIDMod", channel.CallerIDMod);
                        //cmd.Parameters.AddWithValue("@HangupDTRMVoltage", channel.HangupDTRMVoltage);
                        cmd.Parameters.AddWithValue("@LineHoldTime", channel.LineHoldTime);
                        cmd.Parameters.AddWithValue("@EnableAGC", channel.EnableAGC);
                        cmd.Parameters.AddWithValue("@GatewayId", channel.GatewayId);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateAudioChannels", _tenantId));

                        rowsAffected = cmd.ExecuteNonQuery();
                    }
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Teams Channel

        public List<Channel> GetTeamsChannels()
        {
            List<Channel> channels = null;
            Channel channel = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.TEAMS_CHANNELS_GETLIST;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetTeamsChannels", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (channels == null) channels = new List<Channel>();
                        while (dr.Read())
                        {
                            channel = new Channel();
                            channel.Ext = Convert.ToString(dr["Ext"]);
                            channel.RecID = Convert.ToInt32(dr["RecID"]);
                            channel.ChNum = Convert.ToInt32(dr["ChNum"]);
                            channel.ExtName = Convert.ToString(dr["ExtName"]);
                            channel.RecName = Convert.ToString(dr["RecName"]);
                            channel.Channeltype = Convert.ToInt32(dr["Channeltype"]);
                            channel.TeamsUserId = Convert.ToString(dr["TeamsUserId"]);

                            channels.Add(channel);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return channels;
        }

        public Channel GetTeamsChannel(int channelId)
        {
            Channel channel = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.TEAMS_CHANNEL_GETBYID;
                    cmd.Parameters.AddWithValue("@ChannelId", channelId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetTeamsChannel", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.Read())
                        {
                            channel = new Channel();
                            channel.Ext = Convert.ToString(dr["Ext"]);
                            channel.RecID = Convert.ToInt32(dr["RecID"]);
                            channel.ChNum = Convert.ToInt32(dr["ChNum"]);
                            channel.ExtName = Convert.ToString(dr["ExtName"]);
                            channel.RecName = Convert.ToString(dr["RecName"]);
                            channel.Channeltype = Convert.ToInt32(dr["Channeltype"]);
                            channel.TeamsUserId = Convert.ToString(dr["TeamsUserId"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return channel;
        }

        public int DeleteTeamsChannel(List<int> channelIds)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();

                    using (var tran = conn.BeginTransaction())
                    {
                        using (var cmd = new SqlCommand("DELETE FROM t_ExtInfo WHERE Ext IN (" + String.Join(",", channelIds) + ")", conn, tran))
                        {
                            rowsAffected = cmd.ExecuteNonQuery();

                            cmd.CommandText = "DELETE FROM t_Account WHERE Ext IN (" + String.Join(",", channelIds) + ")";
                            rowsAffected += cmd.ExecuteNonQuery();

                            cmd.CommandText = "DELETE FROM t_ExtGroup WHERE Ext IN (" + String.Join(",", channelIds) + ")";
                            rowsAffected += cmd.ExecuteNonQuery();
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "DeleteTeamsChannel", _tenantId));

                        }
                        tran.Commit();
                    }
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }


        public int CreateTeamsChannel(Channel channel, int recorderId)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();

                    using (var tran = conn.BeginTransaction())
                    {
                        using (SqlCommand cmd = new SqlCommand(DBConstants.VoiceRec.TEAMS_CHANNEL_INSERT, conn, tran))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@ExtName", channel.ExtName);
                            cmd.Parameters.AddWithValue("@TeamsUserId", channel.TeamsUserId);
                            cmd.Parameters.AddWithValue("@RecID", recorderId);

                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "CreateTeamsChannel", _tenantId));

                            rowsAffected += cmd.ExecuteNonQuery();
                        }
                        tran.Commit();
                    }
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateTeamsChannel(Channel channel, int recorderId)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.VoiceRec.TEAMS_CHANNEL_UPDATE;
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@Ext", channel.Ext);
                        cmd.Parameters.AddWithValue("@ExtName", channel.ExtName);
                        cmd.Parameters.AddWithValue("@TeamsUserId", channel.TeamsUserId);

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateTeamsChannel", _tenantId));

                        rowsAffected = cmd.ExecuteNonQuery();
                    }
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Twilio
        public List<ChatTranscript> GetChatTranscript(string eventId)
        {
            List<ChatTranscript> chatTranscriptList = new List<ChatTranscript>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.EVENTS_GET_CHAT_TRANSCRIPT;
                    cmd.Parameters.AddWithValue("@EventId", eventId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetChatTranscript", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                ChatTranscript chatTranscript = new ChatTranscript();

                                chatTranscript.Id = Convert.ToInt32(dr["Id"]);
                                chatTranscript.EventId = Convert.ToString(dr["EventId"]);
                                chatTranscript.ChannelSid = Convert.ToString(dr["ChannelSid"]);
                                chatTranscript.Transcript = Convert.ToString(dr["Transcript"]);
                                chatTranscript.ParticipantIdsCSV = Convert.ToString(dr["ParticipantIdsCSV"]);
                                chatTranscript.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                chatTranscript.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                                chatTranscriptList.Add(chatTranscript);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return chatTranscriptList;
        }
        #endregion

        public List<Recorder> GetAllSites(bool isEnterpriseRecorder)
        {
            List<Recorder> sites = new List<Recorder>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = string.Format(isEnterpriseRecorder == true ? "SELECT * FROM t_RecInfo WHERE Status = 1" : "SELECT * FROM t_RecInfo WHERE Status = 1 AND IsPrimary = '{0}'", isEnterpriseRecorder.ToString().ToLower());
                    cmd.CommandText = string.Format(isEnterpriseRecorder == true ? "SELECT * FROM t_RecInfo WHERE Status = 1" : "SELECT * FROM t_RecInfo WHERE Status = 1 AND IsPrimary = 1");
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetChatTranscript", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sites = ORMapper.MapRecorders(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return sites;
        }

        #region Schedule Event
        public List<ScheduleEventInfo> GetAllScheduledEvents(int scheduledBy)
        {
            try
            {
                List<ScheduleEventInfo> scheduledEvents = new List<ScheduleEventInfo>();
                List<ScheduleEventParticipant> participants = new List<ScheduleEventParticipant>();
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.LiveMonitor.GETALL_SCHEDULED_EVENTS;
                    cmd.Parameters.AddWithValue("@ScheduledBy", scheduledBy);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetAllScheduledEvents", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        scheduledEvents = ORMapper.MapAllScheduledEvents(dr);
                        dr.NextResult();
                        participants = ORMapper.MapParticipants(dr);
                        if (scheduledEvents != null)
                        {
                            scheduledEvents.ForEach(se => se.Participants = participants.FindAll(o => o.EventId == se.EventId));
                        }
                    }
                }
                return scheduledEvents;
            }
            catch (Exception ex) { throw ex; }
        }

        public ScheduleEventInfo GetScheduledEventDetails(int scheduledBy, string eventId)
        {
            try
            {
                ScheduleEventInfo scheduledEvent = new ScheduleEventInfo();
                List<ScheduleEventParticipant> participants = new List<ScheduleEventParticipant>();
                List<DataContracts.EMREntities.CustomField> customFields = new List<DataContracts.EMREntities.CustomField>();

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.LiveMonitor.GET_SCHEDULED_EVENT_DETAILS_BY_EVENTID;
                    cmd.Parameters.AddWithValue("@ScheduledBy", scheduledBy);
                    cmd.Parameters.AddWithValue("@EventId", eventId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetScheduledEventDetails", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            scheduledEvent = new ScheduleEventInfo();
                            scheduledEvent.Id = Convert.ToInt32(dr["Id"]);
                            scheduledEvent.EventId = Convert.ToString(dr["EventId"]);
                            scheduledEvent.EventName = Convert.ToString(dr["EventName"]);
                            scheduledEvent.Notes = Convert.ToString(dr["Notes"]);
                            scheduledEvent.Duration = Convert.ToString(dr["Duration"]);
                            scheduledEvent.TenantId = Convert.ToInt32(dr["TenantId"]);
                            scheduledEvent.ScheduledDateTime = Convert.ToDateTime(dr["ScheduledDateTime"]);
                            scheduledEvent.ScheduledDateTimeStr = Convert.ToString(dr["ScheduledDateTimeStr"]);
                            scheduledEvent.ScheduledBy = Convert.ToInt32(dr["ScheduledBy"]);
                            scheduledEvent.ScheduledByEmail = Convert.ToString(dr["ScheduledByEmail"]);
                            scheduledEvent.ScheduledByName = Convert.ToString(dr["ScheduledByName"]);
                            scheduledEvent.IsActive = Convert.ToBoolean(dr["IsActive"]);
                            scheduledEvent.IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]);
                            scheduledEvent.IsOnDemand = Convert.ToBoolean(dr["IsOnDemand"]);
                            scheduledEvent.Notes = Convert.ToString(dr["Notes"]);
                            scheduledEvent.EndUserEmail = Convert.ToString(dr["EndUserEmail"]);
                            scheduledEvent.EndUserPhoneNumber = Convert.ToString(dr["EndUserPhoneNumber"]);
                            scheduledEvent.CalendarEventId = dr["CalendarEventId"] == DBNull.Value ? "" : Convert.ToString(dr["CalendarEventId"]);
                            scheduledEvent.RemoteInvitationLink = Convert.ToString(dr["RemoteInvitationLink"]);
                            scheduledEvent.MarkerTypeId = Convert.ToInt32(dr["MarkerTypeId"]);
                            scheduledEvent.InspectionTemplateId = dr["InspectionTemplateId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["InspectionTemplateId"]);
                            scheduledEvent.InspectionTemplateType = dr["InspectionTemplateType"] == DBNull.Value ? "" : Convert.ToString(dr["InspectionTemplateType"]);
                            scheduledEvent.PreInspectionDetails = dr["PreInspectionDetails"] == DBNull.Value ? "" : Convert.ToString(dr["PreInspectionDetails"]);
                            scheduledEvent.SentOn = Convert.ToDateTime(dr["SentOn"]);
                            scheduledEvent.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            scheduledEvent.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                        }
                        dr.NextResult();

                        scheduledEvent.Participants = ORMapper.MapParticipants(dr);
                        dr.NextResult();

                        while(dr.Read())
                        {
                            DataContracts.EMREntities.CustomField objCustomField = new DataContracts.EMREntities.CustomField();

                            objCustomField.CustomFieldId = Convert.ToInt32(dr["CFId"]);
                            objCustomField.CustomFieldData = Convert.ToString(dr["CFText"]);

                            customFields.Add(objCustomField);
                        }

                        if (customFields.Count > 0)
                        {
                            scheduledEvent.CustomFields = customFields;
                        }
                    }
                }
                return scheduledEvent;
            }
            catch (Exception ex) { throw ex; }
        }
        public bool UpdateEventDetail(string eventId, string eventName, string duration, DateTime scheduledDateTime, DateTime modifiedDate, List<ScheduleEventParticipant> participants)
        {

            return false;
        }
        public List<ScheduleEventInfo> DeleteAndGetAllScheduledEvents(string eventId, int scheduledBy)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.LiveMonitor.DELETE_SCHEDULED_EVENT;
                    cmd.Parameters.AddWithValue("@EventId", eventId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "DeleteAndGetAllScheduledEvents", _tenantId));

                    conn.Open();
                    int rowsAffected = cmd.ExecuteNonQuery();
                }
                List<ScheduleEventInfo> scheduledEvents = new List<ScheduleEventInfo>();
                scheduledEvents = GetAllScheduledEvents(scheduledBy);
                return scheduledEvents;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<ScheduleEventInfo> SaveScheduledEventAndGetAll(string eventId, out int lastSavedId, string eventName, string duration, DateTime scheduledDateTime, int scheduledBy, string scheduledByEmail, bool isActive, List<ScheduleEventParticipant> participansts, bool isVirtualInspection, bool isOnDemand, string endUserPhoneNumber, string endUserEmail, string remoteInvitationLink, int markerTypeId, List<RevCord.DataContracts.EMREntities.CustomField> customFields, string notes, int inspectionTemplateId, string inspectionTemplateType, string preInspectionDetails, MGOReportData mgoReportData)
        {
            try
            {
                int rowsaffected = 0;
                int lastId = 0;
                // Here you go for schedule events
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.LiveMonitor.SAVE_SCHEDULED_EVENT;
                    cmd.Parameters.AddWithValue("@EventId", eventId);
                    cmd.Parameters.AddWithValue("@EventName", eventName);
                    cmd.Parameters.AddWithValue("@Duration", duration);
                    cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    cmd.Parameters.AddWithValue("@ScheduledDateTime", scheduledDateTime);
                    cmd.Parameters.AddWithValue("@ScheduledBy", scheduledBy);
                    cmd.Parameters.AddWithValue("@ScheduledByEmail", scheduledByEmail);
                    cmd.Parameters.AddWithValue("@IsActive", isActive);
                    cmd.Parameters.AddWithValue("@IsVirtualInspection", isVirtualInspection);
                    cmd.Parameters.AddWithValue("@EndUserPhoneNumber", endUserPhoneNumber);
                    cmd.Parameters.AddWithValue("@EndUserEmail", endUserEmail);
                    cmd.Parameters.AddWithValue("@RemoteInvitationLink", remoteInvitationLink);
                    cmd.Parameters.AddWithValue("@IsOnDemand", isOnDemand);
                    cmd.Parameters.AddWithValue("@MarkerTypeId", markerTypeId);
                    cmd.Parameters.AddWithValue("@Notes", notes);
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                    cmd.Parameters.AddWithValue("@InspectionTemplateType", inspectionTemplateType);
                    cmd.Parameters.AddWithValue("@PreInspectionDetails", preInspectionDetails);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SaveScheduledEventAndGetAll", _tenantId));

                    //return Convert.ToBoolean(cmd.ExecuteScalar());
                    //rowsaffected = cmd.ExecuteNonQuery();
                    lastId = Convert.ToInt32(cmd.ExecuteScalar());

                    lastSavedId = lastId;

                    conn.Close();
                }
                //if (rowsaffected > 0)
                if (lastId > 0)
                {
                    SaveParticipant(participansts);
                    SaveCustomFields(customFields);
                }

                try
                {
                    if (mgoReportData != null)
                    {
                        new MGODataDAL(_tenantId).SaveMGOReportData(mgoReportData);
                    }
                }
                catch (Exception ex)
                {

                }
                // Get All Scheduled Events
                List<ScheduleEventInfo> scheduledEvents = new List<ScheduleEventInfo>();
                scheduledEvents = GetAllScheduledEvents(scheduledBy);
                return scheduledEvents;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<ScheduleEventInfo> UpdateScheduledEventAndGetAll(string eventId, string eventName, string duration, DateTime scheduledDateTime, int scheduledBy, string scheduledByEmail, bool isActive, List<ScheduleEventParticipant> participansts, bool isVirtualInspection, string endUserPhoneNumber, string endUserEmail, string remoteInvitationLink, int markerTypeId, List<RevCord.DataContracts.EMREntities.CustomField> customFields, string notes,int inspectionTemplateId, string inspectionTemplateType, string preInspectionDetails)
        {
            try
            {
                int rowsaffected = 0;
                // Here you go for schedule events
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.LiveMonitor.UPDATE_SCHEDULED_EVENT;
                    cmd.Parameters.AddWithValue("@EventId", eventId);
                    cmd.Parameters.AddWithValue("@EventName", eventName);
                    cmd.Parameters.AddWithValue("@Duration", duration);
                    cmd.Parameters.AddWithValue("@ScheduledDateTime", scheduledDateTime);
                    cmd.Parameters.AddWithValue("@EndUserPhoneNumber", endUserPhoneNumber);
                    cmd.Parameters.AddWithValue("@EndUserEmail", endUserEmail);
                    cmd.Parameters.AddWithValue("@MarkerTypeId", markerTypeId);
                    cmd.Parameters.AddWithValue("@RemoteInvitationLink", remoteInvitationLink);
                    cmd.Parameters.AddWithValue("@Notes", notes);
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                    cmd.Parameters.AddWithValue("@InspectionTemplateType", inspectionTemplateType);
                    cmd.Parameters.AddWithValue("@PreInspectionDetails", preInspectionDetails);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateScheduledEventAndGetAll", _tenantId));

                    rowsaffected = cmd.ExecuteNonQuery();
                    conn.Close();
                }
                //if (rowsaffected > 0)
                SaveParticipant(participansts);
                SaveCustomFields(customFields);


                // Get All Scheduled Events
                List<ScheduleEventInfo> scheduledEvents = new List<ScheduleEventInfo>();
                scheduledEvents = GetAllScheduledEvents(scheduledBy);
                return scheduledEvents;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool SaveParticipant(List<ScheduleEventParticipant> participansts)
        {
            try
            {
                // Here you go for schedule events
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    //cmd.CommandType = CommandType.StoredProcedure;
                    //cmd.CommandText = DBConstants.LiveMonitor.SAVE_PARTICIPANT;
                    
                    conn.Open();

                    foreach (var participant in participansts)
                    {
                        cmd.Parameters.Clear();
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.LiveMonitor.SAVE_PARTICIPANT;

                        cmd.Parameters.AddWithValue("@EventId", participant.EventId);
                        cmd.Parameters.AddWithValue("@ScheduledDateTime", participant.ScheduledDateTime);
                        cmd.Parameters.AddWithValue("@ScheduledBy", participant.ScheduledBy);
                        cmd.Parameters.AddWithValue("@ParticipantId", participant.ParticipantId);
                        cmd.Parameters.AddWithValue("@ParticipantEmail", participant.ParticipantEmail);
                        cmd.Parameters.AddWithValue("@ParticipantPhoneNumber", participant.ParticipantPhoneNumber);
                        cmd.Parameters.AddWithValue("@ParticipantName", string.IsNullOrEmpty(participant.ParticipantName) ? participant.ParticipantEmail.Split('@')[0] : participant.ParticipantName);
                        cmd.Parameters.AddWithValue("@InvitationLink", participant.InvitationLink);
                        cmd.Parameters.AddWithValue("@HasLiveMonitorPermission", participant.HasLiveMonitorPermission);
                        cmd.Parameters.AddWithValue("@ParticipantIsEndUser", participant.ParticipantIsEndUser);
                        cmd.Parameters.AddWithValue("@Ext", participant.Ext);
                        cmd.Parameters.AddWithValue("@ExtName", participant.ExtName);
                        cmd.Parameters.AddWithValue("@InspectorEmail", participant.InspectorEmail);
                        cmd.Parameters.AddWithValue("@HasJoined", participant.HasJoined);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SaveParticipant", _tenantId));

                        cmd.ExecuteNonQuery();
                    }

                    conn.Close();
                }
                return true;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool SaveCustomFields(List<RevCord.DataContracts.EMREntities.CustomField> customFields)
        {
            try
            {
                // Here you go for schedule events
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "iq3AddCustomFieldData";

                    conn.Open();

                    foreach (var customField in customFields)
                    {
                        try
                        {
                            cmd.Parameters.Clear();
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = "iq3AddCustomFieldData";

                            cmd.Parameters.AddWithValue("@CustomFieldId", customField.CustomFieldId);
                            cmd.Parameters.AddWithValue("@CustomFieldText", customField.CustomFieldData);
                            cmd.Parameters.AddWithValue("@EventId", customField.EventId);
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SaveParticipant", _tenantId));

                            cmd.ExecuteNonQuery();
                        }
                        catch (Exception ex)
                        {
                            // To avoid not saving of complete event just because of Custom Field
                        }
                    }

                    conn.Close();
                }
                return true;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool DeleteScheduledEvent(string eventId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.LiveMonitor.DELETE_SCHEDULED_EVENT;
                    cmd.Parameters.AddWithValue("@EventId", eventId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "DeleteScheduledEvent", _tenantId));

                    return Convert.ToBoolean(cmd.ExecuteNonQuery());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool IsScheduledEventInvitationActive(string eventId, string participantEmail, string participantPhoneNumber)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.LiveMonitor.CHECK_SCHEDULED_EVENT_INVITATION_STATUS;
                    cmd.Parameters.AddWithValue("@EventId", eventId);
                    cmd.Parameters.AddWithValue("@ParticipantEmail", participantEmail);
                    cmd.Parameters.AddWithValue("@ParticipantPhoneNumber", participantPhoneNumber);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "IsScheduledEventInvitationActive", _tenantId));

                    return Convert.ToBoolean(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<ScheduleEventParticipant> FetchAllParticipant(int scheduledBy)
        {
            try
            {
                List<ScheduleEventInfo> scheduledEvents = new List<ScheduleEventInfo>();
                List<ScheduleEventParticipant> participants = new List<ScheduleEventParticipant>();
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.LiveMonitor.GETALL_PARTICIPANTS;
                    cmd.Parameters.AddWithValue("@ScheduledBy", scheduledBy);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "FetchAllParticipant", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        participants = ORMapper.MapParticipants(dr);
                    }
                }
                return participants;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<ScheduleEventParticipant> FetchParticipantsByEventId(int scheduledBy, string eventId)
        {
            try
            {
                List<ScheduleEventInfo> scheduledEvents = new List<ScheduleEventInfo>();
                List<ScheduleEventParticipant> participants = new List<ScheduleEventParticipant>();
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.LiveMonitor.FETCH_PARTICIPANTS_BY_EVENTID;
                    cmd.Parameters.AddWithValue("@ScheduledBy", scheduledBy);
                    cmd.Parameters.AddWithValue("@EventId", eventId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "FetchParticipantsByEventId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        participants = ORMapper.MapParticipants(dr);
                    }
                }
                return participants;
            }
            catch (Exception ex) { throw ex; }
        }

        public string GetCalendarId()
        {
            string calendarId = "";
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM lmCalendar";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetCalendarId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            calendarId = Convert.ToString(dr["CalendarId"]);
                        }
                    }
                }
                return calendarId;
            }
            catch (Exception ex) { throw ex; }
        }

        public int InsertCalendarId(string calendarId)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "INSERT INTO lmCalendar (CalendarId) VALUES (@CalendarId);SELECT SCOPE_IDENTITY();";
                    cmd.Parameters.AddWithValue("@CalendarId", calendarId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertCalendarId", _tenantId));

                    //rowsAffected = Convert.ToInt32(cmd.ExecuteScalar());
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool UpdateCalendarEventId(int schduleEventId, string eventId)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE lmScheduleEvent SET CalendarEventId = @CalendarEventId WHERE Id=(SELECT MAX(Id) FROM lmScheduleEvent)";
                    cmd.Parameters.AddWithValue("@CalendarEventId", eventId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateCalendarEventId", _tenantId));

                    //rowsAffected = Convert.ToInt32(cmd.ExecuteScalar());
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected > 0;
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion

        public int CallAuditSave(string callId, int userNum, string ipAddress, bool isSaved)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CallAudit.CALL_AUDIT_SAVE;
                    cmd.Parameters.AddWithValue("@CallId", callId);
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@IPAddress", ipAddress);
                    cmd.Parameters.AddWithValue("@IsSaved", isSaved);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "CallAuditSave", _tenantId));

                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }


        #region User Searches

        public int InsertUserSearch(UserSearch userSearch)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var cmd = conn.CreateCommand())
                    {
                        //  1. User Search
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.SearchCriteria.USER_SERACHES_INSERT;

                        cmd.Parameters.AddWithValue("@Name", userSearch.Name);
                        cmd.Parameters.AddWithValue("@UserId", userSearch.UserId);
                        cmd.Parameters.AddWithValue("@Comments", userSearch.Comments);
                        cmd.Parameters.AddWithValue("@CreatedDate", userSearch.CreatedDate);
                        cmd.Parameters.AddWithValue("@Criteria", userSearch.CriteriaString);

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertUserSearch", _tenantId));

                        //cmd.ExecuteNonQuery();
                        int lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                        userSearch.Id = lastSavedId;

                        cmd.Parameters.Clear();
                        rowAffected++;
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }


        public int InsertUserSearches(UserSearch userSearch)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction())
                    {
                        try
                        {
                            using (var cmd = conn.CreateCommand())
                            {
                                //  1. User Search
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.SearchCriteria.USER_SERACHES_INSERT;
                                cmd.Transaction = tran as SqlTransaction;

                                cmd.Parameters.AddWithValue("@Name", userSearch.Name);
                                cmd.Parameters.AddWithValue("@UserId", userSearch.UserId);
                                cmd.Parameters.AddWithValue("@Comments", userSearch.Comments);
                                cmd.Parameters.AddWithValue("@CreatedDate", userSearch.CreatedDate);
                                cmd.Parameters.AddWithValue("@Criteria", userSearch.CriteriaString);

                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertUserSearches", _tenantId));

                                //cmd.ExecuteNonQuery();
                                int lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                userSearch.Id = lastSavedId;

                                cmd.Parameters.Clear();
                                rowAffected++;


                                //  2. Search Detail
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.CommandText = DBConstants.SearchCriteria.USER_SERACH_DETAIL_INSERT;
                                cmd.Transaction = tran as SqlTransaction;

                                cmd.Parameters.AddWithValue("@SearchId", userSearch.Id);
                                cmd.Parameters.AddWithValue("@StartDate", userSearch.UserSearchDetail.StartDate.ToString("MM-dd-yyyy"));
                                cmd.Parameters.AddWithValue("@EndDate", userSearch.UserSearchDetail.EndDate.ToString("MM-dd-yyyy"));
                                cmd.Parameters.AddWithValue("@StartTime", userSearch.UserSearchDetail.StartTime);
                                cmd.Parameters.AddWithValue("@EndTime", userSearch.UserSearchDetail.EndTime);
                                cmd.Parameters.AddWithValue("@StartDuration", userSearch.UserSearchDetail.StartDuration);
                                cmd.Parameters.AddWithValue("@EndDuration", userSearch.UserSearchDetail.EndDuration);
                                cmd.Parameters.AddWithValue("@CustomText", userSearch.UserSearchDetail.CustomText);
                                cmd.Parameters.AddWithValue("@SearchType", userSearch.UserSearchDetail.SearchType.ToString());
                                cmd.Parameters.AddWithValue("@CategoryGroupExtensions", userSearch.UserSearchDetail.GroupExtensions);

                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertUserSearches", _tenantId));

                                //cmd.ExecuteNonQuery();
                                lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                                userSearch.UserSearchDetail.Id = lastSavedId;

                                rowAffected++;
                            }
                            tran.Commit();
                        }
                        catch
                        {
                            tran.Rollback();
                            throw;
                        }
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex) { throw ex; }

        }

        public List<UserSearch> GetUserSearches(int userId)
        {
            List<UserSearch> userSearches = null;
            List<UserSearchDetail> userSearchDetails = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.SearchCriteria.USER_SERACH_GETALLBY_USERID;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetUserSearches", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        userSearches = new List<UserSearch>();
                        while (dr.Read())
                        {
                            var us = new UserSearch();

                            us.Id = Convert.ToInt32(dr["Id"]);
                            us.Name = Convert.ToString(dr["Name"]);
                            us.UserId = Convert.ToInt32(dr["UserId"]);
                            us.Comments = Convert.ToString(dr["Comments"]);
                            us.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            us.CriteriaString = Convert.ToString(dr["Criteria"]);

                            userSearches.Add(us);
                        }
                        //2. UserSearchDetail ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            //if (dr.Read())
                            //dsfCalls = ORMapper.MapCallInfos(dr);
                            userSearchDetails = new List<UserSearchDetail>();
                            while (dr.Read())
                            {
                                var usd = new UserSearchDetail();

                                usd.Id = Convert.ToInt32(dr["Id"]);
                                usd.SearchId = Convert.ToInt32(dr["SearchId"]);
                                usd.StartDate = Convert.ToDateTime(dr["StartDate"]);
                                usd.EndDate = Convert.ToDateTime(dr["EndDate"]);
                                usd.StartTime = (TimeSpan)(dr["StartTime"]);
                                usd.EndTime = (TimeSpan)(dr["EndTime"]);
                                usd.StartDuration = (TimeSpan)(dr["StartDuration"]);
                                usd.EndDuration = (TimeSpan)(dr["EndDuration"]);
                                usd.CustomText = Convert.ToString(dr["CustomText"]);
                                usd.SearchType = (SearchType)Enum.Parse(typeof(SearchType), Convert.ToString(dr["SearchType"]));
                                usd.GroupExtensions = Convert.ToString(dr["CategoryGroupExtensions"]);
                                //usd.CategoryGroupExtensions =

                                userSearchDetails.Add(usd);

                                userSearches.FirstOrDefault(us => us.Id == usd.SearchId).UserSearchDetail = usd;
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return userSearches;
        }

        public UserSearch GetUserSearchById(int id)
        {
            UserSearch us = null;
            UserSearchDetail usd = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.SearchCriteria.USER_SERACH_DETAIL_GET_BY_SEARCHID;
                    cmd.Parameters.AddWithValue("@SearchId", id);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetUserSearchById", _tenantId));

                    using (var dr = cmd.ExecuteReader())
                    {
                        // 1. UserSearch ResultSet
                        if (dr.Read())
                        {
                            us = new UserSearch();
                            us.Id = Convert.ToInt32(dr["Id"]);
                            us.Name = Convert.ToString(dr["Name"]);
                            us.UserId = Convert.ToInt32(dr["UserId"]);
                            us.Comments = Convert.ToString(dr["Comments"]);
                            us.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            us.CriteriaString = Convert.ToString(dr["Criteria"]);
                        }

                        // 2. UserSearchDetail ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            if (dr.Read())
                            {
                                usd = new UserSearchDetail();
                                usd.Id = Convert.ToInt32(dr["Id"]);
                                usd.SearchId = Convert.ToInt32(dr["SearchId"]);
                                usd.StartDate = Convert.ToDateTime(dr["StartDate"]);
                                usd.EndDate = Convert.ToDateTime(dr["EndDate"]);
                                usd.StartTime = (TimeSpan)(dr["StartTime"]);
                                usd.EndTime = (TimeSpan)(dr["EndTime"]);
                                usd.StartDuration = (TimeSpan)(dr["StartDuration"]);
                                usd.EndDuration = (TimeSpan)(dr["EndDuration"]);
                                usd.CustomText = Convert.ToString(dr["CustomText"]);
                                usd.SearchType = (SearchType)Enum.Parse(typeof(SearchType), Convert.ToString(dr["SearchType"]));
                                usd.GroupExtensions = Convert.ToString(dr["CategoryGroupExtensions"]);
                                //usd.CategoryGroupExtensions =
                            }
                            us.UserSearchDetail = usd;
                        }
                    }
                }
                return us;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool DeleteUserSearch(int id)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.SearchCriteria.USER_SERACHES_DELETE;
                    cmd.Parameters.AddWithValue("@SearchId", id);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "DeleteUserSearch", _tenantId));

                    //return Convert.ToBoolean(cmd.ExecuteNonQuery());
                    return cmd.ExecuteNonQuery() > 0;
                }
            }
            catch (Exception ex) { throw ex; }

        }

        #endregion


        #region Custom Search

        public List<CallInfo> CustomSearchInPrimaryDB(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria, int noOfCalls)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "vr_Call_SearchPrimarySM";// DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "CustomSearchInPrimaryDB", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }


        public List<CallInfo> CustomSearchInPrimaryDBSA(int pageSize, int pageIndex, out int totalPages, out long totalRecords, string duration, string criteria, CallCriteria callCriteria, OperatorExpression searchOperator)
        {
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "vr_Call_SearchPrimary_SA_Test1";

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", searchOperator == OperatorExpression.Like ? string.Format("Like '%{0}%'", callCriteria.CustomText) : string.Format("= '{0}'", callCriteria.CustomText));//string.Format("{0} {1}", callCriteria.CustomSearchOperator, callCriteria.CustomText));

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "CustomSearchInPrimaryDBSA", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        #endregion

        public Tuple<string, string> GetAppUserCredentialsByExt(int extId)
        {
            Tuple<string, string> tuple = null;
            string userId = string.Empty;
            string userPassword = string.Empty;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_Account WHERE Ext = @Ext;";

                    cmd.Parameters.AddWithValue("@Ext", extId);

                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Extensions
                        if (dr.Read())
                        {
                            userId = Convert.ToString(dr["UserID"]);
                            userPassword = Convert.ToString(dr["UserPW"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            tuple = new Tuple<string, string>(userId, userPassword);
            return tuple;
        }

        public int SaveMultiCallEvaluation(MultiCallEvaluationGroup multiCallEvaluationGroup)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.MultiCallEvaluation.MULT_CALL_EVALUATION_INSERT;
                        cmd.CommandTimeout = CMD_TIMEOUT;

                        cmd.Parameters.AddWithValue("@Name", multiCallEvaluationGroup.Name);
                        cmd.Parameters.AddWithValue("@Comment", multiCallEvaluationGroup.Comment);
                        cmd.Parameters.AddWithValue("@SurveyId", multiCallEvaluationGroup.SurveyId);
                        cmd.Parameters.AddWithValue("@EvaluatorId", multiCallEvaluationGroup.EvaluatorId);
                        cmd.Parameters.AddWithValue("@CSVCallIds", multiCallEvaluationGroup.CSVCallIds);

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "InsertMultiCallEvaluation", _tenantId));

                        //cmd.ExecuteNonQuery();
                        int lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                        multiCallEvaluationGroup.Id = lastSavedId;

                        cmd.Parameters.Clear();
                        rowAffected++;
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<CustomField> GetCustomFields(int userNum)
        {
            List<CustomField> customFields = new List<CustomField>();
            CustomField customField = null;
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = @"SELECT cf.* FROM iq3CustomFields cf LEFT JOIN t_Account acc ON cf.UserNum = acc.UserNum  WHERE acc.UserNum = @UserNum AND acc.Status = 1 AND cf.IsDeleted = 0 AND cf.Title <> 'Empty' ORDER BY cf.Id, cf.[Order] ASC";
                cmd.Parameters.AddWithValue("@UserNum", userNum);

                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetCustomFields", _tenantId));
                using (SqlDataReader dr = cmd.ExecuteReader())
                {
                    while (dr.Read())
                    {
                        customField = new CustomField();
                        customField.Id = Convert.ToInt32(dr["Id"]);
                        customField.UserNum = Convert.ToInt32(dr["UserNum"]);
                        customField.Order = Convert.ToInt32(dr["Order"]);
                        customField.Title = Convert.ToString(dr["Title"]);
                        customField.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                        customField.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                        customFields.Add(customField);
                    }
                }
            }
            return customFields;
        }

        public List<CallInfo> GetCallInfosFromPrimaryDB(string callIds, DateTime startDate, DateTime endDate)
        {
            List<CallInfo> callInfos = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandText = DBConstants.VoiceRec.EXPORT_RESULTS_GET_BY_COMMA_SEPERATED_IDS;
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));
                    cmd.Parameters.AddWithValue("@StartDateTime", startDate.ToString("yyyyMMddHHmmss"));
                    cmd.Parameters.AddWithValue("@EndDateTime", endDate.ToString("yyyyMMddHHmmss"));

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "GetCallInfosFromPrimaryDB", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callInfos = ORMapper.MapCallInfosForPrimaryDB(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callInfos;
        }

        public List<TenantGateway> FetchTenantGateways()
        {
            try
            {
                TenantGateway tenantGateway = new TenantGateway();
                List<TenantGateway> tenantGateways = new List<TenantGateway>();
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = "SELECT * FROM mtGateway WHERE TenantId = @TenantId AND IsDeleted = 0;";
                    //cmd.CommandText = "SELECT gw.*, (SELECT COUNT(GatewayId) FROM t_ExtInfo WHERE GatewayId  = gw.GatewayId) AS UsedSlots, (gw.AvailableSlots - (SELECT COUNT(GatewayId) FROM t_ExtInfo WHERE GatewayId = gw.GatewayId)) AS VacantSlots FROM mtGateway gw WHERE gw.TenantId = @TenantId AND gw.IsDeleted = 0 AND(gw.AvailableSlots - (SELECT COUNT(GatewayId) FROM t_ExtInfo WHERE GatewayId = gw.GatewayId)) > 0";
                    cmd.CommandText = "SELECT gw.*, (SELECT COUNT(GatewayId) FROM t_ExtInfo WHERE GatewayId  = gw.GatewayId) AS UsedSlots, (gw.AvailableSlots - (SELECT COUNT(GatewayId) FROM t_ExtInfo WHERE GatewayId = gw.GatewayId)) AS VacantSlots FROM mtGateway gw WHERE gw.TenantId = @TenantId AND gw.IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "FetchTenantGateways", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            tenantGateway = new TenantGateway();
                            tenantGateway.Id = Convert.ToInt32(dr["Id"]);
                            tenantGateway.Name = Convert.ToString(dr["Name"]);
                            tenantGateway.GatewayId = Convert.ToString(dr["GatewayId"]);
                            tenantGateway.AvailableSlots = Convert.ToInt32(dr["AvailableSlots"]);
                            tenantGateway.UsedSlots = Convert.ToInt32(dr["UsedSlots"]);
                            tenantGateway.VacantSlots = Convert.ToInt32(dr["VacantSlots"]);
                            tenantGateway.TenantId = Convert.ToInt32(dr["TenantId"]);
                            tenantGateway.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            tenantGateway.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            tenantGateways.Add(tenantGateway);
                        }
                    }
                }
                return tenantGateways;
            }
            catch (Exception ex) { throw ex; }
        }

        public int SaveTenantGateway(TenantGateway tenantGateway)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction())
                    {
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.CommandText = "INSERT INTO mtGateway(Name, TenantId, GatewayId, AvailableSlots)  VALUES (@Name, @TenantId, @GatewayId, @AvailableSlots); SELECT SCOPE_IDENTITY();";
                            cmd.CommandTimeout = CMD_TIMEOUT;

                            cmd.Parameters.AddWithValue("@Name", tenantGateway.Name);
                            cmd.Parameters.AddWithValue("@TenantId", tenantGateway.TenantId);
                            cmd.Parameters.AddWithValue("@GatewayId", tenantGateway.GatewayId);
                            cmd.Parameters.AddWithValue("@AvailableSlots", tenantGateway.AvailableSlots);
                            cmd.Transaction = tran;

                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "SaveTenantGateway", _tenantId));

                            int lastSavedId = Convert.ToInt32(cmd.ExecuteScalar());
                            tenantGateway.Id = lastSavedId;

                            try
                            {
                                using (var con = DALHelper.GetConnectionMasterDB())
                                {
                                    using (var command = con.CreateCommand())
                                    {
                                        command.CommandType = CommandType.Text;
                                        command.CommandText = "INSERT INTO mtTenantGateway(TenantId, GatewayId, GatewayName)  VALUES (@TenantId, @GatewayId, @GatewayName); SELECT SCOPE_IDENTITY();";
                                        command.CommandTimeout = CMD_TIMEOUT;

                                        command.Parameters.AddWithValue("@TenantId", tenantGateway.TenantId);
                                        command.Parameters.AddWithValue("@GatewayId", tenantGateway.GatewayId);
                                        command.Parameters.AddWithValue("@GatewayName", tenantGateway.Name);
                                        con.Open();
                                        int id = Convert.ToInt32(command.ExecuteScalar());
                                        if (id > 0)
                                        {
                                            tran.Commit();
                                        }
                                        else
                                            tran.Rollback();
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                tran.Rollback();
                            }
                            rowAffected++;
                        }


                    }
                    return rowAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateTenantGateway(TenantGateway tenantGateway)
        {
            try
            {
                int rowAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "UPDATE mtGateway SET Name = @Name, GatewayId = @GatewayId, AvailableSlots = @AvailableSlots WHERE Id = @Id";
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@Id", tenantGateway.Id);
                        cmd.Parameters.AddWithValue("@Name", tenantGateway.Name);
                        cmd.Parameters.AddWithValue("@GatewayId", tenantGateway.GatewayId);
                        cmd.Parameters.AddWithValue("@AvailableSlots", tenantGateway.AvailableSlots);

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "UpdateTenantGateway", _tenantId));

                        rowAffected = cmd.ExecuteNonQuery();
                        cmd.Parameters.Clear();
                    }
                    return rowAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int DeleteTenantGateway(int tenantGatewayId, string gatewayId, int tenantId)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();

                    using (var tran = conn.BeginTransaction())
                    {
                        using (var cmd = new SqlCommand("UPDATE mtGateway SET IsDeleted = 1 WHERE Id = @Id; UPDATE t_ExtInfo SET GatewayId = NULL WHERE GatewayId = (SELECT GatewayId FROM mtGateway WHERE Id = @Id)", conn, tran))
                        {
                            cmd.Parameters.AddWithValue("@Id", tenantGatewayId);
                            rowsAffected = cmd.ExecuteNonQuery();
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "DeleteTenantGateway", _tenantId));
                        }

                        using (var con = DALHelper.GetConnectionMasterDB())
                        {
                            using (var command = con.CreateCommand())
                            {
                                command.CommandText = "UPDATE mtTenantGateway SET IsDeleted = 1 WHERE TenantId = @TenantId AND GatewayId = '" + gatewayId + "'";
                                command.CommandType = CommandType.Text;
                                command.Parameters.AddWithValue("@TenantId", tenantId);
                                con.Open();
                                rowsAffected = command.ExecuteNonQuery();
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(command), Originator.VoiceLogging, "DeleteTenantGateway", _tenantId));
                                if (rowsAffected > 0)
                                {
                                    tran.Commit();
                                }
                                else
                                    tran.Rollback();
                            }
                        }
                    }
                    return rowsAffected;
                }
            }
            catch (Exception ex) { throw ex; }
        } 

        public List<TenantGateway> FetchAvailableTenantGateways()
        {
            try
            {
                TenantGateway tenantGateway = new TenantGateway();
                List<TenantGateway> tenantGateways = new List<TenantGateway>();
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT gw.*, (SELECT COUNT(GatewayId) FROM t_ExtInfo WHERE GatewayId  = gw.GatewayId) AS UsedSlots, (gw.AvailableSlots - (SELECT COUNT(GatewayId) FROM t_ExtInfo WHERE GatewayId = gw.GatewayId)) AS VacantSlots FROM mtGateway gw WHERE gw.TenantId = @TenantId AND gw.IsDeleted = 0 AND(gw.AvailableSlots - (SELECT COUNT(GatewayId) FROM t_ExtInfo WHERE GatewayId = gw.GatewayId)) > 0";
                    cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "FetchTenantGateways", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            tenantGateway = new TenantGateway();
                            tenantGateway.Id = Convert.ToInt32(dr["Id"]);
                            tenantGateway.Name = Convert.ToString(dr["Name"]);
                            tenantGateway.GatewayId = Convert.ToString(dr["GatewayId"]);
                            tenantGateway.AvailableSlots = Convert.ToInt32(dr["AvailableSlots"]);
                            tenantGateway.UsedSlots = Convert.ToInt32(dr["UsedSlots"]);
                            tenantGateway.VacantSlots = Convert.ToInt32(dr["VacantSlots"]);
                            tenantGateway.TenantId = Convert.ToInt32(dr["TenantId"]);
                            tenantGateway.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            tenantGateway.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            tenantGateways.Add(tenantGateway);
                        }
                    }
                }
                return tenantGateways;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public bool VerifyInvitationAccessCode(int invitationId, string accessCode)
        {
            var returnValue = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from umShareEvent WHERE Id = @InvitationId AND RandomPassword=@AccessCode and IsDeleted = 0";
                    cmd.Parameters.AddWithValue("@InvitationId", invitationId);
                    cmd.Parameters.AddWithValue("@AccessCode", accessCode);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "VerifyInvitationAccessCode", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        returnValue = dr.HasRows;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return returnValue;
        }

        public List<PreInspectionData> FetchPreInspectionData(string eventId)
        {
            List<PreInspectionData> preInspectionDataList = new List<PreInspectionData>();
            PreInspectionData preInspectionData = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from iq3PreInspectionData WHERE EventId = @EventId AND IsDeleted = 0";
                    cmd.Parameters.AddWithValue("@EventId", eventId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.VoiceLogging, "FetchPreInspectionData", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            
                            while (dr.Read())
                            {
                                preInspectionData = new PreInspectionData();
                                preInspectionData.Id = Convert.ToInt32(dr["Id"]);
                                preInspectionData.EventId = Convert.ToString(dr["EventId"]);
                                preInspectionData.PreInspectionId = Convert.ToInt32(dr["PreInspectionId"]);
                                preInspectionData.PreInspectionTitle = Convert.ToString(dr["PreInspectionTitle"]);
                                preInspectionData.PreInspectionText = Convert.ToString(dr["PreInspectionText"]);
                                preInspectionData.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                preInspectionData.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                                preInspectionDataList.Add(preInspectionData);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return preInspectionDataList;
        }
    }
}
