﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.CustomerDBEntities;
using RevCord.DataContracts.EMREntities;
using RevCord.DataContracts.MGODataEntities;

namespace RevCord.DataContracts.InquireEntities
{
    public class Event
    {
        public string Id { get; set; }
        public string DateTime { get; set; }
        public string GPS { get; set; }
        public string Interviewer { get; set; }
        public string Interviewee { get; set; }
        public string Notes { get; set; }
        

        public string TotalTime { get; set; }
        public string InterviewType { get; set; }
        public string FilePath { get; set; }

        public string Thumb { get; set; }
        public string IsRecorded { get; set; }

        public string UserEmail { get; set; }
        public string UserPhone { get; set; }

        public string PrimaryArea { get; set; }
        public bool IsVirtualInspection { get; set; }

        public string ReportName { get; set; }
        public string FileName { get; set; }
        public string AssetId { get; set; }

        public List<AssetModel> AssetModel { get; set; }
        public Dictionary<string, object> AssetDictionary { get; set; }
        public List<IQ3AssetDetail> IQ3AssetDetails { get; set; }
        public MGOReportData MGOReportData { get; set; }
    }
}
