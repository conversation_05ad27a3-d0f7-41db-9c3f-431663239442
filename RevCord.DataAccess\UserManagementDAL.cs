﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data.SqlClient;
using System.Data;
using System.Xml.Linq;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using Newtonsoft.Json;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.Response;
using RevCord.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.RoleManagement;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.IWBEntities;

namespace RevCord.DataAccess
{
    public class UserManagementDAL
    {
        //public static SqlConnection GetConnection()
        //{
        //    return new SqlConnection(ConfigurationManager.ConnectionStrings["DALConnectionString"].ConnectionString.ToString());
        //}
        private int _tenantId;
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);
        public UserManagementDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }


        #region Global Groups

        public List<GlobalGroup> GetGropus(int uId = 1000)
        {
            List<GlobalGroup> gGroups = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GROUP_GETLIST;
                    cmd.Parameters.AddWithValue("@UserId", uId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetGropus", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        gGroups = ORMapper.MapGlobalGroups(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return gGroups;
        }

        public List<GlobalGroup> CreateGroup(GlobalGroup globalGroup, int uId = 1000)
        {

            List<GlobalGroup> gGroups = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GROUP_INSERT_N_GET;
                    cmd.Parameters.AddWithValue("@ParentId", globalGroup.ParentId);
                    cmd.Parameters.AddWithValue("@GroupName", globalGroup.Name);
                    cmd.Parameters.AddWithValue("@Description", globalGroup.Description);

                    cmd.Parameters.AddWithValue("@UserId", uId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CreateGroup", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        dr.NextResult();
                        gGroups = ORMapper.MapGlobalGroups(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return gGroups;
        }

        public List<GlobalGroup> UpdateGroup(GlobalGroup globalGroup, int uId = 1000)
        {
            List<GlobalGroup> gGroups = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GROUP_UPDATE_N_GET;
                    cmd.Parameters.AddWithValue("@Id", globalGroup.Id);
                    cmd.Parameters.AddWithValue("@ParentId", globalGroup.ParentId);
                    cmd.Parameters.AddWithValue("@GroupName", globalGroup.Name);
                    cmd.Parameters.AddWithValue("@Description", globalGroup.Description);
                    cmd.Parameters.AddWithValue("@Depth", globalGroup.Depth);
                    cmd.Parameters.AddWithValue("@Ordering", globalGroup.Ordering);
                    cmd.Parameters.AddWithValue("@IsActive", globalGroup.IsActive);
                    cmd.Parameters.AddWithValue("@permissionList", Convert.DBNull);
                    cmd.Parameters.AddWithValue("@UserId", uId);
                    cmd.Parameters.AddWithValue("@ModifiedDate", globalGroup.ModifiedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", globalGroup.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateGroup", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        gGroups = ORMapper.MapGlobalGroups(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return gGroups;
        }

        public List<GlobalGroup> DeleteteGroup(int id, int userId = 1000)
        {
            List<GlobalGroup> gGroups = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GROUP_DELETE_N_GET;
                    cmd.Parameters.AddWithValue("@Id ", id);
                    cmd.Parameters.AddWithValue("@UserId ", userId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "DeleteteGroup", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        gGroups = ORMapper.MapGlobalGroups(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return gGroups;
        }

        #endregion

        #region App Users

        public List<AppUser> GetAppUsersOnly(int uId = 1000)
        {
            List<AppUser> aUsers = null;
            List<Permission> permissions = null;
            List<GlobalGroup> groups = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GETLIST;
                    cmd.Parameters.AddWithValue("@UserId", uId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetAppUsersOnly", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        aUsers = ORMapper.MapAppUsers(dr);
                        dr.NextResult();
                        permissions = ORMapper.MapPermissions(dr);//Permission Table
                        dr.NextResult();
                        groups = ORMapper.MapGlobalGroups(dr); //Group Table
                        if (aUsers != null)
                        {
                            aUsers.ForEach(u => u.Permissions = permissions.FindAll(o => o.UserNum == u.Id));
                            aUsers.ForEach(u => u.Group = (GlobalGroup)groups.FirstOrDefault(g => g.Id == u.GroupId).Clone()); // TODO: Null object Handling
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return aUsers;
        }

        public UserManagementResponse GetAppUsers(int uId = 1000)
        {
            List<User> users = null;
            List<UserGroup> uGroups = null;
            List<Permission> permissions = null;
            List<GlobalGroup> groups = null;
            List<TreeViewDataDTO> treeViewDataDTOs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GETLIST;
                    cmd.Parameters.AddWithValue("@UserId", uId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetAppUsers", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsers(dr);
                        dr.NextResult();
                        uGroups = ORMapper.MapUserGroups(dr);//User Permission Table
                        //dr.NextResult();
                        //userPermissions = ORMapper.MapPermissions(dr);//Create User Permissions
                        dr.NextResult();
                        groups = ORMapper.MapGlobalGroups(dr); //Group Table
                        dr.NextResult();
                        permissions = ORMapper.MapPermissions(dr); //Permissions Table
                        dr.NextResult();
                        treeViewDataDTOs = ORMapper.MapTreeViewDataDTOs(dr); //treeViewData Table
                        uGroups.ForEach(g =>
                        {

                            List<TreeViewDataDTO> tvdDTOs = treeViewDataDTOs.FindAll(tvd => tvd.Param1 == Convert.ToString(g.GroupNum) && tvd.IsGroup == false);
                            if (tvdDTOs != null && tvdDTOs.Count != 0)
                                g.TreeViewDataDTOs = tvdDTOs.Select(i => (TreeViewDataDTO)i.Clone()).ToList();
                        });
                        if (users != null)
                        {
                            users.ForEach(u => u.UserGroup = uGroups.FirstOrDefault(ug => ug.UserNum == u.UserNum));
                            users.ForEach(u =>
                            {
                                u.Permissions = this.GetPermissions(u.UserNum, u.UserGroup, permissions);

                            });
                            users.ForEach(u =>
                            {
                                GlobalGroup gGroup = groups.FirstOrDefault(g => g.Id == u.GroupNum);
                                if (gGroup != null) u.GlobalGroup = (GlobalGroup)gGroup.Clone();
                            });
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new UserManagementResponse { Users = users, GlobalGroups = groups, Permissions = permissions, TreeViewDataDTOs = treeViewDataDTOs };
        }

        public UserManagementResponse GetAppUsers_User_Manager_Simple_User_Rights(int uId)
        {
            List<User> users = null;
            List<UserGroup> uGroups = null;
            List<Permission> permissions = null;
            List<GlobalGroup> groups = null;
            List<TreeViewDataDTO> treeViewDataDTOs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GETLIST_Simple_User;
                    cmd.Parameters.AddWithValue("@UserId", uId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetAppUsers_User_Manager_Simple_User_Rights", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsers(dr);
                        dr.NextResult();
                        uGroups = ORMapper.MapUserGroups(dr);//User Permission Table
                        //dr.NextResult();
                        //userPermissions = ORMapper.MapPermissions(dr);//Create User Permissions
                        dr.NextResult();
                        groups = ORMapper.MapGlobalGroups(dr); //Group Table
                        dr.NextResult();
                        permissions = ORMapper.MapPermissions(dr); //Permissions Table
                        dr.NextResult();
                        treeViewDataDTOs = ORMapper.MapTreeViewDataDTOs(dr); //treeViewData Table
                        uGroups.ForEach(g =>
                        {

                            List<TreeViewDataDTO> tvdDTOs = treeViewDataDTOs.FindAll(tvd => tvd.Param1 == Convert.ToString(g.GroupNum) && tvd.IsGroup == false);
                            if (tvdDTOs != null && tvdDTOs.Count != 0)
                                g.TreeViewDataDTOs = tvdDTOs.Select(i => (TreeViewDataDTO)i.Clone()).ToList();
                        });
                        if (users != null)
                        {
                            users.ForEach(u => u.UserGroup = uGroups.FirstOrDefault(ug => ug.UserNum == u.UserNum));
                            users.ForEach(u =>
                            {
                                u.Permissions = this.GetPermissions(u.UserNum, u.UserGroup, permissions);
                                u.EnterprisePermissions = GetEnterprisePermissions(u.UserNum, u.UserGroup, permissions);
                            });
                            users.ForEach(u =>
                            {
                                GlobalGroup gGroup = groups.FirstOrDefault(g => g.Id == u.GroupNum);
                                if (gGroup != null) u.GlobalGroup = (GlobalGroup)gGroup.Clone();
                            });
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new UserManagementResponse { Users = users, GlobalGroups = groups, Permissions = permissions, TreeViewDataDTOs = treeViewDataDTOs };
        }

        public UserManagementResponse GetEnterpriseUsers(int uId = 1000)
        {
            List<User> users = null;
            List<UserGroup> uGroups = null;
            List<Permission> permissions = null;
            List<GlobalGroup> groups = null;
            List<TreeViewDataDTO> treeViewDataDTOs = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GETLIST;
                    cmd.Parameters.AddWithValue("@UserId", uId);
                    cmd.Parameters.AddWithValue("@FetchEnterpriseOnly", 1);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetEnterpriseUsers", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsers(dr);
                        dr.NextResult();
                        uGroups = ORMapper.MapUserGroups(dr);//User Permission Table
                        //dr.NextResult();
                        //userPermissions = ORMapper.MapPermissions(dr);//Create User Permissions
                        dr.NextResult();
                        groups = ORMapper.MapGlobalGroups(dr); //Group Table
                        dr.NextResult();
                        permissions = ORMapper.MapPermissions(dr); //Permissions Table
                        dr.NextResult();
                        treeViewDataDTOs = ORMapper.MapTreeViewDataDTOs(dr); //treeViewData Table
                        uGroups.ForEach(g =>
                        {
                            List<TreeViewDataDTO> tvdDTOs = treeViewDataDTOs.FindAll(tvd => tvd.Param1 == Convert.ToString(g.GroupNum) && tvd.IsGroup == false);
                            if (tvdDTOs != null && tvdDTOs.Count != 0)
                                g.TreeViewDataDTOs = tvdDTOs.Select(i => (TreeViewDataDTO)i.Clone()).ToList();
                        });
                        if (users != null)
                        {
                            users.ForEach(u => u.UserGroup = uGroups.FirstOrDefault(ug => ug.UserNum == u.UserNum));
                            users.ForEach(u =>
                            {
                                u.Permissions = this.GetPermissions(u.UserNum, u.UserGroup, permissions);

                            });
                            users.ForEach(u =>
                            {
                                GlobalGroup gGroup = groups.FirstOrDefault(g => g.Id == u.GroupNum);
                                if (gGroup != null) u.GlobalGroup = (GlobalGroup)gGroup.Clone();
                            });
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new UserManagementResponse { Users = users, GlobalGroups = groups, Permissions = permissions, TreeViewDataDTOs = treeViewDataDTOs };
        }



        public UserManagementResponse GetUsersWithoutExtension()
        {
            List<User> users = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GETLIST_WITHOUT_EXT;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetUsersWithoutExtension", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsersWithoutChannel(dr);
                        dr.NextResult();
                    }

                }
            }
            catch (Exception ex) { throw ex; }
            return new UserManagementResponse { Users = users, };
        }

        public List<User> GetAllUsersAsAgents()
        {
            List<User> users = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_ACCOUNT WHERE Status = 1 AND (Ext = 0 OR Ext >= 2000)";
                    //cmd.CommandText = "SELECT * FROM t_ACCOUNT WHERE STATUS = 1 AND (EXT = 0 OR EXT >=2000)";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetAllUsersAsAgents", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsersAsAgents(dr);
                        dr.NextResult();
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return users;
        }



        public List<User> GetAllUsersWithPermission()
        {
            List<User> users = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.LiveMonitor.GET_ALL_USERS_WITH_LIVE_MONITOR_PERMISSION;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetAllUsersWithPermission", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapAllUsersWithPermission(dr);
                        dr.NextResult();
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return users;
        }

        public List<User> GetAllAppUsers(int userTypeId)
        {
            List<User> users = new List<User>();
            try
            {
                // UserName Ext Status SelectType  IsDeviceUser 
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GET_ALL_APP_USERS;
                    cmd.Parameters.AddWithValue("@UserType", userTypeId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetAllAppUsers", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                User user = new User();
                                user.UserNum = Convert.ToInt32(dr["UserNum"]);
                                user.UserType = Convert.ToInt32(dr["UserType"]);
                                user.UserID = Convert.ToString(dr["UserID"]);
                                user.UserName = Convert.ToString(dr["UserName"]);
                                user.Ext = Convert.ToString(dr["Ext"]);
                                user.Status = Convert.ToInt32(dr["Status"]);
                                user.SelectType = Convert.ToInt32(dr["SelectType"]);
                                user.IsDeviceUser = Convert.ToInt32(dr["IsDeviceUser"]);
                                user.EnableDisableInquireUser = Convert.ToInt32(dr["Enable_User"]);
                                users.Add(user);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return users;
        }


        public List<UserInfoLite> GetUserData()
        {
            List<UserInfoLite> userInfos = new List<UserInfoLite>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_Account WHERE Status = 1 AND Ext='0';";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetUserData", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        UserInfoLite liteUser = null;
                        while (dr.Read())
                        {
                            liteUser = new UserInfoLite();
                            liteUser.Id = (int)dr["UserNum"];
                            liteUser.FullName = Convert.ToString(dr["UserName"]);
                            liteUser.RecId = 1;
                            userInfos.Add(liteUser);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return userInfos;
        }

        public UserManagementResponse GetAppUserAccount(long appUserId, long uId = 1000)
        {
            List<User> users = null;
            List<UserGroup> uGroup = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GETBYID;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@UserNum", appUserId);
                    cmd.Parameters.AddWithValue("@UserId", uId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetAppUserAccount", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsers(dr);
                        dr.NextResult();
                        uGroup = ORMapper.MapUserGroups(dr); //Permissions Table

                    }
                    if (users != null)
                    {
                        users.ForEach(u => u.UserGroup = uGroup.FirstOrDefault(ug => ug.UserNum == u.UserNum));
                    }
                    if (SiteConfig.RevSyncEnabled)
                    {
                        using (var RevSyncconn = DALHelper.GetRevSyncConnection())
                        using (var RevSynccmd = RevSyncconn.CreateCommand())
                        {
                            RevSynccmd.CommandType = CommandType.StoredProcedure;
                            RevSynccmd.CommandText = DBConstants.UserManagement.APPUSER_GETBYID;
                            RevSynccmd.Parameters.AddWithValue("@UserNum", appUserId);
                            RevSynccmd.Parameters.AddWithValue("@UserId", uId);
                            RevSyncconn.Open();
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(RevSynccmd), Originator.UserManagement, "GetAppUserAccount", _tenantId));
                            try
                            {
                                RevSynccmd.ExecuteNonQuery();
                            }
                            catch (Exception e)
                            {
                                tran.Rollback();
                            }
                        }
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new UserManagementResponse { Users = users, };
        }

        public UserManagementResponse GetAppUserAccount(Recorder recorder, long appUserId, long uId = 1000)
        {
            List<User> users = null;
            List<UserGroup> uGroup = null;
            List<Role> roles = null;
            List<string> userPasswords = new List<string>();
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GETBYID;
                    cmd.Parameters.AddWithValue("@UserNum", appUserId);
                    cmd.Parameters.AddWithValue("@UserId", uId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetAppUserAccount", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsers(dr, recorder.Id, recorder.Name);
                        dr.NextResult();
                        uGroup = ORMapper.MapUserGroups(dr); //Permissions Table
                        dr.NextResult();
                        roles = ORMapper.MapRoles(dr);
                        dr.NextResult();
                        while (dr.Read())
                        {
                            userPasswords.Add(Convert.ToString(dr["UserPassword"]));
                        }
                    }
                    if (users != null)
                    {
                        users.ForEach(u => u.UserGroup = uGroup.FirstOrDefault(ug => ug.UserNum == u.UserNum));
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new UserManagementResponse { Users = users, Last5Passwords = userPasswords, Roles = roles };
        }

        public List<User> CreateAppUserAccount(User appUser, bool isOnlyIQ3ModeEnabled)
        {
            List<User> appUsers = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_INSERT;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@UserID ", appUser.UserID);
                    cmd.Parameters.AddWithValue("@UserPW", appUser.UserPW);
                    cmd.Parameters.AddWithValue("@UserName", appUser.UserName);
                    cmd.Parameters.AddWithValue("@Ext", appUser.Ext);
                    cmd.Parameters.AddWithValue("@SearchRest", appUser.SearchRest);
                    cmd.Parameters.AddWithValue("@UserEmail", appUser.UserID);
                    cmd.Parameters.AddWithValue("@IdentityNumber", appUser.IdentityNumber);
                    cmd.Parameters.AddWithValue("@UserPhone", appUser.UserPhone);
                    cmd.Parameters.AddWithValue("@UserFax", appUser.UserFax);
                    cmd.Parameters.AddWithValue("@JoinBeginDate", appUser.JoinBeginDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@JoinEndDate", appUser.JoinEndDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@Descr", appUser.Descr);
                    cmd.Parameters.AddWithValue("@GroupNum", appUser.GroupNum);
                    cmd.Parameters.AddWithValue("@POD", appUser.POD);
                    cmd.Parameters.AddWithValue("@EOD", appUser.EOD);
                    cmd.Parameters.AddWithValue("@Pause", appUser.Pause);
                    cmd.Parameters.AddWithValue("@CompanyName", appUser.CompanyName);
                    cmd.Parameters.AddWithValue("@IsEnterpriseUser", appUser.IsEnterpriseUser);
                    cmd.Parameters.AddWithValue("@UserType", appUser.UserType);
                    cmd.Parameters.AddWithValue("@IsRevCell", appUser.IsRevCell);
                    cmd.Parameters.AddWithValue("@RoleId", appUser.RoleId);
                    cmd.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", isOnlyIQ3ModeEnabled);
                    cmd.Parameters.AddWithValue("@createdBy", appUser.CreatedBy);
                    cmd.Parameters.AddWithValue("@isIwbUser", appUser.IsIwbUser);
                    cmd.Parameters.AddWithValue("@organizationId", appUser.OrganizationId);
                    cmd.Parameters.AddWithValue("@dob", appUser.DOB);
                    cmd.Parameters.AddWithValue("@city", appUser.City);
                    cmd.Parameters.AddWithValue("@state", appUser.State);
                    cmd.Parameters.AddWithValue("@SocialSecurityNumber", appUser.SocialSecurityNumber);
                    cmd.Parameters.AddWithValue("@StencilNumber", appUser.WelderStencilNumber);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CreateAppUserAccount", _tenantId));
                    int appUserId = (int)cmd.ExecuteScalar();
                    appUser.UserNum = appUserId;
                    if (appUsers == null) appUsers = new List<User>();
                    appUsers.Add(appUser);
                    int lastId = appUserId;// Convert.ToInt32(cmd.ExecuteScalar());
                    int RevSyncServerUserNum = 0;
                    if (SiteConfig.RevSyncEnabled)
                    {
                        RevSyncServerUserNum = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "CreateAppUserAccount", JsonConvert.SerializeObject(appUser)));
                        if (RevSyncServerUserNum > 0)
                        {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE t_Account SET [RevSyncServerUserNum] = " + RevSyncServerUserNum + " Where UserNum = " + lastId;
                                Updatecmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            tran.Rollback();
                            Task.Run(() => RevAuditLogger.WriteSQL("um_t_Account_Insert Rolled Back because RevSyncServerUserNum is not greater than 0", Originator.UserManagement, "CreateAppUserAccount", _tenantId));
                        }
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "CreateAppUserAccount", JsonConvert.SerializeObject(appUser));

                        if (isSent)
                        {
                            tran.Commit();
                        }
                        else
                        {
                            tran.Rollback();
                            Task.Run(() => RevAuditLogger.WriteSQL("um_t_Account_Insert Rolled Back because SendMessageToHub returned false", Originator.UserManagement, "CreateAppUserAccount", _tenantId));
                        }
                    }
                    else
                    {
                        tran.Commit();
                    }
                }

                if (SiteConfig.IsMTEnable)
                    new TenantDAL(_tenantId).InsertUserAccount(_tenantId, appUser.UserNum, appUser.UserID, appUser.UserID, "", "", appUser.UserName, appUser.Descr);
            }
            catch (Exception ex) { throw ex; }
            return appUsers;
        }

        public List<User> UpdateAppUserAccount(User appUser) //TODO
        {
            List<User> appUsers = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_UPDATE;
                    cmd.Parameters.AddWithValue("@UserNum", appUser.UserNum);
                    cmd.Parameters.AddWithValue("@UserType", appUser.UserType);
                    cmd.Parameters.AddWithValue("@UserID", appUser.UserID);
                    cmd.Parameters.AddWithValue("@UserName", appUser.UserName);
                    cmd.Parameters.AddWithValue("@UserPW", appUser.UserPW);
                    cmd.Parameters.AddWithValue("@Ext", appUser.Ext);
                    cmd.Parameters.AddWithValue("@SearchRest", appUser.SearchRest);
                    cmd.Parameters.AddWithValue("@UserEmail", appUser.UserID);
                    cmd.Parameters.AddWithValue("@IdentityNumber", appUser.IdentityNumber);
                    cmd.Parameters.AddWithValue("@UserPhone", appUser.UserPhone);
                    cmd.Parameters.AddWithValue("@UserFax", appUser.UserFax);
                    cmd.Parameters.AddWithValue("@JoinBeginDate", appUser.JoinBeginDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@JoinEndDate", appUser.JoinEndDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@Descr", appUser.Descr);
                    cmd.Parameters.AddWithValue("@POD", appUser.POD);
                    cmd.Parameters.AddWithValue("@EOD", appUser.EOD);
                    cmd.Parameters.AddWithValue("@Pause", appUser.Pause);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateAppUserAccount", _tenantId));
                    cmd.ExecuteScalar();
                    //appUser.UserNum = appUserId;
                    if (appUsers == null) appUsers = new List<User>();
                    appUsers.Add(appUser);
                }

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_PASSWORD_CHANGE;
                    cmd.Parameters.AddWithValue("@UserID", appUser.UserID);
                    cmd.Parameters.AddWithValue("@UserPW", appUser.UserPW);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateAppUserAccount", _tenantId));
                    cmd.ExecuteScalar();
                    //sp_inquire_password_change
                }
            }
            catch (Exception ex) { throw ex; }
            return appUsers;
        }
        public int UpdateQBUserInfo(int userNum, int qbUserNum, bool existsOnQB)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.QBUSER_UPDATE;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@QBUserNum", qbUserNum);
                    cmd.Parameters.AddWithValue("@ExistsOnQB", existsOnQB);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateQBUserInfo", _tenantId));
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public bool CheckUserAlreadyExistsAndActive(string email)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "SELECT * FROM t_Account where (Status = 1) AND (UserId = @email)";
                cmd.Parameters.AddWithValue("@email", email);
                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CheckUserAlreadyExistsAndActive", _tenantId));
                using (var reader = cmd.ExecuteReader())
                {
                    return reader.HasRows;
                }
            }
        }
        public bool IsInviteeActiveAndLiveMonitorPermitted(string email)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.UserManagement.INVITEE_VALIDATION;
                cmd.Parameters.AddWithValue("@emailId", email);

                SqlParameter returnParameter = cmd.Parameters.Add("RetVal", SqlDbType.Int);
                returnParameter.Direction = ParameterDirection.ReturnValue;

                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "IsInviteeActiveAndLiveMonitorPermitted", _tenantId));
                cmd.ExecuteNonQuery();

                int retVal = (int)returnParameter.Value; ;
                if (retVal == 1)
                    return true;
                else
                    return false;
            }
        }
        public List<User> UpdateAppUserAccount(Recorder recorder, User appUser, bool updateUserPwd) //TODO
        {
            List<User> appUsers = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_UPDATE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@UserNum", appUser.UserNum);
                    cmd.Parameters.AddWithValue("@UserType", appUser.UserType);
                    cmd.Parameters.AddWithValue("@UserID", appUser.UserID);
                    cmd.Parameters.AddWithValue("@UserName", appUser.UserName);
                    cmd.Parameters.AddWithValue("@UserPW", (updateUserPwd ? appUser.UserPW : ""));
                    cmd.Parameters.AddWithValue("@Ext", appUser.Ext);
                    cmd.Parameters.AddWithValue("@SearchRest", appUser.SearchRest);
                    cmd.Parameters.AddWithValue("@UserEmail", appUser.UserID);
                    cmd.Parameters.AddWithValue("@IdentityNumber", appUser.IdentityNumber);
                    cmd.Parameters.AddWithValue("@UserPhone", appUser.UserPhone);
                    cmd.Parameters.AddWithValue("@UserFax", appUser.UserFax);
                    cmd.Parameters.AddWithValue("@JoinBeginDate", appUser.JoinBeginDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@JoinEndDate", appUser.JoinEndDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@Descr", appUser.Descr);
                    cmd.Parameters.AddWithValue("@POD", appUser.POD);
                    cmd.Parameters.AddWithValue("@EOD", appUser.EOD);
                    cmd.Parameters.AddWithValue("@Pause", appUser.Pause);
                    cmd.Parameters.AddWithValue("@IsEnterpriseUser", appUser.IsEnterpriseUser);
                    cmd.Parameters.AddWithValue("@HasShiftRest", appUser.HasShiftRest);
                    cmd.Parameters.AddWithValue("@RoleId", appUser.RoleId);
                    cmd.Parameters.AddWithValue("@Is2FAEnabled", appUser.Is2FAEnabled);

                    cmd.Parameters.AddWithValue("@dob", appUser.DOB);
                    cmd.Parameters.AddWithValue("@city", appUser.City);
                    cmd.Parameters.AddWithValue("@state", appUser.State);
                    cmd.Parameters.AddWithValue("@ssn", appUser.SocialSecurityNumber);

                    //cmd.Parameters.AddWithValue("@UpdatePwd", updateUserPwd);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateAppUserAccount", _tenantId));
                    cmd.ExecuteScalar();
                    //appUser.UserNum = appUserId;
                    if (appUsers == null) appUsers = new List<User>();
                    appUsers.Add(appUser);
                    if (appUser.Ext.StartsWith("2"))
                    {
                        using (var UpdateExtcmd = conn.CreateCommand())
                        {
                            UpdateExtcmd.Transaction = tran;
                            UpdateExtcmd.CommandType = CommandType.Text;
                            UpdateExtcmd.CommandText = "UPDATE t_ExtInfo SET ExtName = @ExtName WHERE Ext = @ExtNum";
                            UpdateExtcmd.Parameters.AddWithValue("@ExtName", appUser.UserName);
                            UpdateExtcmd.Parameters.AddWithValue("@ExtNum", appUser.Ext);
                            int rowaffected = 0;
                            rowaffected = UpdateExtcmd.ExecuteNonQuery();
                        }
                    }

                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "UpdateAppUserAccount", JsonConvert.SerializeObject(appUser)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "UpdateAppUserAccount", JsonConvert.SerializeObject(appUser));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }

                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_PASSWORD_CHANGE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@UserID", appUser.UserID);
                    cmd.Parameters.AddWithValue("@UserPW", appUser.UserPW);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateAppUserAccount", _tenantId));
                    cmd.ExecuteScalar();
                    //sp_inquire_password_change
                    tran.Commit();
                }

            }
            catch (Exception ex) { throw ex; }
            return appUsers;
        }

        public UserManagementResponse GetAppUserProfile(long appUserId, long uId = 0)
        {
            List<AppUser> aUsers = null;
            List<UserProfile> userProfiles = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USERPROFILE_GETBYAPPUSERID;
                    cmd.Parameters.AddWithValue("@AppUserId", appUserId);
                    cmd.Parameters.AddWithValue("@UserId", uId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetAppUserProfile", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        aUsers = ORMapper.MapAppUsers(dr);
                        dr.NextResult();
                        userProfiles = ORMapper.MapUserProfiles(dr);
                    }
                    if (aUsers != null)
                    {
                        aUsers.ForEach(u => u.UserProfile = userProfiles.FirstOrDefault(o => o.AppUserId == u.Id));
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new UserManagementResponse { AppUsers = aUsers, };
        }

        public List<AppUser> CreateAppUserProfile(AppUser appUser, int uId = 1000)
        {
            List<AppUser> appUsers = null;
            try
            {

                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USERPROFILE_INSERT;
                    cmd.Parameters.AddWithValue("@AppUserId", appUser.UserProfile.AppUserId);
                    cmd.Parameters.AddWithValue("@FirstName", appUser.UserProfile.FirstName);
                    cmd.Parameters.AddWithValue("@LastName", appUser.UserProfile.LastName);
                    cmd.Parameters.AddWithValue("@IdentityNumber", appUser.UserProfile.IdentityNumber);
                    cmd.Parameters.AddWithValue("@Description", appUser.UserProfile.Description);
                    cmd.Parameters.AddWithValue("@SocialSecurityNumber", appUser.UserProfile.SocialSecurityNumber);
                    cmd.Parameters.AddWithValue("@JoinBeginDate", appUser.UserProfile.JoinBeginDate);
                    cmd.Parameters.AddWithValue("@JoinEndDate", appUser.UserProfile.JoinEndDate == DateTime.MinValue ? Convert.DBNull : appUser.UserProfile.JoinEndDate);
                    cmd.Parameters.AddWithValue("@DateOfBirth", appUser.UserProfile.DateOfBirth);
                    cmd.Parameters.AddWithValue("@Gender", appUser.UserProfile.Gender);
                    cmd.Parameters.AddWithValue("@EmailDefault", appUser.UserProfile.EmailDefault);
                    cmd.Parameters.AddWithValue("@EmailsOther", appUser.UserProfile.EmailsOther);
                    cmd.Parameters.AddWithValue("@UserMobile", appUser.UserProfile.UserMobile);
                    cmd.Parameters.AddWithValue("@UserPhone", appUser.UserProfile.UserPhone);
                    cmd.Parameters.AddWithValue("@UserFax", appUser.UserProfile.UserFax);
                    cmd.Parameters.AddWithValue("@IsCompleted", appUser.UserProfile.IsCompleted);
                    cmd.Parameters.AddWithValue("@UserId", uId);
                    cmd.Parameters.AddWithValue("@IsDeleted", appUser.UserProfile.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CreateAppUserProfile", _tenantId));
                    int uProfileId = (int)cmd.ExecuteScalar();
                    appUser.UserProfile.Id = uProfileId;
                    if (appUsers == null) appUsers = new List<AppUser>();
                    appUsers.Add(appUser);
                }
            }
            catch (Exception ex) { throw ex; }
            return appUsers;
        }

        public List<AppUser> UpdateAppUserProfile(AppUser appUser, int uId = 1000) //TODO
        {
            List<AppUser> appUsers = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USERPROFILE_UPDATE;
                    cmd.Parameters.AddWithValue("@AppUserId", appUser.UserProfile.AppUserId);
                    cmd.Parameters.AddWithValue("@UserProfileId", appUser.UserProfile.Id);
                    cmd.Parameters.AddWithValue("@FirstName", appUser.UserProfile.FirstName);
                    cmd.Parameters.AddWithValue("@LastName", appUser.UserProfile.LastName);
                    cmd.Parameters.AddWithValue("@IdentityNumber", appUser.UserProfile.IdentityNumber);
                    cmd.Parameters.AddWithValue("@Description", appUser.UserProfile.Description);
                    cmd.Parameters.AddWithValue("@SocialSecurityNumber", appUser.UserProfile.SocialSecurityNumber);
                    cmd.Parameters.AddWithValue("@JoinBeginDate", appUser.UserProfile.JoinBeginDate);
                    cmd.Parameters.AddWithValue("@JoinEndDate", appUser.UserProfile.JoinEndDate == DateTime.MinValue ? Convert.DBNull : appUser.UserProfile.JoinEndDate);
                    cmd.Parameters.AddWithValue("@DateOfBirth", appUser.UserProfile.DateOfBirth);
                    cmd.Parameters.AddWithValue("@Gender", appUser.UserProfile.Gender);
                    cmd.Parameters.AddWithValue("@EmailDefault", appUser.UserProfile.EmailDefault);
                    cmd.Parameters.AddWithValue("@EmailsOther", appUser.UserProfile.EmailsOther);
                    cmd.Parameters.AddWithValue("@UserMobile", appUser.UserProfile.UserMobile);
                    cmd.Parameters.AddWithValue("@UserPhone", appUser.UserProfile.UserPhone);
                    cmd.Parameters.AddWithValue("@UserFax", appUser.UserProfile.UserFax);
                    cmd.Parameters.AddWithValue("@IsCompleted", appUser.UserProfile.IsCompleted);
                    cmd.Parameters.AddWithValue("@UserId", uId);
                    cmd.Parameters.AddWithValue("@IsDeleted", appUser.UserProfile.IsDeleted);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateAppUserProfile", _tenantId));

                    int uProfileId = (int)cmd.ExecuteScalar();
                    appUser.UserProfile.Id = uProfileId;
                    if (appUsers == null) appUsers = new List<AppUser>();
                    appUsers.Add(appUser);
                }
            }
            catch (Exception ex) { throw ex; }
            return appUsers;
        }

        public UserManagementResponse DeleteAppUserAccount(int id, int userId = 1000) //TODO:
        {
            UserManagementResponse aUsers = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_DELETE;
                    cmd.Parameters.AddWithValue("@userNum_STR", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "DeleteAppUserAccount", _tenantId));

                    cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
            return aUsers;
        }
        public UserManagementResponse DeleteAppUserAccount(Recorder recorder, int id, int RevSyncServerUserNum, int userId = 1000) //TODO:
        {
            UserManagementResponse aUsers = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_DELETE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@userNum_STR", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "DeleteAppUserAccount", _tenantId));

                    cmd.ExecuteNonQuery();

                    if (SiteConfig.RevSyncEnabled && RevSyncServerUserNum > 0)
                    {
                        try
                        {
                            bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "DeleteAppUserAccount", JsonConvert.SerializeObject(id)));
                            if (bReturn) tran.Commit(); else tran.Rollback();
                        }
                        catch (Exception ex)
                        {
                            tran.Rollback();
                            throw ex;
                        }
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "DeleteAppUserAccount", JsonConvert.SerializeObject(id));

                        if (isSent)
                            tran.Commit();
                        else
                            tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return aUsers;
        }

        public int PermanentRemoveAppUserAccount(int userNum)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_PERMANENT_REMOVE;
                    cmd.Parameters.AddWithValue("@userNum", userNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "PermanentRemoveAppUserAccount", _tenantId));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public UserManagementResponse RecoverAppUserAccount(string userIds, int userId = 1000)
        {
            UserManagementResponse aUsers = null;
            try
            {
                userIds = userIds.Replace("\"", "").Replace("'", "");
                string[] s_useridslist = userIds.Split(',');
                int i_recovered_users = 0;
                for (int i = 0; i < s_useridslist.Count(); i++)
                {
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        conn.Open();
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.UserManagement.APPUSER_RECOVER;
                        cmd.Parameters.AddWithValue("@userNum_STR", s_useridslist[i]);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "RecoverAppUserAccount", _tenantId));

                        cmd.ExecuteNonQuery();
                        i_recovered_users++;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return aUsers;
        }
        public int RecoverEnterpriseUserAccount(string userIds, int userId = 1000)
        {
            try
            {
                userIds = userIds.Replace("\"", "").Replace("'", "");
                string[] s_useridslist = userIds.Split(',');
                int i_recovered_users = 0;
                for (int i = 0; i < s_useridslist.Count(); i++)
                {
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        conn.Open();
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.UserManagement.APPUSER_RECOVER;
                        cmd.Parameters.AddWithValue("@userNum_STR", s_useridslist[i]);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "RecoverEnterpriseUserAccount", _tenantId));

                        cmd.ExecuteNonQuery();
                        i_recovered_users++;
                    }
                }
                return i_recovered_users;
            }
            catch (Exception ex) { throw ex; }
        }
        public int RecoverAppUserAccount(Recorder recorder, string userIds)
        {
            try
            {
                userIds = userIds.Replace("\"", "").Replace("'", "");
                string[] s_useridslist = userIds.Split(',');
                int i_recovered_users = 0;
                for (int i = 0; i < s_useridslist.Count(); i++)
                {
                    using (var conn = new SqlConnection(recorder.ConnectionString))
                    using (var cmd = conn.CreateCommand())
                    {
                        conn.Open();
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.UserManagement.APPUSER_RECOVER;
                        cmd.Parameters.AddWithValue("@userNum_STR", s_useridslist[i]);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "RecoverAppUserAccount", _tenantId));

                        cmd.ExecuteNonQuery();
                        i_recovered_users++;
                    }
                }
                return i_recovered_users;
            }
            catch (Exception ex) { throw ex; }
        }

        public int InquireRecoverAppUserAccount(Recorder recorder, string userIds)
        {
            try
            {
                userIds = userIds.Replace("\"", "").Replace("'", "");
                string[] s_useridslist = userIds.Split(',');
                int i_recovered_users = 0;
                for (int i = 0; i < s_useridslist.Count(); i++)
                {
                    using (var conn = new SqlConnection(recorder.ConnectionString))
                    using (var cmd = conn.CreateCommand())
                    {
                        conn.Open();
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.UserManagement.APPUSER_RECOVER;
                        cmd.Parameters.AddWithValue("@userNum_STR", s_useridslist[i]);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "InquireRecoverAppUserAccount", _tenantId));

                        cmd.ExecuteNonQuery();
                        i_recovered_users++;
                    }
                }
                return i_recovered_users;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool SaveAssignedGroup(long uId, int gId, DateTime? modifiedDate = null)
        {
            if (modifiedDate == null)
                modifiedDate = DateTime.Now;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GROUP_ASSIGN;
                    cmd.Parameters.AddWithValue("@UserNum", uId);
                    cmd.Parameters.AddWithValue("@GroupNum", gId);
                    //cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "SaveAssignedGroup", _tenantId));

                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }

        }
        public bool SaveAssignedGroup(Recorder recorder, long uId, int gId, int RevSyncUID, DateTime? modifiedDate = null)
        {
            if (modifiedDate == null)
                modifiedDate = DateTime.Now;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GROUP_ASSIGN;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@UserNum", uId);
                    cmd.Parameters.AddWithValue("@GroupNum", gId);
                    //cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "SaveAssignedGroup", _tenantId));

                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("uId", Convert.ToString(uId));
                        _dic.Add("gId", Convert.ToString(gId));
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "SaveAssignedGroup", JsonConvert.SerializeObject(_dic)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("uId", Convert.ToString(uId));
                        _dic.Add("gId", Convert.ToString(gId));
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "SaveAssignedGroup", JsonConvert.SerializeObject(_dic));

                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool EnableDisableInquireUser(User _user, out int channelType)
        {
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_ENABLE_DISABLE_USER;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@extension", _user.Ext);
                    cmd.Parameters.AddWithValue("@enable_user", _user.EnableDisableInquireUser);
                    cmd.Parameters.AddWithValue("@usernum", _user.UserNum);
                    cmd.Parameters.AddWithValue("@ChannelType", 0);
                    cmd.Parameters["@ChannelType"].Direction = ParameterDirection.Output;

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "EnableDisableInquireUser", _tenantId));


                    cmd.ExecuteNonQuery();
                    if (_user.EnableDisableInquireUser == 0)
                        channelType = Convert.ToInt32(cmd.Parameters["@ChannelType"].Value);
                    else
                        channelType = 0;

                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "EnableDisableInquireUser", JsonConvert.SerializeObject(_user)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "EnableDisableInquireUser", JsonConvert.SerializeObject(_user));

                        if (isSent)
                            tran.Commit();
                        else
                            tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                channelType = 0;
                return false;
                throw ex;
            }
        }

        public bool EnableDisableAvrisView(User _user)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_ENABLE_DISABLE_AVRIS_VIEW;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@extension", _user.Ext);
                    cmd.Parameters.AddWithValue("@enable_avrisview", _user.IsAvrisView);
                    cmd.Parameters.AddWithValue("@usernum", _user.UserNum);

                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "EnableDisableAvrisView", JsonConvert.SerializeObject(_user)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "EnableDisableAvrisView", JsonConvert.SerializeObject(_user));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else
                        tran.Commit();

                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
                throw ex;
            }
        }

        public bool EnableDisableIQ3View(User _user)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_ENABLE_DISABLE_IQ3_VIEW;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@extension", _user.Ext);
                    cmd.Parameters.AddWithValue("@enable_iq3view", _user.IsIQ3View);
                    cmd.Parameters.AddWithValue("@usernum", _user.UserNum);
                    cmd.ExecuteNonQuery();

                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "EnableDisableIQ3View", JsonConvert.SerializeObject(_user)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "EnableDisableIQ3View", JsonConvert.SerializeObject(_user));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
                throw ex;
            }
        }

        public bool EnableDisableRevCell(User _user)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.RevCell_Enable_Disable;
                    cmd.Parameters.AddWithValue("@extension", _user.Ext);
                    cmd.Parameters.AddWithValue("@isRevCell", _user.IsRevCell);
                    cmd.Parameters.AddWithValue("@userNum", _user.UserNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "EnableDisableRevCell", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool ManageRevcellPermission(User _user)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.RevCell_MANAGE_PERMISSIONS;
                    cmd.Parameters.AddWithValue("@Extension", _user.Ext);
                    cmd.Parameters.AddWithValue("@UserNum", _user.UserNum);
                    cmd.Parameters.AddWithValue("@EnableFlag", _user.IsRevCell);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "ManageRevcellPermission", _tenantId));
                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool HasRevcellForCurrentServer(string userEmail)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_RevRec_Credentials where (UserName = @UserEmail)";
                    //cmd.CommandText = "SELECT * FROM t_RevRec_Credentials rc LEFT JOIN t_Account acc on acc.UserId = rc.username  where (rc.UserName = @UserEmail) and acc.IsRevCell = 1";
                    cmd.Parameters.AddWithValue("@UserEmail", userEmail);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "HasRevcellForCurrentServer", _tenantId));

                    using (var reader = cmd.ExecuteReader())
                    {
                        return reader.HasRows;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool HasActiveRevcellLicense(string userEmail)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from t_DeviceUsersLic where DeviceTypeId = 2 AND EmailId = @UserEmail";
                    cmd.Parameters.AddWithValue("@UserEmail", userEmail);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "HasActiveRevcellLicense", _tenantId));

                    using (var reader = cmd.ExecuteReader())
                    {
                        return reader.HasRows;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool HasActiveIQ3License(string userEmail)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from t_DeviceUsersLic where DeviceTypeId = 1 AND EmailId = @UserEmail";
                    cmd.Parameters.AddWithValue("@UserEmail", userEmail);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "HasActiveIQ3License", _tenantId));

                    using (var reader = cmd.ExecuteReader())
                    {
                        return reader.HasRows;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool ManageIQ3Permission(User _user)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.IQ3_MANAGE_PERMISSIONS;
                    cmd.Parameters.AddWithValue("@Extension", _user.Ext);
                    cmd.Parameters.AddWithValue("@UserNum", _user.UserNum);
                    cmd.Parameters.AddWithValue("@EnableFlag", (_user.EnableDisableInquireUser == 1 ? true : false));
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "ManageRevcellPermission", _tenantId));
                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool IsDeviceUser(string userEmail)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = "SELECT * FROM t_RevRec_Credentials where (UserName = @UserEmail)";
                    cmd.CommandText = "SELECT * FROM t_Account where (UserID = @UserEmail) and IsDeviceUser = 1;";
                    cmd.Parameters.AddWithValue("@UserEmail", userEmail);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "IsDeviceUser", _tenantId));

                    using (var reader = cmd.ExecuteReader())
                    {
                        return reader.HasRows;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<UserExtensionInfo> GetUserExtensionInfos(int recId)
        {
            UserExtensionInfo userExtensionInfo = null;
            List<UserExtensionInfo> userExtensionInfos = new List<UserExtensionInfo>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_EXTENSION_INFO_GET;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetUserExtensionInfos", _tenantId));

                    using (var dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                userExtensionInfo = new UserExtensionInfo();
                                userExtensionInfo.Id = Convert.ToInt32(dr["Id"]);
                                userExtensionInfo.UserNum = Convert.ToInt32(dr["UserNum"]);
                                userExtensionInfo.UserID = Convert.ToString(dr["UserID"]);
                                userExtensionInfo.UserName = Convert.ToString(dr["UserName"]);
                                userExtensionInfo.Ext = Convert.ToInt32(dr["Ext"]);
                                userExtensionInfo.ExtName = Convert.ToString(dr["ExtName"]);
                                userExtensionInfo.ChannelType = Convert.ToInt32(dr["ChannelType"]);
                                userExtensionInfo.IsRevcell = Convert.ToBoolean(dr["IsRevcell"]);
                                userExtensionInfo.RecId = recId;

                                userExtensionInfos.Add(userExtensionInfo);
                            }
                        }
                    }
                }
                return userExtensionInfos;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool SaveUserGroup(UserGroup uGroup)
        {
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_USERGROUP_SAVE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@GroupNum", uGroup.GroupNum);
                    cmd.Parameters.AddWithValue("@UserNum", uGroup.UserNum);
                    cmd.Parameters.AddWithValue("@AssignAuth", uGroup.AssignAuth);
                    cmd.Parameters.AddWithValue("@Descr", uGroup.Descr == null ? Convert.DBNull : uGroup.Descr);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "SaveUserGroup", _tenantId));

                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "SaveUserGroup", JsonConvert.SerializeObject(uGroup)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "SaveUserGroup", JsonConvert.SerializeObject(uGroup));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool SaveUserGroup(Recorder recorder, UserGroup uGroup)
        {
            try
            {

                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_USERGROUP_SAVE;
                    cmd.Parameters.AddWithValue("@GroupNum", uGroup.GroupNum);
                    cmd.Parameters.AddWithValue("@UserNum", uGroup.UserNum);
                    cmd.Parameters.AddWithValue("@AssignAuth", uGroup.AssignAuth);
                    cmd.Parameters.AddWithValue("@Descr", uGroup.Descr == null ? Convert.DBNull : uGroup.Descr);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "SaveUserGroup", _tenantId));

                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool DeleteUserGroup(UserGroup uGroup)
        {
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_USERGROUP_DELETE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@GroupNum", uGroup.GroupNum);
                    cmd.Parameters.AddWithValue("@UserNum", uGroup.UserNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "DeleteUserGroup", _tenantId));

                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "DeleteUserGroup", JsonConvert.SerializeObject(uGroup)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "DeleteUserGroup", JsonConvert.SerializeObject(uGroup));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool DeleteUserGroup(Recorder recorder, UserGroup uGroup)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_USERGROUP_DELETE;
                    cmd.Parameters.AddWithValue("@GroupNum", uGroup.GroupNum);
                    cmd.Parameters.AddWithValue("@UserNum", uGroup.UserNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "DeleteUserGroup", _tenantId));

                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool SaveAssignedPermissions(long uId, List<Permission> permissions, DateTime? modifiedDate = null)
        {
            if (modifiedDate == null)
                modifiedDate = DateTime.Now;
            XElement xPermissions = this.CreatePermissionsXML(uId, permissions);

            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_PERMISSIONS_ASSIGN;
                    cmd.Parameters.AddWithValue("@AppUserId", uId);
                    cmd.Parameters.AddWithValue("@Permissions", xPermissions.ToString());
                    cmd.Parameters.AddWithValue("@ModifiedDate", modifiedDate);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "SaveAssignedPermissions", _tenantId));

                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Utility Methods

        private List<Permission> GetPermissions(int uNum, UserGroup uGroup, List<Permission> permissions)
        {
            List<Permission> uPermissions = new List<Permission>();
            Permission uPermission = null;
            if (uGroup == null)
                return new List<Permission>();
            foreach (var p in permissions)
            {
                if (Convert.ToInt32(uGroup.AssignAuth.Substring(p.Id, 1)) == 1)
                {
                    uPermission = (Permission)p.Clone();
                    uPermission.UserNum = uNum;
                    uPermissions.Add(uPermission);
                }

            }
            return uPermissions;

        }

        private static List<Permission> GetEnterprisePermissions(int uNum, UserGroup uGroup, List<Permission> permissions)
        {
            List<Permission> uPermissions = new List<Permission>();
            Permission uEntPermission = null;
            if (uGroup == null)
                return new List<Permission>();
            foreach (var p in permissions)
            {
                if (Convert.ToInt32(uGroup.EnterpriseAssignAuth.Substring(p.Id, 1)) == 1)
                {
                    uEntPermission = (Permission)p.Clone();
                    uEntPermission.UserNum = uNum;
                    uPermissions.Add(uEntPermission);
                }

            }
            return uPermissions;
        }


        private List<Permission> GetPermissions(int uNum, string uAuth, List<Permission> permissions)
        {
            List<Permission> uPermissions = new List<Permission>();
            Permission uPermission = null;
            if (uAuth == null)
                return new List<Permission>();
            foreach (var p in permissions)
            {
                if (Convert.ToInt32(uAuth.Substring(p.Id, 1)) == 1)
                {
                    uPermission = (Permission)p.Clone();
                    uPermission.UserNum = uNum;
                    uPermissions.Add(uPermission);
                }

            }
            return uPermissions;
        }

        private XElement CreatePermissionsXML(long uId, List<Permission> permissions)
        {
            XElement userPermissions = new XElement("UserPermissions");
            foreach (var permission in permissions)
            {
                userPermissions.Add(new XElement("UserPermission",
                                new XAttribute("PermissionId", permission.Id),
                                new XAttribute("AppUserId", uId)
                            ));
            }
            return userPermissions;
        }

        #endregion

        public User GetRevcordSupportCredentials()
        {
            User supportUser = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_Account WHERE UserID='<EMAIL>'";
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetRevcordSupportCredentials", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.Read())
                        {
                            supportUser = new User();
                            supportUser.UserID = Convert.ToString(dr["UserID"]);
                            supportUser.UserPW = Convert.ToString(dr["UserPW"]);
                        }
                    }
                }
                return supportUser;
            }
            catch (Exception ex) { throw ex; }
        }

        #region Logedin User

        public User GetMyInformation(string uId, string uPwd, bool IsADUser, int tenantId, bool forcePasswordChange, bool isRoleBasedAccessEnabled, out List<Recorder> recorders, out bool isChainDBsConfigured, out bool isInquireEnabled, out bool isECEnabled, out bool isEnterpriseRecorder, out List<int> sttEnabledChannels, out bool isLocked, out bool isUserExists, out bool isUserAgreedToLicense)
        {
            User loginUser = null;
            UserGroup uGroup = null;
            List<Permission> permissions = null;
            List<RolePermission> rolePermissions = null;
            List<IwbPermission> iwbPermissions = null;

            List<Recorder> recs = null;
            string sAccessRight = null;
            string originalSimpleAccessRight = "**********";
            string sAssignedNodes = null;
            bool isChainDB = false;
            bool isInquire = false;
            List<int> sttChannels = new List<int>();
            isECEnabled = false;
            isEnterpriseRecorder = false;
            bool isUserLocked = false;
            //isUserExists = false;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GET_LOGIN_USER;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@UserID", uId);
                    cmd.Parameters.AddWithValue("@UserPW", uPwd);
                    cmd.Parameters.AddWithValue("@ForcePasswordChange", forcePasswordChange);
                    cmd.Parameters.AddWithValue("@IsUserExists", false);
                    cmd.Parameters.AddWithValue("@IsUserAgreedToLicense", false);
                    cmd.Parameters.AddWithValue("@IsRoleBasedAccessEnabled", isRoleBasedAccessEnabled);
                    cmd.Parameters["@IsUserExists"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@IsUserAgreedToLicense"].Direction = ParameterDirection.Output;
                    if (IsADUser)
                        cmd.Parameters.AddWithValue("@IsADUser", 1);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetMyInformation", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        loginUser = ORMapper.MapLoginUser(dr);
                        dr.NextResult();
                        uGroup = ORMapper.MapLoginUserGroup(dr);
                        dr.NextResult();
                        sAccessRight = ORMapper.MapSimpleAccessRights(dr, out originalSimpleAccessRight);
                        dr.NextResult();
                        sAssignedNodes = ORMapper.MapAssignedNodes(dr);
                        dr.NextResult();
                        permissions = ORMapper.MapPermissions(dr);
                        dr.NextResult();
                        recs = ORMapper.MapRecorders(dr);
                        if (loginUser != null)
                        {
                            loginUser.GroupNum = uGroup == null ? 0 : uGroup.GroupNum;
                            loginUser.UserID = uId;
                            loginUser.UserPW = uPwd;
                            loginUser.UserGroup = uGroup == null ? new UserGroup { UserNum = loginUser.UserNum, AssignAuth = "**********" } : uGroup;
                            loginUser.SimpleAccessRight = sAccessRight;
                            loginUser.OriginalSimpleAccessRight = originalSimpleAccessRight;
                            if (loginUser.UserGroup.AssignAuth == "")
                                loginUser.UserGroup.AssignAuth = "**********";
                            loginUser.Permissions = this.GetPermissions(loginUser.UserNum, loginUser.SelectType == 0 ? loginUser.UserGroup.AssignAuth : loginUser.SimpleAccessRight, permissions);
                            loginUser.AssignedNodes = sAssignedNodes != null ? sAssignedNodes.Split(',').ToList() : new List<string>();
                            loginUser.IsDeviceUser = loginUser.IsDeviceUser;
                        }
                        //Chain DB's Existis ResultSet
                        dr.NextResult();
                        if (dr.Read())//(dr.HasRows)
                        {
                            isChainDB = Convert.ToInt32(dr["DBCount"]) > 1 ? true : false;
                        }
                        // IsInquire
                        dr.NextResult();
                        if (dr.Read())
                            isInquire = Convert.ToBoolean(dr["IsInquire"]);
                        // User Recorder Permissions
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            loginUser.RecorderAccessRights = new List<RecorderAccessRight>();
                            while (dr.Read())
                            {
                                loginUser.RecorderAccessRights.Add(new RecorderAccessRight { RecId = Convert.ToInt32(dr["RecId"]), AccessRight = Convert.ToString(dr["AccessRight"]) });
                            }
                        }
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            List<int> channels = new List<int>();
                            while (dr.Read())
                            {
                                channels.Add(Convert.ToInt32(dr["STTEnabledChannels"]));
                            }
                            sttChannels = channels;
                        }
                        //dr.NextResult();
                        //if (dr.HasRows)
                        //{
                        //    while (dr.Read())
                        //    {
                        //        isECEnabled = Convert.ToBoolean(dr["IsECEnabled"]);
                        //        isEnterpriseRecorder = Convert.ToBoolean(dr["IsEnterpriseRecorder"]);
                        //    }
                        //}

                        //Last Password Change. ResultSet
                        dr.NextResult();
                        if (dr.Read())
                        {
                            loginUser.LastPasswordChanged = Convert.ToDateTime(dr["DateChanged"]);
                            //loginUser.LastPasswordChanged = dr["DateChanged"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(dr["DateChanged"]);
                        }

                        //Is User LockedOut. ResultSet
                        dr.NextResult();
                        if (dr.Read())
                        {
                            isUserLocked = Convert.ToBoolean(dr["IsLockedOut"]);
                        }

                        // User Role and RolePermissions
                        dr.NextResult();
                        rolePermissions = ORMapper.MapRolePermissions(dr);
                        if (loginUser != null)
                        {
                            loginUser.RolePermissions = new List<RolePermission>();
                            loginUser.RolePermissions = rolePermissions;
                        }

                        dr.NextResult();
                        if (dr.Read())
                        {
                            if (loginUser != null)
                            {
                                loginUser.Role = new Role();
                                loginUser.Role.Id = Convert.ToInt32(dr["Id"]);
                                loginUser.Role.Name = Convert.ToString(dr["Name"]);
                                loginUser.Role.RoleType = Convert.ToInt32(dr["RoleType"]);
                                loginUser.Role.IsSystemRole = Convert.ToBoolean(dr["IsSystemRole"]);
                                loginUser.Role.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            }
                        }
                        if (loginUser != null)
                        {
                            //iwbRole
                            dr.NextResult();
                            if (dr.Read())
                            {
                                loginUser.IwbRole = new IwbRole();
                                loginUser.IwbRole.Id = Convert.ToInt32(dr["Id"]);
                                loginUser.IwbRole.Name = Convert.ToString(dr["Name"]);
                                //loginUser.IwbRole.Modules = new List<IwbModule>();
                            }
                            //iwbRoleModule
                            dr.NextResult();
                            if (dr.HasRows)
                            {
                                loginUser.IwbRole.Modules = new List<IwbModule>();
                                while (dr.Read())
                                {
                                    var module = new IwbModule
                                    {
                                        Id = Convert.ToInt32(dr["ModuleId"]),
                                        Name = Convert.ToString(dr["Name"]),
                                        URL = Convert.ToString(dr["Url"]),
                                        Permissions = new List<IwbPermission>(),
                                    };

                                    loginUser.IwbRole.Modules.Add(module);
                                }
                            }
                            //iwbModulePermission
                            dr.NextResult();
                            if (dr.HasRows)
                            {
                                iwbPermissions = new List<IwbPermission>();
                                while (dr.Read())
                                {
                                    var permission = new IwbPermission
                                    {
                                        Id = Convert.ToInt32(dr["Id"]),
                                        Name = Convert.ToString(dr["Name"]),
                                        CssClassName = Convert.ToString(dr["CssClassName"]),
                                        ModuleId = Convert.ToInt32(dr["ModuleId"]),
                                    };

                                    iwbPermissions.Add(permission);
                                }
                            }
                            loginUser.IwbRole.Modules.ForEach(m => m.Permissions = iwbPermissions.Where(p => p.ModuleId == m.Id).ToList());
                        }

                    }
                    isUserExists = Convert.ToBoolean(cmd.Parameters["@IsUserExists"].Value);
                    isUserAgreedToLicense = Convert.ToBoolean(cmd.Parameters["@IsUserAgreedToLicense"].Value);
                }
            }
            catch (SqlException sqe)
            {
                throw sqe;
            }
            catch (Exception ex) { throw ex; }
            recorders = recs;
            isChainDBsConfigured = isChainDB;
            isInquireEnabled = isInquire;
            sttEnabledChannels = sttChannels;
            isLocked = isUserLocked;
            return loginUser;
        }
        #endregion

        public List<Recorder> GetRecorderInfo()
        {
            List<Recorder> recorders = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GET_RECORDER_INFO;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetRecorderInfo", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        recorders = ORMapper.MapRecorders(dr);
                    }
                }
            }
            catch (SqlException sqe)
            {
                throw sqe;
            }

            return recorders;
        }

        #region Tree

        public List<TreeViewDataDTO> GetTreeView(int userNum, string userId, int authNum, string authType, int type, bool getOnlyAudioChannels)
        {
            //EXEC sp_Init_Tree @UserNum = 1000,  @AuthNum = 4,  @AuthType = '1', @Type = 4
            List<TreeViewDataDTO> treeViewDataDTOs = null;
            TreeViewDataDTO TreeViewDataDTO = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = getOnlyAudioChannels ? DBConstants.UserManagement.TREEVIEW_GET : DBConstants.UserManagement.TREEVIEW_GET_WRAPPER;

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetTreeView", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (treeViewDataDTOs == null) treeViewDataDTOs = new List<TreeViewDataDTO>();
                        while (dr.Read())
                        {
                            TreeViewDataDTO = new TreeViewDataDTO();

                            TreeViewDataDTO.NodeId = Convert.ToString(dr["NodeId"]);
                            TreeViewDataDTO.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                            TreeViewDataDTO.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                            TreeViewDataDTO.Depth = Convert.ToInt32(dr["Depth"]);
                            TreeViewDataDTO.MenuType = Convert.ToInt32(dr["MenuType"]);
                            TreeViewDataDTO.ViewType = Convert.ToInt32(dr["ViewType"]);
                            TreeViewDataDTO.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                            TreeViewDataDTO.Param1 = Convert.ToString(dr["Param1"]);
                            TreeViewDataDTO.Param2 = Convert.ToString(dr["Param2"]);
                            TreeViewDataDTO.Param3 = Convert.ToString(dr["Param3"]);

                            treeViewDataDTOs.Add(TreeViewDataDTO);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeViewDataDTOs;
        }


        public List<TreeViewDataDTO> GetTreeView(int userNum, string userId, int authNum, string authType, int type, bool getOnlyAudioChannels, int selectType)
        {
            //EXEC sp_Init_Tree @UserNum = 1000,  @AuthNum = 4,  @AuthType = '1', @Type = 4
            List<TreeViewDataDTO> treeViewDataDTOs = null;
            TreeViewDataDTO TreeViewDataDTO = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    if (!getOnlyAudioChannels)
                    {
                        cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_WRAPPER;
                        cmd.Parameters.AddWithValue("@SelectType", selectType);
                    }
                    else
                    {
                        cmd.CommandText = selectType == 0 ? DBConstants.UserManagement.TREEVIEW_GET : DBConstants.UserManagement.TREEVIEW_SIMPLE_USER_RIGHTS_GET;

                    }

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetTreeView", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (treeViewDataDTOs == null) treeViewDataDTOs = new List<TreeViewDataDTO>();
                        while (dr.Read())
                        {
                            TreeViewDataDTO = new TreeViewDataDTO();

                            TreeViewDataDTO.NodeId = Convert.ToString(dr["NodeId"]);
                            TreeViewDataDTO.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                            TreeViewDataDTO.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                            //TreeViewDataDTO.Depth = Convert.ToInt32(dr["Depth"]);
                            TreeViewDataDTO.Depth = dr["Depth"] == DBNull.Value ? 0 : Convert.ToInt32(dr["Depth"]);
                            TreeViewDataDTO.MenuType = Convert.ToInt32(dr["MenuType"]);
                            TreeViewDataDTO.ViewType = Convert.ToInt32(dr["ViewType"]);
                            TreeViewDataDTO.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                            TreeViewDataDTO.Param1 = Convert.ToString(dr["Param1"]);
                            TreeViewDataDTO.Param2 = Convert.ToString(dr["Param2"]);
                            TreeViewDataDTO.Param3 = Convert.ToString(dr["Param3"]);

                            treeViewDataDTOs.Add(TreeViewDataDTO);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeViewDataDTOs;
        }
        public List<TreeViewDataDTO> GetTreeViewFromRecorder(Recorder recorder, int userNum, string userId, int authNum, string authType, int type, bool getOnlyAudioChannels, int selectType)
        {
            List<TreeViewDataDTO> treeViewDataDTOs = null;
            TreeViewDataDTO TreeViewDataDTO = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    if (!getOnlyAudioChannels)
                    {
                        cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_WRAPPER;
                        cmd.Parameters.AddWithValue("@SelectType", selectType);
                    }
                    else
                    {
                        cmd.CommandText = selectType == 0 ? DBConstants.UserManagement.TREEVIEW_GET : DBConstants.UserManagement.TREEVIEW_SIMPLE_USER_RIGHTS_GET;

                    }

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetTreeViewFromRecorder", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (treeViewDataDTOs == null) treeViewDataDTOs = new List<TreeViewDataDTO>();
                        while (dr.Read())
                        {
                            TreeViewDataDTO = new TreeViewDataDTO();

                            TreeViewDataDTO.NodeId = Convert.ToString(dr["NodeId"]);
                            TreeViewDataDTO.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                            TreeViewDataDTO.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                            //TreeViewDataDTO.Depth = Convert.ToInt32(dr["Depth"]);
                            TreeViewDataDTO.Depth = dr["Depth"] == DBNull.Value ? 0 : Convert.ToInt32(dr["Depth"]);
                            TreeViewDataDTO.MenuType = Convert.ToInt32(dr["MenuType"]);
                            TreeViewDataDTO.ViewType = Convert.ToInt32(dr["ViewType"]);
                            TreeViewDataDTO.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                            TreeViewDataDTO.Param1 = Convert.ToString(dr["Param1"]);
                            TreeViewDataDTO.Param2 = Convert.ToString(dr["Param2"]);
                            TreeViewDataDTO.Param3 = Convert.ToString(dr["Param3"]);

                            treeViewDataDTOs.Add(TreeViewDataDTO);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeViewDataDTOs;
        }

        #endregion

        public bool SaveUserImage(long userNum, string UserImage, int RevSyncServerUserNum)
        {

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_UPDATE_USERIMAGE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserPic", UserImage);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "SaveUserImage", _tenantId));

                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("UserNum", Convert.ToString(userNum));
                        _dic.Add("UserImage", Convert.ToString(UserImage));

                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "SaveUserImage", JsonConvert.SerializeObject(_dic)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("UserNum", Convert.ToString(userNum));
                        _dic.Add("UserImage", Convert.ToString(UserImage));

                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "SaveUserImage", JsonConvert.SerializeObject(_dic));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }

        }

        public int RemoveUserImage(long userNum, int revsyncServerUserNum)
        {

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE t_Account SET UserPic = '' WHERE UserNum = @UserNum";
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "RemoveUserImage", _tenantId));

                    int rowaffected = 0;
                    rowaffected = cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool isSent = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "RemoveUserImage", JsonConvert.SerializeObject(userNum)));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "RemoveUserImage", JsonConvert.SerializeObject(userNum));

                        if (isSent)
                            tran.Commit();
                        else
                            tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                    return rowaffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }


        public bool UpdateRootNode(int nodeId, string nodeText)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GROUP_NODE_UPDATE;
                    cmd.Parameters.AddWithValue("@NodeId", nodeId);
                    cmd.Parameters.AddWithValue("@NodeText", nodeText);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateRootNode", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0;
                }
            }
            catch (Exception ex) { throw ex; }
        }


        #region Invitation
        public bool CheckAlreadyInvited(string email, InvitationStatus status)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "SELECT * FROM umInvitation where (SentToEmail = @email) AND (StatusId = @status) AND (IsDeleted = 0)";
                cmd.Parameters.AddWithValue("@email", email);
                cmd.Parameters.AddWithValue("@status", (int)status);
                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CheckAlreadyInvited", _tenantId));

                using (var reader = cmd.ExecuteReader())
                {
                    return reader.HasRows;
                }
            }
        }

        public int InsertInvitation(Invitation invitation)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INVITATION_INSERT;


                    cmd.Parameters.AddWithValue("@InvitationURL", invitation.InvitationURL);
                    cmd.Parameters.AddWithValue("@InviteCode", invitation.InviteCode);
                    cmd.Parameters.AddWithValue("@SentBy", invitation.SentBy);
                    cmd.Parameters.AddWithValue("@SentTo", invitation.SentTo);
                    cmd.Parameters.AddWithValue("@SentByEmail", invitation.SentByEmail);
                    cmd.Parameters.AddWithValue("@SentToEmail", invitation.SentToEmail);
                    cmd.Parameters.AddWithValue("@StatusId", (int)invitation.Status);
                    cmd.Parameters.AddWithValue("@SentOn", invitation.SentOn);
                    cmd.Parameters.AddWithValue("@TenantId", _tenantId);
                    //cmd.Parameters.AddWithValue("@AcceptedOn", invitation.AcceptedOn);
                    cmd.Parameters.AddWithValue("@Comments", invitation.Comments);
                    //cmd.Parameters.AddWithValue("@IsDeleted", 0);
                    cmd.Parameters.AddWithValue("@CreatedDate", invitation.CreatedDate);
                    //cmd.Parameters.AddWithValue("@LastModifiedDate", invitation.
                    cmd.Parameters.AddWithValue("@CreatedBy", invitation.CreatedBy);
                    cmd.Parameters.AddWithValue("@ISDeviceUser", 0);

                    //cmd.Parameters.AddWithValue("@LastModifiedBy", invitation.


                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "InsertInvitation", _tenantId));

                    //int noOfRowsAffected = Convert.ToInt32(cmd.ExecuteNonQuery());
                    //return noOfRowsAffected;
                    int lastId = (int)cmd.ExecuteScalar();
                    return lastId;

                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int InsertEventDispatchInfo(int ext, int userNum, string identity, string eventId, string callerInvitationLink)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.EVENT_DISPATCHER_INFO_INSERT;

                    cmd.Parameters.AddWithValue("@Ext", ext);
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@Identity", identity);
                    cmd.Parameters.AddWithValue("@EventId", eventId);
                    cmd.Parameters.AddWithValue("@CallerInvitationLink", callerInvitationLink);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "InsertEventDispatchInfo", _tenantId));
                    int lastId = (int)cmd.ExecuteScalar();
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int ChangeInvitationStatus(int invitationId, InvitationStatus status, int? userId, DateTime? modifiedDate)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INVITATION_STATUS_UPDATE;
                    cmd.Parameters.AddWithValue("@InvitationId", invitationId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)status);
                    cmd.Parameters.AddWithValue("@LastModifiedDate", modifiedDate);
                    cmd.Parameters.AddWithValue("@LastModifiedBy", userId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "ChangeInvitationStatus", _tenantId));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateInvitationURL(int invitationId, string invitationURL)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE umInvitation Set InvitationURL = @InvitationURL WHERE Id = @Id";
                    conn.Open();

                    cmd.Parameters.AddWithValue("@Id", invitationId);
                    cmd.Parameters.AddWithValue("@InvitationURL", invitationURL);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateInvitationURL", _tenantId));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int CancelInvitation(int invitationId, InvitationStatus status, int userId, DateTime modifiedDate)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INVITATION_STATUS_UPDATE;
                    cmd.Parameters.AddWithValue("@InvitationId", invitationId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)status);
                    cmd.Parameters.AddWithValue("@IsDeleted", 1);
                    cmd.Parameters.AddWithValue("@LastModifiedDate", modifiedDate);
                    cmd.Parameters.AddWithValue("@LastModifiedBy", userId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CancelInvitation", _tenantId));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int RejectInvitation(int invitationId, InvitationStatus status, DateTime modifiedDate)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INVITATION_STATUS_UPDATE;
                    cmd.Parameters.AddWithValue("@InvitationId", invitationId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)status);
                    cmd.Parameters.AddWithValue("@LastModifiedDate", modifiedDate);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "RejectInvitation", _tenantId));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public Invitation GetInvitationById(int invitationId)
        {
            Invitation invitation = null;

            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "SELECT * FROM umInvitation where (Id = @invitationId) AND (IsDeleted = 0)";
                cmd.Parameters.AddWithValue("@invitationId", invitationId);

                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetInvitationById", _tenantId));

                using (var dr = cmd.ExecuteReader())
                {
                    //if (dr.HasRows)
                    if (dr.Read())
                    {
                        invitation = new Invitation();
                        invitation.Id = Convert.ToInt32(dr["Id"]);
                        invitation.InvitationURL = Convert.ToString(dr["InvitationURL"]);
                        invitation.InviteCode = Convert.ToString(dr["InviteCode"]);
                        invitation.SentBy = Convert.ToInt32(dr["SentBy"]);
                        //invitation.SentTo 
                        invitation.SentByEmail = Convert.ToString(dr["SentByEmail"]);
                        invitation.SentToEmail = Convert.ToString(dr["SentToEmail"]);
                        invitation.SentOn = Convert.ToDateTime(dr["SentOn"]);
                        //invitation.AcceptedOn 
                        invitation.Comments = Convert.ToString(dr["InvitationURL"]);
                        invitation.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                        invitation.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                        //invitation.LastModifiedDate 
                        //invitation.CreatedBy 
                        //invitation.LastModifiedBy 
                        invitation.Status = (InvitationStatus)Enum.Parse(typeof(InvitationStatus), Convert.ToString(dr["StatusId"]));
                    }
                }
            }
            return invitation;
        }

        public List<Invitation> GetInvitationsByWhereClause(string whereClause)
        {
            List<Invitation> invitations = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INVITATION_GETBY_WHERECLAUSE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause + "and ISDeviceUser = 0");

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetInvitationsByWhereClause", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        invitations = new List<Invitation>();
                        while (dr.Read())
                        {
                            var invitation = new Invitation
                            {
                                Id = Convert.ToInt32(dr["Id"]),
                                InvitationURL = Convert.ToString(dr["InvitationURL"]),
                                InviteCode = Convert.ToString(dr["InviteCode"]),
                                SentBy = Convert.ToInt32(dr["SentBy"]),
                                //SentTo 
                                SentByEmail = Convert.ToString(dr["SentByEmail"]),
                                SentToEmail = Convert.ToString(dr["SentToEmail"]),
                                //SentOn = Convert.ToDateTime(dr["SentOn"]);
                                //AcceptedOn 
                                Comments = Convert.ToString(dr["InvitationURL"]),
                                IsDeleted = Convert.ToBoolean(dr["IsDeleted"]),
                                CreatedDate = Convert.ToDateTime(dr["CreatedDate"]),
                                //LastModifiedDate 
                                //CreatedBy 
                                //LastModifiedBy 
                                Status = (InvitationStatus)Enum.Parse(typeof(InvitationStatus), Convert.ToString(dr["StatusId"]))

                            };
                            invitations.Add(invitation);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return invitations;
        }

        public int AcceptInvitation(User user, int invitationId, InvitationStatus status, DateTime modifiedDate, bool isOnlyIQ3ModeEnabled)
        {
            int lastUserId = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction("Inq"))
                    {
                        using (var cmdInvitation = conn.CreateCommand())
                        {
                            cmdInvitation.CommandText = DBConstants.UserManagement.INVITATION_ACCEPT;
                            cmdInvitation.CommandType = CommandType.StoredProcedure;
                            cmdInvitation.Transaction = tran as SqlTransaction;

                            cmdInvitation.Parameters.AddWithValue("@InvitationId", invitationId);
                            cmdInvitation.Parameters.AddWithValue("@StatusId", (int)status);
                            cmdInvitation.Parameters.AddWithValue("@AcceptedOn", modifiedDate);
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmdInvitation), Originator.UserManagement, "AcceptInvitation", _tenantId));

                            int rowsAffected = cmdInvitation.ExecuteNonQuery();
                        }
                        //2. User Registration
                        using (var cmdUserManager = conn.CreateCommand())
                        {

                            cmdUserManager.CommandType = CommandType.StoredProcedure;
                            cmdUserManager.CommandText = DBConstants.UserManagement.APPUSER_INSERT;
                            cmdUserManager.Transaction = tran as SqlTransaction;

                            cmdUserManager.Parameters.AddWithValue("@UserID ", user.UserID);
                            cmdUserManager.Parameters.AddWithValue("@UserPW", user.UserPW);
                            cmdUserManager.Parameters.AddWithValue("@UserName", user.UserName);
                            cmdUserManager.Parameters.AddWithValue("@Ext", user.Ext);
                            cmdUserManager.Parameters.AddWithValue("@SearchRest", user.SearchRest);
                            cmdUserManager.Parameters.AddWithValue("@UserEmail", user.UserEmail);
                            cmdUserManager.Parameters.AddWithValue("@IdentityNumber", user.IdentityNumber);
                            cmdUserManager.Parameters.AddWithValue("@UserPhone", user.UserPhone);
                            cmdUserManager.Parameters.AddWithValue("@UserFax", user.UserFax);
                            cmdUserManager.Parameters.AddWithValue("@JoinBeginDate", user.JoinBeginDate.ToString("yyyyMMdd"));
                            cmdUserManager.Parameters.AddWithValue("@JoinEndDate", user.JoinEndDate.ToString("yyyyMMdd"));
                            cmdUserManager.Parameters.AddWithValue("@Descr", user.Descr);
                            cmdUserManager.Parameters.AddWithValue("@GroupNum", user.GroupNum);
                            cmdUserManager.Parameters.AddWithValue("@POD", user.POD);
                            cmdUserManager.Parameters.AddWithValue("@EOD", user.EOD);
                            cmdUserManager.Parameters.AddWithValue("@Pause", user.Pause);
                            cmdUserManager.Parameters.AddWithValue("@CompanyName", user.CompanyName);
                            cmdUserManager.Parameters.AddWithValue("@IsOnlyIQ3ModeEnabled", isOnlyIQ3ModeEnabled);

                            lastUserId = (int)cmdUserManager.ExecuteScalar();
                            int RevSyncServerUserNum = 0;
                            if (SiteConfig.RevSyncEnabled)
                            {
                                RevSyncServerUserNum = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "CreateAppUserAccount", JsonConvert.SerializeObject(user)));
                                if (RevSyncServerUserNum > 0)
                                {
                                    tran.Commit();
                                    using (var Updatecmd = conn.CreateCommand())
                                    {
                                        Updatecmd.CommandType = CommandType.Text;
                                        Updatecmd.CommandText = "UPDATE t_Account SET [RevSyncServerUserNum] = " + RevSyncServerUserNum + " Where UserNum = " + lastUserId;
                                        Updatecmd.ExecuteNonQuery();
                                    }
                                }
                                else tran.Rollback();
                            }
                            else if (SiteConfig.IsMTEnable)
                            {
                                Dictionary<string, string> _dic = new Dictionary<string, string>();
                                _dic.Add("User", JsonConvert.SerializeObject(user));
                                _dic.Add("InvitationId", Convert.ToString(invitationId));
                                _dic.Add("Status", JsonConvert.SerializeObject(status));
                                _dic.Add("ModifiedDate", Convert.ToString(modifiedDate));

                                bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "AcceptInvitation", JsonConvert.SerializeObject(_dic));
                                if (isSent) tran.Commit(); else tran.Rollback();
                            }
                            else tran.Commit();
                        }
                    }
                }

                if (SiteConfig.IsMTEnable)
                    new TenantDAL(_tenantId).InsertUserAccount(_tenantId, lastUserId, user.UserID, user.UserID, "", "", user.UserName, user.Descr);
            }
            catch (Exception ex) { throw ex; }
            return lastUserId;
        }

        public bool CheckUniqueId(string email)
        {
            bool bExistsInAccountTable = false;
            bool bExistsInAgentTable = false;
            bool bUnique = false;
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "SELECT * FROM t_Account WHERE UserId = @email";
                cmd.Parameters.AddWithValue("@email", email);
                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CheckUniqueId", _tenantId));

                using (var reader = cmd.ExecuteReader()) bExistsInAccountTable = reader.HasRows;
                if (SiteConfig.RevSyncEnabled && !bExistsInAccountTable) bExistsInAccountTable = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "CheckUniqueId", email));
            }
            bUnique = (bExistsInAccountTable) == true ? false : true;
            return bUnique;
        }

        #endregion

        #region Inquire Group Management
        public List<GlobalGroup> InqCreateGroup(GlobalGroup globalGroup, int uId = 1000)
        {
            List<GlobalGroup> gGroups = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GROUP_INSERT_N_GET;
                    cmd.Parameters.AddWithValue("@ParentId", globalGroup.ParentId);
                    cmd.Parameters.AddWithValue("@GroupName", globalGroup.Name);
                    cmd.Parameters.AddWithValue("@Description", globalGroup.Description);
                    cmd.Parameters.AddWithValue("@UserId", uId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "InqCreateGroup", _tenantId));

                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex) { throw ex; }
            return gGroups;
        }

        public List<GlobalGroup> InquireDeleteGroup(int id, int userId = 1000)
        {
            List<GlobalGroup> gGroups = null;
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_DEL_GROUPS;
                    cmd.Parameters.AddWithValue("@GroupNum ", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "InquireDeleteGroup", _tenantId));

                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex) { throw ex; }
            return gGroups;
        }
        public bool InqSaveGroupUser(UserGroup uGroup)
        {
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;

                    cmd.CommandText = DBConstants.UserManagement.INQ_ADD_USERS_TO_GROUP;
                    cmd.Parameters.AddWithValue("@GroupNum", uGroup.GroupNum);
                    cmd.Parameters.AddWithValue("@UserNum", uGroup.UserNum);
                    cmd.Parameters.AddWithValue("@AssignAuth", uGroup.AssignAuth);
                    cmd.Parameters.AddWithValue("@Descr", uGroup.Descr == null ? Convert.DBNull : uGroup.Descr);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "InqSaveGroupUser", _tenantId));

                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool InqDeleteGroupUser(UserGroup uGroup)
        {
            try
            {
                //using (var conn = GetConnection())
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_DEL_USERS_FROM_GROUP;
                    cmd.Parameters.AddWithValue("@GroupNum", uGroup.GroupNum);
                    cmd.Parameters.AddWithValue("@UserNum", uGroup.UserNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "InqDeleteGroupUser", _tenantId));

                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion
        #region Inquire Custom Marker

        public bool AddCustomMarker(CustomMarkersData cmData)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;

                    // cmd.CommandText = DBConstants.UserManagement.INQUIRE_ADDUSERSTOGROUP;
                    //  cmd.Parameters.AddWithValue("@GroupNum", uGroup.GroupNum);
                    // cmd.Parameters.AddWithValue("@UserNum", uGroup.UserNum);
                    // cmd.Parameters.AddWithValue("@AssignAuth", uGroup.AssignAuth);
                    // cmd.Parameters.AddWithValue("@Descr", uGroup.Descr == null ? Convert.DBNull : uGroup.Descr);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "AddCustomMarker", _tenantId));

                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion
        #region MonitorPlaye

        public List<User> GetLPSettingsInfo(int UserNum)
        {
            List<User> users = new List<User>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = DBConstants.UserManagement.GET_LP_SETTINGS_INFO;
                    cmd.Parameters.AddWithValue("@UserNum", UserNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetLPSettingsInfo", _tenantId));


                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            var user = new User
                            {
                                UserNum = Convert.ToInt32(dr["UserNum"]),
                                IsMultiChannel = Convert.ToBoolean(dr["IsMultiChannel"]),
                                IsContinuousPlay = Convert.ToBoolean((dr["IsContinuousPlay"]))

                            };
                            users.Add(user);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return users;
        }
        public bool saveMonitorSettings(int UId, bool multiChannel, bool continuousPlay)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.SAVE_LP_SETTINGS;

                    cmd.Parameters.AddWithValue("@UserNum", UId);
                    cmd.Parameters.AddWithValue("@IsMultiChannel", multiChannel);
                    cmd.Parameters.AddWithValue("@IsContinuousPlay", continuousPlay);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "saveMonitorSettings", _tenantId));

                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
            //return saved;
        }
        #endregion
        public List<User> GetInviteeUser(string inviteeEmail)
        {
            List<User> users = new List<User>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT TOP 1 * FROM t_Account WHERE UserId = @email AND Status = 1";
                    cmd.Parameters.AddWithValue("@email", inviteeEmail);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetInviteeUser", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            var user = new User
                            {
                                UserNum = (int)dr["UserNum"],
                                UserType = (int)dr["UserType"],
                                UserName = Convert.ToString(dr["UserName"]),
                                UserID = Convert.ToString(dr["UserID"]),
                                UserPW = Convert.ToString(dr["UserPW"]),
                                ViewID = Convert.ToInt32(dr["ViewID"]),
                                Ext = Convert.ToString(dr["Ext"]),
                                UserEmail = Convert.ToString(dr["UserEmail"]),
                                Status = Convert.ToInt32(dr["Status"]),
                                UserPhone = Convert.ToString(dr["UserPhone"]),
                                UserFax = Convert.ToString(dr["UserFax"]),
                                IdentityNumber = Convert.ToString(dr["IdentityNumber"]),
                                SearchRest = Convert.ToInt32(dr["SearchRest"]),
                                Pause = Convert.ToInt32(dr["Pause"]),
                                EOD = Convert.ToInt32(dr["EOD"]),
                                POD = Convert.ToInt32(dr["POD"]),
                                ExtName = Convert.ToString(dr["ExtName"]),
                                SelectType = Convert.ToInt32(dr["SelectType"]),
                                DNISCheck = Convert.ToInt32(dr["DNISCheck"]),
                                DNIS = Convert.ToString(dr["DNIS"]),
                                UserPic = Convert.ToString(dr["UserPic"]),
                                TempUserPW = Convert.ToString(dr["TempUserPW"]),
                            };
                            users.Add(user);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return users;
        }

        public List<User> UpdateEmailPassword(string newuserid, string newuserpw, string olduserid)
        {
            List<User> users = new List<User>();
            try
            {
                if (!CheckUserAlreadyExistsAndActive(newuserid))
                {
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.UserManagement.UPDATE_EMAIL_PASSWORD;
                        cmd.Parameters.AddWithValue("@Userid", newuserid);
                        cmd.Parameters.AddWithValue("@oldUserid", olduserid);
                        cmd.Parameters.AddWithValue("@Userpassword", newuserpw);

                        conn.Open();
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateEmailPassword", _tenantId));

                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {

                            while (dr.Read())
                            {
                                var user = new User
                                {
                                    UserNum = (int)dr["UserNum"],
                                    UserType = (int)dr["UserType"],
                                    UserName = Convert.ToString(dr["UserName"]),
                                    UserID = Convert.ToString(dr["UserID"]),
                                    UserPW = Convert.ToString(dr["UserPW"]),
                                    ViewID = Convert.ToInt32(dr["ViewID"]),
                                    Ext = Convert.ToString(dr["Ext"]),
                                    UserEmail = Convert.ToString(dr["UserEmail"]),
                                    Status = Convert.ToInt32(dr["Status"]),
                                    UserPhone = Convert.ToString(dr["UserPhone"]),
                                    UserFax = Convert.ToString(dr["UserFax"]),
                                    IdentityNumber = Convert.ToString(dr["IdentityNumber"]),
                                    SearchRest = Convert.ToInt32(dr["SearchRest"]),
                                    Pause = Convert.ToInt32(dr["Pause"]),
                                    EOD = Convert.ToInt32(dr["EOD"]),
                                    POD = Convert.ToInt32(dr["POD"]),
                                    ExtName = Convert.ToString(dr["ExtName"]),
                                    SelectType = Convert.ToInt32(dr["SelectType"]),
                                    DNISCheck = Convert.ToInt32(dr["DNISCheck"]),
                                    DNIS = Convert.ToString(dr["DNIS"]),
                                    UserPic = Convert.ToString(dr["UserPic"]),
                                };
                                users.Add(user);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return users;
        }

        public List<User> GetAllUsers(int userType = 0)
        {
            List<User> users = new List<User>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var command = conn.CreateCommand())
                {
                    command.CommandType = CommandType.Text;
                    if (userType != 11)
                    {
                        command.CommandText = "SELECT * FROM t_Account Where Status = 1";
                    }
                    else
                    {
                        command.CommandText = "SELECT * FROM t_Account Where Status = 1 AND UserType = 11";
                    }

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(command), Originator.UserManagement, "GetAllUsers", _tenantId));

                    using (SqlDataReader dr = command.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            var user = new User
                            {
                                UserNum = (int)dr["UserNum"],
                                UserType = (int)dr["UserType"],
                                UserName = Convert.ToString(dr["UserName"]),
                                UserID = Convert.ToString(dr["UserID"]),
                                //UserPW = Convert.ToString(dr["UserPW"]),
                                Ext = Convert.ToString(dr["Ext"]),
                                UserEmail = Convert.ToString(dr["UserEmail"]),
                                Status = Convert.ToInt32(dr["Status"]),
                                ExtName = Convert.ToString(dr["ExtName"]),
                                SelectType = Convert.ToInt32(dr["SelectType"]),
                                IsDeviceUser = dr["IsDeviceUser"] == DBNull.Value ? 0 : Convert.ToInt32(dr["IsDeviceUser"])
                            };
                            users.Add(user);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return users;
        }

        public List<User> GetAllUsersOthers(long userNum)
        {
            List<User> users = new List<User>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var command = conn.CreateCommand())
                {
                    command.CommandType = CommandType.Text;
                    command.CommandText = "SELECT * FROM t_Account Where Status = 1 AND UserNum <> (@UserNum)";
                    command.Parameters.AddWithValue("@UserNum", userNum);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(command), Originator.UserManagement, "GetAllUsers", _tenantId));

                    using (SqlDataReader dr = command.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            var user = new User
                            {
                                UserNum = (int)dr["UserNum"],
                                UserType = (int)dr["UserType"],
                                UserName = Convert.ToString(dr["UserName"]),
                                UserID = Convert.ToString(dr["UserID"]),
                                //UserPW = Convert.ToString(dr["UserPW"]),
                                Ext = Convert.ToString(dr["Ext"]),
                                UserEmail = Convert.ToString(dr["UserEmail"]),
                                Status = Convert.ToInt32(dr["Status"]),
                                ExtName = Convert.ToString(dr["ExtName"]),
                                SelectType = Convert.ToInt32(dr["SelectType"]),
                                IsDeviceUser = dr["IsDeviceUser"] == DBNull.Value ? 0 : Convert.ToInt32(dr["IsDeviceUser"])
                            };
                            users.Add(user);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return users;
        }

        #region Event Invitation
        public User CreateEventSpecificUserAccount(User user)
        {
            User eventSpecificUser = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.EVENTSPECIFIC_USER_INSERT;
                    cmd.Parameters.AddWithValue("@UserID ", user.UserID);
                    cmd.Parameters.AddWithValue("@UserPW", user.UserPW);
                    cmd.Parameters.AddWithValue("@UserName", user.UserName);
                    cmd.Parameters.AddWithValue("@Ext", user.Ext);
                    cmd.Parameters.AddWithValue("@SearchRest", user.SearchRest);
                    cmd.Parameters.AddWithValue("@UserEmail", user.UserEmail);
                    cmd.Parameters.AddWithValue("@IdentityNumber", user.IdentityNumber);
                    cmd.Parameters.AddWithValue("@UserPhone", user.UserPhone);
                    cmd.Parameters.AddWithValue("@UserFax", user.UserFax);
                    cmd.Parameters.AddWithValue("@JoinBeginDate", user.JoinBeginDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@JoinEndDate", user.JoinEndDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@Descr", user.Descr);
                    cmd.Parameters.AddWithValue("@GroupNum", user.GroupNum);
                    cmd.Parameters.AddWithValue("@POD", user.POD);
                    cmd.Parameters.AddWithValue("@EOD", user.EOD);
                    cmd.Parameters.AddWithValue("@Pause", user.Pause);
                    cmd.Parameters.AddWithValue("@CompanyName", user.CompanyName);
                    //cmd.Parameters.AddWithValue("@IsEventSpecific", user.IsEventSpecific);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CreateEventSpecificUserAccount", _tenantId));

                    int appUserId = (int)cmd.ExecuteScalar();
                    user.UserNum = appUserId;
                    eventSpecificUser = user;
                    if (eventSpecificUser == null) eventSpecificUser = new User();
                }
            }
            catch (Exception ex) { throw ex; }
            return eventSpecificUser;
        }
        public User CreateEventSpecificSimpleUserAccount(User user)
        {
            User eventSpecificUser = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.EVENTSPECIFIC_SIMPLE_USER_INSERT;
                    cmd.Parameters.AddWithValue("@UserID ", user.UserID);
                    cmd.Parameters.AddWithValue("@UserPW", user.UserPW);
                    cmd.Parameters.AddWithValue("@UserName", user.UserName);
                    cmd.Parameters.AddWithValue("@Ext", user.Ext);
                    cmd.Parameters.AddWithValue("@SearchRest", user.SearchRest);
                    cmd.Parameters.AddWithValue("@UserEmail", user.UserEmail);
                    cmd.Parameters.AddWithValue("@IdentityNumber", user.IdentityNumber);
                    cmd.Parameters.AddWithValue("@UserPhone", user.UserPhone);
                    cmd.Parameters.AddWithValue("@UserFax", user.UserFax);
                    cmd.Parameters.AddWithValue("@JoinBeginDate", user.JoinBeginDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@JoinEndDate", user.JoinEndDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@Descr", user.Descr);
                    cmd.Parameters.AddWithValue("@GroupNum", user.GroupNum);
                    cmd.Parameters.AddWithValue("@POD", user.POD);
                    cmd.Parameters.AddWithValue("@EOD", user.EOD);
                    cmd.Parameters.AddWithValue("@Pause", user.Pause);
                    cmd.Parameters.AddWithValue("@CompanyName", user.CompanyName);
                    //cmd.Parameters.AddWithValue("@IsEventSpecific", user.IsEventSpecific);
                    cmd.Parameters.AddWithValue("@SelectType", user.SelectType);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CreateEventSpecificSimpleUserAccount", _tenantId));

                    int appUserId = (int)cmd.ExecuteScalar();
                    user.UserNum = appUserId;
                    eventSpecificUser = user;
                    if (eventSpecificUser == null) eventSpecificUser = new User();
                }
            }
            catch (Exception ex) { throw ex; }
            return eventSpecificUser;
        }
        public User UpdateEventSpecificSimpleRights(User user)
        {
            User eventSpecificUser = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.EVENTSPECIFIC_SIMPLE_RIGHTS_UPDATE;
                    cmd.Parameters.AddWithValue("@UserNum ", user.UserNum);
                    cmd.Parameters.AddWithValue("@Ext", user.Ext);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateEventSpecificSimpleRights", _tenantId));

                    int appUserId = (int)cmd.ExecuteScalar();
                    user.UserNum = appUserId;
                    eventSpecificUser = user;
                    if (eventSpecificUser == null) eventSpecificUser = new User();
                }
            }
            catch (Exception ex) { throw ex; }
            return eventSpecificUser;
        }

        public EventInvitation AddEventInvitation(EventInvitation eventInvitation)
        {
            EventInvitation evtInvitation = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.EVENT_INVITATION_ADD;
                    cmd.Parameters.AddWithValue("@eventId ", eventInvitation.EventId);
                    cmd.Parameters.AddWithValue("@userNum", eventInvitation.UserNum);
                    cmd.Parameters.AddWithValue("@userId", eventInvitation.UserId);
                    cmd.Parameters.AddWithValue("@invitedBy", eventInvitation.InvitedBy);
                    cmd.Parameters.AddWithValue("@isEventActive", eventInvitation.IsEventActive);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "AddEventInvitation", _tenantId));

                    int eventInvitationId = (int)cmd.ExecuteScalar();
                    //int eventInvitationId = (int)cmd.ExecuteNonQuery();//.ExecuteScalar();
                    eventInvitation.Id = eventInvitationId;
                    evtInvitation = eventInvitation;
                }
                if (evtInvitation == null) evtInvitation = new EventInvitation();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return evtInvitation;
        }
        public List<string> GetInvitedActiveEventIdsByUserNum(int userNum)
        {
            List<string> activeEventIds = new List<string>();
            try
            {

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.EVENT_GET_ACTIVE_BY_USERNUM;
                    cmd.Parameters.AddWithValue("@userNum", userNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetInvitedActiveEventIdsByUserNum", _tenantId));

                    using (var dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            string activeEventId = Convert.ToString(dr["EventId"]);
                            activeEventIds.Add(activeEventId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return activeEventIds;
        }
        public User GetUserByEmailId(string emailId)
        {
            User user = null;
            UserGroup uGroup = null;
            List<Permission> permissions = null;
            List<Recorder> recs = null;
            string sAccessRight = null;
            string sAssignedNodes = null;
            string sSimpleAccessRight = "**********";
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GET_USER_BY_EMAILID;
                    cmd.Parameters.AddWithValue("@UserID", emailId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetUserByEmailId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        user = ORMapper.MapLoginUser(dr);
                        dr.NextResult();
                        uGroup = ORMapper.MapLoginUserGroup(dr);
                        dr.NextResult();
                        sAccessRight = ORMapper.MapSimpleAccessRights(dr, out sSimpleAccessRight);
                        dr.NextResult();
                        sAssignedNodes = ORMapper.MapAssignedNodes(dr);
                        dr.NextResult();
                        permissions = ORMapper.MapPermissions(dr);
                        dr.NextResult();
                        recs = ORMapper.MapRecorders(dr);
                        if (user != null)
                        {
                            user.GroupNum = uGroup == null ? 0 : uGroup.GroupNum;
                            user.UserID = emailId;
                            user.UserGroup = uGroup == null ? new UserGroup { UserNum = user.UserNum, AssignAuth = "**********" } : uGroup;
                            user.SimpleAccessRight = sAccessRight;
                            user.Permissions = this.GetPermissions(user.UserNum, user.SelectType == 0 ? user.UserGroup.AssignAuth : user.SimpleAccessRight, permissions);
                            user.AssignedNodes = sAssignedNodes != null ? sAssignedNodes.Split(',').ToList() : new List<string>();
                            user.IsDeviceUser = user.IsDeviceUser;
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return user;
        }
        public bool CheckValidEventSpecificUser(string email)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.UserManagement.EVENT_SPECIFIC_USER_VALIDATION;
                cmd.Parameters.AddWithValue("@emailId", email);

                SqlParameter returnParameter = cmd.Parameters.Add("RetVal", SqlDbType.Int);
                returnParameter.Direction = ParameterDirection.ReturnValue;

                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CheckValidEventSpecificUser", _tenantId));

                cmd.ExecuteNonQuery();

                int retVal = (int)returnParameter.Value; ;
                if (retVal == 1)
                    return true;
                else
                    return false;
            }
        }
        public bool GrantLiveMonitorPermission(int userNum, string userPermissions, int extension)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.UserManagement.EVENT_GRANT_LIVE_MONITOR_PERMISSOIN;
                cmd.Parameters.AddWithValue("@userNum", userNum);
                cmd.Parameters.AddWithValue("@userPermissions", userPermissions);
                cmd.Parameters.AddWithValue("@extension", extension);

                SqlParameter returnParameter = cmd.Parameters.Add("RetVal", SqlDbType.Int);
                returnParameter.Direction = ParameterDirection.ReturnValue;

                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GrantLiveMonitorPermission", _tenantId));

                cmd.ExecuteNonQuery();

                int retVal = (int)returnParameter.Value; ;
                if (retVal == 1)
                    return true;
                else
                    return false;
            }
        }
        public bool EnableUserAccount(int userNum)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.UserManagement.EVENT_RECOVER_USER;
                cmd.Parameters.AddWithValue("@UserNum", userNum);

                SqlParameter returnParameter = cmd.Parameters.Add("RetVal", SqlDbType.Int);
                returnParameter.Direction = ParameterDirection.ReturnValue;

                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "EnableUserAccount", _tenantId));

                cmd.ExecuteNonQuery();

                int retVal = (int)returnParameter.Value; ;
                if (retVal == 1)
                    return true;
                else
                    return false;
            }
        }
        public User EnableDisableInvitationPermission(int userNum, bool bInvitationEnabled)
        {
            User user = new User();
            try
            {
                int rowsUpdated = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE t_Account SET CanInvite = @CanInvite WHERE UserNum = @UserNum";
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@CanInvite", bInvitationEnabled);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "EnableDisableInvitationPermission", _tenantId));

                    rowsUpdated = cmd.ExecuteNonQuery();
                }
                if (rowsUpdated > 0)
                {
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var command = conn.CreateCommand())
                    {
                        command.CommandType = CommandType.Text;
                        command.CommandText = "SELECT TOP 1 * FROM t_Account WHERE UserNum = @UserNum";
                        command.Parameters.AddWithValue("@UserNum", userNum);

                        conn.Open();
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(command), Originator.UserManagement, "EnableDisableInvitationPermission", _tenantId));

                        using (SqlDataReader dr = command.ExecuteReader())
                        {
                            while (dr.Read())
                            {
                                var usr = new User
                                {
                                    UserNum = (int)dr["UserNum"],
                                    UserType = (int)dr["UserType"],
                                    UserName = Convert.ToString(dr["UserName"]),
                                    UserID = Convert.ToString(dr["UserID"]),
                                    UserPW = Convert.ToString(dr["UserPW"]),
                                    ViewID = Convert.ToInt32(dr["ViewID"]),
                                    Ext = Convert.ToString(dr["Ext"]),
                                    UserEmail = Convert.ToString(dr["UserEmail"]),
                                    Status = Convert.ToInt32(dr["Status"]),
                                    UserPhone = Convert.ToString(dr["UserPhone"]),
                                    UserFax = Convert.ToString(dr["UserFax"]),
                                    IdentityNumber = Convert.ToString(dr["IdentityNumber"]),
                                    SearchRest = Convert.ToInt32(dr["SearchRest"]),
                                    Pause = Convert.ToInt32(dr["Pause"]),
                                    EOD = Convert.ToInt32(dr["EOD"]),
                                    POD = Convert.ToInt32(dr["POD"]),
                                    ExtName = Convert.ToString(dr["ExtName"]),
                                    SelectType = Convert.ToInt32(dr["SelectType"]),
                                    DNISCheck = Convert.ToInt32(dr["DNISCheck"]),
                                    DNIS = Convert.ToString(dr["DNIS"]),
                                    UserPic = Convert.ToString(dr["UserPic"]),
                                    CanInvite = dr["CanInvite"] == DBNull.Value ? false : Convert.ToBoolean(dr["CanInvite"])
                                };
                                user = usr;
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return user;
        }

        public bool EnableDisableUsersetting(int userNum, bool bAutoUploadEnabled, bool bCustomAssetIdEnabled)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_UPDATE_AUTOUPLOAD;
                    cmd.Parameters.AddWithValue("@userNum", userNum);
                    cmd.Parameters.AddWithValue("@AutoUpload", bAutoUploadEnabled);
                    cmd.Parameters.AddWithValue("@DisableCustomAssetId", bCustomAssetIdEnabled);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "EnableDisableUsersetting", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0;
                }
            }
            catch (Exception ex) { throw ex; }
            //return true;
        }

        #endregion
        public bool ValidateUserPassword(int userNum, string currentPassword)
        {
            string currPassword = string.Empty;
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "SELECT UserPW FROM t_Account where UserNum = @userNum";
                cmd.Parameters.AddWithValue("@userNum", userNum);
                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "ValidateUserPassword", _tenantId));

                using (var reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        currPassword = Convert.ToString(reader["UserPW"]);
                    }
                }
            }
            return currentPassword == currPassword ? true : false;
        }
        public bool ValidateUserPasswordByEmail(string email, string currentPassword)
        {
            string currPassword = string.Empty;
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "SELECT UserPW FROM t_Account where UserID = @userEmail";
                cmd.Parameters.AddWithValue("@userEmail", email);
                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "ValidateUserPasswordByEmail", _tenantId));

                using (var reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        currPassword = Convert.ToString(reader["UserPW"]);
                    }
                }
            }
            return currentPassword == currPassword ? true : false;
        }

        public bool ValidateTempUserPassword(int userNum, string currentPassword)
        {
            string currPassword = string.Empty;
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "SELECT TempUserPW FROM t_Account where UserNum = @userNum";
                cmd.Parameters.AddWithValue("@userNum", userNum);
                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "ValidateTempUserPassword", _tenantId));

                using (var reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        currPassword = Convert.ToString(reader["TempUserPW"]);
                    }
                }
            }
            return currentPassword == currPassword ? true : false;
        }


        public bool UpdateUserPassword(int userNum, string newPassword)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_UPDATE_PASSWORD;
                    cmd.Parameters.AddWithValue("@userNum", userNum);
                    cmd.Parameters.AddWithValue("@newPassword", newPassword);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateUserPassword", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool UpdateUserPasswordByEmail(string email, string newPassword)
        {
            int rowsaffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_UPDATE_PASSWORD_BY_EMAIL;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@email", email);
                    cmd.Parameters.AddWithValue("@newPassword", newPassword);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateUserPasswordByEmail", _tenantId));
                    rowsaffected = Convert.ToInt32(cmd.ExecuteNonQuery());

                    if (SiteConfig.RevSyncEnabled)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("email", Convert.ToString(email));
                        _dic.Add("newPassword", Convert.ToString(newPassword));

                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagementDAL", "UpdateUserPasswordByEmail", JsonConvert.SerializeObject(_dic)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("email", Convert.ToString(email));
                        _dic.Add("newPassword", Convert.ToString(newPassword));

                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagementDAL", "UpdateUserPasswordByEmail", JsonConvert.SerializeObject(_dic));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return rowsaffected > 0;
        }

        public List<string> GetPasswordHistoryByUser(int userNum)
        {
            List<string> userPasswords = new List<string>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_GET_PASSWORD_HISTORY;
                    cmd.Parameters.AddWithValue("@userNum", userNum);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetPasswordHistoryByUser", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            userPasswords.Add(Convert.ToString(dr["UserPassword"]));
                        }
                    }
                }
                return userPasswords;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<string> GetPasswordHistoryByEmail(string email)
        {
            List<string> userPasswords = new List<string>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_GET_PASSWORD_HISTORY_BY_EMAIL;
                    cmd.Parameters.AddWithValue("@email", email);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetPasswordHistoryByEmail", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            userPasswords.Add(Convert.ToString(dr["UserPassword"]));
                        }
                    }
                }
                return userPasswords;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool LockUnlockUserAccount(string email, bool lockAccount)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_LOCK_UNLOCK;
                    cmd.Parameters.AddWithValue("@Email", email);
                    cmd.Parameters.AddWithValue("@Lock", lockAccount);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "LockUnlockUserAccount", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<User> GetUsersAboutToExpirePassword()
        {
            List<User> users = null;
            User user = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GET_ALL_USERS_PASSWORDHISTORY;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetUsersAboutToExpirePassword", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            users = new List<User>();
                            while (dr.Read())
                            {
                                user = new User();
                                user.UserNum = (int)dr["UserNum"];
                                user.UserID = Convert.ToString(dr["UserID"]);
                                user.UserPW = Convert.ToString(dr["UserPW"]);
                                user.UserName = Convert.ToString(dr["UserName"]);
                                user.UserEmail = Convert.ToString(dr["UserEmail"]);
                                user.LastPasswordChanged = dr["DateChanged"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["DateChanged"]);

                                users.Add(user);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return users;

        }


        public bool UpdateUserTempPassword(int userNum, string newPassword)
        {
            List<User> users = new List<User>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE t_Account Set UserPW = @newPassword, TempUserPW = '', IsTempLogin = @IsTempLogin where UserNum = @userNum";
                    cmd.Parameters.AddWithValue("@newPassword", newPassword);
                    cmd.Parameters.AddWithValue("@userNum", userNum);
                    cmd.Parameters.AddWithValue("@IsTempLogin", 0);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateUserTempPassword", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool ResetUserPasswordToTemp(string userID, string tempPassword)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE t_Account SET TempUserPW = @tempPassword, IsTempLogin = @IsTempLogin WHERE UserID = @userID AND Status = 1";
                    cmd.Parameters.AddWithValue("@tempPassword", tempPassword);
                    cmd.Parameters.AddWithValue("@userID", userID);
                    cmd.Parameters.AddWithValue("@IsTempLogin", 1);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "ResetUserPasswordToTemp", _tenantId));

                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public bool IsRevcellEnable(string email)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                cmd.CommandType = CommandType.Text;
                cmd.CommandText = "SELECT * FROM t_Account where (Status = 1) AND (UserId = @email) AND IsRevcell = 1;";
                cmd.Parameters.AddWithValue("@email", email);
                conn.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "IsRevcellEnable", _tenantId));

                using (var reader = cmd.ExecuteReader())
                {
                    return reader.HasRows;
                }
            }
        }

        public AppUserAccess GetAppUserAccess(string userEmail)
        {
            try
            {
                AppUserAccess appUserAccess = null;

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_APP_ACCESS_GET;
                    cmd.Parameters.AddWithValue("@UserEmail", userEmail);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetAppUserAccess", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            appUserAccess = new AppUserAccess();
                            appUserAccess.UserNum = Convert.ToInt32(dr["UserNum"]);
                            appUserAccess.UserEmail = Convert.ToString(dr["UserEmail"]);
                            appUserAccess.EnableUser = Convert.ToBoolean(dr["EnableUser"]);
                            appUserAccess.IsDeviceUser = Convert.ToBoolean(dr["IsDeviceUser"]);
                            appUserAccess.IsRevcellOnlyEnable = Convert.ToBoolean(dr["IsRevcellOnlyEnable"]);
                            appUserAccess.IsIQ3OnlyEnable = Convert.ToBoolean(dr["IsIQ3OnlyEnable"]);
                            appUserAccess.BothIQ3AndRevcellEnable = Convert.ToBoolean(dr["BothIQ3AndRevcellEnable"]);
                            appUserAccess.IsMDEnable = Convert.ToBoolean(dr["IsMDEnable"]);
                            appUserAccess.Ext = Convert.ToInt32(dr["Ext"]);
                            appUserAccess.ChannelType = dr["ChannelType"] == DBNull.Value ? 0 : Convert.ToInt32(dr["ChannelType"]); //Convert.ToInt32(dr["ChannelType"]);
                        }
                    }
                }
                return appUserAccess;
            }
            catch (Exception ex) { throw ex; }
        }

        public bool CopyMarkers(int copyTo, int copyOf)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.COPY_MARKERS;
                    cmd.Parameters.AddWithValue("@CopyTo", copyTo);
                    cmd.Parameters.AddWithValue("@CopyOf", copyOf);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CopyMarkers", _tenantId));
                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public List<User> FetchAllActiveUsers()
        {
            List<User> users = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text; ;
                    cmd.CommandText = "SELECT * FROM t_ACCOUNT WHERE STATUS = 1;";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "FetchAllActiveUsersFromRecorder", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapAllActiveUsers(dr);
                        dr.NextResult();
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return users;
        }

        public int UpdateCompactViewStatus(int userNum, bool isCompactView)
        {
            try
            {
                int rowsAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE t_Account SET IsCompactView = @IsCompactView WHERE UserNum = @UserNum;";
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@IsCompactView", isCompactView);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "UpdateCompactViewStatus", _tenantId));
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }
        public bool CopyTenantUserMarkers(int copyTo, int copyOf, int sourceTenantId, int destinationTenantId)
        {
            try
            {
                string sTenantId = sourceTenantId < 10 ? "0" + sourceTenantId.ToString() : sourceTenantId.ToString();
                string dTenantId = destinationTenantId < 10 ? "0" + destinationTenantId.ToString() : destinationTenantId.ToString();
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.COPY_TENANT_USER_MARKERS;
                    cmd.Parameters.AddWithValue("@CopyTo", copyTo);
                    cmd.Parameters.AddWithValue("@CopyOf", copyOf);
                    cmd.Parameters.AddWithValue("@SourceTenantId", sTenantId);
                    cmd.Parameters.AddWithValue("@DestinationTenantId", dTenantId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "CopyTenantUserMarkers", _tenantId));
                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool AcceptLicenseAgreement(int userNum, string userName, string userEmail, bool isLicenseAccepted, DateTime createdDate)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.ACCEPT_LICENSE_AGREEMENT;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserName", userName);
                    cmd.Parameters.AddWithValue("@UserEmail", userEmail);
                    cmd.Parameters.AddWithValue("@IsLicenseAccepted", isLicenseAccepted);
                    cmd.Parameters.AddWithValue("@CreatedDate", createdDate);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "AcceptLicenseAgreement", _tenantId));
                    return cmd.ExecuteNonQuery() > 0;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool AuthenticateUser(string email, string password)
        {
            bool isValidAccount = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_Account WHERE UserID = @Email AND (UserPW = @Password COLLATE Latin1_General_CS_AS )";
                    cmd.Parameters.AddWithValue("@Email", email);
                    cmd.Parameters.AddWithValue("@Password", password);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "AuthenticateUser", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            isValidAccount = true;
                        }
                    }
                }
            }
            catch (SqlException sqe)
            {
                throw sqe;
            }
            catch (Exception ex) { throw ex; }
            return isValidAccount;
        }

        #region Logedin User

        public User GetUserInformation(string uId, bool IsADUser, int tenantId, bool forcePasswordChange, bool isRoleBasedAccessEnabled, out List<Recorder> recorders, out bool isChainDBsConfigured, out bool isInquireEnabled, out bool isECEnabled, out bool isEnterpriseRecorder, out List<int> sttEnabledChannels, out bool isLocked, out bool isUserExists, out bool isUserAgreedToLicense)
        {
            User loginUser = null;
            UserGroup uGroup = null;
            List<Permission> permissions = null;
            List<RolePermission> rolePermissions = null;
            List<Recorder> recs = null;
            string sAccessRight = null;
            string originalSimpleAccessRight = "**********";
            string sAssignedNodes = null;
            bool isChainDB = false;
            bool isInquire = false;
            List<int> sttChannels = new List<int>();
            isECEnabled = false;
            isEnterpriseRecorder = false;
            bool isUserLocked = false;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GET_LOGIN_USER_WITH_EMAIL;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@UserID", uId);
                    cmd.Parameters.AddWithValue("@ForcePasswordChange", forcePasswordChange);
                    cmd.Parameters.AddWithValue("@IsUserExists", false);
                    cmd.Parameters.AddWithValue("@IsUserAgreedToLicense", false);
                    cmd.Parameters.AddWithValue("@IsRoleBasedAccessEnabled", isRoleBasedAccessEnabled);
                    cmd.Parameters["@IsUserExists"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@IsUserAgreedToLicense"].Direction = ParameterDirection.Output;
                    if (IsADUser)
                        cmd.Parameters.AddWithValue("@IsADUser", 1);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetUserInformation", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        loginUser = ORMapper.MapLoginUser(dr);
                        dr.NextResult();
                        uGroup = ORMapper.MapLoginUserGroup(dr);
                        dr.NextResult();
                        sAccessRight = ORMapper.MapSimpleAccessRights(dr, out originalSimpleAccessRight);
                        dr.NextResult();
                        sAssignedNodes = ORMapper.MapAssignedNodes(dr);
                        dr.NextResult();
                        permissions = ORMapper.MapPermissions(dr);
                        dr.NextResult();
                        recs = ORMapper.MapRecorders(dr);
                        if (loginUser != null)
                        {
                            loginUser.GroupNum = uGroup == null ? 0 : uGroup.GroupNum;
                            loginUser.UserID = uId;
                            loginUser.UserGroup = uGroup == null ? new UserGroup { UserNum = loginUser.UserNum, AssignAuth = "**********" } : uGroup;
                            loginUser.SimpleAccessRight = sAccessRight;
                            loginUser.OriginalSimpleAccessRight = originalSimpleAccessRight;
                            if (loginUser.UserGroup.AssignAuth == "")
                                loginUser.UserGroup.AssignAuth = "**********";
                            loginUser.Permissions = this.GetPermissions(loginUser.UserNum, loginUser.SelectType == 0 ? loginUser.UserGroup.AssignAuth : loginUser.SimpleAccessRight, permissions);
                            loginUser.AssignedNodes = sAssignedNodes != null ? sAssignedNodes.Split(',').ToList() : new List<string>();
                            loginUser.IsDeviceUser = loginUser.IsDeviceUser;
                        }
                        //Chain DB's Existis ResultSet
                        dr.NextResult();
                        if (dr.Read())//(dr.HasRows)
                        {
                            isChainDB = Convert.ToInt32(dr["DBCount"]) > 1 ? true : false;
                        }
                        // IsInquire
                        dr.NextResult();
                        if (dr.Read())
                            isInquire = Convert.ToBoolean(dr["IsInquire"]);
                        // User Recorder Permissions
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            loginUser.RecorderAccessRights = new List<RecorderAccessRight>();
                            while (dr.Read())
                            {
                                loginUser.RecorderAccessRights.Add(new RecorderAccessRight { RecId = Convert.ToInt32(dr["RecId"]), AccessRight = Convert.ToString(dr["AccessRight"]) });
                            }
                        }
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            List<int> channels = new List<int>();
                            while (dr.Read())
                            {
                                channels.Add(Convert.ToInt32(dr["STTEnabledChannels"]));
                            }
                            sttChannels = channels;
                        }
                        //Last Password Change. ResultSet
                        dr.NextResult();
                        if (dr.Read())
                        {
                            loginUser.LastPasswordChanged = Convert.ToDateTime(dr["DateChanged"]);
                        }

                        //Is User LockedOut. ResultSet
                        dr.NextResult();
                        if (dr.Read())
                        {
                            isUserLocked = Convert.ToBoolean(dr["IsLockedOut"]);
                        }

                        // User Role and RolePermissions
                        dr.NextResult();
                        rolePermissions = ORMapper.MapRolePermissions(dr);
                        if (loginUser != null)
                        {
                            loginUser.RolePermissions = new List<RolePermission>();
                            loginUser.RolePermissions = rolePermissions;
                        }

                        dr.NextResult();
                        if (dr.Read())
                        {
                            if (loginUser != null)
                            {
                                loginUser.Role = new Role();
                                loginUser.Role.Id = Convert.ToInt32(dr["Id"]);
                                loginUser.Role.Name = Convert.ToString(dr["Name"]);
                                loginUser.Role.RoleType = Convert.ToInt32(dr["RoleType"]);
                                loginUser.Role.IsSystemRole = Convert.ToBoolean(dr["IsSystemRole"]);
                                loginUser.Role.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                            }
                        }
                    }
                    isUserExists = Convert.ToBoolean(cmd.Parameters["@IsUserExists"].Value);
                    isUserAgreedToLicense = Convert.ToBoolean(cmd.Parameters["@IsUserAgreedToLicense"].Value);
                }
            }
            catch (SqlException sqe)
            {
                throw sqe;
            }
            catch (Exception ex) { throw ex; }
            recorders = recs;
            isChainDBsConfigured = isChainDB;
            isInquireEnabled = isInquire;
            sttEnabledChannels = sttChannels;
            isLocked = isUserLocked;
            return loginUser;
        }

        #endregion


        #region IWB

        public bool IwbAddExtension(User _user)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.IWB_ADD_EXTENSION;
                    cmd.Parameters.AddWithValue("@extension", _user.Ext);
                    cmd.Parameters.AddWithValue("@usernum", _user.UserNum);
                    cmd.Parameters.AddWithValue("@ChannelType", 3);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "IwbAddExtension", _tenantId));


                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        #endregion

        public List<User> GetAvailableAutoReportRecipients()
        {
            List<User> users = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.AutoReportRecipient.AUTOREPORTRECIPIENT_GET_AVAILABLE;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.AutoReportRecipient, "GetAvailableAutoReportRecipients", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapAllUsersWithPermission(dr);
                        dr.NextResult();
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return users;
        }
    }
}