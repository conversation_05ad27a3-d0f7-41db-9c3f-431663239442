<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.ViewModelEntities" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd10" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Response" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd17" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.EvaluationEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd15" namespace="http://schemas.datacontract.org/2004/07/System" />
  <xs:complexType name="ArrayOfCustomMarkersData">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomMarkersData" nillable="true" type="tns:CustomMarkersData" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfCustomMarkersData" nillable="true" type="tns:ArrayOfCustomMarkersData" />
  <xs:complexType name="CustomMarkersData">
    <xs:sequence>
      <xs:element minOccurs="0" name="ExistingMarker" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Ext" type="xs:int" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IsGroup" type="xs:boolean" />
      <xs:element minOccurs="0" name="Level" type="xs:int" />
      <xs:element minOccurs="0" name="MarkerNote" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Markers" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ParentId" type="xs:int" />
      <xs:element minOccurs="0" name="RevSyncServerUserNum" type="xs:int" />
      <xs:element minOccurs="0" name="SNo" type="xs:int" />
      <xs:element minOccurs="0" name="UserNum" type="xs:int" />
      <xs:element minOccurs="0" name="customMarkerId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CustomMarkersData" nillable="true" type="tns:CustomMarkersData" />
  <xs:complexType name="ArrayOfTreeviewData">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="TreeviewData" nillable="true" type="tns:TreeviewData" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfTreeviewData" nillable="true" type="tns:ArrayOfTreeviewData" />
  <xs:complexType name="TreeviewData">
    <xs:sequence>
      <xs:element minOccurs="0" name="ChannelType" type="xs:int" />
      <xs:element minOccurs="0" name="Childrens" nillable="true" type="tns:ArrayOfTreeviewData" />
      <xs:element minOccurs="0" name="Depth" type="xs:int" />
      <xs:element minOccurs="0" name="GroupName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GroupNumber" type="xs:int" />
      <xs:element minOccurs="0" name="IsGroup" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRevAgentAssociated" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRevCell" type="xs:boolean" />
      <xs:element minOccurs="0" name="MenuType" type="xs:int" />
      <xs:element minOccurs="0" name="NodeCaption" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="NodeId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Param1" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Param2" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Param3" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ParentNodeId" type="xs:int" />
      <xs:element minOccurs="0" name="RecId" type="xs:int" />
      <xs:element minOccurs="0" name="ViewType" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TreeviewData" nillable="true" type="tns:TreeviewData" />
  <xs:complexType name="ArrayOfGroupTree">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="GroupTree" nillable="true" type="tns:GroupTree" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfGroupTree" nillable="true" type="tns:ArrayOfGroupTree" />
  <xs:complexType name="GroupTree">
    <xs:sequence>
      <xs:element minOccurs="0" name="CssName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DbNodesAudio" nillable="true" type="tns:TreeviewData" />
      <xs:element minOccurs="0" name="DbNodesInquire" nillable="true" type="tns:TreeviewData" />
      <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="GroupType" type="q1:GroupType" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="TreeviewData" nillable="true" type="tns:TreeviewData" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GroupTree" nillable="true" type="tns:GroupTree" />
  <xs:complexType name="ArrayOfRecorderGroupTree">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RecorderGroupTree" nillable="true" type="tns:RecorderGroupTree" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRecorderGroupTree" nillable="true" type="tns:ArrayOfRecorderGroupTree" />
  <xs:complexType name="RecorderGroupTree">
    <xs:sequence>
      <xs:element minOccurs="0" name="AudioGroup" nillable="true" type="tns:GroupTree" />
      <xs:element minOccurs="0" name="DbNodes" nillable="true" type="tns:ArrayOfTreeviewData" />
      <xs:element minOccurs="0" name="DbNodesAudio" nillable="true" type="tns:ArrayOfTreeviewData" />
      <xs:element minOccurs="0" name="DbNodesInquire" nillable="true" type="tns:ArrayOfTreeviewData" />
      <xs:element minOccurs="0" name="GroupsTree" nillable="true" type="tns:ArrayOfGroupTree" />
      <xs:element minOccurs="0" name="InquireGroup" nillable="true" type="tns:GroupTree" />
      <xs:element minOccurs="0" name="RecorderId" type="xs:int" />
      <xs:element minOccurs="0" name="RecorderName" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RecorderGroupTree" nillable="true" type="tns:RecorderGroupTree" />
  <xs:complexType name="ArrayOfRecorderUserOld">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RecorderUserOld" nillable="true" type="tns:RecorderUserOld" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRecorderUserOld" nillable="true" type="tns:ArrayOfRecorderUserOld" />
  <xs:complexType name="RecorderUserOld">
    <xs:sequence>
      <xs:element minOccurs="0" name="RecorderId" type="xs:int" />
      <xs:element minOccurs="0" name="RecorderName" nillable="true" type="xs:string" />
      <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Response" minOccurs="0" name="UserManagementResponse" nillable="true" type="q2:UserManagementResponse" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RecorderUserOld" nillable="true" type="tns:RecorderUserOld" />
  <xs:complexType name="ArrayOfGridViewData">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="GridViewData" nillable="true" type="tns:GridViewData" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfGridViewData" nillable="true" type="tns:ArrayOfGridViewData" />
  <xs:complexType name="GridViewData">
    <xs:sequence>
      <xs:element minOccurs="0" name="Ext" type="xs:int" />
      <xs:element minOccurs="0" name="SNo" type="xs:int" />
      <xs:element minOccurs="0" name="UserName" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GridViewData" nillable="true" type="tns:GridViewData" />
  <xs:complexType name="RecorderEvaluation">
    <xs:sequence>
      <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.EvaluationEntities" minOccurs="0" name="CallEvaluation" nillable="true" type="q3:CallEvaluation" />
      <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="CallEvaluationDTO" nillable="true" type="q4:ArrayOfCallEvaluationDTO" />
      <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/System" minOccurs="0" name="EvalsByCampaigns" nillable="true" type="q5:ArrayOfTupleOfintstringintstringstring" />
      <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/System" minOccurs="0" name="EvalsByEvaluators" nillable="true" type="q6:ArrayOfTupleOfintstringintstringstring" />
      <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/System" minOccurs="0" name="EvalsByStatus" nillable="true" type="q7:ArrayOfTupleOfintstringintstringstring" />
      <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/System" minOccurs="0" name="EvalsByTopScorers" nillable="true" type="q8:ArrayOfTupleOfintstringintstringstring" />
      <xs:element xmlns:q9="http://schemas.datacontract.org/2004/07/System" minOccurs="0" name="MultiCallEvalsByStatus" nillable="true" type="q9:ArrayOfTupleOfintstringintstringstring" />
      <xs:element minOccurs="0" name="PageSize" type="xs:int" />
      <xs:element minOccurs="0" name="RecorderId" type="xs:int" />
      <xs:element minOccurs="0" name="RecorderName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TotalPages" type="xs:int" />
      <xs:element minOccurs="0" name="TotalRecords" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RecorderEvaluation" nillable="true" type="tns:RecorderEvaluation" />
</xs:schema>