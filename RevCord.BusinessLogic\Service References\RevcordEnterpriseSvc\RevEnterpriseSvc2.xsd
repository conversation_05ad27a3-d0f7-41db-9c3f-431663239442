<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Response" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Response" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd8" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd11" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.RoleManagement" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd6" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.MessageBase" />
  <xs:complexType name="UserManagementResponse">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q1="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.MessageBase" base="q1:ResponseBase">
        <xs:sequence>
          <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="AppUserAccess" nillable="true" type="q2:AppUserAccess" />
          <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="AppUsers" nillable="true" type="q3:ArrayOfAppUser" />
          <xs:element minOccurs="0" name="ChannelType" type="xs:int" />
          <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="EventInvitation" nillable="true" type="q4:EventInvitation" />
          <xs:element minOccurs="0" name="FlagStatus" type="xs:boolean" />
          <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="GlobalGroups" nillable="true" type="q5:ArrayOfGlobalGroup" />
          <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="GroupCategories" nillable="true" type="q6:ArrayOfGroupCategory" />
          <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="Invitation" nillable="true" type="q7:Invitation" />
          <xs:element minOccurs="0" name="InvitationId" type="xs:int" />
          <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="Invitations" nillable="true" type="q8:ArrayOfInvitation" />
          <xs:element minOccurs="0" name="IsChainDBsConfigured" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsECEnabled" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsEnterpriseRecorder" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsInquire" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsUserExists" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsUserLocked" type="xs:boolean" />
          <xs:element xmlns:q9="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="Last5Passwords" nillable="true" type="q9:ArrayOfstring" />
          <xs:element xmlns:q10="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="LiteUsers" nillable="true" type="q10:ArrayOfUserInfoLite" />
          <xs:element xmlns:q11="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="LoginUser" nillable="true" type="q11:User" />
          <xs:element xmlns:q12="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="Permissions" nillable="true" type="q12:ArrayOfPermission" />
          <xs:element xmlns:q13="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="Recorders" nillable="true" type="q13:ArrayOfRecorder" />
          <xs:element xmlns:q14="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.RoleManagement" minOccurs="0" name="Roles" nillable="true" type="q14:ArrayOfRole" />
          <xs:element xmlns:q15="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="STTEnabledChannels" nillable="true" type="q15:ArrayOfint" />
          <xs:element xmlns:q16="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.DTO" minOccurs="0" name="TreeViewDataDTOs" nillable="true" type="q16:ArrayOfTreeViewDataDTO" />
          <xs:element xmlns:q17="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="User" nillable="true" type="q17:User" />
          <xs:element xmlns:q18="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.VoiceRecEntities" minOccurs="0" name="UserExtensionInfos" nillable="true" type="q18:ArrayOfUserExtensionInfo" />
          <xs:element minOccurs="0" name="UserId" type="xs:int" />
          <xs:element xmlns:q19="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.UserManagement" minOccurs="0" name="Users" nillable="true" type="q19:ArrayOfUser" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UserManagementResponse" nillable="true" type="tns:UserManagementResponse" />
</xs:schema>