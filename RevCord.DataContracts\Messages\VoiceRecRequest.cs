﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.MessageBase;

namespace RevCord.DataContracts.Request
{
    /// <summary>
    /// Added to Handle All the requests regarding VoiceRec DB
    /// </summary>
    public class VoiceRecRequest : RequestBase
    {
        /************* Purpose: used for Paging ****************/
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        /*******************************************************/
        public int UserId { get; set; }

        public bool UseGroup { get; set; }
        public bool UseAdvanceSearch { get; set; }

        public List<string> GroupExtensions { get; set; }

        public List<ExtensionCallInfo> ExtensionCallInfos { get; set; }

        public bool GetAllChannels { get; set; }

        public short MaxPlaylistItem { get; set; }

        #region Simple User Parameters

        public int UserType { get; set; }

        public string SimpleUserSearchValues { get; set; }

        #endregion


        #region Association

        public CallInfoSearchCriteriaDTO SearchCriteria { get; set; }
        public Playlist Playlist { get; set; }
        public Bookmark Bookmark { get; set; }
        public CallInfo CallInfo { get; set; }
        public ActionType Operation { get; set; }
        
        public SimpleUserSearchCriteria SimpleUserSearchCriteria { get; set; }
        public Recorder Recorder { get; set; }

        #endregion


        public string TableFieldCharSet { get; set; } // used this value to pass to SP sp_getFieldsHead
        public int ViewId { get; set; } // used this value to pass to SP sp_getFieldsHead
    }
}
