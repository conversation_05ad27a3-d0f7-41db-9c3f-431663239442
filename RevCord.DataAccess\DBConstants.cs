﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataAccess
{
    public static class DBConstants
    {
        public struct Survey
        {
            public const string SURVEY_GETLIST = "em_Survey_GetAll";
            public const string SURVEY_GETDETAIL = "em_Survey_GetDetailById";
            public const string SURVEY_DELETE_N_GET = "em_Survey_DeleteAndGetAll";
            public const string SURVEY_INSERT = "em_Survey_Insert";
            public const string SURVEY_UPDATE = "em_Survey_Update";
            public const string SURVEY_PUBLISH_N_GET = "em_Survey_PublishAndGetAll";
            public const string SURVEY_UPDATE_ISPUBLISH = "em_Survey_Insert_IsPublished"; // For QA-Published Forms Edit/Delete ARIVU

            public const string QUESTION_GETBYSURVEYID = "em_Question_GetBySurveyId";
            public const string QUESTION_GETBYSECTIONID = "em_Question_GetBySectionId";
            public const string QUESTION_DELETE = "em_Question_Delete";
            public const string QUESTION_INSERT = "em_Question_Insert";
            public const string QUESTION_UPDATE = "em_Question_Update";
            public const string QUESTION_UPDATEORDER = "em_Question_UpdateOrder";
            public const string QUESTION_UPDATESECTIONS = "em_Question_UpdateSections";
            public const string QUESTION_OPTIONS_GETBYQID = "em_QuestionOptions_GetByQID"; //For QA-Published Forms Edit/Delete ARIVU

            public const string SECTION_GETBYSURVEYID = "em_SurveySection_GetBySurveyId";
            public const string SECTION_DELETE_N_GET = "em_SurveySection_DeleteAndGet";
            public const string SECTION_INSERT = "em_SurveySection_Insert";
            public const string SECTION_INSERT_N_GET = "em_SurveySection_InsertAndGet";
            public const string SECTION_UPDATE_N_GET = "em_SurveySection_UpdateAndGet";
        }

        public struct RoleManagement
        {
            public const string ROLE_Add = "umRoleAdd";
            public const string ROLE_GETALL = "umGetALLRole";
            public const string ROLE_GETROLE_BYID = "umGetRoleById";
            public const string ROLE_DELETEROLE_BYID = "umDeleteRoleById";
            public const string ROLE_EDITROLE_BYID = "umEditRoleById";

            public const string RolePERMISSION_AddForMapping = "umAddRolePermissionForMapping";
            public const string UPDATE_ROLE_PERMISSIONS = "rmUpdateRolePermissions";
            public const string RolePERMISSION_GETPermissionListByID = "umgetPermissionListByRoleId";
            public const string RolePERMISSION_GETCHANNELPermissionListByID = "umgetChannelPermissionListByRoleId";
            public const string RolePERMISSION_UPDATE_CHANNEL_BASED = "um_CB_Role_Permissions";

            public const string UPDATE_ROLE_Channel_PERMISSIONS = "rmUpdateRoleChannelPermissions";

        }

        public struct PermissionManagement
        {
            public const string PERMISSION_Add = "umPermissionAdd";
            public const string PERMISSION_GETALL = "umGetALLPermission";
            public const string PERMISSION_GETPERMISSIONE_BYID = "umGetPermissionById";
            public const string PERMISSION_DELETEPERMISSION_BYID = "umDeletePermissionById";
            public const string PERMISSION_EDITPERMISSION_BYID = "umEditPermissionById";
        }

        public struct Evaluation
        {
            public const string CALLEVALUATIONDTO_GETALL_BY_WHERE_CLAUSE = "em_CallEvaluationDTO_GetByWhereClause";
            public const string CALLEVALUATIONDTO_GETALL_BY_WHERE_CLAUSE_EC = "em_CallEvaluationDTO_GetByWhereClause_EC";
            public const string CALLEVALUATIONDTO_GETCLOSED_BY_AGENT_ID = "em_CallEvaluationDTO_GetClosedByAgentId";
            public const string CALLEVALUATIONDTO_DO_ACTION_N_GETALL_BY_WHERE_CLAUSE = "em_CallEvaluation_DoActionAndGetDTO";//internally call "em_CallEvaluationDTO_GetByWhereClause";
            public const string CALLEVALUATIONDTO_UPDATE_N_GETALL_BY_WHERE_CLAUSE = "em_CallEvaluation_UpdateSatusAndGetDTO";
            public const string CALLEVALUATION_DELETE = "em_CallEvaluation_Delete";

            public const string CALLEVALUATION_GET_DETAILS_BY_ID = "em_CallEvaluation_GetDetailsById";
            public const string CALLEVALUATION_UPDATE = "em_CallEvaluation_Update";
            public const string CALLEVALUATION_UPDATE_ASSOCIATED_AGENT = "em_CallEvaluation_Update_Associated_Agent";

            public const string RPT_CALLEVALUATION_GET_DETAILS_BY_SURVEYID = "rpt_CallEvaluation_GetDetailsBySurveyId";
            public const string RPT_CALLEVALUATION_GET_BY_SURVEYID = "rpt_CallEvaluation_GetBySurveyId";
            public const string RPT_CALLEVALUATION_MAIN_GET_BY_SURVEYID = "rpt_CallEvaluationMain_GetBySurveyId";

            public const string CALLEVALUATION_INSERT_SINGLE = "em_CallEvaluation_InsertOne";
            public const string CALLEVALUATION_INSERT_SEGMENTED = "em_CallEvaluation_Insert_Segmented";
            public const string CALLEVALUATION_GET_SEGMENTED_CALLS = "em_CallEvaluation_Get_Segmented";

            public const string CALLEVALUATIONFEEDBACK_INSERT = "em_CallEvaluationFeedback_Insert";
            public const string CALLEVALUATIONFEEDBACK_DELETE = "em_CallEvaluationFeedback_Delete";
            public const string CALLEVALUATIONFEEDBACK_GET_BY_AGENT_ID = "em_CallEvaluationFeedback_Get_By_Agent_Id";

            public const string CALLEVALUATION_GETALL_BY_SURVEYID = "em_CallEvaluation_GetDetailsBySurveyId"; //For QA-Published Forms Edit/Delete ARIVU
            public const string STATUSID_UPDATE_BY_SURVEYID = "em_UpdateStausId"; //For QA-Published Forms Edit/Delete ARIVU

            public const string CALLEVALUATIONDTO_DO_ACTION_EC_N_GETALL_BY_WHERE_CLAUSE = "em_CallEvaluation_DoActionAndGetDTO_EC";//internally call "em_CallEvaluationDTO_GetByWhereClause";
            public const string CALLEVALUATIONDTO_DO_ACTION_EC = "em_CallEvaluation_DoAction_EC";

            // User Evaluations
            public const string USER_EVALUATION_INSERT_SINGLE = "em_UserEvaluation_InsertOne";
            public const string USER_EVALUATION_INSERT = "em_UserEvaluation_Insert";
            public const string USER_EVALUATION_GET_DETAILS_BY_ID = "em_UserEvaluation_GetDetailsById";
            public const string USER_EVALUATION_UPDATE = "em_UserEvaluation_Update";
            public const string USER_EVALUATION_UPDATE_STATUS = "em_UserEvaluation_UpdateSatus";
            public const string USER_EVALUATION_GETALL_BY_WHERE_CLAUSE = "em_UserEvaluationDTO_GetByWhereClause";
            public const string GET_EVALUATION_ID = "em_GetEvaluationId"; //Arivu

            // Enterprise Evaluation
            public const string ENTERPRISE_EVALUATION_INSERT = "em_Enterprise_Evaluation_Insert";

            public const string CALLS_SEARCH_MEDIA_PRIMARY_DB = "vr_Call_SearchMedia_QA_Primary";
            public const string CALLS_SEARCH_MEDIA_SECONDARY_DBS = "vr_Call_SearchMedia_QA_SecondaryDBs";
            public const string CALLS_SEARCH_MEDIA_SECONDARY_DBS_2N = "vr_Call_SearchMedia_QA_SecondaryDBs2N";
        }

        public struct Charts
        {
            public const string CALLEVALUATION_BY_STATUS = "rpt_CallEvaluation_By_Status";
            public const string CallEVALUATION_BY_CAMPAIGN = "rpt_CallEvaluation_By_Campaign";
            public const string CallEVALUATION_BY_EVALUATOR = "rpt_CallEvaluation_By_Evaluator";
            public const string CALLEVALUATION_BY_TOPSCORERS = "rpt_CallEvaluation_By_TopScorers";
            public const string EVALUATION_BY_STATUS = "rpt_Evaluation_By_Status";
            public const string DASHBOARD_GETALL = "em_Dashboard_GetAll";

            public const string MULTICALL_EVALUATION_BY_STATUS = "rpt_MultiCallEvaluation_By_Status";

        }

        public struct VoiceRec
        {
            public const string CALLS_SEARCH_AND_GET_PRIMARY_DB = "vr_Call_SearchPrimary";
            public const string CALLS_SEARCH_AND_GET_PRIMARY_DB_LITE = "vr_Call_SearchPrimary_Lite";
            public const string CALLS_SEARCH_AND_GET_CHAIN_DB = "vr_Call_SearchSecondaryDBs";
            public const string CALLS_SEARCH_AND_GET_RANDOM_CHAIN_DB = "vr_Call_SearchSecondaryDBs_Random";
            public const string CALLS_SEARCH_FOR_EXPORT_DATA_IN_CHAIN_DB = "vr_Call_SearchSecondaryDBsForExportData";
            public const string CALLS_UPDATE_CUSTOM_FIELDS_CHAIN_DB = "vr_Call_UpdateCustomFieldsChainDBs";
            public const string CALL_GET_BY_ID_PRIMARY_DB = "vr_Call_GetByIdPrimary";
            public const string CALLS_SEARCH_AND_GET_RANDOM_PRIMARY_DB = "vr_Call_SearchPrimary_Random";
            public const string CALLS_SEARCH_AND_GET_EXPORT_RESULT = "vr_Call_Search_Export_Result";
            public const string EVENTS_GET_BY_ASSET_ID = "vr_Call_GetEventsByAssetId";

            public const string CALLS_SEARCH_MEDIA_PRIMARY_DB = "vr_Call_SearchMedia_Primary";
            public const string CALLS_SEARCH_MEDIA_PRIMARY_DB_BYEVENTID = "vr_Call_SearchMedia_Primary_ByEventId";
            public const string CALLS_SEARCH_MEDIA_SECONDARY_DBS = "vr_Call_SearchMedia_SecondaryDBs";
            public const string CALLS_SEARCH_MEDIA_SECONDARY_DBS_2N = "vr_Call_SearchMedia_SecondaryDBs2N";

            public const string CALL_TRANSCRIPT_INSERT = "vr_CallTranscript_Insert";
            public const string CALLS_GET_BY_EVENT_ID = "vr_CallInfo_GetByEventId";
            public const string EVENT_GET_BY_EVENT_ID = "vr_CallInfo_EventGetByEventId";

            public const string BOOKMARK_INSERT = "sp_Add_BookMark";
            public const string BOOKMARK_UPDATE = "vr_BookMark_Update";
            public const string BOOKMARK_INSERT_N_GET = "vr_BookMark_InsertAndGetAll";
            public const string BOOKMARK_UPDATE_N_GET = "vr_BookMark_UpdateAndGetAll";

            public const string CHANNELS_MONITOR_GETBY_WHERECLAUSE = "vr_Channels_GetByWhereClause";
            public const string AUDIO_CHANNELS_GETLIST = "vr_AudioChannels_GetAll";
            public const string AUDIO_CHANNEL_GETBYID = "vr_AudioChannels_GetById";
            public const string AUDIO_CHANNEL_INSERT = "vr_AudioChannels_Insert";
            public const string AUDIO_CHANNEL_UPDATE = "vr_AudioChannels_Update";

            public const string TEAMS_CHANNELS_GETLIST = "vr_TeamsChannels_GetAll";
            public const string TEAMS_CHANNEL_GETBYID = "vr_TeamsChannels_GetById";
            public const string TEAMS_CHANNEL_INSERT = "vr_TeamsChannels_Insert";
            public const string TEAMS_CHANNEL_UPDATE = "vr_TeamsChannels_Update";
            //
            public const string CALLS_CREATE_TABLE_AND_SEARCH = "sp_EvalCreateCallSearchResults";
            public const string CALLS_SEARCH_AND_GET = "vr_CallInfo_GetByWhereClause";
            public const string CALLS_SEARCH_AND_GET_PAGED = "vr_CallInfo_GetByWhereClausePaged";
            public const string CALLS_SEARCH_CHAINEDDB_AND_GET_PAGED = "vr_CallInfo_GetSearchResultsPaged";
            public const string CALLS_SEARCH_AND_GET_BY_LOCATION = "vr_CallInfo_GetByLocations";
            public const string CALLS_SEARCH_AND_GET_PAGED_DEMO = "wrapper_CallInfo_GetByWhereClausePaged";

            public const string CALLS_GET_BY_COMMA_SEPERATED_IDS = "vr_CallInfo_GetByIds";
            public const string CALLS_ONLY_GET_BY_COMMA_SEPERATED_IDS = "vr_CallInfo_GetByCsIds";
            public const string CALL_GET_BY_ID = "vr_CallInfo_GetById";

            public const string EXPORT_RESULTS_GET_BY_COMMA_SEPERATED_IDS = "vr_ExportResults_GetByIds";

            public const string CALLS_SEARCH = "sp_EvalGetCalls";
            public const string CALLS_GETBY_COMMA_SEPERATED_IDS = "sp_EvalGetCallsByCallIds";
            public const string CALLS_CUSTOM_FIELDS_UPDATE = "vr_CallInfo_UpdateCustomField";
            public const string CALLS_CUSTOM_FIELDS_MULTIPLE_UPDATE = "vr_CallInfo_UpdateCustomFields";
            public const string CALLS_RETAIN_VALUE_UPDATE = "vr_CallInfo_RetainValue";
            public const string CALLS_INQUIRE_MARKER_UPDATE = "vr_CallInfo_Inquire_marker_update";


            public const string PLAYLIST_INSERT = "vr_Playlist_Insert";
            public const string PLAYLIST_UPDATE = "vr_Playlist_Update";
            public const string PLAYLIST_DELETE = "vr_Playlist_Delete";
            public const string PLAYLIST_GETLIST_BY_USERID = "vr_Playlist_GetByUser";
            public const string REPO_LIST_BY_USERID = "sp_GetRepoList";
            public const string PLAYLIST_GETBYID = "vr_Playlist_GetById";
            public const string PLAYLIST_NAME_UPDATE = "vr_Playlist_Name_Update";
            public const string PLAYLIST_UPDATE_ATTRIBUTES = "vr_Playlist_Update_Attributes";
            public const string PLAYLIST_ALTERNATE_FILENAME_ADD = "vr_Playlist_Alternate_FileName_Add";

            public const string PLAYLIST_DETAILS_INSERT = "vr_PlaylistDetails_Insert";
            public const string PLAYLIST_DETAILS_INSERT_SINGLE_ITEM = "vr_PlaylistDetails_InsertItem";
            public const string PLAYLIST_DETAILS_BY_ID = "vr_PlaylistDetails_GetById";
            public const string PLAYLIST_DETAILS_BY_PLAYLISTID = "vr_PlaylistDetails_GetByPlId";
            public const string PLAYLIST_DETAILS_DELETE_BY_PLAYLIST_ID = "vr_PlaylistDetails_DeleteByPlaylistId";
            public const string PLAYLIST_DETAILS_DELETE_BY_ID = "vr_PlaylistDetails_DeleteById";
            public const string PLAYLIST_DETAILS_DELETE_BY_CALLID = "vr_PlaylistDetails_DeleteByCallId";
            public const string PLAYLIST_DETAILS_DELETE_BY_PLID_N_CALLID = "vr_PlaylistDetails_DeleteByPlIdnCallId";

            public const string PLAYLIST_NOTE_GETALL = "vr_Playlist_Note_GetAll";
            public const string PLAYLIST_NOTE_INSERT = "vr_PlaylistNote_Insert";
            public const string PLAYLIST_NOTE_UPDATE = "vr_PlaylistNote_Update";
            public const string PLAYLIST_NOTE_DELETE = "vr_PlaylistNote_Delete";

            public const string PLAYLIST_SHARE_INSERT = "vr_PlaylistShare_Insert";

            // AD SyncGroups
            public const string ADSYNCGROUP_INSERT = "vr_ADSyncGroup_Insert";



            //public const string CHANNELS_MONITOR_GET = "vr_MonitorChannels_GetAll";
            public const string CHANNELS_MONITOR_GET = "vr_Channels_GetAll";

            public const string SIMPLE_USER_SEARCH_CRITERIA = "vr_SimpleUser_SearchCriteria"; //

            public const string CHECK_QB_DIALOG_EXISTS = "vr_Check_QB_Dialog_Exists";
            public const string ADD_QB_DIALOG_DETAILS = "vr_Add_QB_Dialog_Details";

            public const string EVENTS_GET_CHAT_TRANSCRIPT = "vr_ChatTranscript_GetByEventId";

            public const string ONSITE_CONTACT_INFO_CONFIRMATION_UPDATE = "vr_OnsiteContactInfo_Confirmation_Update";
            public const string ONSITE_CONTACT_INFO_CONFIRMATION_GET = "vr_OnsiteContactInfo_Confirmation_Get";

            public const string MTR_JSON_INSERT = "vr_MTRJson_Insert";
            public const string GETMTR_SIGNOFFHISTORTY = "vr_GetMTR_SignOffHistory";
            public const string MTR_CHECKLIST_INSERT = "vr_MTR_Checklist_Insert";

            public const string PLAYLIST_DOCUMENT_ADD = "PRO_AddDocumentToPlayList";
        }

        public struct RevSign
        {
            public const string REVSIGN_GET_DOCUMENTS = "rs_GetDocuments";
            public const string REVSIGN_GET_DOCUMENT_AND_SIGNER_BY_ID = "rs_GetDocument_And_Signer_By_Id";
            public const string REVSIGN_GET_DOCUMENT_BY_ID = "rs_GetDocument_By_Id";
            public const string REVSIGN_DELETE_DOCUMENT = "rs_Document_Delete";
            public const string REVSIGN_SAVE_SIGNOFF_REQUEST = "rs_Document_Insert";
            public const string REVSIGN_SAVE_SIGNOFF_REQUEST_SIGNER = "rs_Document_Signer_Insert";


            public const string REVSIGN_GET_SIGNED_DOCUMENTS = "rs_Get_Sign_DocumentInfo";
            public const string REVSIGN_SAVE_DOCUMENTS_AGREED = "rs_Save_Document_Agreed";
            public const string REVSIGN_SAVE_DOCUMENTS_SIGNED = "rs_Save_Document_Signed";
            public const string REVSIGN_GET_DOCUMENTS_SIGNED = "rs_Get_Signed_DocumentInfo";
            public const string ASSOCIATE_REVSIGN_AND_IWB_DOCUMENT = "rs_AssociateRevsignAndIwbDocument";

        }

        public struct ConditionalLogic
        {
            public const string CONDITIONALLOGIC_SAVE_MARKER_LOGIC = "cl_MarkerLogic_Insert";
            public const string CONDITIONALLOGIC_SAVE_LOGIC_TRIGGER = "cl_LogicTrigger_Insert";
            public const string CONDITIONALLOGIC_SAVE_TRIGGER_ACTION = "cl_TriggerAction_Insert";
            public const string CONDITIONALLOGIC_DELETE_MARKER_LOGIC = "cl_MarkerLogic_Delete";
            public const string CONDITIONALLOGIC_DELETE_LOGIC_TRIGGER = "cl_LogicTrigger_Delete";

            public const string CONDITIONALLOGIC_UPDATE_MARKER_LOGIC = "cl_MarkerLogic_Update";
            public const string CONDITIONALLOGIC_UPDATE_LOGIC_TRIGGER = "cl_LogicTrigger_Update";
            public const string CONDITIONALLOGIC_UPDATE_TRIGGER_ACTION = "cl_TriggerAction_Update";

            public const string CONDITIONALLOGIC_UPDATE_MARKER_LOGIC_TYPE = "cl_MarkerLogic_Type_Update";
            public const string CONDITIONALLOGIC_UPDATE_MARKER_LOGIC_MATCH_TEXT = "cl_MarkerLogic_MatchText_Update";
        }

        public struct LiveMonitor
        {
            public const string GETALL_SCHEDULED_EVENTS = "lm_GetAll_Scheduled_Events";
            public const string GET_SCHEDULED_EVENT_DETAILS_BY_EVENTID = "lm_Get_ScheduledEventDetails_By_EventId";
            public const string GETALL_PARTICIPANTS = "lm_GetAll_Participants";
            public const string FETCH_PARTICIPANTS_BY_EVENTID = "lm_Fetch_Participants_By_EventId";
            public const string SAVE_SCHEDULED_EVENT = "lm_Schedule_Add";

            public const string UPDATE_SCHEDULED_EVENT = "lm_Schedule_Update";
            public const string SAVE_PARTICIPANT = "lm_Participant_Add";
            public const string DELETE_SCHEDULED_EVENT = "lm_Schedule_Delete";
            public const string CHECK_SCHEDULED_EVENT_INVITATION_STATUS = "lm_Check_Scheduled_Event_Status";
            public const string GET_ALL_USERS_WITH_LIVE_MONITOR_PERMISSION = "lm_Get_All_Users_With_Permission";

            public const string SAVE_ONDEMAND_EVENT = "lm_OnDemand_Save";
            public const string SAVE_ONDEMAND_EVENT_PARTICIPANTS = "lm_OnDemand_Save_Participant";
        }

        public struct SearchCriteria
        {
            public const string GENERIC_GETAUTOCOMPLETE = "cm_Generic_GetAutoComplete";
            public const string USER_SERACHES_INSERT = "um_UserSearch_Insert";
            public const string USER_SERACH_DETAIL_INSERT = "um_UserSearchDetail_Insert";
            public const string USER_SERACH_GETALLBY_USERID = "um_UserSearch_GetByUserId";
            public const string USER_SERACH_DETAIL_GET_BY_SEARCHID = "um_UserSearchDetail_GetBySearchId";
            public const string USER_SERACHES_DELETE = "um_UserSearch_Delete";
        }

        public struct MultiCallEvaluation
        {
            public const string MULT_CALL_EVALUATION_GETBY_WHERECLAUSE = "em_MultiCallEvaluation_GetBy_WhereClause";
            public const string MULTI_CALL_EVALUATION_GET_DETAILS = "em_MultiCallEvaluationDetails";
            public const string MULT_CALL_EVALUATION_INSERT = "em_MultiCallEvaluation_Insert";
            public const string MULT_CALL_EVALUATION_DELETE = "em_MultiCallEvaluation_Delete";
            public const string MULT_CALL_EVALUATION_MARK_CLOSED = "em_MultiCallEvaluation_MarkClosed";
        }

        public struct Iwb
        {
            public const string IWB_WPS_INSERT = "iwb_Wps_Insert";
            public const string IWB_WPS_GETBY_WHERECLAUSE = "iwb_Wps_GetByWhereClause";

            public const string IWB_DOCUMENT_INSERT = "iwb_Document_Insert";
            public const string IWB_GET_DOCUMENT_BY_ID = "iwb_Get_DocumentById";
            public const string IWB_DOCUMENT_SECTION_INSERT = "iwb_Document_Section_Insert";
            public const string IWB_DOCUMENT_SECTION_FIELD_INSERT = "iwb_Document_Section_Field_Insert";
            public const string IWB_DOCUMENTS_GETBY_WHERECLAUSE = "iwb_Document_GetByWhereClause";
            public const string IWB_DOCUMENTS_VERIFY = "iwb_Document_Verify";

            public const string IWB_MTR_DOCUMENT_INSERT = "iwb_MTR_Document_Insert";
            public const string IWB_MTR_DOCUMENT_SECTION_INSERT = "iwb_MTR_Document_Section_Insert";
            public const string IWB_MTR_DOCUMENT_SECTION_FIELD_INSERT = "iwb_MTR_Document_Section_Field_Insert";

            public const string IWB_WPS_DOCUMENT_INSERT = "iwb_Wps_Insert";
            public const string IWB_WPS_DOCUMENT_DETAIL_INSERT = "iwb_WPS_DocumentDetail_Insert";

            public const string IWB_WPQ_DOCUMENT_INSERT = "iwb_WPQ_Document_Insert";
            public const string IWB_WPQ_DOCUMENT_SECTION_INSERT = "iwb_WPQ_Document_Section_Insert";
            public const string IWB_WPQ_DOCUMENT_SECTION_FIELD_INSERT = "iwb_WPQ_Document_Section_Field_Insert";


            public const string IWB_JOB_INSERT = "iwb_Job_Insert";
            public const string IWB_JOB_Assign = "iwb_Job_Assign";
            public const string IWB_JOBS_GETBY_WHERECLAUSE = "iwb_Job_GetByWhereClause";
            public const string IWB_JOB_UPDATE_STATUS = "iwb_Job_UpdateStatus";
            public const string IWB_JOB_APPLY = "iwb_Job_Apply";
            public const string IWB_JOB_APPLICANTS = "iwb_Job_GetApplicants";
            public const string IWB_JOB_UPDATE_APPLICANT_STATUS = "iwb_Job_UpdateApplicantStatus";

            public const string IWB_WELDER_DASHBOARD = "iwb_Dashboard_GetWelderView";

            public const string IWB_WORK_HISTORY_GETBYUSER = "iwb_WorkHistory_GetByUser";
            public const string IWB_WORK_HISTORY_INSERT = "iwb_WorkHistory_Insert";

            public const string GET_JOB_BY_WELDER = "GetJobByWelder";

            public const string IWB_ORGANIZATION_GETBY_WHERECLAUSE = "iwb_Organization_GetByWhereClause";
            public const string IWB_ORGANIZATION_INSERT = "iwb_Organization_Insert";
            public const string IWB_ORGANIZATION_LOCATION_INSERT = "iwb_OrganizationLocation_Insert";

            public const string IWB_WPQ_INSERT = "iwb_Wpq_Insert";
            public const string IWB_WPQ_GETBY_USERID = "iwb_Wpq_GetByUser";
            public const string IWB_WPQ_GETBY_WHERECLAUSE = "iwb_Wpq_GetByWhereClause";
            public const string IWB_WPQ_GETLIST = "PRO_GetMyWPQList";
            public const string IWB_WPQ_GETDETAIL = "PRO_GetWPQDetail";

            public const string IWB_WELDER_GETBY_WHERECLAUSE = "iwb_Welder_GetByWhereClause";

            public const string IWB_GETSIGNOFF_REQUESTUSERS = "PRO_GetSignOffRequestUsers";

            public const string IWB_TEST_INSERT = "iwb_Test_Insert";
            public const string IWB_TEST_GETBY_WHERECLAUSE = "iwb_Test_GetByWhereClause";
            public const string IWB_TEST_ATTENDEES_GETBY_TESTID = "iwb_TestAttendees_GetByTestId";
            public const string IWB_TEST_ATTENDEES_INSERT = "iwb_TestAttendee_Insert";
            public const string IWB_TEST_UPDATE_STATUS = "PRO_UpdateTestStatus";
            public const string IWB_TEST_INVITATION_INSERT = "iwb_TestInvitation_Insert";



            public const string IWB_GET_CONTRACT_REPORT = "PRO_GetContractorReportData";
            public const string IWB_GET_OWNER_REPORT = "PRO_GetOwnerReportData";
            public const string IWB_GET_INSURANCE_REPORT = "PRO_GetInsuranceReportData";
            public const string IWB_GET_TESTING_REPORT = "PRO_GetTestingReportData";

            public const string IWB_GET_CONTRACTOR_WORK_HISTORY = "PRO_GetWorkerHistory";
            public const string IWB_GET_WELDER_WORK_HISTORY = "PRO_GetWelderHistory";
            public const string IWB_GET_WELDER_INFO = "PRO_GetWelderInfo";

            public const string IWB_INSERT_AI_RESPONSE = "PRO_SaveAIApiResponse";
            public const string IWB_UPDATE_WELDER_TEST_STATUS = "PRO_UpdateWelderTestStatus";

            public const string IWB_USER_INSERT = "iwb_User_Insert";

            public const string WELDERRATINGS_INSERT = "InsertWelderRating";
            public const string VIEW_WELDERRATINGS_GETBY_ID = "ViewWelderERatingsById";

            public const string GET_JOB_DETAILS_BY_ID = "GetJobDetailsById";
            public const string GET_JOB_TITLES = "GetJobTitles";
        }


        public struct UserManagement
        {
            public const string TREEVIEW_GET_GROUPBASED = "sp_GroupBased_Tree";
            public const string TREEVIEW_GET_CHANNELBASED = "sp_ChannelBased_Tree";
            public const string TREEVIEW_GET_ADMIN = "sp_Init_Tree";
            public const string TREEVIEW_GET_NON_ADMIN = "sp_MyExt_Tree";
            public const string TREEVIEW_GET_ADDITIONAL = "sp_Init_Tree";
            public const string TREEVIEW_GET_SIMPLE = "sp_MyExt_Tree";
            public const string ENTERPRISEUSER_GETNODEDATA = "um_EnterpriseUser_GetNodeData";
            public const string ENTERPRISEUSER_GETEXTNAGENT_BYGROUPID = "um_EnterpriseUser_GetExtNAgent_ByGroupId";

            public const string ENTERPRISEUSER_SET_RIGHTS = "um_EnterpriseUserRight_Insert";
            public const string ENTERPRISEUSER_SET_GROUP_RIGHTS = "um_EnterpriseGroupRight_Insert";     //Internally Call um_EnterpriseUser_GroupRight_Insert and um_EnterpriseTabRight_Insert
            public const string ENTERPRISEUSER_RECORDERS_Group = "um_EnterpriseUser_Recorder_Group";

            public const string GROUP_GETLIST = "vr_t_Group_GetAll"; //"vr_Group_GetAll";
            public const string GROUP_INSERT_N_GET = "vr_t_Group_InsertAndGetAll";
            public const string GROUP_UPDATE_N_GET = "vr_t_Group_UpdateAndGetAll";
            public const string GROUP_DELETE_N_GET = "vr_t_Group_DeleteAndGetAll";
            #region iNQUIRE GROUP MANAGEMENT : ARIVU
            public const string INQ_GROUP_GETALL = " SP_INQ_GROUP_ADD";
            public const string INQ_DEL_GROUPS = "SP_INQ_GROUP_DELETE";
            public const string INQ_GetUSERS_BY_GROUPID = "SP_INQ_GET_USERS_BY_GROUPID";
            public const string INQ_ADD_USERS_TO_GROUP = "SP_INQ_GROUPUSER_ADD";
            public const string INQ_DEL_USERS_FROM_GROUP = "SP_INQ_GROUPUSER_DELETE";
            public const string INQ_GET_GROUP_DEPTH = "SP_INQ_GET_GROUPDEPTH";
            #endregion
            #region INQUIRE CUSTOM MARKER : ARIVU
            public const string INQ_CUSTOM_MARKER = "SP_INQ_CUSTOM_MARKER";
            public const string INQ_GET_NONINSPECTED_MARKERS = "vr_SavedReport_GetNonInspectedMarkers";

            public const string INQ_ENABLE_DISABLE_USER = "INQ_ENABLE_DISABLE_USER";
            public const string INQ_ENABLE_DISABLE_AVRIS_VIEW = "INQ_ENABLE_DISABLE_AVRIS_VIEW";
            public const string INQ_ENABLE_DISABLE_IQ3_VIEW = "INQ_ENABLE_DISABLE_IQ3_VIEW";
            #endregion
            public const string INQ_PASSWORD_CHANGE = "sp_inquire_password_change";

            // RevCell
            public const string RevCell_Enable_Disable = "RevCell_Enable_Disable";
            public const string RevCell_MANAGE_PERMISSIONS = "RevCell_Manage_Permissions";
            public const string IQ3_MANAGE_PERMISSIONS = "IQ3_Manage_Permissions";


            #region IWB
            public const string IWB_ADD_EXTENSION = "IWB_Add_Extension";

            #endregion
            //public const string APPUSER_GETLIST = "um_AppUser_GetAll";
            //public const string APPUSER_GETBYID = "um_AppUser_GetById";
            //public const string APPUSER_INSERT = "um_AppUser_Insert";
            //public const string APPUSER_UPDATE = "um_AppUser_Update";
            public const string APPUSER_GETLIST = "um_t_Account_GetAll";
            public const string APPUSER_GETLIST_Simple_User = "um_t_Account_Simple_User";//"um_t_Account_GetAll";
            public const string APPUSER_GETLIST_WITHOUT_EXT = "um_GetUsers_NonExt";
            public const string APPUSER_GETBYID = "um_t_Account_GetDetailByUserNum";
            public const string APPUSER_INSERT = "um_t_Account_Insert";
            public const string APPUSER_UPDATE = "sp_Account_save";
            public const string QBUSER_UPDATE = "um_QBUser_Update";
            public const string APPUSER_DELETE = "SP_INQ_ACCOUNT_DELETE";// Changed For IQ3 //"sp_Account_delete";
            public const string APPUSER_PERMANENT_REMOVE = "sp_Account_Permanent_Remove";
            public const string APPUSER_DELETE_N_GET = "um_t_Account_DeleteAndGetAll";
            public const string APPUSER_RECOVER = "SP_INQ_RECOVER_USER";// Changed For IQ3 //"sp_set_Account_Status";
            public const string AGENTS_GETLIST = "um_GetAgents";

            public const string APPUSER_USERGROUP_SAVE = "sp_GroupUser_save";
            public const string APPUSER_USERGROUP_DELETE = "sp_GroupUser_delete";

            public const string USERPROFILE_GETBYAPPUSERID = "um_UserProfile_GetByAppUserId";
            public const string USERPROFILE_INSERT = "um_UserProfile_Insert";
            public const string USERPROFILE_UPDATE = "um_UserProfile_Update";
            //public const string APPUSER_GROUP_ASSIGN = "sp_GroupUser_add";
            public const string APPUSER_GROUP_ASSIGN = "um_AccountGroup_AssignUnAssign";
            public const string APPUSER_PERMISSIONS_ASSIGN = "um_UserPermission_Assign";

            public const string GET_LOGIN_USER = "um_GetLoginUser";
            public const string GET_LOGIN_USER_WITH_EMAIL = "um_GetLoginUser_With_Email";
            public const string GET_LOGIN_AGENT = "um_GetLoginAgent";

            public const string GET_RECORDER_INFO = "sr_GetRecorderInfo";


            public const string TREEVIEW_GET = "sp_Init_Tree";
            public const string TREEVIEW_SIMPLE_USER_RIGHTS_GET = "vr_MyExt_Tree";
            public const string TREEVIEW_GET_WRAPPER = "wrapper_Init_Tree_Demo";

            public const string TREEVIEW_GET_RVI_TREE = "rvi_Tree";

            public const string APPUSER_UPDATE_USERIMAGE = "sp_Save_UserImage";

            public const string GROUP_NODE_UPDATE = "um_Group_UpdateNode";

            public const string USER_ACTIVITY_INSERT = "um_UserActivity_Insert";
            public const string USER_ACTIVITY_SEARCH = "um_UserActivity_Search";
            public const string USER_ACTIVITY_COUNT = "um_UserActivity_Count";

            public const string INVITATION_INSERT = "um_Invitation_Add_Plugin";
            public const string INVITATION_STATUS_UPDATE = "um_InvitationStatus_Update";
            public const string INVITATION_GETBY_WHERECLAUSE = "um_Invitation_GetByWhereClause";

            public const string UPDATE_EMAIL_PASSWORD = "SP_UpdateEmailPassword";
            public const string USER_UPDATE_PASSWORD = "um_UpdatePassword";
            public const string USER_UPDATE_PASSWORD_BY_EMAIL = "um_UpdatePasswordByEmail";
            public const string USER_GET_PASSWORD_HISTORY = "um_GetPasswordHistory";
            public const string USER_GET_PASSWORD_HISTORY_BY_EMAIL = "um_GetPasswordHistoryByEmail";
            public const string USER_LOCK_UNLOCK = "um_LockUnlockByEmail";
            public const string GET_ALL_USERS_PASSWORDHISTORY = "um_GetActiveUsersWithPasswordHistory";

            public const string INVITATION_ACCEPT = "um_Invitation_Accept";

            public const string INVITEE_VALIDATION = "um_Invitee_Validation";
            public const string EVENT_DISPATCHER_INFO_INSERT = "um_EventDispatcherInfo_Insert";
            public const string USER_UPDATE_AUTOUPLOAD = "um_Update_Usersetting";

            #region Simple User Rights : Arivu
            public const string GET_SIMPLE_USER_RIGHTS_CHANNELS = "um_Simple_User_Rights_GetChannels";
            public const string SIMPLE_USER_RIGHTS_INSERT = "um_Simple_User_Rights_Insert";
            public const string SIMPLE_USER_RIGHTS_GET_ASSIGNED_RIGHTS = "UM_SIMPLE_USER_RIGHTS_GET_ASSIGNED_RIGHTS";
            public const string SIMPLE_USER_RIGHTS_ENABLE_DISABLE = "UM_SIMPLE_USER_RIGHTS_ENABLE_DISABLE";
            #endregion
            #region LivePlayer :Arivu
            public const string SAVE_LP_SETTINGS = "UM_SAVE_LIVEPLAYER_SETTINGS";
            public const string GET_LP_SETTINGS_INFO = "UM_GET_LP_SETTINGS_INFO";
            #endregion
            #region QB
            public const string GET_SERIAL_KEY = "UM_GET_SERIAL_KEY";
            #endregion

            #region Event Invitation and Twilio
            public const string GET_USER_BY_EMAILID = "um_GetUserByEmailId";
            public const string EVENTSPECIFIC_USER_INSERT = "um_EventSpecific_Account_Insert";
            public const string EVENTSPECIFIC_SIMPLE_USER_INSERT = "um_EventSpecific_Simple_Account_Insert";
            public const string EVENTSPECIFIC_SIMPLE_RIGHTS_UPDATE = "um_EventSpecific_Simple_Rights_Update";
            public const string EVENT_INVITATION_ADD = "um_EventInvitation_Add";
            public const string EVENT_GET_ACTIVE_BY_USERNUM = "um_Event_Get_Active_By_UserNum";
            public const string EVENT_SPECIFIC_USER_VALIDATION = "um_Event_Specific_User_Validation";
            public const string EVENT_GRANT_LIVE_MONITOR_PERMISSOIN = "um_Event_Grant_LiveMonitorPermission";
            public const string EVENT_RECOVER_USER = "um_Event_RecoverUser";
            #endregion

            public const string USER_EXTENSION_INFO_GET = "umGetUserExtensionInfo";
            public const string USER_APP_ACCESS_GET = "umGetUserAppAccess";

            public const string GET_ALL_APP_USERS = "umGetAllAppUsers";
            public const string COPY_MARKERS = "umCopyMarkers";
            public const string COPY_TENANT_USER_MARKERS = "umCopyTenantUserMarkers";
            public const string ACCEPT_LICENSE_AGREEMENT = "umAcceptLicenseAgreement";

            public struct SignalWire
            {
                public const string SIGNALWIRE_INFO_ADD = "vr_SignalWireInfo_Add";
                public const string SIGNALWIRE_INFO_DELETE = "DELETE";
            }
        }

        public struct CustomMarker
        {
            public const string INQ_CUSTOM_MARKER_ADD = "SP_INQ_CUSTOM_MARKER_ADD";
            public const string INQ_CUSTOM_MARKER_UPDATE = "SP_INQ_CUSTOM_MARKER_UPDATE";
            public const string INQ_CUSTOM_MARKER_REMOVE = "SP_INQ_CUSTOM_MARKER_REMOVE";
            public const string INQ_CUSTOM_MARKER_Note_ADD_UPDATE = "SP_INQ_CUSTOM_MARKER_NOTE_ADDUPDATE";
            public const string INQ_CUSTOM_MARKER_Note_DELETE = "SP_INQ_CUSTOM_MARKER_NOTE_DELETE";
        }

        public struct CallTags
        {
            public const string GETALL_CALLTAGS = "vrGetCallTags";
            public const string DELETE_CALLTAG = "vrDeleteCallTag";
            public const string UPDATE_CALLTAG = "vrUpdateCallTag";
            public const string CALLS_CALLTAG_UPDATE = "vr_CallInfo_UpdateCallTag";
        }

        public struct BookmarkTags
        {
            public const string GETALL_BOOKMARKFLAGS = "vrGetBookmarkFlag";
            public const string DELETE_BOOKMARKFLAGS = "vrDeleteBookmarkFlag";
            public const string UPDATE_BOOKMARKFLAGS = "vrUpdateBookmarkFlag";
            public const string BOOKMARK_BOOKMARKFLAGS_UPDATE = "vr_CallInfo_UpdateBookmarkFlags";
        }


        public struct Inquire
        {
            public const string INVITATION_INSERT = "um_Invitation_Add_Plugin";
            public const string INVITATION_STATUS_UPDATE = "um_InvitationStatus_Update";
            public const string INVITATION_GETBY_WHERECLAUSE = "um_Invitation_GetByWhereClause";

            public const string INVITATION_ACCEPT = "um_Invitation_Accept";
            public const string REGISTER_INVITED_USER = "um_Inquire_Account_Insert";

            public const string UPDATE_LICENSE_INFORMATION = "INQ_INVITATION_LICENSE_INFORMATION_INSERT";
            public const string GET_CUSTOMER_ID = "INQ_INVITATION_GET_CUSTOMER_DETAILS";
            public const string GET_EXPIRY_DATE = "INQ_INVITATION_GET_CUSTOMER_EXPIRY_DATE";
            public const string GET_EXT_USING_USER_NUM = "INQ_INVITATION_GET_EXT_USING_USER_NUM";
            public const string checkifalreadyexistscheck = "INQ_INVITATION_check_user_exists";

            public const string INVITATION_GROUP_INSERT = "um_ShareEvent_Add";
            public const string INVITATION_GROUP_UPDATE = "um_ShareEvent_Update";
            public const string INVITATION_GROUP_INSERT_Details = "um_ShareEventDetails_Add";
            public const string INVITATION_GROUP_GET_BY_ID = "um_ShareEvent_GetById";
            public const string PICTURE_EVENT_GET_BY_ID = "mm_PictureEvent_GetById";
            public const string PICTURE_EVENT_GET_BY_CALL_ID = "mm_PictureEvent_GetByCallId";

        }

        public struct InquireRx
        {
            public const string CASE_DETAILS_GETBYID = "emr_CaseData_GetByEventId";
            public const string CASE_IMAGES_GETBYID = "emr_CaseImages_GetByEventId";
        }

        public struct Reports
        {
            public struct AdvanceReports
            {
                public const string SEARCH_RESULT_EXCEL_GETALL = "sp_AdvanceReports";
                public const string SEARCH_RESULT_HOUR_GETALL = "sp_AdvanceReportsHour";
                public const string COLLECT_DATA_FROM_LINKED_DATABASES = "sp_CollectDataFromLinkedDatabases";
                public const string SEARCH_RESULT_CALLDETAILS_GETALL = "sp_AdvanceReportsCallDetails";
                public const string SEARCH_RESULT_CUSTOMCALLDETAILS_GETALL = "sp_CustomReports";
                public const string SEARCH_RESULT_EXCEL_911PBX_GETALL = "sp_AdvanceReports911";

                public const string TABLE_FIELDSHEAD_GETALL = "sp_getFieldsHead";
            }
            public struct CallAuditReport
            {
                public const string CALL_AUDIT_GETALL = "sp_CallAuditReport";
                public const string CALL_AUDIT_NOT_AUDITED_CALLS_GETALL = "sp_CallsNotAuditedReport";
                public const string CALL_AUDIT_RESULT_EXCEL_GETALL = "sp_CallAuditReports";
                public const string CALL_AUDIT_GET_BY_EXTENSION = "sp_GetAuditedCallsByExt";
                public const string CALL_AUDIT_TRAIL = "sp_CallAuditTrail";
            }

            public struct SavedReport
            {
                public const string SAVEDREPORT_GETALL = "vr_SavedReport_GetAll";
                public const string SAVEDREPORT_GETALL_PAGED = "vr_SavedReport_GetAll_Paged";
                public const string SHAREDSAVEDREPORT_GETALL_PAGED = "vr_Shared_SavedReport_GetAll_Paged";
                public const string SAVEDREPORT_GET_BY_ID = "vr_SavedReport_GetById";
                public const string SAVEDREPORT_GET_CRITERIA_BY_ID = "vr_SavedReport_GetCriteriaById";
                public const string SAVEDREPORT_ADD = "vr_SavedReport_Add";
                public const string SAVEDREPORT_DUPLICATE = "vr_SavedReport_Duplicate";
                public const string SAVEDREPORT_UPDATE = "vr_SavedReport_Update";
                public const string SAVEDREPORT_DELETE = "vr_SavedReport_Delete";
            }

            public struct MapReports
            {
                public const string RAPIDSOSMAP_GET = "vrRapidSOSGPSDataGet";
            }

            public struct TenantIQ3UsageReport
            {
                public const string TENANT_IQ3_USAGE_REPORT_FETCH_BY_USER = "tenant_IQ3UsageReportByUser";
                public const string TENANT_IQ3_USAGE_DETAILS_FETCH_BY_TENANT = "tenant_IQ3UsageDetailsByTenant";
            }
        }

        public struct Common
        {
            public const string PRODUCT_LICENCE_INFORMATION = "cm_GetLicenceInfo";
            public const string DASHBOARD_GET_DATA = "cm_Dashboard_GetData";
            public const string DASHBOARD_RELOAD_DATA = "cm_Dashboard_ReloadData";
            public const string DASHBOARD_GET_DATA_LITE = "cm_Dashboard_GetData_Lite";
            public const string DASHBOARD_GETLITEDATA = "cm_Dashboard_GetLiteData";
            public const string DASHBOARD_RELOAD_DATA_LITE = "cm_Dashboard_ReloadData_Lite";
            public const string DASHBOARD_GET_IR_DATA = "cm_Dashboard_GetIRData";
            public const string DASHBOARD_GET_IR_DATA_RECENT = "cm_Dashboard_GetIRData_Recent";
            public const string DASHBOARD_GET_CallCount_DATA = "cm_Dashboard_GetCallCountData";
            public const string DASHBOARD_LOAD_DATA_LITE = "cm_Dashboard_LoadData_Lite";
            public const string DASHBOARD_LOAD_SEVENDAY_CALLDATA = "cm_Dashboard_Load_SevenDay_CallData";
            public const string DASHBOARD_GET_LiveEvents = "cm_Dashboard_GetLiveEvents";

            public const string GET_IPCAMINFO_BY_USERNUM = "cm_IPCamInfo_GetAll_ByUserNum";
            public const string GET_IPCAMINFO_GetAll = "cm_IPCamInfo_GetAll";
            public const string GET_IPCAMINFO_BY_ID = "cm_IPCamInfo_Get_By_ID";
            public const string ADD_IPCAMERA_SETTING = "cm_IPCamInfo_Add";
            public const string UPDATE_IPCAMERA_SETTING = "cm_IPCamInfo_Update";
            public const string DELETE_IPCAMERA_SETTING = "cm_IPCamInfo_Delete";
            public const string UPDATE_IPCAMERA_STATUS = "cm_IPCamInfo_StatusUpdate";
            public const string GET_IPCamAssociated_GetAllUser = "cm_IPCamAssociated_GetAllUser";
            public const string GET_IPCamAssociated_SaveUser = "cm_IPCamAssociated_SaveUser";

        }

        public struct InqFileVideos //Arivu
        {
            public const string GET_INQ_FILEVIDEO_BOOKMARK = "SP_INQ_GET_FILEVIDEO_BOOKMARK";
            public const string SAVE_INQ_FILEVIDEO_ADDBOOKMARK = "SP_INQ_SAVE_FILEVIDEO_ADDBOOKMARK";
            public const string SAVE_INQ_FILEVIDEO_EDITEDBOOKMARK = "SP_INQ_SAVE_FILEVIDEO_EDITEDBOOKMARK";
            public const string SAVE_INQ_FILEVIDEO_EDITEDBOOKMARKNOTES = "SP_INQ_SAVE_FILEVIDEO_EDITEDBOOKMARKNOTES";
            public const string DELETE_INQ_FILEVIDEO_BOOKMARK = "SP_INQ_DELETE_FILEVIDEO_BOOKMARK";
            public const string SAVE_EVENT_NOTES = "SP_INQ_SAVE_EVENT_NOTES";
            public const string IQ3INSPECTIONADDBOOKMARK = "SPIQ3InspectionAddBookmark";
        }

        public struct Inspection
        {
            public const string INSPECTION_TEMPLATE_GETALL = "iq3_InspectionTemplate_GetAll";
            public const string INSPECTION_TEMPLATE_GETALL_NODETAILS = "iq3_InspectionTemplate_GetAll_NoDetails";
            public const string INSPECTION_TEMPLATE_GET_BY_ID = "iq3_InspectionTemplate_GetById";
            public const string INSPECTION_TEMPLATE_INSERT = "iq3_InspectionTemplate_Insert";
            public const string INSPECTION_TEMPLATE_DELETE = "iq3_InspectionTemplate_Delete";
            public const string INSPECTION_TEMPLATE_SHARE = "iq3_InspectionTemplate_Share";
            public const string INSPECTION_TEMPLATE_UNSHARE = "iq3_InspectionTemplate_Unshare";
            public const string INSPECTION_TEMPLATE_COPY = "iq3_InspectionTemplate_Copy";
            public const string INSPECTION_TEMPLATE_PREINSPECTION_GETALL = "iq3_InspectionTemplate_PreInspection_GetAll";
            public const string INSPECTION_TEMPLATE_PREINSPECTION_INSERT = "iq3_InspectionTemplate_PreInspection_Insert";
            public const string INSPECTION_TEMPLATE_PREINSPECTION_UPDATE = "iq3_InspectionTemplate_PreInspection_Update";
            public const string INSPECTION_TEMPLATE_MARKER_INSERT = "iq3_InspectionTemplate_Marker_Insert";
            public const string INSPECTION_TEMPLATE_MARKER_UPDATE = "iq3_InspectionTemplate_Marker_Update";
            public const string INSPECTION_TEMPLATE_MARKER_DELETE = "iq3_InspectionTemplate_Marker_Delete";
            public const string SECTION_GETBYTEMPLATEID = "iq3_Section_GetByTemplateId";
            public const string SECTION_INSERT_AND_GET = "iq3_Section_InsertAndGet";
            public const string SECTION_INSERT = "iq3_Section_Insert";
            public const string SECTION_UPDATE_AND_GET = "iq3_Section_UpdateAndGet";
            public const string SECTION_DELETE_AND_GET = "iq3_Section_DeleteAndGet";
            public const string MARKER_GETBY_INSPECTIONTEMPLATEID = "iq3_Marker_GetByInspectionTemplateId";
            public const string MARKER_GET_MULTISECTION_MARKERS = "iq3_Marker_GetMultiSectionMarkers";
            public const string MARKER_GET_MARKER_SECTIONS = "iq3_Marker_GetMarkerSections";
            public const string MARKER_UPDATE_SECTION = "iq3_Marker_UpdateSections";
            public const string MARKER_UPDATE_MARKER_SECTIONS = "iq3_Marker_UpdateMarkerSections";

            public const string INSPECTION_GET_BY_EVENTID = "iq3_Inspection_GetBy_EventId";
            public const string INSPECTION_GET_BY_PLAYLISTID = "iq3_Inspection_GetBy_PlaylistId";

            public const string INSPECTION_TEMPLATE_UPDATE = "iq3_InspectionTemplate_Update";
            public const string INSPECTION_TEMPLATE_PRE_INSPECTION_DELETE = "iq3_InspectionTemplate_PreInspection_Delete";
            public const string INSPECTION_SECTION_CREATE_AND_MOVE_MARKER = "iq3_Section_Create_MoveMarker";
            public const string MARKER_GETBY_DEFAULTSECTION = "iq3_Marker_GetByDefaultSection";

            public const string SAVE_GRAPHIC_MARKER_SECTION = "iq3_Save_GraphicMarker_Section";
            public const string GRAPHIC_MARKER_GET_BY_ID = "iq3_GraphicMarker_GetById";
            public const string GRAPHIC_MARKER_DETAILS_SECTION_DELETE = "iq3_GraphicMarkerDetails_Section_Delete";

            public const string IQ3_ASSET_HISTORY_GET_BY_ID = "iq3_IQ3AssetHistory_GetById";
            public const string IQ3_ASSET_HISTORY_GET_BY_ASSET_ID = "iq3_IQ3AssetHistory_GetByAssetId";
            public const string INSPECTION_GET_PARAMETERS = "iq3_Inspection_GetParameters";
        }

        public struct AutoReportRecipient
        {
            public const string AUTOREPORTRECIPIENT_ACTIVE_STATUS_UPDATE = "iq3_AutoReportRecipient_ActiveStatusUpdate";
            public const string AUTOREPORTRECIPIENT_SAVE = "iq3_AutoReportRecipient_Save";
            public const string AUTOREPORTRECIPIENT_DELETE = "iq3_AutoReportRecipient_Delete";
            public const string AUTOREPORTRECIPIENT_GET_AVAILABLE = "iq3_AutoReportRecipients_Get_Available";
        }

        public struct Tenant
        {
            public const string VALIDATE_EMAIL_EXISTS = "mt_Email_Exists";
            public const string GET_DB_CONNECTION = "mt_Get_Database_Connection";
            public const string VALIDATE_EMAIL_AND_GET_TENANTS = "mt_ValidateEmailAndGetTenants";
            public const string COLUMN_MODEL_UPDATE = "mt_ColumnModel_Update";
            public const string COLUMN_MODEL_GET = "mt_ColumnModel_Get";
            public const string COLUMN_MODEL_GET_BY_WHERE = "mt_ColumnModel_GetByWhere";
            public const string COLUMN_MODEL_REORDER = "mt_ColumnModel_Reorder";
            public const string CONFIGURATION_UPDATE = "mt_Configuration_Update";
            public const string CONFIGURATION_GET = "mt_Configuration_Get";
            public const string CONFIGURATION_GET_BY_WHERE = "mt_Configuration_GetByWhere";
            public const string TENANT_USER_INSERT = "mt_TenantUser_Insert";

            public const string TENANT_GET_IQ3ENABLED = "mt_Get_IQ3Enabled";
            public const string TENANT_GET_TENANTINFORMATION = "mt_Get_TenantInformation";
        }

        public struct CallAudit
        {
            public const string CALL_AUDIT_SAVE = "vr_CallAudit_Save";
        }

        public struct ScheduleReport
        {
            public const string SCHEDULE_REPORT_GET_ALL = "SP_ScheduleReport_GetAll";
            public const string SCHEDULE_REPORT_GET_ByUserId = "SP_ScheduleReport_GetByUserId";
            public const string SCHEDULE_REPORT_GETBYID = "SP_ScheduleReport_GetById";
            public const string SCHEDULE_REPORT_INSERT = "SP_ScheduleReport_Insert";
            public const string SCHEDULE_REPORT_UPDATE = "SP_ScheduleReport_Update";
            public const string SCHEDULE_REPORT_REMOVE = "SP_ScheduleReport_Remove";
            public const string SCHEDULE_REPORT_GETBY_SAVE_REPORT_ID = "SP_ScheduleReport_GetBy_Save_Report_Id";

            public const string SCHEDULE_REPORT_GET_ByUserId_ReportToBeSend = "SP_ScheduleReport_GetByUserId_ReportToBeSend";
            public const string SCHEDULE_REPORT_UPDATE_AFTER_ReportSend = "SP_ScheduleReport_Update_After_ReportSend";

            public const string REPORT_RECIPIENT_GETBY_SCHEDULE_REPORT_ID = "SP_ReportRecipients_GetBy_ScheduleReportId";
        }

        public struct SharedReportInfo
        {
            public const string SHARED_REPORT_GET_ALL = "vr_SharedReport_GetAll";
            public const string SHARED_REPORT_GET_BY_SHAREDBY = "vr_SharedReport_GetBySharedBy";
            public const string SHARED_REPORT_GET_BY_ID = "vr_SharedReport_GetById";
            public const string SHARED_REPORT_GET_BY_SAVED_REPORT_ID = "vr_SharedReport_GetBy_SavedReportId";
            public const string SHARED_REPORT_INSERT = "vr_SharedReport_Insert";
            public const string SHARED_REPORT_UPDATE = "vr_SharedReport_Update";
            public const string SHARED_REPORT_REMOVE = "vr_SharedReport_Remove";
        }

        public struct Revcell
        {
            public const string ADD_REVCELL_INFO = "swAddRevcellInfo";
            public const string GET_REVCELL_INFO = "swGetRevcellInfo";
            public const string UPDATE_REVCELL_INFO = "swUpdateRevcellInfo";
            public const string DELETE_REVCELL_INFO = "swDeleteRevcellInfo";

            public const string ADD_PLIVO_INFO = "vrAddPlivoInfo";
            public const string DELETE_PLIVO_INFO = "vrDeletePlivoInfo";
            public const string GET_PLIVO_INFO = "vrGetPlivoInfo";
            public const string GET_PLIVO_INFO_BY_EMAIL = "vrGetPlivoInfo_By_Email";

            public const string ADD_PLIVO_INFO_MASTER = "vrAddPlivoInfoMaster";
            public const string DELETE_PLIVO_INFO_MASTER = "vrDeletePlivoInfoMaster";
        }

        public struct RevLog
        {
            public const string REV_LOG_AddAuditLog = "rlAddAuditLog";
            public const string REV_LOG_AUDITLOG_SEARCH = "rlAuditLog_Search";
            public const string REV_LOG_AUDITLOG_SEARCH_BY_CATEGORY = "rlAuditLog_Search_By_Category";
            public const string REV_LOG_AUDITLOG_GET_COUNT = "rlAuditLog_Get_Count";
        }

        public struct IQ3
        {
            public const string CUSTOMFIELDS_GET_BY_USER = "iq3_CustomFields_Get_By_User";
        }

        public struct ChannelConfiguration
        {
            public const string ChannelConfiguration_ = "rmUpdateRoleChannelPermissions";
        }

        public struct CustomerDB
        {
            public const string IQ3_ASSET_MODEL_GET = "cd_IQ3Asset_Model_Get";
            public const string IQ3_ASSET_MODEL_GET_FOR_DELETE = "cd_IQ3Asset_Model_Get_For_Delete";
            public const string IQ3_ASSET_MODEL_GET_FOR_RECOVER = "cd_IQ3Asset_Model_Get_For_Recover";

            public const string IQ3_ASSET_MODEL_UPDATE = "cd_IQ3Asset_Model_Update";
            public const string IQ3_ASSET_MODEL_ADD_FIELD = "SP_IQ3Asset_Add_Column";
            public const string IQ3_ASSET_MODEL_DELETE = "cd_IQ3Asset_Model_Delete";
            public const string IQ3_ASSET_MODEL_UNDELETE = "cd_IQ3Asset_Model_UnDelete";
            public const string IQ3_ASSET_MODEL_REORDER = "cd_IQ3Asset_Model_Reorder";

            public const string IQ3_ASSET_DATA_GET_ALL = "cd_IQ3Asset_Data_GetAll";
            public const string IQ3_ASSET_DATA_GET_SEARCH_ALL = "cd_IQ3Asset_Data_GetSearchAll";
            public const string IQ3_ASSET_DATA_GET_BY_ID = "iq3_IQ3Asset_Data_GetById";
            public const string IQ3_ASSET_DATA_ADD = "iq3_IQ3Asset_Data_Add";
            public const string IQ3_ASSET_DATA_DETAILS = "iq3_IQ3Asset_Details_Add";

            public const string IQ3_ASSET_HISTORY_GET_BY_ASSET_ID = "iq3_IQ3Asset_History_GetByAssetId";
            public const string IQ3_ASSET_HISTORY_INSERT = "iq3_IQ3Asset_History_Insert";
            public const string IQ3_ASSET_FETCH_ALL = "iq3_Asset_Fetch_All";
            public const string IQ3_ASSET_FETCH_DETAILS_BY_ID = "iq3_Asset_Fetch_Details_By_Id";
        }

        public struct MGOData
        {
            public const string MGO_TEMP_DATA_ADD = "MGO_TempData_Add";
            public const string MGO_TEMP_DATA_GET_BY_ID = "MGO_TempData_GetById";

            public const string MGO_REPORT_DATA_ADD = "MGO_ReportData_Add";
            public const string MGO_STATUS_PASS_FAIL_UPDATE = "MGO_ReportData_PassFail_Update";
            public const string MGO_REPORT_DATA_GET_BY_EVENTID = "MGO_ReportData_GetByEventId";

            public const string MGO_INSPECTION_TYPES_GET = "MGO_InspectionTypes_Get";
            public const string MGO_INSPECTION_TYPES_GET_BY_PROJECT_TYPE_ID = "MGO_InspectionTypes_GetByProjectTypeID";
            public const string MGO_INSPECTION_OPTIONS_GET = "MGO_InspectionOptions_Get";
            public const string MGO_INSPECTION_OPTIONS_GET_BY_PROJECT_TYPE_ID = "MGO_InspectionOptions_GetByProjectTypeID";
        }

        public struct UserPreInspection
        {
            public const string IQ3_USER_PREINSPECTION_GROUP_GET_ALL = "iq3_UserPreInspectionGroup_GetAll";
            public const string IQ3_USER_PREINSPECTION_GROUP_GET_SEARCH_ALL = "iq3_UserPreInspectionGroup_GetAll";
            public const string IQ3_USER_PREINSPECTION_GROUP_ADD = "iq3_UserPreInspectionGroup_Insert";
            public const string IQ3_USER_PREINSPECTION_GROUP_UPDATE = "iq3_UserPreInspectionGroup_Update";
            public const string IQ3_USER_PREINSPECTION_GROUP_DELETE = "iq3_UserPreInspectionGroup_Delete";

            public const string IQ3_USER_PREINSPECTION_GET_ALL = "iq3_UserPreInspection_GetAll";
            public const string IQ3_USER_PREINSPECTION_GET_ALL_BYGROUPID = "iq3_UserPreInspection_GetAll_ByGroupId";
            public const string IQ3_USER_PREINSPECTION_GET_SEARCH_ALL = "iq3_UserPreInspection_GetSearchAll";
            public const string IQ3_USER_PREINSPECTION_ADD = "iq3_UserPreInspection_Insert";
            public const string IQ3_USER_PREINSPECTION_UPDATE = "iq3_UserPreInspection_Update";
            public const string IQ3_USER_PREINSPECTION_DELETE = "iq3_UserPreInspection_Delete";
            public const string IQ3_USER_PREINSPECTION_UNDELETE = "iq3_UserPreInspection_UnDelete";


            public const string IQ3_INSPECTION_TEMPLATE_GET_USER_PREINSPECTION_GROUP = "iq3_InspectionTemplate_GetPreInspectionGroup";
            public const string IQ3_INSPECTION_TEMPLATE_USER_PREINSPECTION_UPDATE = "iq3_InspectionTemplate_UserPreInspection_Update";
        }
    }
}
