<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Net.Http.Formatting</name>
    </assembly>
    <members>
        <member name="T:System.Web.Http.Error">
            <summary>
            Utility class for creating and unwrapping <see cref="T:System.Exception"/> instances.
            </summary>
        </member>
        <member name="M:System.Web.Http.Error.Format(System.String,System.Object[])">
            <summary>
            Formats the specified resource string using <see cref="M:CultureInfo.CurrentCulture"/>.
            </summary>
            <param name="format">A composite format string.</param>
            <param name="args">An object array that contains zero or more objects to format.</param>
            <returns>The formatted string.</returns>
        </member>
        <member name="M:System.Web.Http.Error.Argument(System.String,System.Object[])">
            <summary>
            Creates an <see cref="T:System.ArgumentException"/> with the provided properties.
            </summary>
            <param name="messageFormat">A composite format string explaining the reason for the exception.</param>
            <param name="messageArgs">An object array that contains zero or more objects to format.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.Argument(System.String,System.String,System.Object[])">
            <summary>
            Creates an <see cref="T:System.ArgumentException"/> with the provided properties.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <param name="messageFormat">A composite format string explaining the reason for the exception.</param>
            <param name="messageArgs">An object array that contains zero or more objects to format.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.ArgumentUriNotHttpOrHttpsScheme(System.String,System.Uri)">
            <summary>
            Creates an <see cref="T:System.ArgumentException"/> with a message saying that the argument must be an "http" or "https" URI.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <param name="actualValue">The value of the argument that causes this exception.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.ArgumentUriNotAbsolute(System.String,System.Uri)">
            <summary>
            Creates an <see cref="T:System.ArgumentException"/> with a message saying that the argument must be an absolute URI.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <param name="actualValue">The value of the argument that causes this exception.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.ArgumentUriHasQueryOrFragment(System.String,System.Uri)">
            <summary>
            Creates an <see cref="T:System.ArgumentException"/> with a message saying that the argument must be an absolute URI 
            without a query or fragment identifier and then logs it with <see cref="F:TraceLevel.Error"/>.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <param name="actualValue">The value of the argument that causes this exception.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.PropertyNull">
            <summary>
            Creates an <see cref="T:System.ArgumentNullException"/> with the provided properties.
            </summary>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.ArgumentNull(System.String)">
            <summary>
            Creates an <see cref="T:System.ArgumentNullException"/> with the provided properties.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.ArgumentNull(System.String,System.String,System.Object[])">
            <summary>
            Creates an <see cref="T:System.ArgumentNullException"/> with the provided properties.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <param name="messageFormat">A composite format string explaining the reason for the exception.</param>
            <param name="messageArgs">An object array that contains zero or more objects to format.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.ArgumentNullOrEmpty(System.String)">
            <summary>
            Creates an <see cref="T:System.ArgumentException"/> with a default message.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.ArgumentOutOfRange(System.String,System.Object,System.String,System.Object[])">
            <summary>
            Creates an <see cref="T:System.ArgumentOutOfRangeException"/> with the provided properties.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <param name="actualValue">The value of the argument that causes this exception.</param>
            <param name="messageFormat">A composite format string explaining the reason for the exception.</param>
            <param name="messageArgs">An object array that contains zero or more objects to format.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.ArgumentMustBeGreaterThanOrEqualTo(System.String,System.Object,System.Object)">
            <summary>
            Creates an <see cref="T:System.ArgumentOutOfRangeException"/> with a message saying that the argument must be greater than or equal to <paramref name="minValue"/>.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <param name="actualValue">The value of the argument that causes this exception.</param>
            <param name="minValue">The minimum size of the argument.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.ArgumentMustBeLessThanOrEqualTo(System.String,System.Object,System.Object)">
            <summary>
            Creates an <see cref="T:System.ArgumentOutOfRangeException"/> with a message saying that the argument must be less than or equal to <paramref name="maxValue"/>.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <param name="actualValue">The value of the argument that causes this exception.</param>
            <param name="maxValue">The maximum size of the argument.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.KeyNotFound">
            <summary>
            Creates an <see cref="T:System.Collections.Generic.KeyNotFoundException"/> with a message saying that the key was not found.
            </summary>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.KeyNotFound(System.String,System.Object[])">
            <summary>
            Creates an <see cref="T:System.Collections.Generic.KeyNotFoundException"/> with a message saying that the key was not found.
            </summary>
            <param name="messageFormat">A composite format string explaining the reason for the exception.</param>
            <param name="messageArgs">An object array that contains zero or more objects to format.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.ObjectDisposed(System.String,System.Object[])">
            <summary>
            Creates an <see cref="T:System.ObjectDisposedException"/> initialized according to guidelines.
            </summary>
            <param name="messageFormat">A composite format string explaining the reason for the exception.</param>
            <param name="messageArgs">An object array that contains zero or more objects to format.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.OperationCanceled">
            <summary>
            Creates an <see cref="T:System.OperationCanceledException"/> initialized with the provided parameters.
            </summary>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.OperationCanceled(System.String,System.Object[])">
            <summary>
            Creates an <see cref="T:System.OperationCanceledException"/> initialized with the provided parameters.
            </summary>
            <param name="messageFormat">A composite format string explaining the reason for the exception.</param>
            <param name="messageArgs">An object array that contains zero or more objects to format.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.InvalidEnumArgument(System.String,System.Int32,System.Type)">
            <summary>
            Creates an <see cref="T:System.ArgumentException"/> for an invalid enum argument.
            </summary>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
            <param name="invalidValue">The value of the argument that failed.</param>
            <param name="enumClass">A <see cref="T:System.Type"/> that represents the enumeration class with the valid values.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.InvalidOperation(System.String,System.Object[])">
            <summary>
            Creates an <see cref="T:System.InvalidOperationException"/>.
            </summary>
            <param name="messageFormat">A composite format string explaining the reason for the exception.</param>
            <param name="messageArgs">An object array that contains zero or more objects to format.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.InvalidOperation(System.Exception,System.String,System.Object[])">
            <summary>
            Creates an <see cref="T:System.InvalidOperationException"/>.
            </summary>
            <param name="innerException">Inner exception</param>
            <param name="messageFormat">A composite format string explaining the reason for the exception.</param>
            <param name="messageArgs">An object array that contains zero or more objects to format.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="M:System.Web.Http.Error.NotSupported(System.String,System.Object[])">
            <summary>
            Creates an <see cref="T:System.NotSupportedException"/>.
            </summary>
            <param name="messageFormat">A composite format string explaining the reason for the exception.</param>
            <param name="messageArgs">An object array that contains zero or more objects to format.</param>
            <returns>The logged <see cref="T:System.Exception"/>.</returns>
        </member>
        <member name="T:System.Threading.Tasks.TaskHelpers">
            <summary>
            Helpers for safely using Task libraries. 
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskHelpers.Canceled">
            <summary>
            Returns a canceled Task. The task is completed, IsCanceled = True, IsFaulted = False.
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskHelpers.Canceled``1">
            <summary>
            Returns a canceled Task of the given type. The task is completed, IsCanceled = True, IsFaulted = False.
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskHelpers.Completed">
            <summary>
            Returns a completed task that has no result. 
            </summary>        
        </member>
        <member name="M:System.Threading.Tasks.TaskHelpers.FromError(System.Exception)">
            <summary>
            Returns an error task. The task is Completed, IsCanceled = False, IsFaulted = True
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskHelpers.FromError``1(System.Exception)">
            <summary>
            Returns an error task of the given type. The task is Completed, IsCanceled = False, IsFaulted = True
            </summary>
            <typeparam name="TResult"></typeparam>
        </member>
        <member name="T:System.Threading.Tasks.TaskHelpers.AsyncVoid">
            <summary>
            Used as the T in a "conversion" of a Task into a Task{T}
            </summary>
        </member>
        <member name="T:System.Threading.Tasks.TaskHelpers.CancelCache`1">
            <summary>
            This class is a convenient cache for per-type cancelled tasks
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskHelpersExtensions.CastToObject(System.Threading.Tasks.Task)">
            <summary>
            Cast Task to Task of object
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskHelpersExtensions.CastToObject``1(System.Threading.Tasks.Task{``0})">
            <summary>
            Cast Task of T to Task of object
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskHelpersExtensions.ThrowIfFaulted(System.Threading.Tasks.Task)">
            <summary>
            Throws the first faulting exception for a task which is faulted. It preserves the original stack trace when
            throwing the exception. Note: It is the caller's responsibility not to pass incomplete tasks to this
            method, because it does degenerate into a call to the equivalent of .Wait() on the task when it hasn't yet
            completed.
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskHelpersExtensions.TryGetResult``1(System.Threading.Tasks.Task{``0},``0@)">
            <summary>
            Attempts to get the result value for the given task. If the task ran to completion, then
            it will return true and set the result value; otherwise, it will return false.
            </summary>
        </member>
        <member name="T:System.Web.Http.UriQueryUtility">
            <summary>
            Helpers for encoding, decoding, and parsing URI query components. In .Net 4.5
            please use the WebUtility class.
            </summary>
        </member>
        <member name="T:System.Collections.Generic.CollectionExtensions">
            <summary>
            Helper extension methods for fast use of collections.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.AppendAndReallocate``1(``0[],``0)">
            <summary>
            Return a new array with the value added to the end. Slow and best suited to long lived arrays with few writes relative to reads.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.AsArray``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Return the enumerable as an Array, copying if required. Optimized for common case where it is an Array. 
            Avoid mutating the return value.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.AsCollection``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Return the enumerable as a Collection of T, copying if required. Optimized for the common case where it is 
            a Collection of T and avoiding a copy if it implements IList of T. Avoid mutating the return value.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.AsIList``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Return the enumerable as a IList of T, copying if required. Avoid mutating the return value.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.AsList``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Return the enumerable as a List of T, copying if required. Optimized for common case where it is an List of T 
            or a ListWrapperCollection of T. Avoid mutating the return value.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.RemoveFrom``1(System.Collections.Generic.List{``0},System.Int32)">
            <summary>
            Remove values from the list starting at the index start.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.SingleDefaultOrError``2(System.Collections.Generic.IList{``0},System.Action{``1},``1)">
            <summary>
            Return the only value from list, the type's default value if empty, or call the errorAction for 2 or more.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.SingleOfTypeDefaultOrError``3(System.Collections.Generic.IList{``0},System.Action{``2},``2)">
            <summary>
            Returns a single value in list matching type TMatch if there is only one, null if there are none of type TMatch or calls the
            errorAction with errorArg1 if there is more than one.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.ToArrayWithoutNulls``1(System.Collections.Generic.ICollection{``0})">
            <summary>
            Convert an ICollection to an array, removing null values. Fast path for case where there are no null values.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.ToDictionaryFast``2(``1[],System.Func{``1,``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Convert the array to a Dictionary using the keySelector to extract keys from values and the specified comparer. Optimized for array input.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.ToDictionaryFast``2(System.Collections.Generic.IList{``1},System.Func{``1,``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Convert the list to a Dictionary using the keySelector to extract keys from values and the specified comparer. Optimized for IList of T input with fast path for array.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.ToDictionaryFast``2(System.Collections.Generic.IEnumerable{``1},System.Func{``1,``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Convert the enumerable to a Dictionary using the keySelector to extract keys from values and the specified comparer. Fast paths for array and IList of T.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.CollectionExtensions.ToDictionaryFastNoCheck``2(System.Collections.Generic.IList{``1},System.Func{``1,``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Convert the list to a Dictionary using the keySelector to extract keys from values and the specified comparer. Optimized for IList of T input. No checking for other types.
            </summary>
        </member>
        <member name="T:System.Collections.ObjectModel.ListWrapperCollection`1">
            <summary>
            A class that inherits from Collection of T but also exposes its underlying data as List of T for performance.
            </summary>
        </member>
        <member name="T:System.Net.Http.FormattingUtilities">
            <summary>
            Provides various internal utility functions
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.Match">
            <summary>
            Quality factor to indicate a perfect match.
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.NoMatch">
            <summary>
            Quality factor to indicate no match.
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.DefaultMaxDepth">
            <summary>
            The default max depth for our formatter is 256
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.DefaultMinDepth">
            <summary>
            The default min depth for our formatter is 1
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.HttpRequestedWithHeader">
            <summary>
            HTTP X-Requested-With header field name
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.HttpRequestedWithHeaderValue">
            <summary>
            HTTP X-Requested-With header field value
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.HttpHostHeader">
            <summary>
            HTTP Host header field name
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.HttpVersionToken">
            <summary>
            HTTP Version token
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.HttpRequestMessageType">
            <summary>
            A <see cref="T:System.Type"/> representing <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.HttpResponseMessageType">
            <summary>
            A <see cref="T:System.Type"/> representing <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.HttpContentType">
            <summary>
            A <see cref="T:System.Type"/> representing <see cref="T:System.Net.Http.HttpContent"/>.
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.DelegatingEnumerableGenericType">
            <summary>
            A <see cref="T:System.Type"/> representing <see cref="T:System.Net.Http.Formatting.DelegatingEnumerable`1"/>.
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.EnumerableInterfaceGenericType">
            <summary>
            A <see cref="T:System.Type"/> representing <see cref="T:System.Collections.Generic.IEnumerable`1"/>.
            </summary>
        </member>
        <member name="F:System.Net.Http.FormattingUtilities.QueryableInterfaceGenericType">
            <summary>
            A <see cref="T:System.Type"/> representing <see cref="T:System.Linq.IQueryable`1"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.FormattingUtilities.IsJTokenType(System.Type)">
            <summary>
            Determines whether <paramref name="type"/> is a <see cref="T:Newtonsoft.Json.Linq.JToken"/> type.
            </summary>
            <param name="type">The type to test.</param>
            <returns>
              <c>true</c> if <paramref name="type"/> is a <see cref="T:Newtonsoft.Json.Linq.JToken"/> type; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:System.Net.Http.FormattingUtilities.CreateEmptyContentHeaders">
            <summary>
            Creates an empty <see cref="T:System.Net.Http.Headers.HttpContentHeaders"/> instance. The only way is to get it from a dummy 
            <see cref="T:System.Net.Http.HttpContent"/> instance.
            </summary>
            <returns>The created instance.</returns>
        </member>
        <member name="M:System.Net.Http.FormattingUtilities.CreateDefaultReaderQuotas">
            <summary>
            Create a default reader quotas with a default depth quota of 1K
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Net.Http.FormattingUtilities.UnquoteToken(System.String)">
            <summary>
            Remove bounding quotes on a token if present
            </summary>
            <param name="token">Token to unquote.</param>
            <returns>Unquoted token.</returns>
        </member>
        <member name="M:System.Net.Http.FormattingUtilities.TryParseInt32(System.String,System.Int32@)">
            <summary>
            Parses valid integer strings with no leading signs, whitespace or other <see cref="T:System.Globalization.NumberStyles"/>
            </summary>
            <param name="value">The value to parse</param>
            <param name="result">The result</param>
            <returns>True if value was valid; false otherwise.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter">
            <summary>
            Abstract <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> class to support Bson and Json.
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.MediaTypeFormatter">
            <summary>
            Base class to handle serializing and deserializing strongly-typed objects using <see cref="T:System.Net.Http.ObjectContent"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> class.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.#ctor(System.Net.Http.Formatting.MediaTypeFormatter)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> class.
            </summary>
            <param name="formatter">The <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instance to copy settings from.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.ReadFromStreamAsync(System.Type,System.IO.Stream,System.Net.Http.HttpContent,System.Net.Http.Formatting.IFormatterLogger)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> to deserialize an object of the given <paramref name="type"/> from the given <paramref name="readStream"/>
            </summary>
            <remarks>
            <para>This implementation throws a <see cref="T:System.NotSupportedException"/>. Derived types should override this method if the formatter
            supports reading.</para>
            <para>An implementation of this method should NOT close <paramref name="readStream"/> upon completion. The stream will be closed independently when
            the <see cref="T:System.Net.Http.HttpContent"/> instance is disposed.
            </para>
            </remarks>
            <param name="type">The type of the object to deserialize.</param>
            <param name="readStream">The <see cref="T:System.IO.Stream"/> to read.</param>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> if available. It may be <c>null</c>.</param>
            <param name="formatterLogger">The <see cref="T:System.Net.Http.Formatting.IFormatterLogger"/> to log events to.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> whose result will be an object of the given type.</returns>
            <exception cref="T:System.NotSupportedException">Derived types need to support reading.</exception>
            <seealso cref="M:System.Net.Http.Formatting.MediaTypeFormatter.CanReadType(System.Type)"/>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.ReadFromStreamAsync(System.Type,System.IO.Stream,System.Net.Http.HttpContent,System.Net.Http.Formatting.IFormatterLogger,System.Threading.CancellationToken)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> to deserialize an object of the given <paramref name="type"/> from the given <paramref name="readStream"/>
            </summary>
            <remarks>
            <para>This implementation throws a <see cref="T:System.NotSupportedException"/>. Derived types should override this method if the formatter
            supports reading.</para>
            <para>An implementation of this method should NOT close <paramref name="readStream"/> upon completion. The stream will be closed independently when
            the <see cref="T:System.Net.Http.HttpContent"/> instance is disposed.
            </para>
            </remarks>
            <param name="type">The type of the object to deserialize.</param>
            <param name="readStream">The <see cref="T:System.IO.Stream"/> to read.</param>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> if available. It may be <c>null</c>.</param>
            <param name="formatterLogger">The <see cref="T:System.Net.Http.Formatting.IFormatterLogger"/> to log events to.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> whose result will be an object of the given type.</returns>
            <exception cref="T:System.NotSupportedException">Derived types need to support reading.</exception>
            <seealso cref="M:System.Net.Http.Formatting.MediaTypeFormatter.CanReadType(System.Type)"/>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.WriteToStreamAsync(System.Type,System.Object,System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that serializes the given <paramref name="value"/> of the given <paramref name="type"/>
            to the given <paramref name="writeStream"/>.
            </summary>
            <remarks>
            <para>This implementation throws a <see cref="T:System.NotSupportedException"/>. Derived types should override this method if the formatter
            supports reading.</para>
            <para>An implementation of this method should NOT close <paramref name="writeStream"/> upon completion. The stream will be closed independently when
            the <see cref="T:System.Net.Http.HttpContent"/> instance is disposed.
            </para>
            </remarks>
            <param name="type">The type of the object to write.</param>
            <param name="value">The object value to write.  It may be <c>null</c>.</param>
            <param name="writeStream">The <see cref="T:System.IO.Stream"/> to which to write.</param>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> if available. It may be <c>null</c>.</param>
            <param name="transportContext">The <see cref="T:System.Net.TransportContext"/> if available. It may be <c>null</c>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that will perform the write.</returns>
            <exception cref="T:System.NotSupportedException">Derived types need to support writing.</exception>
            <seealso cref="M:System.Net.Http.Formatting.MediaTypeFormatter.CanWriteType(System.Type)"/>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.WriteToStreamAsync(System.Type,System.Object,System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext,System.Threading.CancellationToken)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that serializes the given <paramref name="value"/> of the given <paramref name="type"/>
            to the given <paramref name="writeStream"/>.
            </summary>
            <remarks>
            <para>This implementation throws a <see cref="T:System.NotSupportedException"/>. Derived types should override this method if the formatter
            supports reading.</para>
            <para>An implementation of this method should NOT close <paramref name="writeStream"/> upon completion. The stream will be closed independently when
            the <see cref="T:System.Net.Http.HttpContent"/> instance is disposed.
            </para>
            </remarks>
            <param name="type">The type of the object to write.</param>
            <param name="value">The object value to write.  It may be <c>null</c>.</param>
            <param name="writeStream">The <see cref="T:System.IO.Stream"/> to which to write.</param>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> if available. It may be <c>null</c>.</param>
            <param name="transportContext">The <see cref="T:System.Net.TransportContext"/> if available. It may be <c>null</c>.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that will perform the write.</returns>
            <exception cref="T:System.NotSupportedException">Derived types need to support writing.</exception>
            <seealso cref="M:System.Net.Http.Formatting.MediaTypeFormatter.CanWriteType(System.Type)"/>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.TryGetDelegatingTypeForIEnumerableGenericOrSame(System.Type@)">
            <summary>
            This method converts <see cref="T:System.Collections.Generic.IEnumerable`1"/> (and interfaces that mandate it) to a <see cref="T:System.Net.Http.Formatting.DelegatingEnumerable`1"/> for serialization purposes.
            </summary>
            <param name="type">The type to potentially be wrapped. If the type is wrapped, it's changed in place.</param>
            <returns>Returns <c>true</c> if the type was wrapped; <c>false</c>, otherwise</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.TryGetDelegatingTypeForIQueryableGenericOrSame(System.Type@)">
            <summary>
            This method converts <see cref="T:System.Linq.IQueryable`1"/> (and interfaces that mandate it) to a <see cref="T:System.Net.Http.Formatting.DelegatingEnumerable`1"/> for serialization purposes.
            </summary>
            <param name="type">The type to potentially be wrapped. If the type is wrapped, it's changed in place.</param>
            <returns>Returns <c>true</c> if the type was wrapped; <c>false</c>, otherwise</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.SelectCharacterEncoding(System.Net.Http.Headers.HttpContentHeaders)">
            <summary>
            Determines the best <see cref="T:System.Text.Encoding"/> amongst the supported encodings
            for reading or writing an HTTP entity body based on the provided <paramref name="contentHeaders"/>.
            </summary>
            <param name="contentHeaders">The content headers provided as part of the request or response.</param>
            <returns>The <see cref="T:System.Text.Encoding"/> to use when reading the request or writing the response.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.SetDefaultContentHeaders(System.Type,System.Net.Http.Headers.HttpContentHeaders,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Sets the default headers for content that will be formatted using this formatter. This method
            is called from the <see cref="T:System.Net.Http.ObjectContent"/> constructor.
            This implementation sets the Content-Type header to the value of <paramref name="mediaType"/> if it is
            not <c>null</c>. If it is <c>null</c> it sets the Content-Type to the default media type of this formatter.
            If the Content-Type does not specify a charset it will set it using this formatters configured
            <see cref="T:System.Text.Encoding"/>.
            </summary>
            <remarks>
            Subclasses can override this method to set content headers such as Content-Type etc. Subclasses should
            call the base implementation. Subclasses should treat the passed in <paramref name="mediaType"/> (if not <c>null</c>)
            as the authoritative media type and use that as the Content-Type.
            </remarks>
            <param name="type">The type of the object being serialized. See <see cref="T:System.Net.Http.ObjectContent"/>.</param>
            <param name="headers">The content headers that should be configured.</param>
            <param name="mediaType">The authoritative media type. Can be <c>null</c>.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.GetPerRequestFormatterInstance(System.Type,System.Net.Http.HttpRequestMessage,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Returns a specialized instance of the <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> that can handle formatting a response for the given
            parameters. This method is called after a formatter has been selected through content negotiation.
            </summary>
            <remarks>
            The default implementation returns <c>this</c> instance. Derived classes can choose to return a new instance if
            they need to close over any of the parameters.
            </remarks>
            <param name="type">The type being serialized.</param>
            <param name="request">The request.</param>
            <param name="mediaType">The media type chosen for the serialization. Can be <c>null</c>.</param>
            <returns>An instance that can format a response to the given <paramref name="request"/>.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.CanReadType(System.Type)">
            <summary>
            Determines whether this <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> can deserialize
            an object of the specified type.
            </summary>
            <remarks>
            Derived classes must implement this method and indicate if a type can or cannot be deserialized.
            </remarks>
            <param name="type">The type of object that will be deserialized.</param>
            <returns><c>true</c> if this <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> can deserialize an object of that type; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.CanWriteType(System.Type)">
            <summary>
            Determines whether this <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> can serialize
            an object of the specified type.
            </summary>
            <remarks>
            Derived classes must implement this method and indicate if a type can or cannot be serialized.
            </remarks>
            <param name="type">The type of object that will be serialized.</param>
            <returns><c>true</c> if this <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> can serialize an object of that type; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.GetDefaultValueForType(System.Type)">
            <summary>
            Gets the default value for the specified type.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeFormatter.MaxHttpCollectionKeys">
            <summary>
            Gets or sets the maximum number of keys stored in a NameValueCollection. 
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeFormatter.SupportedMediaTypes">
            <summary>
            Gets the mutable collection of <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> elements supported by
            this <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instance.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeFormatter.SupportedEncodings">
            <summary>
            Gets the mutable collection of character encodings supported by
            this <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instance. The encodings are
            used when reading or writing data. 
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.MediaTypeFormatter.MediaTypeHeaderValueCollection">
            <summary>
            Collection class that validates it contains only <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instances
            that are not null and not media ranges.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.MediaTypeHeaderValueCollection.InsertItem(System.Int32,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Inserts the <paramref name="item"/> into the collection at the specified <paramref name="index"/>.
            </summary>
            <param name="index">The zero-based index at which item should be inserted.</param>
            <param name="item">The object to insert. It cannot be <c>null</c>.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatter.MediaTypeHeaderValueCollection.SetItem(System.Int32,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Replaces the element at the specified <paramref name="index"/>.
            </summary>
            <param name="index">The zero-based index of the item that should be replaced.</param>
            <param name="item">The new value for the element at the specified index.  It cannot be <c>null</c>.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter"/> class.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.#ctor(System.Net.Http.Formatting.BaseJsonMediaTypeFormatter)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter"/> class.
            </summary>
            <param name="formatter">The <see cref="T:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter"/> instance to copy settings from.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.CreateDefaultSerializerSettings">
            <summary>
            Creates a <see cref="T:Newtonsoft.Json.JsonSerializerSettings"/> instance with the default settings used by the <see cref="T:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.CanReadType(System.Type)">
            <summary>
            Determines whether this <see cref="T:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter"/> can read objects
            of the specified <paramref name="type"/>.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of object that will be read.</param>
            <returns><c>true</c> if objects of this <paramref name="type"/> can be read, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.CanWriteType(System.Type)">
            <summary>
            Determines whether this <see cref="T:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter"/> can write objects
            of the specified <paramref name="type"/>.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of object that will be written.</param>
            <returns><c>true</c> if objects of this <paramref name="type"/> can be written, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.ReadFromStreamAsync(System.Type,System.IO.Stream,System.Net.Http.HttpContent,System.Net.Http.Formatting.IFormatterLogger)">
            <summary>
            Called during deserialization to read an object of the specified <paramref name="type"/>
            from the specified <paramref name="readStream"/>.
            </summary>
            <param name="type">The <see cref="T:System.Type"/> of object to read.</param>
            <param name="readStream">The <see cref="T:System.IO.Stream"/> from which to read.</param>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> for the content being written.</param>
            <param name="formatterLogger">The <see cref="T:System.Net.Http.Formatting.IFormatterLogger"/> to log events to.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> whose result will be the object instance that has been read.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.ReadFromStream(System.Type,System.IO.Stream,System.Text.Encoding,System.Net.Http.Formatting.IFormatterLogger)">
            <summary>
            Called during deserialization to read an object of the specified <paramref name="type"/>
            from the specified <paramref name="readStream"/>.
            </summary>
            <remarks>
            Public for delegating wrappers of this class.  Expected to be called only from
            <see cref="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.ReadFromStreamAsync(System.Type,System.IO.Stream,System.Net.Http.HttpContent,System.Net.Http.Formatting.IFormatterLogger)"/>.
            </remarks>
            <param name="type">The <see cref="T:System.Type"/> of object to read.</param>
            <param name="readStream">The <see cref="T:System.IO.Stream"/> from which to read.</param>
            <param name="effectiveEncoding">The <see cref="T:System.Text.Encoding"/> to use when reading.</param>
            <param name="formatterLogger">The <see cref="T:System.Net.Http.Formatting.IFormatterLogger"/> to log events to.</param>
            <returns>The <see cref="T:System.Object"/> instance that has been read.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.CreateJsonReader(System.Type,System.IO.Stream,System.Text.Encoding)">
            <summary>
            Called during deserialization to get the <see cref="T:Newtonsoft.Json.JsonReader"/>.
            </summary>
            <remarks>
            Public for delegating wrappers of this class.  Expected to be called only from
            <see cref="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.ReadFromStreamAsync(System.Type,System.IO.Stream,System.Net.Http.HttpContent,System.Net.Http.Formatting.IFormatterLogger)"/>.
            </remarks>
            <param name="type">The <see cref="T:System.Type"/> of object to read.</param>
            <param name="readStream">The <see cref="T:System.IO.Stream"/> from which to read.</param>
            <param name="effectiveEncoding">The <see cref="T:System.Text.Encoding"/> to use when reading.</param>
            <returns>The <see cref="T:Newtonsoft.Json.JsonWriter"/> used during deserialization.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.CreateJsonSerializer">
            <summary>
            Called during serialization and deserialization to get the <see cref="T:Newtonsoft.Json.JsonSerializer"/>.
            </summary>
            <remarks>
            Public for delegating wrappers of this class.  Expected to be called only from
            <see cref="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.ReadFromStreamAsync(System.Type,System.IO.Stream,System.Net.Http.HttpContent,System.Net.Http.Formatting.IFormatterLogger)"/> and <see cref="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.WriteToStreamAsync(System.Type,System.Object,System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext,System.Threading.CancellationToken)"/>.
            </remarks>
            <returns>The <see cref="T:Newtonsoft.Json.JsonSerializer"/> used during serialization and deserialization.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.WriteToStreamAsync(System.Type,System.Object,System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.WriteToStream(System.Type,System.Object,System.IO.Stream,System.Text.Encoding)">
            <summary>
            Called during serialization to write an object of the specified <paramref name="type"/>
            to the specified <paramref name="writeStream"/>.
            </summary>
            <remarks>
            Public for delegating wrappers of this class.  Expected to be called only from
            <see cref="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.WriteToStreamAsync(System.Type,System.Object,System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext,System.Threading.CancellationToken)"/>.
            </remarks>
            <param name="type">The <see cref="T:System.Type"/> of object to write.</param>
            <param name="value">The object to write.</param>
            <param name="writeStream">The <see cref="T:System.IO.Stream"/> to which to write.</param>
            <param name="effectiveEncoding">The <see cref="T:System.Text.Encoding"/> to use when writing.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.CreateJsonWriter(System.Type,System.IO.Stream,System.Text.Encoding)">
            <summary>
            Called during serialization to get the <see cref="T:Newtonsoft.Json.JsonWriter"/>.
            </summary>
            <remarks>
            Public for delegating wrappers of this class.  Expected to be called only from
            <see cref="M:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.WriteToStreamAsync(System.Type,System.Object,System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext,System.Threading.CancellationToken)"/>.
            </remarks>
            <param name="type">The <see cref="T:System.Type"/> of object to write.</param>
            <param name="writeStream">The <see cref="T:System.IO.Stream"/> to which to write.</param>
            <param name="effectiveEncoding">The <see cref="T:System.Text.Encoding"/> to use when writing.</param>
            <returns>The <see cref="T:Newtonsoft.Json.JsonWriter"/> used during serialization.</returns>
        </member>
        <member name="P:System.Net.Http.Formatting.BaseJsonMediaTypeFormatter.SerializerSettings">
            <summary>
            Gets or sets the <see cref="T:Newtonsoft.Json.JsonSerializerSettings"/> used to configure the <see cref="T:Newtonsoft.Json.JsonSerializer"/>.
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.BsonMediaTypeFormatter">
            <summary>
            <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> class to handle Bson.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.BsonMediaTypeFormatter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.BsonMediaTypeFormatter"/> class.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.BsonMediaTypeFormatter.#ctor(System.Net.Http.Formatting.BsonMediaTypeFormatter)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.BsonMediaTypeFormatter"/> class.
            </summary>
            <param name="formatter">The <see cref="T:System.Net.Http.Formatting.BsonMediaTypeFormatter"/> instance to copy settings from.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.BsonMediaTypeFormatter.ReadFromStream(System.Type,System.IO.Stream,System.Text.Encoding,System.Net.Http.Formatting.IFormatterLogger)">
            <inheritdoc />
        </member>
        <member name="M:System.Net.Http.Formatting.BsonMediaTypeFormatter.CreateJsonReader(System.Type,System.IO.Stream,System.Text.Encoding)">
            <inheritdoc />
        </member>
        <member name="M:System.Net.Http.Formatting.BsonMediaTypeFormatter.WriteToStream(System.Type,System.Object,System.IO.Stream,System.Text.Encoding)">
            <inheritdoc />
        </member>
        <member name="M:System.Net.Http.Formatting.BsonMediaTypeFormatter.CreateJsonWriter(System.Type,System.IO.Stream,System.Text.Encoding)">
            <inheritdoc />
        </member>
        <member name="P:System.Net.Http.Formatting.BsonMediaTypeFormatter.DefaultMediaType">
            <summary>
            Gets the default media type for Json, namely "application/bson".
            </summary>
            <remarks>
            The default media type does not have any <c>charset</c> parameter as
            the <see cref="T:System.Text.Encoding"/> can be configured on a per <see cref="T:System.Net.Http.Formatting.BsonMediaTypeFormatter"/>
            instance basis.
            </remarks>
            <value>
            Because <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> is mutable, the value
            returned will be a new instance every time.
            </value>
        </member>
        <member name="T:System.Net.Http.Formatting.DelegatingEnumerable`1">
            <summary>
            Helper class to serialize <see cref="T:System.Collections.Generic.IEnumerable`1"/> types by delegating them through a concrete implementation."/&gt;.
            </summary>
            <typeparam name="T">The interface implementing <see cref="T:System.Collections.Generic.IEnumerable`1"/> to proxy.</typeparam>
        </member>
        <member name="M:System.Net.Http.Formatting.DelegatingEnumerable`1.#ctor">
            <summary>
            Initialize a DelegatingEnumerable. This constructor is necessary for <see cref="T:System.Runtime.Serialization.DataContractSerializer"/> to work.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.DelegatingEnumerable`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Initialize a DelegatingEnumerable with an <see cref="T:System.Collections.Generic.IEnumerable`1"/>. This is a helper class to proxy <see cref="T:System.Collections.Generic.IEnumerable`1"/> interfaces for <see cref="T:System.Xml.Serialization.XmlSerializer"/>.
            </summary>
            <param name="source">The <see cref="T:System.Collections.Generic.IEnumerable`1"/> instance to get the enumerator from.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.DelegatingEnumerable`1.GetEnumerator">
            <summary>
            Get the enumerator of the associated <see cref="T:System.Collections.Generic.IEnumerable`1"/>.
            </summary>
            <returns>The enumerator of the <see cref="T:System.Collections.Generic.IEnumerable`1"/> source.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.DelegatingEnumerable`1.Add(System.Object)">
            <summary>
            This method is not implemented but is required method for serialization to work. Do not use.
            </summary>
            <param name="item">The item to add. Unused.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.DelegatingEnumerable`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Get the enumerator of the associated <see cref="T:System.Collections.Generic.IEnumerable`1"/>.
            </summary>
            <returns>The enumerator of the <see cref="T:System.Collections.Generic.IEnumerable`1"/> source.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.FormDataCollection">
            <summary>
            Represent the form data.
            - This has 100% fidelity (including ordering, which is important for deserializing ordered array). 
            - using interfaces allows us to optimize the implementation. E.g., we can avoid eagerly string-splitting a 10gb file. 
            - This also provides a convenient place to put extension methods. 
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.FormDataCollection.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Initialize a form collection around incoming data. 
            The key value enumeration should be immutable. 
            </summary>
            <param name="pairs">incoming set of key value pairs. Ordering is preserved.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.FormDataCollection.#ctor(System.Uri)">
            <summary>
            Initialize a form collection from a query string. 
            Uri and FormURl body have the same schema. 
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.FormDataCollection.#ctor(System.String)">
            <summary>
            Initialize a form collection from a URL encoded query string. Any leading question
            mark (?) will be considered part of the query string and treated as any other value.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.FormDataCollection.ReadAsNameValueCollection">
            <summary>
            Get the collection as a NameValueCollection.
            Beware this loses some ordering. Values are ordered within a key,
            but keys are no longer ordered against each other.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.FormDataCollection.Get(System.String)">
            <summary>
            Get values associated with a given key. If there are multiple values, they're concatenated. 
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.FormDataCollection.GetValues(System.String)">
            <summary>
            Get a value associated with a given key. 
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.FormDataCollection.Item(System.String)">
            <summary>
            Gets values associated with a given key. If there are multiple values, they're concatenated.
            </summary>
            <param name="name">The name of the entry that contains the values to get. The name can be null.</param>
            <returns>Values associated with a given key. If there are multiple values, they're concatenated.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.FormUrlEncodedJson">
            <summary>
            This class provides a low-level API for parsing HTML form URL-encoded data, also known as <c>application/x-www-form-urlencoded</c> 
            data. The output of the parser is a <see cref="T:Newtonsoft.Json.Linq.JObject"/> instance. 
            <remarks>This is a low-level API intended for use by other APIs. It has been optimized for performance and 
            is not intended to be called directly from user code.</remarks>
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.FormUrlEncodedJson.Parse(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Parses a collection of query string values as a <see cref="T:Newtonsoft.Json.Linq.JObject"/>.
            </summary>
            <remarks>This is a low-level API intended for use by other APIs. It has been optimized for performance and 
            is not intended to be called directly from user code.</remarks>
            <param name="nameValuePairs">The collection of query string name-value pairs parsed in lexical order. Both names
            and values must be un-escaped so that they don't contain any <see cref="T:System.Uri"/> encoding.</param>
            <returns>The <see cref="T:Newtonsoft.Json.Linq.JObject"/> corresponding to the given query string values.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.FormUrlEncodedJson.Parse(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}},System.Int32)">
            <summary>
            Parses a collection of query string values as a <see cref="T:Newtonsoft.Json.Linq.JObject"/>.
            </summary>
            <remarks>This is a low-level API intended for use by other APIs. It has been optimized for performance and 
            is not intended to be called directly from user code.</remarks>
            <param name="nameValuePairs">The collection of query string name-value pairs parsed in lexical order. Both names
            and values must be un-escaped so that they don't contain any <see cref="T:System.Uri"/> encoding.</param>
            <param name="maxDepth">The maximum depth of object graph encoded as <c>x-www-form-urlencoded</c>.</param>
            <returns>The <see cref="T:Newtonsoft.Json.Linq.JObject"/> corresponding to the given query string values.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.FormUrlEncodedJson.TryParse(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}},Newtonsoft.Json.Linq.JObject@)">
            <summary>
            Parses a collection of query string values as a <see cref="T:Newtonsoft.Json.Linq.JObject"/>.
            </summary>
            <remarks>This is a low-level API intended for use by other APIs. It has been optimized for performance and 
            is not intended to be called directly from user code.</remarks>
            <param name="nameValuePairs">The collection of query string name-value pairs parsed in lexical order. Both names
            and values must be un-escaped so that they don't contain any <see cref="T:System.Uri"/> encoding.</param>
            <param name="value">The parsed result or null if parsing failed.</param>
            <returns><c>true</c> if <paramref name="nameValuePairs"/> was parsed successfully; otherwise false.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.FormUrlEncodedJson.TryParse(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}},System.Int32,Newtonsoft.Json.Linq.JObject@)">
            <summary>
            Parses a collection of query string values as a <see cref="T:Newtonsoft.Json.Linq.JObject"/>.
            </summary>
            <remarks>This is a low-level API intended for use by other APIs. It has been optimized for performance and 
            is not intended to be called directly from user code.</remarks>
            <param name="nameValuePairs">The collection of query string name-value pairs parsed in lexical order. Both names
            and values must be un-escaped so that they don't contain any <see cref="T:System.Uri"/> encoding.</param>
            <param name="maxDepth">The maximum depth of object graph encoded as <c>x-www-form-urlencoded</c>.</param>
            <param name="value">The parsed result or null if parsing failed.</param>
            <returns><c>true</c> if <paramref name="nameValuePairs"/> was parsed successfully; otherwise false.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.FormUrlEncodedJson.ParseInternal(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}},System.Int32,System.Boolean)">
            <summary>
            Parses a collection of query string values as a <see cref="T:Newtonsoft.Json.Linq.JObject"/>.
            </summary>
            <remarks>This is a low-level API intended for use by other APIs. It has been optimized for performance and 
            is not intended to be called directly from user code.</remarks>
            <param name="nameValuePairs">The collection of query string name-value pairs parsed in lexical order. Both names
            and values must be un-escaped so that they don't contain any <see cref="T:System.Uri"/> encoding.</param>
            <param name="maxDepth">The maximum depth of object graph encoded as <c>x-www-form-urlencoded</c>.</param>
            <param name="throwOnError">Indicates whether to throw an exception on error or return false</param>
            <returns>The <see cref="T:Newtonsoft.Json.Linq.JObject"/> corresponding to the given query string values.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate">
            <summary>
            Class that wraps key-value pairs.
            </summary>
            <remarks>
            This use of this class avoids a FxCop warning CA908 which happens if using various generic types.
            </remarks>
        </member>
        <member name="M:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate"/> class.
            </summary>
            <param name="key">The key of this <see cref="T:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate"/> instance.</param>
            <param name="value">The value of this <see cref="T:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate"/> instance.</param>
        </member>
        <member name="P:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate.Key">
            <summary>
            Gets or sets the key of this <see cref="T:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate"/> instance.
            </summary>
            <value>
            The key of this <see cref="T:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate"/> instance.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate.Value">
            <summary>
            Gets or sets the value of this <see cref="T:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate"/> instance.
            </summary>
            <value>
            The value of this <see cref="T:System.Net.Http.Formatting.FormUrlEncodedJson.ArrayCandidate"/> instance.
            </value>
        </member>
        <member name="T:System.Net.Http.Formatting.IFormatterLogger">
            <summary>
            Interface to log events that occur during formatter reads.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.IFormatterLogger.LogError(System.String,System.String)">
            <summary>
            Logs an error.
            </summary>
            <param name="errorPath">The path to the member for which the error is being logged.</param>
            <param name="errorMessage">The error message to be logged.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.IFormatterLogger.LogError(System.String,System.Exception)">
            <summary>
            Logs an error.
            </summary>
            <param name="errorPath">The path to the member for which the error is being logged.</param>
            <param name="exception">The exception to be logged.</param>
        </member>
        <member name="T:System.Net.Http.Formatting.JsonMediaTypeFormatter">
            <summary>
            <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> class to handle Json.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.JsonMediaTypeFormatter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/> class.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.JsonMediaTypeFormatter.#ctor(System.Net.Http.Formatting.JsonMediaTypeFormatter)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/> class.
            </summary>
            <param name="formatter">The <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/> instance to copy settings from.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.JsonMediaTypeFormatter.CreateJsonReader(System.Type,System.IO.Stream,System.Text.Encoding)">
            <inheritdoc />
        </member>
        <member name="M:System.Net.Http.Formatting.JsonMediaTypeFormatter.CreateJsonWriter(System.Type,System.IO.Stream,System.Text.Encoding)">
            <inheritdoc />
        </member>
        <member name="P:System.Net.Http.Formatting.JsonMediaTypeFormatter.DefaultMediaType">
            <summary>
            Gets the default media type for Json, namely "application/json".
            </summary>
            <remarks>
            The default media type does not have any <c>charset</c> parameter as
            the <see cref="T:System.Text.Encoding"/> can be configured on a per <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/>
            instance basis.
            </remarks>
            <value>
            Because <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> is mutable, the value
            returned will be a new instance every time.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.JsonMediaTypeFormatter.Indent">
            <summary>
            Gets or sets a value indicating whether to indent elements when writing data. 
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.MediaTypeConstants">
            <summary>
            Constants related to media types.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeConstants.ApplicationOctetStreamMediaType">
            <summary>
            Gets a <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>application/octet-stream</c>.
            </summary>
            <value>
            A new <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>application/octet-stream</c>.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeConstants.ApplicationXmlMediaType">
            <summary>
            Gets a <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>application/xml</c>.
            </summary>
            <value>
            A new <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>application/xml</c>.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeConstants.ApplicationJsonMediaType">
            <summary>
            Gets a <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>application/json</c>.
            </summary>
            <value>
            A new <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>application/json</c>.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeConstants.TextXmlMediaType">
            <summary>
            Gets a <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>text/xml</c>.
            </summary>
            <value>
            A new <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>text/xml</c>.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeConstants.TextJsonMediaType">
            <summary>
            Gets a <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>text/json</c>.
            </summary>
            <value>
            A new <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>text/json</c>.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeConstants.ApplicationFormUrlEncodedMediaType">
            <summary>
            Gets a <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>application/x-www-form-urlencoded</c>.
            </summary>
            <value>
            A new <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>application/x-www-form-urlencoded</c>.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeConstants.ApplicationBsonMediaType">
            <summary>
            Gets a <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>application/bson</c>.
            </summary>
            <value>
            A new <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instance representing <c>application/bson</c>.
            </value>
            <remarks>
            Not yet a standard. In particular this media type is not currently listed at
            http://www.iana.org/assignments/media-types/application.
            </remarks>
        </member>
        <member name="T:System.Net.Http.Formatting.MediaTypeFormatterCollection">
            <summary>
            Collection class that contains <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatterCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.MediaTypeFormatterCollection"/> class.
            </summary>
            <remarks>
            This collection will be initialized to contain default <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/>
            instances for Xml, JsonValue and Json.
            </remarks>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatterCollection.#ctor(System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.MediaTypeFormatterCollection"/> class.
            </summary>
            <param name="formatters">A collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances to place in the collection.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatterCollection.FindReader(System.Type,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Helper to search a collection for a formatter that can read the .NET type in the given mediaType.
            </summary>
            <param name="type">.NET type to read</param>
            <param name="mediaType">media type to match on.</param>
            <returns>Formatter that can read the type. Null if no formatter found.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatterCollection.FindWriter(System.Type,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Helper to search a collection for a formatter that can write the .NET type in the given mediaType.
            </summary>
            <param name="type">.NET type to read</param>
            <param name="mediaType">media type to match on.</param>
            <returns>Formatter that can write the type. Null if no formatter found.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatterCollection.IsTypeExcludedFromValidation(System.Type)">
            <summary>
            Returns true if the type is one of those loosely defined types that should be excluded from validation
            </summary>
            <param name="type">.NET <see cref="T:System.Type"/> to validate</param>
            <returns><c>true</c> if the type should be excluded.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeFormatterCollection.CreateDefaultFormatters">
            <summary>
            Creates a collection of new instances of the default <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/>s.
            </summary>
            <returns>The collection of default <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances.</returns>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeFormatterCollection.XmlFormatter">
            <summary>
            Gets the <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> to use for Xml.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.MediaTypeFormatterCollection.JsonFormatter">
            <summary>
            Gets the <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> to use for Json.
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.MediaTypeHeaderValueExtensions">
            <summary>
            Extension methods for <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeHeaderValueExtensions.IsSubsetOf(System.Net.Http.Headers.MediaTypeHeaderValue,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Determines whether two <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instances match. The instance
            <paramref name="mediaType1"/> is said to match <paramref name="mediaType2"/> if and only if
            <paramref name="mediaType1"/> is a strict subset of the values and parameters of <paramref name="mediaType2"/>. 
            That is, if the media type and media type parameters of <paramref name="mediaType1"/> are all present 
            and match those of <paramref name="mediaType2"/> then it is a match even though <paramref name="mediaType2"/> may have additional
            parameters.
            </summary>
            <param name="mediaType1">The first media type.</param>
            <param name="mediaType2">The second media type.</param>
            <returns><c>true</c> if this is a subset of <paramref name="mediaType2"/>; false otherwise.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.MediaTypeHeaderValueExtensions.IsSubsetOf(System.Net.Http.Headers.MediaTypeHeaderValue,System.Net.Http.Headers.MediaTypeHeaderValue,System.Net.Http.Formatting.MediaTypeHeaderValueRange@)">
            <summary>
            Determines whether two <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> instances match. The instance
            <paramref name="mediaType1"/> is said to match <paramref name="mediaType2"/> if and only if
            <paramref name="mediaType1"/> is a strict subset of the values and parameters of <paramref name="mediaType2"/>. 
            That is, if the media type and media type parameters of <paramref name="mediaType1"/> are all present 
            and match those of <paramref name="mediaType2"/> then it is a match even though <paramref name="mediaType2"/> may have additional
            parameters.
            </summary>
            <param name="mediaType1">The first media type.</param>
            <param name="mediaType2">The second media type.</param>
            <param name="mediaType2Range">Indicates whether <paramref name="mediaType2"/> is a regular media type, a subtype media range, or a full media range</param>
            <returns><c>true</c> if this is a subset of <paramref name="mediaType2"/>; false otherwise.</returns>
        </member>
        <member name="F:System.Net.Http.Formatting.MediaTypeHeaderValueRange.None">
            <summary>
            Not a media type range
            </summary>
        </member>
        <member name="F:System.Net.Http.Formatting.MediaTypeHeaderValueRange.SubtypeMediaRange">
            <summary>
            A subtype media range, e.g. "application/*".
            </summary>
        </member>
        <member name="F:System.Net.Http.Formatting.MediaTypeHeaderValueRange.AllMediaRange">
            <summary>
            An all media range, e.g. "*/*".
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.FormUrlEncodedParser">
            <summary>
            Buffer-oriented parsing of HTML form URL-ended, also known as <c>application/x-www-form-urlencoded</c>, data. 
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.FormUrlEncodedParser.#ctor(System.Collections.Generic.ICollection{System.Collections.Generic.KeyValuePair{System.String,System.String}},System.Int64)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.FormUrlEncodedParser"/> class.
            </summary>
            <param name="nameValuePairs">The collection to which name value pairs are added as they are parsed.</param>
            <param name="maxMessageSize">Maximum length of all the individual name value pairs.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.FormUrlEncodedParser.ParseBuffer(System.Byte[],System.Int32,System.Int32@,System.Boolean)">
            <summary>
            Parse a buffer of URL form-encoded name-value pairs and add them to the collection.
            Bytes are parsed in a consuming manner from the beginning of the buffer meaning that the same bytes can not be 
            present in the buffer.
            </summary>
            <param name="buffer">Buffer from where data is read</param>
            <param name="bytesReady">Size of buffer</param>
            <param name="bytesConsumed">Offset into buffer</param>
            <param name="isFinal">Indicates whether the end of the URL form-encoded data has been reached.</param>
            <returns>State of the parser. Call this method with new data until it reaches a final state.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.FormUrlEncodedParser.CurrentNameValuePair">
            <summary>
            Maintains information about the current header field being parsed. 
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.FormUrlEncodedParser.CurrentNameValuePair.CopyTo(System.Collections.Generic.ICollection{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Copies current name value pair field to the provided collection instance.
            </summary>
            <param name="nameValuePairs">The collection to copy into.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.FormUrlEncodedParser.CurrentNameValuePair.CopyNameOnlyTo(System.Collections.Generic.ICollection{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Copies current name-only to the provided collection instance.
            </summary>
            <param name="nameValuePairs">The collection to copy into.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.FormUrlEncodedParser.CurrentNameValuePair.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.Parsers.FormUrlEncodedParser.CurrentNameValuePair.Name">
            <summary>
            Gets the name of the name value pair.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.Parsers.FormUrlEncodedParser.CurrentNameValuePair.Value">
            <summary>
            Gets the value of the name value pair
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.HttpRequestHeaderParser">
            <summary>
            The <see cref="T:System.Net.Http.Formatting.Parsers.HttpRequestHeaderParser"/> combines <see cref="T:System.Net.Http.Formatting.Parsers.HttpRequestLineParser"/> for parsing the HTTP Request Line  
            and <see cref="T:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser"/> for parsing each header field. 
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.HttpRequestHeaderParser.#ctor(System.Net.Http.HttpUnsortedRequest)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.HttpRequestHeaderParser"/> class.
            </summary>
            <param name="httpRequest">The parsed HTTP request without any header sorting.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.HttpRequestHeaderParser.#ctor(System.Net.Http.HttpUnsortedRequest,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.HttpRequestHeaderParser"/> class.
            </summary>
            <param name="httpRequest">The parsed HTTP request without any header sorting.</param>
            <param name="maxRequestLineSize">The max length of the HTTP request line.</param>
            <param name="maxHeaderSize">The max length of the HTTP header.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.HttpRequestHeaderParser.ParseBuffer(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Parse an HTTP request header and fill in the <see cref="T:System.Net.Http.HttpRequestMessage"/> instance.
            </summary>
            <param name="buffer">Request buffer from where request is read</param>
            <param name="bytesReady">Size of request buffer</param>
            <param name="bytesConsumed">Offset into request buffer</param>
            <returns>State of the parser.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.HttpRequestLineParser">
            <summary>
            HTTP Request Line parser for parsing the first line (the request line) in an HTTP request.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.HttpRequestLineParser.#ctor(System.Net.Http.HttpUnsortedRequest,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.HttpRequestLineParser"/> class.
            </summary>
            <param name="httpRequest"><see cref="T:System.Net.Http.HttpUnsortedRequest"/> instance where the request line properties will be set as they are parsed.</param>
            <param name="maxRequestLineSize">Maximum length of HTTP header.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.HttpRequestLineParser.ParseBuffer(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Parse an HTTP request line. 
            Bytes are parsed in a consuming manner from the beginning of the request buffer meaning that the same bytes can not be 
            present in the request buffer.
            </summary>
            <param name="buffer">Request buffer from where request is read</param>
            <param name="bytesReady">Size of request buffer</param>
            <param name="bytesConsumed">Offset into request buffer</param>
            <returns>State of the parser.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.HttpResponseHeaderParser">
            <summary>
            The <see cref="T:System.Net.Http.Formatting.Parsers.HttpResponseHeaderParser"/> combines <see cref="T:System.Net.Http.Formatting.Parsers.HttpStatusLineParser"/> for parsing the HTTP Status Line  
            and <see cref="T:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser"/> for parsing each header field. 
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.HttpResponseHeaderParser.#ctor(System.Net.Http.HttpUnsortedResponse)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.HttpResponseHeaderParser"/> class.
            </summary>
            <param name="httpResponse">The parsed HTTP response without any header sorting.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.HttpResponseHeaderParser.#ctor(System.Net.Http.HttpUnsortedResponse,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.HttpResponseHeaderParser"/> class.
            </summary>
            <param name="httpResponse">The parsed HTTP response without any header sorting.</param>
            <param name="maxResponseLineSize">The max length of the HTTP status line.</param>
            <param name="maxHeaderSize">The max length of the HTTP header.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.HttpResponseHeaderParser.ParseBuffer(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Parse an HTTP response header and fill in the <see cref="T:System.Net.Http.HttpResponseMessage"/> instance.
            </summary>
            <param name="buffer">Response buffer from where response is read</param>
            <param name="bytesReady">Size of response buffer</param>
            <param name="bytesConsumed">Offset into response buffer</param>
            <returns>State of the parser.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.HttpStatusLineParser">
            <summary>
            HTTP Status line parser for parsing the first line (the status line) in an HTTP response.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.HttpStatusLineParser.#ctor(System.Net.Http.HttpUnsortedResponse,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.HttpStatusLineParser"/> class.
            </summary>
            <param name="httpResponse"><see cref="T:System.Net.Http.HttpUnsortedResponse"/> instance where the response line properties will be set as they are parsed.</param>
            <param name="maxStatusLineSize">Maximum length of HTTP header.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.HttpStatusLineParser.ParseBuffer(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Parse an HTTP status line. 
            Bytes are parsed in a consuming manner from the beginning of the response buffer meaning that the same bytes can not be 
            present in the response buffer.
            </summary>
            <param name="buffer">Response buffer from where response is read</param>
            <param name="bytesReady">Size of response buffer</param>
            <param name="bytesConsumed">Offset into response buffer</param>
            <returns>State of the parser.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser">
            <summary>
            Buffer-oriented RFC 5322 style Internet Message Format parser which can be used to pass header 
            fields used in HTTP and MIME message entities. 
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser.#ctor(System.Net.Http.Headers.HttpHeaders,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser"/> class.
            </summary>
            <param name="headers">Concrete <see cref="T:System.Net.Http.Headers.HttpHeaders"/> instance where header fields are added as they are parsed.</param>
            <param name="maxHeaderSize">Maximum length of complete header containing all the individual header fields.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser.ParseBuffer(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Parse a buffer of RFC 5322 style header fields and add them to the <see cref="T:System.Net.Http.Headers.HttpHeaders"/> collection.
            Bytes are parsed in a consuming manner from the beginning of the buffer meaning that the same bytes can not be 
            present in the buffer.
            </summary>
            <param name="buffer">Request buffer from where request is read</param>
            <param name="bytesReady">Size of request buffer</param>
            <param name="bytesConsumed">Offset into request buffer</param>
            <returns>State of the parser. Call this method with new data until it reaches a final state.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser.CurrentHeaderFieldStore">
            <summary>
            Maintains information about the current header field being parsed. 
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser.CurrentHeaderFieldStore.CopyTo(System.Net.Http.Headers.HttpHeaders)">
            <summary>
            Copies current header field to the provided <see cref="T:System.Net.Http.Headers.HttpHeaders"/> instance.
            </summary>
            <param name="headers">The headers.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser.CurrentHeaderFieldStore.IsEmpty">
            <summary>
            Determines whether this instance is empty.
            </summary>
            <returns>
              <c>true</c> if this instance is empty; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser.CurrentHeaderFieldStore.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser.CurrentHeaderFieldStore.Name">
            <summary>
            Gets the header field name.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser.CurrentHeaderFieldStore.Value">
            <summary>
            Gets the header field value.
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.MimeMultipartBodyPartParser">
            <summary>
            Complete MIME multipart parser that combines <see cref="T:System.Net.Http.Formatting.Parsers.MimeMultipartParser"/> for parsing the MIME message into individual body parts 
            and <see cref="T:System.Net.Http.Formatting.Parsers.InternetMessageFormatHeaderParser"/> for parsing each body part into a MIME header and a MIME body. The caller of the parser is returned
            the resulting MIME bodies which can then be written to some output.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartBodyPartParser.#ctor(System.Net.Http.HttpContent,System.Net.Http.MultipartStreamProvider)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.MimeMultipartBodyPartParser"/> class.
            </summary>
            <param name="content">An existing <see cref="T:System.Net.Http.HttpContent"/> instance to use for the object's content.</param>
            <param name="streamProvider">A stream provider providing output streams for where to write body parts as they are parsed.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartBodyPartParser.#ctor(System.Net.Http.HttpContent,System.Net.Http.MultipartStreamProvider,System.Int64,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.MimeMultipartBodyPartParser"/> class.
            </summary>
            <param name="content">An existing <see cref="T:System.Net.Http.HttpContent"/> instance to use for the object's content.</param>
            <param name="streamProvider">A stream provider providing output streams for where to write body parts as they are parsed.</param>
            <param name="maxMessageSize">The max length of the entire MIME multipart message.</param>
            <param name="maxBodyPartHeaderSize">The max length of the MIME header within each MIME body part.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartBodyPartParser.IsMimeMultipartContent(System.Net.Http.HttpContent)">
            <summary>
            Determines whether the specified content is MIME multipart content.
            </summary>
            <param name="content">The content.</param>
            <returns>
              <c>true</c> if the specified content is MIME multipart content; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartBodyPartParser.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartBodyPartParser.ParseBuffer(System.Byte[],System.Int32)">
            <summary>
            Parses the data provided and generates parsed MIME body part bodies in the form of <see cref="T:System.ArraySegment`1"/> which are ready to 
            write to the output stream.
            </summary>
            <param name="data">The data to parse</param>
            <param name="bytesRead">The number of bytes available in the input data</param>
            <returns>Parsed <see cref="T:System.Net.Http.MimeBodyPart"/> instances.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartBodyPartParser.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.MimeMultipartParser">
            <summary>
            Buffer-oriented MIME multipart parser.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.#ctor(System.String,System.Int64)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.MimeMultipartParser"/> class.
            </summary>
            <param name="boundary">Message boundary</param>
            <param name="maxMessageSize">Maximum length of entire MIME multipart message.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.ParseBuffer(System.Byte[],System.Int32,System.Int32@,System.ArraySegment{System.Byte}@,System.ArraySegment{System.Byte}@,System.Boolean@)">
            <summary>
            Parse a MIME multipart message. Bytes are parsed in a consuming
            manner from the beginning of the request buffer meaning that the same bytes can not be 
            present in the request buffer.
            </summary>
            <param name="buffer">Request buffer from where request is read</param>
            <param name="bytesReady">Size of request buffer</param>
            <param name="bytesConsumed">Offset into request buffer</param>
            <param name="remainingBodyPart">Any body part that was considered as a potential MIME multipart boundary but which was in fact part of the body.</param>
            <param name="bodyPart">The bulk of the body part.</param>
            <param name="isFinalBodyPart">Indicates whether the final body part has been found.</param>
            <remarks>In order to get the complete body part, the caller is responsible for concatenating the contents of the 
            <paramref name="remainingBodyPart"/> and <paramref name="bodyPart"/> out parameters.</remarks>
            <returns>State of the parser.</returns>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.MimeMultipartParser.State">
            <summary>
            Represents the overall state of the <see cref="T:System.Net.Http.Formatting.Parsers.MimeMultipartParser"/>.
            </summary>
        </member>
        <member name="F:System.Net.Http.Formatting.Parsers.MimeMultipartParser.State.NeedMoreData">
            <summary>
            Need more data
            </summary>
        </member>
        <member name="F:System.Net.Http.Formatting.Parsers.MimeMultipartParser.State.BodyPartCompleted">
            <summary>
            Parsing of a complete body part succeeded.
            </summary>
        </member>
        <member name="F:System.Net.Http.Formatting.Parsers.MimeMultipartParser.State.Invalid">
            <summary>
            Bad data format
            </summary>
        </member>
        <member name="F:System.Net.Http.Formatting.Parsers.MimeMultipartParser.State.DataTooBig">
            <summary>
            Data exceeds the allowed size
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore">
            <summary>
            Maintains information about the current body part being parsed.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore"/> class.
            </summary>
            <param name="referenceBoundary">The reference boundary.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.ResetBoundaryOffset">
            <summary>
            Resets the boundary offset.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.ResetBoundary">
            <summary>
            Resets the boundary.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.AppendBoundary(System.Byte)">
            <summary>
            Appends byte to the current boundary.
            </summary>
            <param name="data">The data to append to the boundary.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.AppendBoundary(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Appends array of bytes to the current boundary.
            </summary>
            <param name="data">The data to append to the boundary.</param>
            <param name="offset">The offset into the data.</param>
            <param name="count">The number of bytes to append.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.GetDiscardedBoundary">
            <summary>
            Gets the discarded boundary.
            </summary>
            <returns>An <see cref="T:System.ArraySegment`1"/> containing the discarded boundary.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.IsBoundaryValid">
            <summary>
            Determines whether current boundary is valid.
            </summary>
            <returns>
              <c>true</c> if curent boundary is valid; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.ClearBodyPart">
            <summary>
            Clears the body part.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.ClearAll">
            <summary>
            Clears all.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.HasPotentialBoundaryLeftOver">
            <summary>
            Gets or sets a value indicating whether this instance has potential boundary left over.
            </summary>
            <value>
            <c>true</c> if this instance has potential boundary left over; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.BoundaryDelta">
            <summary>
            Gets the boundary delta.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.BodyPart">
            <summary>
            Gets or sets the body part.
            </summary>
            <value>
            The body part.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.Parsers.MimeMultipartParser.CurrentBodyPartStore.IsFinal">
            <summary>
            Gets a value indicating whether this body part instance is final.
            </summary>
            <value>
              <c>true</c> if this body part instance is final; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:System.Net.Http.Formatting.Parsers.ParserState">
            <summary>
            Represents the overall state of various parsers.
            </summary>
        </member>
        <member name="F:System.Net.Http.Formatting.Parsers.ParserState.NeedMoreData">
            <summary>
            Need more data
            </summary>
        </member>
        <member name="F:System.Net.Http.Formatting.Parsers.ParserState.Done">
            <summary>
            Parsing completed (final)
            </summary>
        </member>
        <member name="F:System.Net.Http.Formatting.Parsers.ParserState.Invalid">
            <summary>
            Bad data format (final)
            </summary>
        </member>
        <member name="F:System.Net.Http.Formatting.Parsers.ParserState.DataTooBig">
            <summary>
            Data exceeds the allowed size (final)
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.StringComparisonHelper">
            <summary>
            Helper class for validating <see cref="T:System.StringComparison"/> values.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.StringComparisonHelper.IsDefined(System.StringComparison)">
            <summary>
            Determines whether the specified <paramref name="value"/> is defined by the <see cref="T:System.StringComparison"/>
            enumeration.
            </summary>
            <param name="value">The value to verify.</param>
            <returns>
            <c>true</c> if the specified options is defined; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:System.Net.Http.Formatting.StringComparisonHelper.Validate(System.StringComparison,System.String)">
            <summary>
            Validates the specified <paramref name="value"/> and throws an <see cref="T:System.ArgumentException"/>
            exception if not valid.
            </summary>
            <param name="value">The value to validate.</param>
            <param name="parameterName">Name of the parameter to use if throwing exception.</param>
        </member>
        <member name="T:System.Net.Http.Formatting.XmlMediaTypeFormatter">
            <summary>
            <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> class to handle Xml.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/> class.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.#ctor(System.Net.Http.Formatting.XmlMediaTypeFormatter)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/> class.
            </summary>
            <param name="formatter">The <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/> instance to copy settings from.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.SetSerializer(System.Type,System.Runtime.Serialization.XmlObjectSerializer)">
            <summary>
            Registers the <see cref="T:System.Runtime.Serialization.XmlObjectSerializer"/> to use to read or write
            the specified <paramref name="type"/>.
            </summary>
            <param name="type">The type of object that will be serialized or deserialized with <paramref name="serializer"/>.</param>
            <param name="serializer">The <see cref="T:System.Runtime.Serialization.XmlObjectSerializer"/> instance to use.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.SetSerializer``1(System.Runtime.Serialization.XmlObjectSerializer)">
            <summary>
            Registers the <see cref="T:System.Runtime.Serialization.XmlObjectSerializer"/> to use to read or write
            the specified <typeparamref name="T"/> type.
            </summary>
            <typeparam name="T">The type of object that will be serialized or deserialized with <paramref name="serializer"/>.</typeparam>
            <param name="serializer">The <see cref="T:System.Runtime.Serialization.XmlObjectSerializer"/> instance to use.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.SetSerializer(System.Type,System.Xml.Serialization.XmlSerializer)">
            <summary>
            Registers the <see cref="T:System.Xml.Serialization.XmlSerializer"/> to use to read or write
            the specified <paramref name="type"/>.
            </summary>
            <param name="type">The type of objects for which <paramref name="serializer"/> will be used.</param>
            <param name="serializer">The <see cref="T:System.Xml.Serialization.XmlSerializer"/> instance to use.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.SetSerializer``1(System.Xml.Serialization.XmlSerializer)">
            <summary>
            Registers the <see cref="T:System.Xml.Serialization.XmlSerializer"/> to use to read or write
            the specified <typeparamref name="T"/> type.
            </summary>
            <typeparam name="T">The type of object that will be serialized or deserialized with <paramref name="serializer"/>.</typeparam>
            <param name="serializer">The <see cref="T:System.Xml.Serialization.XmlSerializer"/> instance to use.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.RemoveSerializer(System.Type)">
            <summary>
            Unregisters the serializer currently associated with the given <paramref name="type"/>.
            </summary>
            <remarks>
            Unless another serializer is registered for the <paramref name="type"/>, a default one will be created.
            </remarks>
            <param name="type">The type of object whose serializer should be removed.</param>
            <returns><c>true</c> if a serializer was registered for the <paramref name="type"/>; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.CanReadType(System.Type)">
            <summary>
            Determines whether this <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/> can read objects
            of the specified <paramref name="type"/>.
            </summary>
            <param name="type">The type of object that will be read.</param>
            <returns><c>true</c> if objects of this <paramref name="type"/> can be read, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.CanWriteType(System.Type)">
            <summary>
            Determines whether this <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/> can write objects
            of the specified <paramref name="type"/>.
            </summary>
            <param name="type">The type of object that will be written.</param>
            <returns><c>true</c> if objects of this <paramref name="type"/> can be written, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.ReadFromStreamAsync(System.Type,System.IO.Stream,System.Net.Http.HttpContent,System.Net.Http.Formatting.IFormatterLogger)">
            <summary>
            Called during deserialization to read an object of the specified <paramref name="type"/>
            from the specified <paramref name="readStream"/>.
            </summary>
            <param name="type">The type of object to read.</param>
            <param name="readStream">The <see cref="T:System.IO.Stream"/> from which to read.</param>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> for the content being read.</param>
            <param name="formatterLogger">The <see cref="T:System.Net.Http.Formatting.IFormatterLogger"/> to log events to.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> whose result will be the object instance that has been read.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.GetDeserializer(System.Type,System.Net.Http.HttpContent)">
            <summary>
            Called during deserialization to get the XML serializer to use for deserializing objects.
            </summary>
            <param name="type">The type of object to deserialize.</param>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> for the content being read.</param>
            <returns>An instance of <see cref="T:System.Runtime.Serialization.XmlObjectSerializer"/> or <see cref="T:System.Xml.Serialization.XmlSerializer"/> to use for deserializing the object.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.CreateXmlReader(System.IO.Stream,System.Net.Http.HttpContent)">
            <summary>
            Called during deserialization to get the XML reader to use for reading objects from the stream.
            </summary>
            <param name="readStream">The <see cref="T:System.IO.Stream"/> to read from.</param>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> for the content being read.</param>
            <returns>The <see cref="T:System.Xml.XmlReader"/> to use for reading objects.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.WriteToStreamAsync(System.Type,System.Object,System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext,System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.GetSerializer(System.Type,System.Object,System.Net.Http.HttpContent)">
            <summary>
            Called during serialization to get the XML serializer to use for serializing objects.
            </summary>
            <param name="type">The type of object to serialize.</param>
            <param name="value">The object to serialize.</param>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> for the content being written.</param>
            <returns>An instance of <see cref="T:System.Runtime.Serialization.XmlObjectSerializer"/> or <see cref="T:System.Xml.Serialization.XmlSerializer"/> to use for serializing the object.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.CreateXmlWriter(System.IO.Stream,System.Net.Http.HttpContent)">
            <summary>
            Called during serialization to get the XML writer to use for writing objects to the stream.
            </summary>
            <param name="writeStream">The <see cref="T:System.IO.Stream"/> to write to.</param>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> for the content being written.</param>
            <returns>The <see cref="T:System.Xml.XmlWriter"/> to use for writing objects.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.CreateXmlSerializer(System.Type)">
            <summary>
            Called during deserialization to get the XML serializer.
            </summary>
            <param name="type">The type of object that will be serialized or deserialized.</param>
            <returns>The <see cref="T:System.Xml.Serialization.XmlSerializer"/> used to serialize the object.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.CreateDataContractSerializer(System.Type)">
            <summary>
            Called during deserialization to get the DataContractSerializer serializer.
            </summary>
            <param name="type">The type of object that will be serialized or deserialized.</param>
            <returns>The <see cref="T:System.Runtime.Serialization.DataContractSerializer"/> used to serialize the object.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.InvokeCreateXmlReader(System.IO.Stream,System.Net.Http.HttpContent)">
            <summary>
            This method is to support infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.InvokeCreateXmlWriter(System.IO.Stream,System.Net.Http.HttpContent)">
            <summary>
            This method is to support infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.InvokeGetDeserializer(System.Type,System.Net.Http.HttpContent)">
            <summary>
            This method is to support infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.XmlMediaTypeFormatter.InvokeGetSerializer(System.Type,System.Object,System.Net.Http.HttpContent)">
            <summary>
            This method is to support infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.XmlMediaTypeFormatter.DefaultMediaType">
            <summary>
            Gets the default media type for xml, namely "application/xml".
            </summary>
            <value>
            <remarks>
            The default media type does not have any <c>charset</c> parameter as 
            the <see cref="T:System.Text.Encoding"/> can be configured on a per <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/> 
            instance basis.
            </remarks>
            Because <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/> is mutable, the value
            returned will be a new instance every time.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.XmlMediaTypeFormatter.UseXmlSerializer">
            <summary>
            Gets or sets a value indicating whether to use <see cref="T:System.Xml.Serialization.XmlSerializer"/> instead of <see cref="T:System.Runtime.Serialization.DataContractSerializer"/> by default.
            </summary>
            <value>
                <c>true</c> if use <see cref="T:System.Xml.Serialization.XmlSerializer"/> by default; otherwise, <c>false</c>. The default is <c>false</c>.
            </value>
        </member>
        <member name="P:System.Net.Http.Formatting.XmlMediaTypeFormatter.Indent">
            <summary>
            Gets or sets a value indicating whether to indent elements when writing data. 
            </summary>
        </member>
        <member name="P:System.Net.Http.Formatting.XmlMediaTypeFormatter.WriterSettings">
            <summary>
            Gets the <see cref="T:System.Xml.XmlWriterSettings"/> to be used while writing.
            </summary>
        </member>
        <member name="T:System.Net.Http.Formatting.HttpValueCollection">
            <summary>
             NameValueCollection to represent form data and to generate form data output.
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.HttpValueCollection.#ctor">
            <summary>
            Creates a new <see cref="T:System.Net.Http.Formatting.HttpValueCollection"/> instance 
            </summary>
        </member>
        <member name="M:System.Net.Http.Formatting.HttpValueCollection.Add(System.String,System.String)">
            <summary>
            Adds a name-value pair to the collection.
            </summary>
            <param name="name">The name to be added as a case insensitive string.</param>
            <param name="value">The value to be added.</param>
        </member>
        <member name="M:System.Net.Http.Formatting.HttpValueCollection.ToString">
            <summary>
            Converts the content of this instance to its equivalent string representation.
            </summary>
            <returns>The string representation of the value of this instance, multiple values with a single key are comma separated.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.HttpValueCollection.Get(System.String)">
            <summary>
            Gets the values associated with the specified name
            combined into one comma-separated list.
            </summary>
            <param name="name">The name of the entry that contains the values to get. The name can be null.</param>
            <returns>
            A <see cref="T:System.String"/> that contains a comma-separated list of url encoded values associated
            with the specified name if found; otherwise, null. The values are Url encoded.
            </returns>
        </member>
        <member name="M:System.Net.Http.Formatting.HttpValueCollection.GetValues(System.String)">
            <summary>
            Gets the values associated with the specified name.
            </summary>
            <param name="name">The <see cref="T:System.String"/></param>
            <returns>A <see cref="T:System.String"/> that contains url encoded values associated with the name, or null if the name does not exist.</returns>
        </member>
        <member name="M:System.Net.Http.Formatting.HttpValueCollection.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:System.Net.Http.Formatting.HttpValueCollection.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="P:System.Net.Http.Formatting.HttpValueCollection.Item(System.String)">
            <summary>
            Gets the values associated with the specified name
            combined into one comma-separated list.
            </summary>
            <param name="name">The name of the entry that contains the values to get. The name can be null.</param>
            <returns>A <see cref="T:System.String"/> that contains a comma-separated list of url encoded values associated
            with the specified name if found; otherwise, null. The values are Url encoded.</returns>
        </member>
        <member name="P:System.Net.Http.Formatting.HttpValueCollection.Count">
            <summary>
            Gets the number of names in the collection.
            </summary>
        </member>
        <member name="T:System.Net.Http.UriExtensions">
            <summary>
            Extension methods to allow strongly typed objects to be read from the query component of <see cref="T:System.Uri"/> instances.
            </summary>
        </member>
        <member name="M:System.Net.Http.UriExtensions.ParseQueryString(System.Uri)">
            <summary>
            Parses the query portion of the specified <see cref="T:System.Uri"/>.
            </summary>
            <param name="address">The <see cref="T:System.Uri"/> instance from which to read.</param>
            <returns>A <see cref="T:System.Net.Http.Formatting.HttpValueCollection"/> containing the parsed result.</returns>
        </member>
        <member name="M:System.Net.Http.UriExtensions.TryReadQueryAsJson(System.Uri,Newtonsoft.Json.Linq.JObject@)">
            <summary>
            Reads HTML form URL encoded data provided in the <see cref="T:System.Uri"/> query component as a <see cref="T:Newtonsoft.Json.Linq.JObject"/> object.
            </summary>
            <param name="address">The <see cref="T:System.Uri"/> instance from which to read.</param>
            <param name="value">An object to be initialized with this instance or null if the conversion cannot be performed.</param>
            <returns><c>true</c> if the query component can be read as <see cref="T:Newtonsoft.Json.Linq.JObject"/>; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.Net.Http.UriExtensions.TryReadQueryAs(System.Uri,System.Type,System.Object@)">
            <summary>
            Reads HTML form URL encoded data provided in the <see cref="T:System.Uri"/> query component as an <see cref="T:System.Object"/> of the given <paramref name="type"/>.
            </summary>
            <param name="address">The <see cref="T:System.Uri"/> instance from which to read.</param>
            <param name="type">The type of the object to read.</param>
            <param name="value">An object to be initialized with this instance or null if the conversion cannot be performed.</param>
            <returns><c>true</c> if the query component can be read as the specified type; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.Net.Http.UriExtensions.TryReadQueryAs``1(System.Uri,``0@)">
            <summary>
            Reads HTML form URL encoded data provided in the <see cref="T:System.Uri"/> query component as an <see cref="T:System.Object"/> of type <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">The type of the object to read.</typeparam>
            <param name="address">The <see cref="T:System.Uri"/> instance from which to read.</param>
            <param name="value">An object to be initialized with this instance or null if the conversion cannot be performed.</param>
            <returns><c>true</c> if the query component can be read as the specified type; otherwise <c>false</c>.</returns>
        </member>
        <member name="T:System.Net.Http.Handlers.HttpProgressEventArgs">
            <summary>
            Provides data for the events generated by <see cref="T:System.Net.Http.Handlers.ProgressMessageHandler"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.Handlers.HttpProgressEventArgs.#ctor(System.Int32,System.Object,System.Int64,System.Nullable{System.Int64})">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Handlers.HttpProgressEventArgs"/> with the parameters given.
            </summary>
            <param name="progressPercentage">The percent completed of the overall exchange.</param>
            <param name="userToken">Any user state provided as part of reading or writing the data.</param>
            <param name="bytesTransferred">The current number of bytes either received or sent.</param>
            <param name="totalBytes">The total number of bytes expected to be received or sent.</param>
        </member>
        <member name="P:System.Net.Http.Handlers.HttpProgressEventArgs.BytesTransferred">
            <summary>
            Gets the current number of bytes transferred.
            </summary>
        </member>
        <member name="P:System.Net.Http.Handlers.HttpProgressEventArgs.TotalBytes">
            <summary>
            Gets the total number of expected bytes to be sent or received. If the number is not known then this is null.
            </summary>
        </member>
        <member name="T:System.Net.Http.Handlers.ProgressContent">
            <summary>
            Wraps an inner <see cref="T:System.Net.Http.HttpContent"/> in order to insert a <see cref="T:System.Net.Http.Handlers.ProgressStream"/> on writing data.
            </summary>
        </member>
        <member name="T:System.Net.Http.Handlers.ProgressMessageHandler">
            <summary>
            The <see cref="T:System.Net.Http.Handlers.ProgressMessageHandler"/> provides a mechanism for getting progress event notifications
            when sending and receiving data in connection with exchanging HTTP requests and responses.
            Register event handlers for the events <see cref="E:System.Net.Http.Handlers.ProgressMessageHandler.HttpSendProgress"/> and <see cref="E:System.Net.Http.Handlers.ProgressMessageHandler.HttpReceiveProgress"/>
            to see events for data being sent and received.
            </summary>
        </member>
        <member name="M:System.Net.Http.Handlers.ProgressMessageHandler.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Handlers.ProgressMessageHandler"/> class.
            </summary>
        </member>
        <member name="M:System.Net.Http.Handlers.ProgressMessageHandler.#ctor(System.Net.Http.HttpMessageHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.Handlers.ProgressMessageHandler"/> class.
            </summary>
            <param name="innerHandler">The inner handler to which this handler submits requests.</param>
        </member>
        <member name="M:System.Net.Http.Handlers.ProgressMessageHandler.OnHttpRequestProgress(System.Net.Http.HttpRequestMessage,System.Net.Http.Handlers.HttpProgressEventArgs)">
            <summary>
            Raises the <see cref="E:System.Net.Http.Handlers.ProgressMessageHandler.HttpSendProgress"/> event.
            </summary>
            <param name="request">The request.</param>
            <param name="e">The <see cref="T:System.Net.Http.Handlers.HttpProgressEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:System.Net.Http.Handlers.ProgressMessageHandler.OnHttpResponseProgress(System.Net.Http.HttpRequestMessage,System.Net.Http.Handlers.HttpProgressEventArgs)">
            <summary>
            Raises the <see cref="E:System.Net.Http.Handlers.ProgressMessageHandler.HttpReceiveProgress"/> event.
            </summary>
            <param name="request">The request.</param>
            <param name="e">The <see cref="T:System.Net.Http.Handlers.HttpProgressEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="E:System.Net.Http.Handlers.ProgressMessageHandler.HttpSendProgress">
            <summary>
            Occurs every time the client sending data is making progress.
            </summary>
        </member>
        <member name="E:System.Net.Http.Handlers.ProgressMessageHandler.HttpReceiveProgress">
            <summary>
            Occurs every time the client receiving data is making progress.
            </summary>
        </member>
        <member name="T:System.Net.Http.Handlers.ProgressStream">
            <summary>
            This implementation of <see cref="T:System.Net.Http.Internal.DelegatingStream"/> registers how much data has been 
            read (received) versus written (sent) for a particular HTTP operation. The implementation
            is client side in that the total bytes to send is taken from the request and the total
            bytes to read is taken from the response. In a server side scenario, it would be the
            other way around (reading the request and writing the response).
            </summary>
        </member>
        <member name="T:System.Net.Http.Internal.DelegatingStream">
            <summary>
            Stream that delegates to inner stream. 
            This is taken from System.Net.Http
            </summary>
        </member>
        <member name="T:System.Net.Http.HttpClientExtensions">
            <summary>
            Extension methods that aid in making formatted requests using <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as JSON.
            </summary>
            <remarks>
            This method uses a default instance of <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as JSON.
            </summary>
            <remarks>
            This method uses a default instance of <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as JSON.
            </summary>
            <remarks>
            This method uses a default instance of <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Threading.CancellationToken)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as JSON.
            </summary>
            <remarks>
            This method uses a default instance of <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsXmlAsync``1(System.Net.Http.HttpClient,System.String,``0)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as XML.
            </summary>
            <remarks>
            This method uses the default instance of <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsXmlAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as XML.
            </summary>
            <remarks>
            This method uses the default instance of <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsXmlAsync``1(System.Net.Http.HttpClient,System.Uri,``0)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as XML.
            </summary>
            <remarks>
            This method uses the default instance of <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsXmlAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Threading.CancellationToken)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as XML.
            </summary>
            <remarks>
            This method uses the default instance of <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Threading.CancellationToken)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue,System.Threading.CancellationToken)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Net.Http.Formatting.MediaTypeFormatter)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Threading.CancellationToken)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PostAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue,System.Threading.CancellationToken)">
            <summary>
            Sends a POST request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as JSON.
            </summary>
            <remarks>
            This method uses a default instance of <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsJsonAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as JSON.
            </summary>
            <remarks>
            This method uses a default instance of <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as JSON.
            </summary>
            <remarks>
            This method uses a default instance of <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsJsonAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Threading.CancellationToken)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as JSON.
            </summary>
            <remarks>
            This method uses a default instance of <see cref="T:System.Net.Http.Formatting.JsonMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsXmlAsync``1(System.Net.Http.HttpClient,System.String,``0)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as XML.
            </summary>
            <remarks>
            This method uses a default instance of <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsXmlAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as XML.
            </summary>
            <remarks>
            This method uses the default instance of <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsXmlAsync``1(System.Net.Http.HttpClient,System.Uri,``0)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as XML.
            </summary>
            <remarks>
            This method uses the default instance of <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsXmlAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Threading.CancellationToken)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with the given <paramref name="value"/> serialized
            as XML.
            </summary>
            <remarks>
            This method uses the default instance of <see cref="T:System.Net.Http.Formatting.XmlMediaTypeFormatter"/>.
            </remarks>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Threading.CancellationToken)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue,System.Threading.CancellationToken)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Net.Http.Formatting.MediaTypeFormatter)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Threading.CancellationToken)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <seealso cref="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.String,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)"/>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Threading.CancellationToken)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientExtensions.PutAsync``1(System.Net.Http.HttpClient,System.Uri,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue,System.Threading.CancellationToken)">
            <summary>
            Sends a PUT request as an asynchronous operation to the specified Uri with <paramref name="value"/>
            serialized using the given <paramref name="formatter"/>.
            </summary>
            <typeparam name="T">The type of <paramref name="value"/>.</typeparam>
            <param name="client">The client used to make the request.</param>
            <param name="requestUri">The Uri the request is sent to.</param>
            <param name="value">The value that will be placed in the request's entity body.</param>
            <param name="formatter">The formatter used to serialize the <paramref name="value"/>.</param>
            <param name="mediaType">The authoritative value of the request's content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing the asynchronous operation.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientFactory.Create(System.Net.Http.DelegatingHandler[])">
            <summary>
            Creates a new <see cref="T:System.Net.Http.HttpClient"/> instance configured with the handlers provided and with an
            <see cref="T:System.Net.Http.HttpClientHandler"/> as the innermost handler.
            </summary>
            <param name="handlers">An ordered list of <see cref="T:System.Net.Http.DelegatingHandler"/> instances to be invoked as an 
            <see cref="T:System.Net.Http.HttpRequestMessage"/> travels from the <see cref="T:System.Net.Http.HttpClient"/> to the network and an 
            <see cref="T:System.Net.Http.HttpResponseMessage"/> travels from the network back to <see cref="T:System.Net.Http.HttpClient"/>.
            The handlers are invoked in a top-down fashion. That is, the first entry is invoked first for 
            an outbound request message but last for an inbound response message.</param>
            <returns>An <see cref="T:System.Net.Http.HttpClient"/> instance with the configured handlers.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientFactory.Create(System.Net.Http.HttpMessageHandler,System.Net.Http.DelegatingHandler[])">
            <summary>
            Creates a new <see cref="T:System.Net.Http.HttpClient"/> instance configured with the handlers provided and with the
            provided <paramref name="innerHandler"/> as the innermost handler.
            </summary>
            <param name="innerHandler">The inner handler represents the destination of the HTTP message channel.</param>
            <param name="handlers">An ordered list of <see cref="T:System.Net.Http.DelegatingHandler"/> instances to be invoked as an 
            <see cref="T:System.Net.Http.HttpRequestMessage"/> travels from the <see cref="T:System.Net.Http.HttpClient"/> to the network and an 
            <see cref="T:System.Net.Http.HttpResponseMessage"/> travels from the network back to <see cref="T:System.Net.Http.HttpClient"/>.
            The handlers are invoked in a top-down fashion. That is, the first entry is invoked first for 
            an outbound request message but last for an inbound response message.</param>
            <returns>An <see cref="T:System.Net.Http.HttpClient"/> instance with the configured handlers.</returns>
        </member>
        <member name="M:System.Net.Http.HttpClientFactory.CreatePipeline(System.Net.Http.HttpMessageHandler,System.Collections.Generic.IEnumerable{System.Net.Http.DelegatingHandler})">
            <summary>
            Creates an instance of an <see cref="T:System.Net.Http.HttpMessageHandler"/> using the <see cref="T:System.Net.Http.DelegatingHandler"/> instances
            provided by <paramref name="handlers"/>. The resulting pipeline can be used to manually create <see cref="T:System.Net.Http.HttpClient"/>
            or <see cref="T:System.Net.Http.HttpMessageInvoker"/> instances with customized message handlers.
            </summary>
            <param name="innerHandler">The inner handler represents the destination of the HTTP message channel.</param>
            <param name="handlers">An ordered list of <see cref="T:System.Net.Http.DelegatingHandler"/> instances to be invoked as part 
            of sending an <see cref="T:System.Net.Http.HttpRequestMessage"/> and receiving an <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            The handlers are invoked in a top-down fashion. That is, the first entry is invoked first for 
            an outbound request message but last for an inbound response message.</param>
            <returns>The HTTP message channel.</returns>
        </member>
        <member name="T:System.Net.Http.HttpContentExtensions">
            <summary>
            Extension methods to allow strongly typed objects to be read from <see cref="T:System.Net.Http.HttpContent"/> instances.
            </summary>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync(System.Net.Http.HttpContent,System.Type)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified <paramref name="type"/>
            from the <paramref name="content"/> instance.
            </summary>
            <remarks>This override use the built-in collection of formatters.</remarks>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="type">The type of the object to read.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync(System.Net.Http.HttpContent,System.Type,System.Threading.CancellationToken)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified <paramref name="type"/>
            from the <paramref name="content"/> instance.
            </summary>
            <remarks>This override use the built-in collection of formatters.</remarks>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="type">The type of the object to read.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync(System.Net.Http.HttpContent,System.Type,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified <paramref name="type"/>
            from the <paramref name="content"/> instance using one of the provided <paramref name="formatters"/>
            to deserialize the content.
            </summary>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="type">The type of the object to read.</param>
            <param name="formatters">The collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances to use.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync(System.Net.Http.HttpContent,System.Type,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Threading.CancellationToken)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified <paramref name="type"/>
            from the <paramref name="content"/> instance using one of the provided <paramref name="formatters"/>
            to deserialize the content.
            </summary>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="type">The type of the object to read.</param>
            <param name="formatters">The collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances to use.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync(System.Net.Http.HttpContent,System.Type,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Net.Http.Formatting.IFormatterLogger)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified <paramref name="type"/>
            from the <paramref name="content"/> instance using one of the provided <paramref name="formatters"/>
            to deserialize the content.
            </summary>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="type">The type of the object to read.</param>
            <param name="formatters">The collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances to use.</param>
            <param name="formatterLogger">The <see cref="T:System.Net.Http.Formatting.IFormatterLogger"/> to log events to.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync(System.Net.Http.HttpContent,System.Type,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Net.Http.Formatting.IFormatterLogger,System.Threading.CancellationToken)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified <paramref name="type"/>
            from the <paramref name="content"/> instance using one of the provided <paramref name="formatters"/>
            to deserialize the content.
            </summary>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="type">The type of the object to read.</param>
            <param name="formatters">The collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances to use.</param>
            <param name="formatterLogger">The <see cref="T:System.Net.Http.Formatting.IFormatterLogger"/> to log events to.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync``1(System.Net.Http.HttpContent)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified
            type <typeparamref name="T"/> from the <paramref name="content"/> instance.
            </summary>
            <remarks>This override use the built-in collection of formatters.</remarks>
            <typeparam name="T">The type of the object to read.</typeparam>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync``1(System.Net.Http.HttpContent,System.Threading.CancellationToken)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified
            type <typeparamref name="T"/> from the <paramref name="content"/> instance.
            </summary>
            <remarks>This override use the built-in collection of formatters.</remarks>
            <typeparam name="T">The type of the object to read.</typeparam>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync``1(System.Net.Http.HttpContent,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified
            type <typeparamref name="T"/> from the <paramref name="content"/> instance.
            </summary>
            <typeparam name="T">The type of the object to read.</typeparam>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="formatters">The collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances to use.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync``1(System.Net.Http.HttpContent,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Threading.CancellationToken)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified
            type <typeparamref name="T"/> from the <paramref name="content"/> instance.
            </summary>
            <typeparam name="T">The type of the object to read.</typeparam>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="formatters">The collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances to use.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync``1(System.Net.Http.HttpContent,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Net.Http.Formatting.IFormatterLogger)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified
            type <typeparamref name="T"/> from the <paramref name="content"/> instance.
            </summary>
            <typeparam name="T">The type of the object to read.</typeparam>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="formatters">The collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances to use.</param>
            <param name="formatterLogger">The <see cref="T:System.Net.Http.Formatting.IFormatterLogger"/> to log events to.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentExtensions.ReadAsAsync``1(System.Net.Http.HttpContent,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Net.Http.Formatting.IFormatterLogger,System.Threading.CancellationToken)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that will yield an object of the specified
            type <typeparamref name="T"/> from the <paramref name="content"/> instance.
            </summary>
            <typeparam name="T">The type of the object to read.</typeparam>
            <param name="content">The <see cref="T:System.Net.Http.HttpContent"/> instance from which to read.</param>
            <param name="formatters">The collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> instances to use.</param>
            <param name="formatterLogger">The <see cref="T:System.Net.Http.Formatting.IFormatterLogger"/> to log events to.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an object of the specified type.</returns>
        </member>
        <member name="T:System.Net.Http.HttpContentMessageExtensions">
            <summary>
            Extension methods to read <see cref="T:System.Net.Http.HttpRequestMessage"/> and <see cref="T:System.Net.Http.HttpResponseMessage"/> entities from <see cref="T:System.Net.Http.HttpContent"/> instances.
            </summary>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.IsHttpRequestMessageContent(System.Net.Http.HttpContent)">
            <summary>
            Determines whether the specified content is HTTP request message content.
            </summary>
            <param name="content">The content.</param>
            <returns>
              <c>true</c> if the specified content is HTTP message content; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.IsHttpResponseMessageContent(System.Net.Http.HttpContent)">
            <summary>
            Determines whether the specified content is HTTP response message content.
            </summary>
            <param name="content">The content.</param>
            <returns>
              <c>true</c> if the specified content is HTTP message content; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpRequestMessageAsync(System.Net.Http.HttpContent)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpRequestMessageAsync(System.Net.Http.HttpContent,System.Threading.CancellationToken)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpRequestMessageAsync(System.Net.Http.HttpContent,System.String)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="uriScheme">The URI scheme to use for the request URI.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpRequestMessageAsync(System.Net.Http.HttpContent,System.String,System.Threading.CancellationToken)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="uriScheme">The URI scheme to use for the request URI.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpRequestMessageAsync(System.Net.Http.HttpContent,System.String,System.Int32)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="uriScheme">The URI scheme to use for the request URI (the 
            URI scheme is not actually part of the HTTP Request URI and so must be provided externally).</param>
            <param name="bufferSize">Size of the buffer.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpRequestMessageAsync(System.Net.Http.HttpContent,System.String,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="uriScheme">The URI scheme to use for the request URI (the 
            URI scheme is not actually part of the HTTP Request URI and so must be provided externally).</param>
            <param name="bufferSize">Size of the buffer.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpRequestMessageAsync(System.Net.Http.HttpContent,System.String,System.Int32,System.Int32)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="uriScheme">The URI scheme to use for the request URI (the 
            URI scheme is not actually part of the HTTP Request URI and so must be provided externally).</param>
            <param name="bufferSize">Size of the buffer.</param>
            <param name="maxHeaderSize">The max length of the HTTP header.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpRequestMessageAsync(System.Net.Http.HttpContent,System.String,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="uriScheme">The URI scheme to use for the request URI (the 
            URI scheme is not actually part of the HTTP Request URI and so must be provided externally).</param>
            <param name="bufferSize">Size of the buffer.</param>
            <param name="maxHeaderSize">The max length of the HTTP header.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpRequestMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpResponseMessageAsync(System.Net.Http.HttpContent)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpResponseMessageAsync(System.Net.Http.HttpContent,System.Threading.CancellationToken)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpResponseMessageAsync(System.Net.Http.HttpContent,System.Int32)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="bufferSize">Size of the buffer.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpResponseMessageAsync(System.Net.Http.HttpContent,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="bufferSize">Size of the buffer.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpResponseMessageAsync(System.Net.Http.HttpContent,System.Int32,System.Int32)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="bufferSize">Size of the buffer.</param>
            <param name="maxHeaderSize">The max length of the HTTP header.</param>
            <returns>A task object representing reading the content as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.ReadAsHttpResponseMessageAsync(System.Net.Http.HttpContent,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Read the <see cref="T:System.Net.Http.HttpContent"/> as an <see cref="T:System.Net.Http.HttpResponseMessage"/>.
            </summary>
            <param name="content">The content to read.</param>
            <param name="bufferSize">Size of the buffer.</param>
            <param name="maxHeaderSize">The max length of the HTTP header.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>The parsed <see cref="T:System.Net.Http.HttpResponseMessage"/> instance.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.CreateRequestUri(System.String,System.Net.Http.HttpUnsortedRequest)">
            <summary>
            Creates the request URI by combining scheme (provided) with parsed values of
            host and path.
            </summary>
            <param name="uriScheme">The URI scheme to use for the request URI.</param>
            <param name="httpRequest">The unsorted HTTP request.</param>
            <returns>A fully qualified request URI.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.CreateHeaderFields(System.Net.Http.Headers.HttpHeaders,System.Net.Http.Headers.HttpHeaders,System.IO.Stream,System.Int32)">
            <summary>
            Copies the unsorted header fields to a sorted collection.
            </summary>
            <param name="source">The unsorted source headers</param>
            <param name="destination">The destination <see cref="T:System.Net.Http.Headers.HttpRequestHeaders"/> or <see cref="T:System.Net.Http.Headers.HttpResponseHeaders"/>.</param>
            <param name="contentStream">The input <see cref="T:System.IO.Stream"/> used to form any <see cref="T:System.Net.Http.HttpContent"/> being part of this HTTP request.</param>
            <param name="rewind">Start location of any request entity within the <paramref name="contentStream"/>.</param>
            <returns>An <see cref="T:System.Net.Http.HttpContent"/> instance if header fields contained and <see cref="T:System.Net.Http.Headers.HttpContentHeaders"/>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.CreateHttpRequestMessage(System.String,System.Net.Http.HttpUnsortedRequest,System.IO.Stream,System.Int32)">
            <summary>
            Creates an <see cref="T:System.Net.Http.HttpRequestMessage"/> based on information provided in <see cref="T:System.Net.Http.HttpUnsortedRequest"/>.
            </summary>
            <param name="uriScheme">The URI scheme to use for the request URI.</param>
            <param name="httpRequest">The unsorted HTTP request.</param>
            <param name="contentStream">The input <see cref="T:System.IO.Stream"/> used to form any <see cref="T:System.Net.Http.HttpContent"/> being part of this HTTP request.</param>
            <param name="rewind">Start location of any request entity within the <paramref name="contentStream"/>.</param>
            <returns>A newly created <see cref="T:System.Net.Http.HttpRequestMessage"/> instance.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMessageExtensions.CreateHttpResponseMessage(System.Net.Http.HttpUnsortedResponse,System.IO.Stream,System.Int32)">
            <summary>
            Creates an <see cref="T:System.Net.Http.HttpResponseMessage"/> based on information provided in <see cref="T:System.Net.Http.HttpUnsortedResponse"/>.
            </summary>
            <param name="httpResponse">The unsorted HTTP Response.</param>
            <param name="contentStream">The input <see cref="T:System.IO.Stream"/> used to form any <see cref="T:System.Net.Http.HttpContent"/> being part of this HTTP Response.</param>
            <param name="rewind">Start location of any Response entity within the <paramref name="contentStream"/>.</param>
            <returns>A newly created <see cref="T:System.Net.Http.HttpResponseMessage"/> instance.</returns>
        </member>
        <member name="T:System.Net.Http.HttpContentMultipartExtensions">
            <summary>
            Extension methods to read MIME multipart entities from <see cref="T:System.Net.Http.HttpContent"/> instances.
            </summary>
        </member>
        <member name="M:System.Net.Http.HttpContentMultipartExtensions.IsMimeMultipartContent(System.Net.Http.HttpContent)">
            <summary>
            Determines whether the specified content is MIME multipart content.
            </summary>
            <param name="content">The content.</param>
            <returns>
              <c>true</c> if the specified content is MIME multipart content; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMultipartExtensions.IsMimeMultipartContent(System.Net.Http.HttpContent,System.String)">
            <summary>
            Determines whether the specified content is MIME multipart content with the 
            specified subtype. For example, the subtype <c>mixed</c> would match content
            with a content type of <c>multipart/mixed</c>. 
            </summary>
            <param name="content">The content.</param>
            <param name="subtype">The MIME multipart subtype to match.</param>
            <returns>
              <c>true</c> if the specified content is MIME multipart content with the specified subtype; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMultipartExtensions.ReadAsMultipartAsync(System.Net.Http.HttpContent)">
            <summary>
            Reads all body parts within a MIME multipart message into memory using a <see cref="T:System.Net.Http.MultipartMemoryStreamProvider"/>.
            </summary>
            <param name="content">An existing <see cref="T:System.Net.Http.HttpContent"/> instance to use for the object's content.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> representing the tasks of getting the result of reading the MIME content.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMultipartExtensions.ReadAsMultipartAsync(System.Net.Http.HttpContent,System.Threading.CancellationToken)">
            <summary>
            Reads all body parts within a MIME multipart message into memory using a <see cref="T:System.Net.Http.MultipartMemoryStreamProvider"/>.
            </summary>
            <param name="content">An existing <see cref="T:System.Net.Http.HttpContent"/> instance to use for the object's content.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> representing the tasks of getting the result of reading the MIME content.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMultipartExtensions.ReadAsMultipartAsync``1(System.Net.Http.HttpContent,``0)">
            <summary>
            Reads all body parts within a MIME multipart message using the provided <see cref="T:System.Net.Http.MultipartStreamProvider"/> instance
            to determine where the contents of each body part is written. 
            </summary>
            <typeparam name="T">The <see cref="T:System.Net.Http.MultipartStreamProvider"/> with which to process the data.</typeparam>
            <param name="content">An existing <see cref="T:System.Net.Http.HttpContent"/> instance to use for the object's content.</param>
            <param name="streamProvider">A stream provider providing output streams for where to write body parts as they are parsed.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> representing the tasks of getting the result of reading the MIME content.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMultipartExtensions.ReadAsMultipartAsync``1(System.Net.Http.HttpContent,``0,System.Threading.CancellationToken)">
            <summary>
            Reads all body parts within a MIME multipart message using the provided <see cref="T:System.Net.Http.MultipartStreamProvider"/> instance
            to determine where the contents of each body part is written. 
            </summary>
            <typeparam name="T">The <see cref="T:System.Net.Http.MultipartStreamProvider"/> with which to process the data.</typeparam>
            <param name="content">An existing <see cref="T:System.Net.Http.HttpContent"/> instance to use for the object's content.</param>
            <param name="streamProvider">A stream provider providing output streams for where to write body parts as they are parsed.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> representing the tasks of getting the result of reading the MIME content.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMultipartExtensions.ReadAsMultipartAsync``1(System.Net.Http.HttpContent,``0,System.Int32)">
            <summary>
            Reads all body parts within a MIME multipart message using the provided <see cref="T:System.Net.Http.MultipartStreamProvider"/> instance
            to determine where the contents of each body part is written and <paramref name="bufferSize"/> as read buffer size.
            </summary>
            <typeparam name="T">The <see cref="T:System.Net.Http.MultipartStreamProvider"/> with which to process the data.</typeparam>
            <param name="content">An existing <see cref="T:System.Net.Http.HttpContent"/> instance to use for the object's content.</param>
            <param name="streamProvider">A stream provider providing output streams for where to write body parts as they are parsed.</param>
            <param name="bufferSize">Size of the buffer used to read the contents.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> representing the tasks of getting the result of reading the MIME content.</returns>
        </member>
        <member name="M:System.Net.Http.HttpContentMultipartExtensions.ReadAsMultipartAsync``1(System.Net.Http.HttpContent,``0,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads all body parts within a MIME multipart message using the provided <see cref="T:System.Net.Http.MultipartStreamProvider"/> instance
            to determine where the contents of each body part is written and <paramref name="bufferSize"/> as read buffer size.
            </summary>
            <typeparam name="T">The <see cref="T:System.Net.Http.MultipartStreamProvider"/> with which to process the data.</typeparam>
            <param name="content">An existing <see cref="T:System.Net.Http.HttpContent"/> instance to use for the object's content.</param>
            <param name="streamProvider">A stream provider providing output streams for where to write body parts as they are parsed.</param>
            <param name="bufferSize">Size of the buffer used to read the contents.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> representing the tasks of getting the result of reading the MIME content.</returns>
        </member>
        <member name="T:System.Net.Http.HttpContentMultipartExtensions.MultipartAsyncContext">
            <summary>
            Managing state for asynchronous read and write operations
            </summary>
        </member>
        <member name="P:System.Net.Http.HttpContentMultipartExtensions.MultipartAsyncContext.ContentStream">
            <summary>
            Gets the <see cref="T:System.IO.Stream"/> that we read from.
            </summary>
        </member>
        <member name="P:System.Net.Http.HttpContentMultipartExtensions.MultipartAsyncContext.Result">
            <summary>
            Gets the collection of parsed <see cref="T:System.Net.Http.HttpContent"/> instances.
            </summary>
        </member>
        <member name="P:System.Net.Http.HttpContentMultipartExtensions.MultipartAsyncContext.Data">
            <summary>
            The data buffer that we use for reading data from the input stream into before processing.
            </summary>
        </member>
        <member name="P:System.Net.Http.HttpContentMultipartExtensions.MultipartAsyncContext.MimeParser">
            <summary>
            Gets the MIME parser instance used to parse the data
            </summary>
        </member>
        <member name="T:System.Net.Http.HttpMessageContent">
            <summary>
            Derived <see cref="T:System.Net.Http.HttpContent"/> class which can encapsulate an <see cref="P:System.Net.Http.HttpMessageContent.HttpResponseMessage"/>
            or an <see cref="P:System.Net.Http.HttpMessageContent.HttpRequestMessage"/> as an entity with media type "application/http".
            </summary>
        </member>
        <member name="M:System.Net.Http.HttpMessageContent.#ctor(System.Net.Http.HttpRequestMessage)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.HttpMessageContent"/> class encapsulating an
            <see cref="P:System.Net.Http.HttpMessageContent.HttpRequestMessage"/>.
            </summary>
            <param name="httpRequest">The <see cref="P:System.Net.Http.HttpMessageContent.HttpResponseMessage"/> instance to encapsulate.</param>
        </member>
        <member name="M:System.Net.Http.HttpMessageContent.#ctor(System.Net.Http.HttpResponseMessage)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.HttpMessageContent"/> class encapsulating an
            <see cref="P:System.Net.Http.HttpMessageContent.HttpResponseMessage"/>.
            </summary>
            <param name="httpResponse">The <see cref="P:System.Net.Http.HttpMessageContent.HttpResponseMessage"/> instance to encapsulate.</param>
        </member>
        <member name="M:System.Net.Http.HttpMessageContent.ValidateHttpMessageContent(System.Net.Http.HttpContent,System.Boolean,System.Boolean)">
            <summary>
            Validates whether the content contains an HTTP Request or an HTTP Response.
            </summary>
            <param name="content">The content to validate.</param>
            <param name="isRequest">if set to <c>true</c> if the content is either an HTTP Request or an HTTP Response.</param>
            <param name="throwOnError">Indicates whether validation failure should result in an <see cref="T:System.Exception"/> or not.</param>
            <returns><c>true</c> if content is either an HTTP Request or an HTTP Response</returns>
        </member>
        <member name="M:System.Net.Http.HttpMessageContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
            <summary>
            Asynchronously serializes the object's content to the given <paramref name="stream"/>.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> to which to write.</param>
            <param name="context">The associated <see cref="T:System.Net.TransportContext"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> instance that is asynchronously serializing the object's content.</returns>
        </member>
        <member name="M:System.Net.Http.HttpMessageContent.TryComputeLength(System.Int64@)">
            <summary>
            Computes the length of the stream if possible.
            </summary>
            <param name="length">The computed length of the stream.</param>
            <returns><c>true</c> if the length has been computed; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:System.Net.Http.HttpMessageContent.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:System.Net.Http.HttpMessageContent.SerializeRequestLine(System.Text.StringBuilder,System.Net.Http.HttpRequestMessage)">
            <summary>
            Serializes the HTTP request line.
            </summary>
            <param name="message">Where to write the request line.</param>
            <param name="httpRequest">The HTTP request.</param>
        </member>
        <member name="M:System.Net.Http.HttpMessageContent.SerializeStatusLine(System.Text.StringBuilder,System.Net.Http.HttpResponseMessage)">
            <summary>
            Serializes the HTTP status line.
            </summary>
            <param name="message">Where to write the status line.</param>
            <param name="httpResponse">The HTTP response.</param>
        </member>
        <member name="M:System.Net.Http.HttpMessageContent.SerializeHeaderFields(System.Text.StringBuilder,System.Net.Http.Headers.HttpHeaders)">
            <summary>
            Serializes the header fields.
            </summary>
            <param name="message">Where to write the status line.</param>
            <param name="headers">The headers to write.</param>
        </member>
        <member name="P:System.Net.Http.HttpMessageContent.HttpRequestMessage">
            <summary>
            Gets the HTTP request message.
            </summary>
        </member>
        <member name="P:System.Net.Http.HttpMessageContent.HttpResponseMessage">
            <summary>
            Gets the HTTP response message.
            </summary>
        </member>
        <member name="T:System.Net.Http.HttpUnsortedHeaders">
            <summary>
            All of the existing non-abstract <see cref="T:System.Net.Http.Headers.HttpHeaders"/> implementations, namely
            <see cref="T:System.Net.Http.Headers.HttpRequestHeaders"/>, <see cref="T:System.Net.Http.Headers.HttpResponseHeaders"/>, and <see cref="T:System.Net.Http.Headers.HttpContentHeaders"/>
            enforce strict rules on what kinds of HTTP header fields can be added to each collection.
            When parsing the "application/http" media type we need to just get the unsorted list. It
            will get sorted later.
            </summary>
        </member>
        <member name="T:System.Net.Http.HttpUnsortedRequest">
            <summary>
            Represents the HTTP Request Line and header parameters parsed by <see cref="T:System.Net.Http.Formatting.Parsers.HttpRequestLineParser"/>
            and <see cref="T:System.Net.Http.Formatting.Parsers.HttpRequestHeaderParser"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.HttpUnsortedRequest.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.HttpUnsortedRequest"/> class.
            </summary>
        </member>
        <member name="P:System.Net.Http.HttpUnsortedRequest.Method">
            <summary>
            Gets or sets the HTTP method.
            </summary>
            <value>
            The HTTP method.
            </value>
        </member>
        <member name="P:System.Net.Http.HttpUnsortedRequest.RequestUri">
            <summary>
            Gets or sets the HTTP request URI portion that is carried in the RequestLine (i.e the URI path + query).
            </summary>
            <value>
            The request URI.
            </value>
        </member>
        <member name="P:System.Net.Http.HttpUnsortedRequest.Version">
            <summary>
            Gets or sets the HTTP version.
            </summary>
            <value>
            The HTTP version.
            </value>
        </member>
        <member name="P:System.Net.Http.HttpUnsortedRequest.HttpHeaders">
            <summary>
            Gets the unsorted HTTP request headers.
            </summary>
        </member>
        <member name="T:System.Net.Http.HttpUnsortedResponse">
            <summary>
            Represents the HTTP Status Line and header parameters parsed by <see cref="T:System.Net.Http.Formatting.Parsers.HttpStatusLineParser"/>
            and <see cref="T:System.Net.Http.Formatting.Parsers.HttpResponseHeaderParser"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.HttpUnsortedResponse.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.HttpUnsortedRequest"/> class.
            </summary>
        </member>
        <member name="P:System.Net.Http.HttpUnsortedResponse.Version">
            <summary>
            Gets or sets the HTTP version.
            </summary>
            <value>
            The HTTP version.
            </value>
        </member>
        <member name="P:System.Net.Http.HttpUnsortedResponse.StatusCode">
            <summary>
            Gets or sets the <see cref="T:System.Net.HttpStatusCode"/>
            </summary>
            <value>
            The HTTP status code
            </value>
        </member>
        <member name="P:System.Net.Http.HttpUnsortedResponse.ReasonPhrase">
            <summary>
            Gets or sets the HTTP reason phrase
            </summary>
            <value>
            The response reason phrase
            </value>
        </member>
        <member name="P:System.Net.Http.HttpUnsortedResponse.HttpHeaders">
            <summary>
            Gets the unsorted HTTP request headers.
            </summary>
        </member>
        <member name="T:System.Net.Http.Internal.ReadOnlyStreamWithEncodingPreamble">
            <summary>
            This implements a read-only, forward-only stream around another readable stream, to ensure
            that there is an appropriate encoding preamble in the stream.
            </summary>
        </member>
        <member name="T:System.Net.Http.MimeBodyPart">
            <summary>
            Maintains information about MIME body parts parsed by <see cref="T:System.Net.Http.Formatting.Parsers.MimeMultipartBodyPartParser"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.MimeBodyPart.#ctor(System.Net.Http.MultipartStreamProvider,System.Int32,System.Net.Http.HttpContent)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.MimeBodyPart"/> class.
            </summary>
            <param name="streamProvider">The stream provider.</param>
            <param name="maxBodyPartHeaderSize">The max length of the MIME header within each MIME body part.</param>
            <param name="parentContent">The part's parent content</param>
        </member>
        <member name="M:System.Net.Http.MimeBodyPart.GetCompletedHttpContent">
            <summary>
            Gets the part's content as an HttpContent.
            </summary>
            <value>
            The part's content, or null if the part had no content.
            </value>
        </member>
        <member name="M:System.Net.Http.MimeBodyPart.WriteSegment(System.ArraySegment{System.Byte},System.Threading.CancellationToken)">
            <summary>
            Writes the <paramref name="segment"/> into the part's output stream.
            </summary>
            <param name="segment">The current segment to be written to the part's output stream.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
        </member>
        <member name="M:System.Net.Http.MimeBodyPart.GetOutputStream">
            <summary>
            Gets the output stream.
            </summary>
            <returns>The output stream to write the body part to.</returns>
        </member>
        <member name="M:System.Net.Http.MimeBodyPart.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:System.Net.Http.MimeBodyPart.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:System.Net.Http.MimeBodyPart.CleanupHttpContent">
            <summary>
            In the success case, the HttpContent is to be used after this Part has been parsed and disposed of.
            Only if Dispose has been called on a non-completed part, the parsed HttpContent needs to be disposed of as well.
            </summary>
        </member>
        <member name="M:System.Net.Http.MimeBodyPart.CleanupOutputStream">
            <summary>
            Resets the output stream by either closing it or, in the case of a <see cref="T:System.IO.MemoryStream"/> resetting
            position to 0 so that it can be read by the caller.
            </summary>
        </member>
        <member name="P:System.Net.Http.MimeBodyPart.HeaderParser">
            <summary>
            Gets the header parser.
            </summary>
            <value>
            The header parser.
            </value>
        </member>
        <member name="P:System.Net.Http.MimeBodyPart.Segments">
            <summary>
            Gets the set of <see cref="T:System.ArraySegment`1"/> pointing to the read buffer with
            contents of this body part.
            </summary>
        </member>
        <member name="P:System.Net.Http.MimeBodyPart.IsComplete">
            <summary>
            Gets or sets a value indicating whether the body part has been completed.
            </summary>
            <value>
            <c>true</c> if this instance is complete; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:System.Net.Http.MimeBodyPart.IsFinal">
            <summary>
            Gets or sets a value indicating whether this is the final body part.
            </summary>
            <value>
            <c>true</c> if this instance is complete; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:System.Net.Http.MultipartMemoryStreamProvider">
            <summary>
            Provides a <see cref="T:System.Net.Http.MultipartStreamProvider"/> implementation that returns a <see cref="T:System.IO.MemoryStream"/> instance.
            This facilitates deserialization or other manipulation of the contents in memory.
            </summary>
        </member>
        <member name="T:System.Net.Http.MultipartStreamProvider">
            <summary>
            An <see cref="T:System.Net.Http.MultipartStreamProvider"/> implementation examines the headers provided by the MIME multipart parser
            as part of the MIME multipart extension methods (see <see cref="T:System.Net.Http.HttpContentMultipartExtensions"/>) and decides 
            what kind of stream to return for the body part to be written to.
            </summary>
        </member>
        <member name="M:System.Net.Http.MultipartStreamProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.MultipartStreamProvider"/> class.
            </summary>
        </member>
        <member name="M:System.Net.Http.MultipartStreamProvider.GetStream(System.Net.Http.HttpContent,System.Net.Http.Headers.HttpContentHeaders)">
            <summary>
            When a MIME multipart body part has been parsed this method is called to get a stream for where to write the body part to.
            </summary>
            <param name="parent">The parent <see cref="T:System.Net.Http.HttpContent"/> MIME multipart instance.</param>
            <param name="headers">The header fields describing the body parts content. Looking for header fields such as 
            Content-Type and Content-Disposition can help provide the appropriate stream. In addition to using the information
            in the provided header fields, it is also possible to add new header fields or modify existing header fields. This can
            be useful to get around situations where the Content-type may say <b>application/octet-stream</b> but based on
            analyzing the <b>Content-Disposition</b> header field it is found that the content in fact is <b>application/json</b>, for example.</param>
            <returns>A stream instance where the contents of a body part will be written to.</returns>
        </member>
        <member name="M:System.Net.Http.MultipartStreamProvider.ExecutePostProcessingAsync">
            <summary>
            Immediately upon reading the last MIME body part but before completing the read task, this method is 
            called to enable the <see cref="T:System.Net.Http.MultipartStreamProvider"/> to do any post processing on the <see cref="T:System.Net.Http.HttpContent"/>
            instances that have been read. For example, it can be used to copy the data to another location, or perform
            some other kind of post processing on the data before completing the read operation.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the post processing.</returns>
        </member>
        <member name="M:System.Net.Http.MultipartStreamProvider.ExecutePostProcessingAsync(System.Threading.CancellationToken)">
            <summary>
            Immediately upon reading the last MIME body part but before completing the read task, this method is 
            called to enable the <see cref="T:System.Net.Http.MultipartStreamProvider"/> to do any post processing on the <see cref="T:System.Net.Http.HttpContent"/>
            instances that have been read. For example, it can be used to copy the data to another location, or perform
            some other kind of post processing on the data before completing the read operation.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the post processing.</returns>
        </member>
        <member name="P:System.Net.Http.MultipartStreamProvider.Contents">
            <summary>
            Gets the collection of <see cref="T:System.Net.Http.HttpContent"/> instances where each instance represents a MIME body part.
            </summary>
        </member>
        <member name="M:System.Net.Http.MultipartMemoryStreamProvider.GetStream(System.Net.Http.HttpContent,System.Net.Http.Headers.HttpContentHeaders)">
            <summary>
            This <see cref="T:System.Net.Http.MultipartStreamProvider"/> implementation returns a <see cref="T:System.IO.MemoryStream"/> instance.
            This facilitates deserialization or other manipulation of the contents in memory. 
            </summary>
        </member>
        <member name="T:System.Net.Http.MultipartRelatedStreamProvider">
            <summary>
            An <see cref="T:System.Net.Http.MultipartStreamProvider"/> suited for reading MIME body parts following the
            <c>multipart/related</c> media type as defined in RFC 2387 (see http://www.ietf.org/rfc/rfc2387.txt).
            </summary>
        </member>
        <member name="M:System.Net.Http.MultipartRelatedStreamProvider.FindRootContent(System.Net.Http.HttpContent,System.Collections.Generic.IEnumerable{System.Net.Http.HttpContent})">
            <summary>
            Looks for the "start" parameter of the parent's content type and then finds the corresponding
            child HttpContent with a matching Content-ID header field.
            </summary>
            <returns>The matching child or null if none found.</returns>
        </member>
        <member name="M:System.Net.Http.MultipartRelatedStreamProvider.FindMultipartRelatedParameter(System.Net.Http.HttpContent,System.String)">
            <summary>
            Looks for a parameter in the <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/>.
            </summary>
            <returns>The matching parameter or null if none found.</returns>
        </member>
        <member name="P:System.Net.Http.MultipartRelatedStreamProvider.RootContent">
            <summary>
            Gets the <see cref="T:System.Net.Http.HttpContent"/> instance that has been marked as the <c>root</c> content in the 
            MIME multipart related message using the <c>start</c> parameter. If no <c>start</c> parameter is
            present then pick the first of the children.
            </summary>
        </member>
        <member name="T:System.Net.Http.ObjectContent">
            <summary>
            Contains a value as well as an associated <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter"/> that will be
            used to serialize the value when writing this content.
            </summary>
        </member>
        <member name="M:System.Net.Http.ObjectContent.#ctor(System.Type,System.Object,System.Net.Http.Formatting.MediaTypeFormatter)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.ObjectContent"/> class.
            </summary>
            <param name="type">The type of object this instance will contain.</param>
            <param name="value">The value of the object this instance will contain.</param>
            <param name="formatter">The formatter to use when serializing the value.</param>
        </member>
        <member name="M:System.Net.Http.ObjectContent.#ctor(System.Type,System.Object,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.ObjectContent"/> class.
            </summary>
            <param name="type">The type of object this instance will contain.</param>
            <param name="value">The value of the object this instance will contain.</param>
            <param name="formatter">The formatter to use when serializing the value.</param>
            <param name="mediaType">The authoritative value of the content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
        </member>
        <member name="M:System.Net.Http.ObjectContent.#ctor(System.Type,System.Object,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.ObjectContent"/> class.
            </summary>
            <param name="type">The type of object this instance will contain.</param>
            <param name="value">The value of the object this instance will contain.</param>
            <param name="formatter">The formatter to use when serializing the value.</param>
            <param name="mediaType">The authoritative value of the content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
        </member>
        <member name="M:System.Net.Http.ObjectContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
            <summary>
            Asynchronously serializes the object's content to the given <paramref name="stream"/>.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> to which to write.</param>
            <param name="context">The associated <see cref="T:System.Net.TransportContext"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> instance that is asynchronously serializing the object's content.</returns>
        </member>
        <member name="M:System.Net.Http.ObjectContent.TryComputeLength(System.Int64@)">
            <summary>
            Computes the length of the stream if possible.
            </summary>
            <param name="length">The computed length of the stream.</param>
            <returns><c>true</c> if the length has been computed; otherwise <c>false</c>.</returns>
        </member>
        <member name="P:System.Net.Http.ObjectContent.ObjectType">
            <summary>
            Gets the type of object managed by this <see cref="T:System.Net.Http.ObjectContent"/> instance.
            </summary>
        </member>
        <member name="P:System.Net.Http.ObjectContent.Formatter">
            <summary>
            The <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter">formatter</see> associated with this content instance.
            </summary>
        </member>
        <member name="P:System.Net.Http.ObjectContent.Value">
            <summary>
            Gets or sets the value of the current <see cref="T:System.Net.Http.ObjectContent"/>.
            </summary>
        </member>
        <member name="T:System.Net.Http.ObjectContent`1">
            <summary>
            Generic form of <see cref="T:System.Net.Http.ObjectContent"/>.
            </summary>
            <typeparam name="T">The type of object this <see cref="T:System.Net.Http.ObjectContent"/> class will contain.</typeparam>
        </member>
        <member name="M:System.Net.Http.ObjectContent`1.#ctor(`0,System.Net.Http.Formatting.MediaTypeFormatter)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.ObjectContent`1"/> class.
            </summary>
            <param name="value">The value of the object this instance will contain.</param>
            <param name="formatter">The formatter to use when serializing the value.</param>
        </member>
        <member name="M:System.Net.Http.ObjectContent`1.#ctor(`0,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.ObjectContent`1"/> class.
            </summary>
            <param name="value">The value of the object this instance will contain.</param>
            <param name="formatter">The formatter to use when serializing the value.</param>
            <param name="mediaType">The authoritative value of the content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
        </member>
        <member name="M:System.Net.Http.ObjectContent`1.#ctor(`0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.ObjectContent`1"/> class.
            </summary>
            <param name="value">The value of the object this instance will contain.</param>
            <param name="formatter">The formatter to use when serializing the value.</param>
            <param name="mediaType">The authoritative value of the content's Content-Type header. Can be <c>null</c> in which case the
            <paramref name="formatter">formatter's</paramref> default content type will be used.</param>
        </member>
        <member name="T:System.Net.Http.PushStreamContent">
            <summary>
            Provides an <see cref="T:System.Net.Http.HttpContent"/> implementation that exposes an output <see cref="T:System.IO.Stream"/>
            which can be written to directly. The ability to push data to the output stream differs from the 
            <see cref="T:System.Net.Http.StreamContent"/> where data is pulled and not pushed.
            </summary>
        </member>
        <member name="M:System.Net.Http.PushStreamContent.#ctor(System.Action{System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext})">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.PushStreamContent"/> class. The
            <paramref name="onStreamAvailable"/> action is called when an output stream
            has become available allowing the action to write to it directly. When the 
            stream is closed, it will signal to the content that is has completed and the 
            HTTP request or response will be completed.
            </summary>
            <param name="onStreamAvailable">The action to call when an output stream is available.</param>
        </member>
        <member name="M:System.Net.Http.PushStreamContent.#ctor(System.Func{System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext,System.Threading.Tasks.Task})">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.PushStreamContent"/> class. 
            </summary>
            <param name="onStreamAvailable">The action to call when an output stream is available. The stream is automatically
            closed when the return task is completed.</param>
        </member>
        <member name="M:System.Net.Http.PushStreamContent.#ctor(System.Action{System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.PushStreamContent"/> class with the given media type.
            </summary>
        </member>
        <member name="M:System.Net.Http.PushStreamContent.#ctor(System.Func{System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext,System.Threading.Tasks.Task},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.PushStreamContent"/> class with the given media type.
            </summary>
        </member>
        <member name="M:System.Net.Http.PushStreamContent.#ctor(System.Action{System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext},System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.PushStreamContent"/> class with the given <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.PushStreamContent.#ctor(System.Func{System.IO.Stream,System.Net.Http.HttpContent,System.Net.TransportContext,System.Threading.Tasks.Task},System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.PushStreamContent"/> class with the given <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.PushStreamContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
            <summary>
            When this method is called, it calls the action provided in the constructor with the output 
            stream to write to. Once the action has completed its work it closes the stream which will 
            close this content instance and complete the HTTP request or response.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> to which to write.</param>
            <param name="context">The associated <see cref="T:System.Net.TransportContext"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> instance that is asynchronously serializing the object's content.</returns>
        </member>
        <member name="M:System.Net.Http.PushStreamContent.TryComputeLength(System.Int64@)">
            <summary>
            Computes the length of the stream if possible.
            </summary>
            <param name="length">The computed length of the stream.</param>
            <returns><c>true</c> if the length has been computed; otherwise <c>false</c>.</returns>
        </member>
        <member name="T:System.Net.Http.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.AsyncResult_CallbackThrewException">
            <summary>
              Looks up a localized string similar to Async Callback threw an exception..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.AsyncResult_MultipleCompletes">
            <summary>
              Looks up a localized string similar to The IAsyncResult implementation &apos;{0}&apos; tried to complete a single operation multiple times. This could be caused by an incorrect application IAsyncResult implementation or other extensibility code, such as an IAsyncResult that returns incorrect CompletedSynchronously values or invokes the AsyncCallback multiple times..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.AsyncResult_MultipleEnds">
            <summary>
              Looks up a localized string similar to End cannot be called twice on an AsyncResult..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.AsyncResult_ResultMismatch">
            <summary>
              Looks up a localized string similar to An incorrect IAsyncResult was provided to an &apos;End&apos; method. The IAsyncResult object passed to &apos;End&apos; must be the one returned from the matching &apos;Begin&apos; or passed to the callback provided to &apos;Begin&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ByteRangeStreamContentNoRanges">
            <summary>
              Looks up a localized string similar to Found zero byte ranges. There must be at least one byte range provided..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ByteRangeStreamContentNotBytesRange">
            <summary>
              Looks up a localized string similar to The range unit &apos;{0}&apos; is not valid. The range must have a unit of &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ByteRangeStreamEmpty">
            <summary>
              Looks up a localized string similar to The stream over which &apos;{0}&apos; provides a range view must have a length greater than or equal to 1..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ByteRangeStreamInvalidFrom">
            <summary>
              Looks up a localized string similar to The &apos;From&apos; value of the range must be less than or equal to {0}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ByteRangeStreamNoneOverlap">
            <summary>
              Looks up a localized string similar to None of the requested ranges ({0}) overlap with the current extent of the selected resource..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ByteRangeStreamNoOverlap">
            <summary>
              Looks up a localized string similar to The requested range ({0}) does not overlap with the current extent of the selected resource..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ByteRangeStreamNotSeekable">
            <summary>
              Looks up a localized string similar to The stream over which &apos;{0}&apos; provides a range view must be seekable..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ByteRangeStreamReadOnly">
            <summary>
              Looks up a localized string similar to This is a read-only stream..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.CannotHaveNullInList">
            <summary>
              Looks up a localized string similar to A null &apos;{0}&apos; is not valid..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.CannotUseMediaRangeForSupportedMediaType">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; of &apos;{1}&apos; cannot be used as a supported media type because it is a media range..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.CannotUseNullValueType">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; type cannot accept a null value for the value type &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.CookieInvalidName">
            <summary>
              Looks up a localized string similar to The specified value is not a valid cookie name..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.CookieNull">
            <summary>
              Looks up a localized string similar to Cookie cannot be null..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.DelegatingHandlerArrayContainsNullItem">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; list is invalid because it contains one or more null items..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.DelegatingHandlerArrayHasNonNullInnerHandler">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; list is invalid because the property &apos;{1}&apos; of &apos;{2}&apos; is not null..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ErrorReadingFormUrlEncodedStream">
            <summary>
              Looks up a localized string similar to Error reading HTML form URL-encoded data stream..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.FormUrlEncodedMismatchingTypes">
            <summary>
              Looks up a localized string similar to Mismatched types at node &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.FormUrlEncodedParseError">
            <summary>
              Looks up a localized string similar to Error parsing HTML form URL-encoded data, byte {0}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.HttpInvalidStatusCode">
            <summary>
              Looks up a localized string similar to Invalid HTTP status code: &apos;{0}&apos;. The status code must be between {1} and {2}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.HttpInvalidVersion">
            <summary>
              Looks up a localized string similar to Invalid HTTP version: &apos;{0}&apos;. The version must start with the characters &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.HttpMessageContentAlreadyRead">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; of the &apos;{1}&apos; has already been read..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.HttpMessageContentStreamMustBeSeekable">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; must be seekable in order to create an &apos;{1}&apos; instance containing an entity body.  .
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.HttpMessageErrorReading">
            <summary>
              Looks up a localized string similar to Error reading HTTP message..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.HttpMessageInvalidMediaType">
            <summary>
              Looks up a localized string similar to Invalid &apos;{0}&apos; instance provided. It does not have a content type header with a value of &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.HttpMessageParserEmptyUri">
            <summary>
              Looks up a localized string similar to HTTP Request URI cannot be an empty string..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.HttpMessageParserError">
            <summary>
              Looks up a localized string similar to Error parsing HTTP message header byte {0} of message {1}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.HttpMessageParserInvalidHostCount">
            <summary>
              Looks up a localized string similar to An invalid number of &apos;{0}&apos; header fields were present in the HTTP Request. It must contain exactly one &apos;{0}&apos; header field but found {1}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.HttpMessageParserInvalidUriScheme">
            <summary>
              Looks up a localized string similar to Invalid URI scheme: &apos;{0}&apos;. The URI scheme must be a valid &apos;{1}&apos; scheme..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.InvalidArrayInsert">
            <summary>
              Looks up a localized string similar to Invalid array at node &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.JQuery13CompatModeNotSupportNestedJson">
            <summary>
              Looks up a localized string similar to Traditional style array without &apos;[]&apos; is not supported with nested object at location {0}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.JsonSerializerFactoryReturnedNull">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; method returned null. It must return a JSON serializer instance..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.JsonSerializerFactoryThrew">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; method threw an exception when attempting to create a JSON serializer..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MaxDepthExceeded">
            <summary>
              Looks up a localized string similar to The maximum read depth ({0}) has been exceeded because the form url-encoded data being read has more levels of nesting than is allowed..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MaxHttpCollectionKeyLimitReached">
            <summary>
              Looks up a localized string similar to The number of keys in a NameValueCollection has exceeded the limit of &apos;{0}&apos;. You can adjust it by modifying the MaxHttpCollectionKeys property on the &apos;{1}&apos; class..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MediaTypeFormatter_BsonParseError_MissingData">
            <summary>
              Looks up a localized string similar to Error parsing BSON data; unable to read content as a {0}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MediaTypeFormatter_BsonParseError_UnexpectedData">
            <summary>
              Looks up a localized string similar to Error parsing BSON data; unexpected dictionary content: {0} entries, first key &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MediaTypeFormatter_JsonReaderFactoryReturnedNull">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; method returned null. It must return a JSON reader instance..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MediaTypeFormatter_JsonWriterFactoryReturnedNull">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; method returned null. It must return a JSON writer instance..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MediaTypeFormatterCannotRead">
            <summary>
              Looks up a localized string similar to The media type formatter of type &apos;{0}&apos; does not support reading because it does not implement the ReadFromStreamAsync method..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MediaTypeFormatterCannotReadSync">
            <summary>
              Looks up a localized string similar to The media type formatter of type &apos;{0}&apos; does not support reading because it does not implement the ReadFromStream method..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MediaTypeFormatterCannotWrite">
            <summary>
              Looks up a localized string similar to The media type formatter of type &apos;{0}&apos; does not support writing because it does not implement the WriteToStreamAsync method..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MediaTypeFormatterCannotWriteSync">
            <summary>
              Looks up a localized string similar to The media type formatter of type &apos;{0}&apos; does not support writing because it does not implement the WriteToStream method..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MediaTypeFormatterNoEncoding">
            <summary>
              Looks up a localized string similar to No encoding found for media type formatter &apos;{0}&apos;. There must be at least one supported encoding registered in order for the media type formatter to read or write content..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MimeMultipartParserBadBoundary">
            <summary>
              Looks up a localized string similar to MIME multipart boundary cannot end with an empty space..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MultipartFormDataStreamProviderNoContentDisposition">
            <summary>
              Looks up a localized string similar to Did not find required &apos;{0}&apos; header field in MIME multipart body part..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.MultipartStreamProviderInvalidLocalFileName">
            <summary>
              Looks up a localized string similar to Could not determine a valid local file name for the multipart body part..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.NestedBracketNotValid">
            <summary>
              Looks up a localized string similar to Nested bracket is not valid for &apos;{0}&apos; data at position {1}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.NonNullUriRequiredForMediaTypeMapping">
            <summary>
              Looks up a localized string similar to A non-null request URI must be provided to determine if a &apos;{0}&apos; matches a given request or response message..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.NoReadSerializerAvailable">
            <summary>
              Looks up a localized string similar to No MediaTypeFormatter is available to read an object of type &apos;{0}&apos; from content with media type &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ObjectAndTypeDisagree">
            <summary>
              Looks up a localized string similar to An object of type &apos;{0}&apos; cannot be used with a type parameter of &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ObjectContent_FormatterCannotWriteType">
            <summary>
              Looks up a localized string similar to The configured formatter &apos;{0}&apos; cannot write an object of type &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.QueryStringNameShouldNotNull">
            <summary>
              Looks up a localized string similar to Query string name cannot be null..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsHttpMessageUnexpectedTermination">
            <summary>
              Looks up a localized string similar to Unexpected end of HTTP message stream. HTTP message is not complete..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartArgumentNoBoundary">
            <summary>
              Looks up a localized string similar to Invalid &apos;{0}&apos; instance provided. It does not have a &apos;{1}&apos; content-type header with a &apos;{2}&apos; parameter..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartArgumentNoContentType">
            <summary>
              Looks up a localized string similar to Invalid &apos;{0}&apos; instance provided. It does not have a content-type header value. &apos;{0}&apos; instances must have a content-type header starting with &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartArgumentNoMultipart">
            <summary>
              Looks up a localized string similar to Invalid &apos;{0}&apos; instance provided. It does not have a content type header starting with &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartErrorReading">
            <summary>
              Looks up a localized string similar to Error reading MIME multipart body part..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartErrorWriting">
            <summary>
              Looks up a localized string similar to Error writing MIME multipart body part to output stream..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartHeaderParseError">
            <summary>
              Looks up a localized string similar to Error parsing MIME multipart body part header byte {0} of data segment {1}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartParseError">
            <summary>
              Looks up a localized string similar to Error parsing MIME multipart message byte {0} of data segment {1}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartStreamProviderException">
            <summary>
              Looks up a localized string similar to The stream provider of type &apos;{0}&apos; threw an exception..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartStreamProviderNull">
            <summary>
              Looks up a localized string similar to The stream provider of type &apos;{0}&apos; returned null. It must return a writable &apos;{1}&apos; instance..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartStreamProviderReadOnly">
            <summary>
              Looks up a localized string similar to The stream provider of type &apos;{0}&apos; returned a read-only stream. It must return a writable &apos;{1}&apos; instance..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.ReadAsMimeMultipartUnexpectedTermination">
            <summary>
              Looks up a localized string similar to Unexpected end of MIME multipart stream. MIME multipart message is not complete..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.SerializerCannotSerializeType">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; serializer cannot serialize the type &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.UnMatchedBracketNotValid">
            <summary>
              Looks up a localized string similar to There is an unmatched opened bracket for the &apos;{0}&apos; at position {1}..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.UnsupportedIndent">
            <summary>
              Looks up a localized string similar to Indentation is not supported by &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.XmlMediaTypeFormatter_InvalidSerializerType">
            <summary>
              Looks up a localized string similar to The object of type &apos;{0}&apos; returned by {1} must be an instance of either XmlObjectSerializer or XmlSerializer..
            </summary>
        </member>
        <member name="P:System.Net.Http.Properties.Resources.XmlMediaTypeFormatter_NullReturnedSerializer">
            <summary>
              Looks up a localized string similar to The object returned by {0} must not be a null value..
            </summary>
        </member>
        <member name="T:System.Net.Http.UnsupportedMediaTypeException">
            <summary>
            Defines an exception type for signalling that a request's media type was not supported.
            </summary>
        </member>
        <member name="M:System.Net.Http.UnsupportedMediaTypeException.#ctor(System.String,System.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Net.Http.UnsupportedMediaTypeException"/> class.
            </summary>
            <param name="message">The message that describes the error.</param>
            <param name="mediaType">The unsupported media type.</param>
        </member>
        <member name="T:System.Web.Http.Properties.CommonWebApiResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:System.Web.Http.Properties.CommonWebApiResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:System.Web.Http.Properties.CommonWebApiResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:System.Web.Http.Properties.CommonWebApiResources.ArgumentInvalidAbsoluteUri">
            <summary>
              Looks up a localized string similar to Relative URI values are not supported: &apos;{0}&apos;. The URI must be absolute..
            </summary>
        </member>
        <member name="P:System.Web.Http.Properties.CommonWebApiResources.ArgumentInvalidHttpUriScheme">
            <summary>
              Looks up a localized string similar to Unsupported URI scheme: &apos;{0}&apos;. The URI scheme must be either &apos;{1}&apos; or &apos;{2}&apos;..
            </summary>
        </member>
        <member name="P:System.Web.Http.Properties.CommonWebApiResources.ArgumentMustBeGreaterThanOrEqualTo">
            <summary>
              Looks up a localized string similar to Value must be greater than or equal to {0}..
            </summary>
        </member>
        <member name="P:System.Web.Http.Properties.CommonWebApiResources.ArgumentMustBeLessThanOrEqualTo">
            <summary>
              Looks up a localized string similar to Value must be less than or equal to {0}..
            </summary>
        </member>
        <member name="P:System.Web.Http.Properties.CommonWebApiResources.ArgumentNullOrEmpty">
            <summary>
              Looks up a localized string similar to The argument &apos;{0}&apos; is null or empty..
            </summary>
        </member>
        <member name="P:System.Web.Http.Properties.CommonWebApiResources.ArgumentUriHasQueryOrFragment">
            <summary>
              Looks up a localized string similar to URI must not contain a query component or a fragment identifier..
            </summary>
        </member>
        <member name="P:System.Web.Http.Properties.CommonWebApiResources.InvalidEnumArgument">
            <summary>
              Looks up a localized string similar to The value of argument &apos;{0}&apos; ({1}) is invalid for Enum type &apos;{2}&apos;..
            </summary>
        </member>
    </members>
</doc>
