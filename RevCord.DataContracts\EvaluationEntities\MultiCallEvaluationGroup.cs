﻿using RevCord.DataContracts.VoiceRecEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.EvaluationEntities
{
    public class MultiCallEvaluationGroup
    {
        public int RowNo { get; set; }
        public int Id { get; set; }
        public string Name { get; set; }
        public string Comment { get; set; }
        public int SurveyId { get; set; }
        public int EvaluatorId { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }

        public string CSVCallIds { get; set; }

        public string EvaluatorName { get; set; }
        public string SurveyTitle { get; set; }
        public int NoOfCalls { get; set; }

        public int PrimaryEvalId { get; set; }
        public int PrimaryEvalStatus { get; set; }

        public string CallId { get; set; }

        public CallEvaluation CallEvaluation { get; set; }
        public List<CallInfo> CallInfos { get; set; }
        public EvaluationStatus Status
        {
            get { return (EvaluationStatus)PrimaryEvalStatus; }
            set { PrimaryEvalStatus = (byte)value; }
        }
    }
}