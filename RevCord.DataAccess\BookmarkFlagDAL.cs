﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.SqlClient;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.CommonEntities;
using System.Threading.Tasks;
using RevCord.Util;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.DataAccess
{
    public class BookmarkFlagDAL
    {
        private int _tenantId;
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 300);

        public BookmarkFlagDAL(int tenantId)
        {
            _tenantId = tenantId;
        }

        public List<BmFlag> GetBookmarkFlag()
        {
            List<BmFlag> BookmarkFlags = null;
            BmFlag _BmFlag = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.BookmarkTags.GETALL_BOOKMARKFLAGS;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CallTag, "GetBookmarkFlag", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        BookmarkFlags = new List<BmFlag>();
                        while (dr.Read())
                        {
                            _BmFlag = new BmFlag();
                            _BmFlag.FlagID = Convert.ToInt32(dr["id"]);
                            _BmFlag.FlagName = Convert.ToString(dr["FlagName"]);
                            _BmFlag.FlagColor = Convert.ToString(dr["FlagColorID"]);
                            _BmFlag.RequiredAlert = !DBRecordExtensions.HasColumn(dr, "RequiredAlert") ? false : Convert.ToBoolean(dr["RequiredAlert"]);
                            BookmarkFlags.Add(_BmFlag);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return BookmarkFlags;

        }

        public List<BmFlag> SaveBookmarkFlag(List<BmFlag> lBmFlag)
        {
            try
            {
                foreach (var BmFlag in lBmFlag)
                {
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.BookmarkTags.UPDATE_BOOKMARKFLAGS;
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@Id", BmFlag.FlagID);
                        cmd.Parameters.AddWithValue("@FlagName", BmFlag.FlagName.Trim());
                        cmd.Parameters.AddWithValue("@FlagColor", BmFlag.FlagColor.Trim());
                        cmd.Parameters.AddWithValue("@RequiredAlert", BmFlag.RequiredAlert);

                        conn.Open();
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CallTag, "SaveBookmarkFlag", _tenantId));

                        cmd.ExecuteReader();
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return lBmFlag;
        }

        public int DeleteBookmarkFlag(int FlagID)
        {
            try
            {
                int rowsAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.BookmarkTags.DELETE_BOOKMARKFLAGS;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@Id", FlagID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CallTag, "DeleteBookmarkFlag", _tenantId));
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }
    }
}
