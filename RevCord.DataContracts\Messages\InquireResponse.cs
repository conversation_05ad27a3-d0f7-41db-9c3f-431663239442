﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.InquireEntities;

namespace RevCord.DataContracts.Messages
{
    public class InquireResponse : ResponseBase
    {
        public int UserId { get; set; }

        public int InvitationId { get; set; }

        public Invitation Invitation { get; set; }

        public IEnumerable<Invitation> Invitations { get; set; }

        public EventInvitationGroup EventInvitationGroup { get; set; }

        public PictureEvent PictureEvent { get; set; }

    }
}
