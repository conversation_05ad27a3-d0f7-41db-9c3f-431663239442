﻿//***********************************************************************
// AssemblyName   : 
// Author               :   <PERSON><PERSON>
// Created ON           :   15-March-2012
//
// Last Modified By     :   <PERSON><PERSON>
// Last Modified On     :   11-April-2012
// Description          :   
//***********************************************************************
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.ReportEntities
{
    public class RPTQueryParameter
    {
        //private QueryParameter() { }

        #region Properties

        public string SelectKey { get; set; }
        public string SelectYear { get; set; }
        public string SelectMonth { get; set; }
        public string SelectDay { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public string TimeStart { get; set; }
        public string TimeEnd { get; set; }
        public string TimeRange { get; set; }
        public string DurationStart { get; set; }
        public string DurationEnd { get; set; }
        public string Duration { get; set; }
        public string GroupKey { get; set; }
        public string GroupYear { get; set; }
        public string GroupMonth { get; set; }
        public string GroupDay { get; set; }
        public string OptionString { get; set; }

        public bool IsAniAli { get; set; }
        public byte CallBarring { get; set; }
        //public string AniAli { get; set; }
        public string TalkTime { get; set; }

        public string CustomText { get; set; }

        public string OptionGroupString { get; set; }
        public string OptionGroupExtString { get; set; }
        public string OptionExtString { get; set; }

        public List<string> GroupExtensions { get; set; }

        public string OptionSurveyString { get; set; }
        public bool IsDateForEvaluation { get; set; }
        public string OptionSurveyId { get; set; }
        public string OptionSurveyName { get; set; }


        public byte XAxisData { get; set; }

        public int InspectionTemplateId { get; set; }
        public int BookmarkId { get; set; }
        public string BookmarkText { get; set; }
        public string BookmarkNotes { get; set; }
        public string PreInspectionTitle { get; set; }
        public string PreInspectionData { get; set; }
        #endregion

    }
}