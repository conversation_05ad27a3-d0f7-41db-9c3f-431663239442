﻿using RevCord.DataContracts.IQ3ConditionalLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class ConditionalLogicResponse
    {
        public MarkerLogic MarkerLogic { get; set; }
        public LogicTrigger LogicTrigger { get; set; }
        public string Message { get; set; }
        public bool Status { get; set; }

        public List<MarkerLogic> MarkerLogics { get; set; }
        public TriggerAction TriggerAction { get; set; }

    }
}
