﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.PlivoEntities
{
    public class Call
    {
        public DateTime answer_time { get; set; }
        public int bill_duration { get; set; }
        public int billed_duration { get; set; }
        public string call_direction { get; set; }
        public int call_duration { get; set; }
        public string call_uuid { get; set; }
        public DateTime end_time { get; set; }
        public string from_number { get; set; }
        public DateTime initiation_time { get; set; }
        public string parent_call_uuid { get; set; }
        public string resource_uri { get; set; }
        public string to_number { get; set; }
        public float total_amount { get; set; }
        public float total_rate { get; set; }
        public string hangup_cause_name { get; set; }
        public int hangup_cause_code { get; set; }
        public string hangup_source { get; set; }
    }
}