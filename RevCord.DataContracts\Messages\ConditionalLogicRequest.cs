﻿using RevCord.DataContracts.IQ3ConditionalLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class ConditionalLogicRequest
    {
        public int MarkerLogicId { get; set; }
        public int MarkerId { get; set; }
        public int TenantId { get; set; }
        public MarkerLogic MarkerLogic { get; set; }
        public LogicTrigger LogicTrigger { get; set; }
        public TriggerAction TriggerAction { get; set; }
        public List<MarkerLogic> MarkerLogics { get; set; }
        public int LogicTriggerId { get; set; }
        public int TriggerTypeId { get; set; }
    }
}
