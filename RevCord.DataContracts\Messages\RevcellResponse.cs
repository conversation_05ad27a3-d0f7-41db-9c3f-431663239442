﻿using Plivo.Resource;
using Plivo.Resource.Endpoint;
using Plivo.Resource.PhoneNumber;
using Plivo.Resource.RentedNumber;
using RevCord.DataContracts.RevcellEntities;

namespace RevCord.DataContracts.Messages
{
    public class RevcellResponse
    {
        //public SIPEndPointRootObject SIPEndPointRootObject { get; set; }
        //public PhoneNumberRootObject PhoneNumberRootObject { get; set; }
        //public NumberGroupRootObject NumberGroupRootObject { get; set; }
        //public NumberGroupMembershipRootObject NumberGroupMembershipRootObject { get; set; }

        //public SipEndpoint SIPEndPoint { get; set; }
        //public PhoneNumber PhoneNumber { get; set; }
        //public NumberGroup NumberGroup { get; set; }
        //public NumberGroupMembership NumberGroupMembership { get; set; }

        //public RevcellInfo RevcellInfo { get; set; }

        public bool IsOperationSucceeded { get; set; }

        #region Plivo
        public EndpointCreateResponse EndpointCreateResponse { get; set; }
        public UpdateResponse<Endpoint> EndpointUpdateResponse { get; set; }
        public DeleteResponse<Endpoint> EndpointDeleteResponse { get; set; }
        public ListResponse<Endpoint> EndpointListResponse { get; set; }

        public DeleteResponse<RentedNumber> RentedNumberDeleteResponse { get; set; }
        public ListResponse<RentedNumber> RentedNumberListResponse { get; set; }
        public UpdateResponse<RentedNumber> RentedNumberUpdateResponse { get; set; }
        public bool IsSIPEndpointByEmailExists { get; set; }
        public PhoneNumberBuyResponse PhoneNumberBuyResponse { get; set; }
        #endregion
    }
}
