﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeFileHandle">
      <summary>Represents a wrapper class for a file handle. </summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeFileHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> class. </summary>
      <param name="preexistingHandle">An <see cref="T:System.IntPtr" /> object that represents the pre-existing handle to use.</param>
      <param name="ownsHandle">true to reliably release the handle during the finalization phase; false to prevent reliable release (not recommended).</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeFileHandle.IsInvalid"></member>
    <member name="T:System.IO.Directory">
      <summary>Exposes static methods for creating, moving, and enumerating through directories and subdirectories. This class cannot be inherited.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>Creates all directories and subdirectories in the specified path unless they already exist.</summary>
      <returns>An object that represents the directory at the specified path. This object is returned regardless of whether a directory at the specified path already exists.</returns>
      <param name="path">The directory to create. </param>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="path" /> is a file.-or-The network name is not known.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.-or-<paramref name="path" /> is prefixed with, or contains, only a colon character (:).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contains a colon character (:) that is not part of a drive label ("C:\").</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>Deletes an empty directory from a specified path.</summary>
      <param name="path">The name of the empty directory to remove. This directory must be writable and empty. </param>
      <exception cref="T:System.IO.IOException">A file with the same name and location specified by <paramref name="path" /> exists.-or-The directory is the application's current working directory.-or-The directory specified by <paramref name="path" /> is not empty.-or-The directory is read-only or contains a read-only file.-or-The directory is being used by another process.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> does not exist or could not be found.-or-The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>Deletes the specified directory and, if indicated, any subdirectories and files in the directory. </summary>
      <param name="path">The name of the directory to remove. </param>
      <param name="recursive">true to remove directories, subdirectories, and files in <paramref name="path" />; otherwise, false. </param>
      <exception cref="T:System.IO.IOException">A file with the same name and location specified by <paramref name="path" /> exists.-or-The directory specified by <paramref name="path" /> is read-only, or <paramref name="recursive" /> is false and <paramref name="path" /> is not an empty directory. -or-The directory is the application's current working directory. -or-The directory contains a read-only file.-or-The directory is being used by another process.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> does not exist or could not be found.-or-The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>Returns an enumerable collection of directory names in a specified path.</summary>
      <returns>An enumerable collection of the full names (including paths) for the directories in the directory specified by <paramref name="path" />.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>Returns an enumerable collection of directory names that match a search pattern in a specified path.</summary>
      <returns>An enumerable collection of the full names (including paths) for the directories in the directory specified by <paramref name="path" /> and that match the specified search pattern.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of directories in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.- or -<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.-or-<paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of directory names that match a search pattern in a specified path, and optionally searches subdirectories.</summary>
      <returns>An enumerable collection of the full names (including paths) for the directories in the directory specified by <paramref name="path" /> and that match the specified search pattern and option.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of directories in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or should include all subdirectories.The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.- or -<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.-or-<paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>Returns an enumerable collection of file names in a specified path.</summary>
      <returns>An enumerable collection of the full names (including paths) for the files in the directory specified by <paramref name="path" />.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>Returns an enumerable collection of file names that match a search pattern in a specified path.</summary>
      <returns>An enumerable collection of the full names (including paths) for the files in the directory specified by <paramref name="path" /> and that match the specified search pattern.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of files in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.- or -<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.-or-<paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of file names that match a search pattern in a specified path, and optionally searches subdirectories.</summary>
      <returns>An enumerable collection of the full names (including paths) for the files in the directory specified by <paramref name="path" /> and that match the specified search pattern and option.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of files in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions.  </param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or should include all subdirectories.The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.- or -<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.-or-<paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>Returns an enumerable collection of file names and directory names in a specified path. </summary>
      <returns>An enumerable collection of file-system entries in the directory specified by <paramref name="path" />.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>Returns an enumerable collection of file names and directory names that  match a search pattern in a specified path.</summary>
      <returns>An enumerable collection of file-system entries in the directory specified by <paramref name="path" /> and that match the specified search pattern.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive. </param>
      <param name="searchPattern">The search string to match against the names of file-system entries in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions.  </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.- or -<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.-or-<paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of file names and directory names that match a search pattern in a specified path, and optionally searches subdirectories.</summary>
      <returns>An enumerable collection of file-system entries in the directory specified by <paramref name="path" /> and that match the specified search pattern and option.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against file-system entries in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values  that specifies whether the search operation should include only the current directory or should include all subdirectories.The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.- or -<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.-or-<paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>Determines whether the given path refers to an existing directory on disk.</summary>
      <returns>true if <paramref name="path" /> refers to an existing directory; false if the directory does not exist or an error occurs when trying to determine if the specified file exists.</returns>
      <param name="path">The path to test. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>Gets the creation date and time of a directory.</summary>
      <returns>A structure that is set to the creation date and time for the specified directory. This value is expressed in local time.</returns>
      <param name="path">The path of the directory. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>Gets the creation date and time, in Coordinated Universal Time (UTC) format, of a directory.</summary>
      <returns>A structure that is set to the creation date and time for the specified directory. This value is expressed in UTC time.</returns>
      <param name="path">The path of the directory. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>Gets the current working directory of the application.</summary>
      <returns>A string that contains the path of the current working directory, and does not end with a backslash (\).</returns>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">The operating system is Windows CE, which does not have current directory functionality.This method is available in the .NET Compact Framework, but is not currently supported.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>Returns the names of subdirectories (including their paths) in the specified directory.</summary>
      <returns>An array of the full names (including paths) of subdirectories in the specified path, or an empty array if no directories are found.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>Returns the names of subdirectories (including their paths) that match the specified search pattern in the specified directory.</summary>
      <returns>An array of the full names (including paths) of the subdirectories that match the search pattern in the specified directory, or an empty array if no directories are found.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of subdirectories in <paramref name="path" />. This parameter can contain a combination of valid literal and wildcard characters (see Remarks), but doesn't support regular expressions. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using <see cref="M:System.IO.Path.GetInvalidPathChars" />.-or- <paramref name="searchPattern" /> doesn't contain a valid pattern. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns the names of the subdirectories (including their paths) that match the specified search pattern in the specified directory, and optionally searches subdirectories.</summary>
      <returns>An array of the full names (including paths) of the subdirectories that match the specified criteria, or an empty array if no directories are found.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of subdirectories in <paramref name="path" />. This parameter can contain a combination of valid literal and wildcard characters (see Remarks), but doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include all subdirectories or only the current directory. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.-or- <paramref name="searchPattern" /> does not contain a valid pattern. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>Returns the volume information, root information, or both for the specified path.</summary>
      <returns>A string that contains the volume information, root information, or both for the specified path.</returns>
      <param name="path">The path of a file or directory. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>Returns the names of files (including their paths) in the specified directory.</summary>
      <returns>An array of the full names (including paths) for the files in the specified directory, or an empty array if no files are found.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.-or-A network error has occurred. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is not found or is invalid (for example, it is on an unmapped drive). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>Returns the names of files (including their paths) that match the specified search pattern in the specified directory.</summary>
      <returns>An array of the full names (including paths) for the files in the specified directory that match the specified search pattern, or an empty array if no files are found.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of files in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions.</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.-or-A network error has occurred. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters by using <see cref="M:System.IO.Path.GetInvalidPathChars" />.-or- <paramref name="searchPattern" /> doesn't contain a valid pattern. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is not found or is invalid (for example, it is on an unmapped drive). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns the names of files (including their paths) that match the specified search pattern in the specified directory, using a value to determine whether to search subdirectories.</summary>
      <returns>An array of the full names (including paths) for the files in the specified directory that match the specified search pattern and option, or an empty array if no files are found.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of files in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include all subdirectories or only the current directory. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. -or- <paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchpattern" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is not found or is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.-or-A network error has occurred. </exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>Returns the names of all files and subdirectories in a specified path.</summary>
      <returns>An array of the names of files and subdirectories in the specified directory, or an empty array if no files or subdirectories are found.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with <see cref="M:System.IO.Path.GetInvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>Returns an array of file names and directory names that that match a search pattern in a specified path.</summary>
      <returns>An array of file names and directory names that match the specified search criteria, or an empty array if no files or directories are found.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of file and directories in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions.</param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.-or- <paramref name="searchPattern" /> does not contain a valid pattern. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> or <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>Returns an array of all the file names and directory names that match a search pattern in a specified path, and optionally searches subdirectories.</summary>
      <returns>An array of file the file names and directory names that match the specified search criteria, or an empty array if no files or directories are found.</returns>
      <param name="path">The relative or absolute path to the directory to search. This string is not case-sensitive.</param>
      <param name="searchPattern">The search string to match against the names of files and directories in <paramref name="path" />.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or should include all subdirectories.The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />is a zero-length string, contains only white space, or contains invalid characters. You can query for invalid characters by using the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.- or -<paramref name="searchPattern" /> does not contain a valid pattern.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.-or-<paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid, such as referring to an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> is a file name.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or combined exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>Returns the date and time the specified file or directory was last accessed.</summary>
      <returns>A structure that is set to the date and time the specified file or directory was last accessed. This value is expressed in local time.</returns>
      <param name="path">The file or directory for which to obtain access date and time information. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="path" /> parameter is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>Returns the date and time, in Coordinated Universal Time (UTC) format, that the specified file or directory was last accessed.</summary>
      <returns>A structure that is set to the date and time the specified file or directory was last accessed. This value is expressed in UTC time.</returns>
      <param name="path">The file or directory for which to obtain access date and time information. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="path" /> parameter is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>Returns the date and time the specified file or directory was last written to.</summary>
      <returns>A structure that is set to the date and time the specified file or directory was last written to. This value is expressed in local time.</returns>
      <param name="path">The file or directory for which to obtain modification date and time information. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>Returns the date and time, in Coordinated Universal Time (UTC) format, that the specified file or directory was last written to.</summary>
      <returns>A structure that is set to the date and time the specified file or directory was last written to. This value is expressed in UTC time.</returns>
      <param name="path">The file or directory for which to obtain modification date and time information. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>Retrieves the parent directory of the specified path, including both absolute and relative paths.</summary>
      <returns>The parent directory, or null if <paramref name="path" /> is the root directory, including the root of a UNC server or share name.</returns>
      <param name="path">The path for which to retrieve the parent directory. </param>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="path" /> is read-only. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path was not found. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>Moves a file or a directory and its contents to a new location.</summary>
      <param name="sourceDirName">The path of the file or directory to move. </param>
      <param name="destDirName">The path to the new location for <paramref name="sourceDirName" />. If <paramref name="sourceDirName" /> is a file, then <paramref name="destDirName" /> must also be a file name.</param>
      <exception cref="T:System.IO.IOException">An attempt was made to move a directory to a different volume. -or- <paramref name="destDirName" /> already exists. -or- The <paramref name="sourceDirName" /> and <paramref name="destDirName" /> parameters refer to the same file or directory. -or-The directory or a file within it is being used by another process.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirName" /> or <paramref name="destDirName" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirName" /> or <paramref name="destDirName" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified by <paramref name="sourceDirName" /> is invalid (for example, it is on an unmapped drive). </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>Sets the creation date and time for the specified file or directory.</summary>
      <param name="path">The file or directory for which to set the creation date and time information. </param>
      <param name="creationTime">The date and time the file or directory was last written to. This value is expressed in local time.</param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifies a value outside the range of dates or times permitted for this operation. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Sets the creation date and time, in Coordinated Universal Time (UTC) format, for the specified file or directory.</summary>
      <param name="path">The file or directory for which to set the creation date and time information. </param>
      <param name="creationTimeUtc">The date and time the directory or file was created. This value is expressed in local time.</param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifies a value outside the range of dates or times permitted for this operation. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>Sets the application's current working directory to the specified directory.</summary>
      <param name="path">The path to which the current working directory is set. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurred. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission to access unmanaged code. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified directory was not found.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Sets the date and time the specified file or directory was last accessed.</summary>
      <param name="path">The file or directory for which to set the access date and time information. </param>
      <param name="lastAccessTime">An object that contains the value to set for the access date and time of <paramref name="path" />. This value is expressed in local time. </param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Sets the date and time, in Coordinated Universal Time (UTC) format, that the specified file or directory was last accessed.</summary>
      <param name="path">The file or directory for which to set the access date and time information. </param>
      <param name="lastAccessTimeUtc">An object that  contains the value to set for the access date and time of <paramref name="path" />. This value is expressed in UTC time. </param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Sets the date and time a directory was last written to.</summary>
      <param name="path">The path of the directory. </param>
      <param name="lastWriteTime">The date and time the directory was last written to. This value is expressed in local time.  </param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Sets the date and time, in Coordinated Universal Time (UTC) format, that a directory was last written to.</summary>
      <param name="path">The path of the directory. </param>
      <param name="lastWriteTimeUtc">The date and time the directory was last written to. This value is expressed in UTC time. </param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters. You can query for invalid characters with the  <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>Exposes instance methods for creating, moving, and enumerating through directories and subdirectories. This class cannot be inherited.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.DirectoryInfo" /> class on the specified path.</summary>
      <param name="path">A string specifying the path on which to create the DirectoryInfo. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> contains invalid characters such as ", &lt;, &gt;, or |. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. The specified path, file name, or both are too long.</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>Creates a directory.</summary>
      <exception cref="T:System.IO.IOException">The directory cannot be created. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>Creates a subdirectory or subdirectories on the specified path. The specified path can be relative to this instance of the <see cref="T:System.IO.DirectoryInfo" /> class.</summary>
      <returns>The last directory specified in <paramref name="path" />.</returns>
      <param name="path">The specified path. This cannot be a different disk volume or Universal Naming Convention (UNC) name. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> does not specify a valid file path or contains invalid DirectoryInfo characters. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">The subdirectory cannot be created.-or- A file or directory already has the name specified by <paramref name="path" />. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. The specified path, file name, or both are too long.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have code access permission to create the directory.-or-The caller does not have code access permission to read the directory described by the returned <see cref="T:System.IO.DirectoryInfo" /> object.  This can occur when the <paramref name="path" /> parameter describes an existing directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> contains a colon character (:) that is not part of a drive label ("C:\").</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>Deletes this <see cref="T:System.IO.DirectoryInfo" /> if it is empty.</summary>
      <exception cref="T:System.UnauthorizedAccessException">The directory contains a read-only file.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory described by this <see cref="T:System.IO.DirectoryInfo" /> object does not exist or could not be found.</exception>
      <exception cref="T:System.IO.IOException">The directory is not empty. -or-The directory is the application's current working directory.-or-There is an open handle on the directory, and the operating system is Windows XP or earlier. This open handle can result from enumerating directories. For more information, see How to: Enumerate Directories and Files.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>Deletes this instance of a <see cref="T:System.IO.DirectoryInfo" />, specifying whether to delete subdirectories and files.</summary>
      <param name="recursive">true to delete this directory, its subdirectories, and all files; otherwise, false. </param>
      <exception cref="T:System.UnauthorizedAccessException">The directory contains a read-only file.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory described by this <see cref="T:System.IO.DirectoryInfo" /> object does not exist or could not be found.</exception>
      <exception cref="T:System.IO.IOException">The directory is read-only.-or- The directory contains one or more files or subdirectories and <paramref name="recursive" /> is false.-or-The directory is the application's current working directory. -or-There is an open handle on the directory or on one of its files, and the operating system is Windows XP or earlier. This open handle can result from enumerating directories and files. For more information, see How to: Enumerate Directories and Files.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>Returns an enumerable collection of directory information in the current directory.</summary>
      <returns>An enumerable collection of directories in the current directory.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>Returns an enumerable collection of directory information that matches a specified search pattern.</summary>
      <returns>An enumerable collection of directories that matches <paramref name="searchPattern" />.</returns>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of directory information that matches a specified search pattern and search subdirectory option. </summary>
      <returns>An enumerable collection of directories that matches <paramref name="searchPattern" /> and <paramref name="searchOption" />.</returns>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories. The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>Returns an enumerable collection of file information in the current directory.</summary>
      <returns>An enumerable collection of the files in the current directory.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>Returns an enumerable collection of file information that matches a search pattern.</summary>
      <returns>An enumerable collection of files that matches <paramref name="searchPattern" />.</returns>
      <param name="searchPattern">The search string to match against the names of files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid, (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of file information that matches a specified search pattern and search subdirectory option.</summary>
      <returns>An enumerable collection of files that matches <paramref name="searchPattern" /> and <paramref name="searchOption" />.</returns>
      <param name="searchPattern">The search string to match against the names of files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories. The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>Returns an enumerable collection of file system information in the current directory.</summary>
      <returns>An enumerable collection of file system information in the current directory. </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>Returns an enumerable collection of file system information that matches a specified search pattern.</summary>
      <returns>An enumerable collection of file system information objects that matches <paramref name="searchPattern" />.</returns>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Returns an enumerable collection of file system information that matches a specified search pattern and search subdirectory option.</summary>
      <returns>An enumerable collection of file system information objects that matches <paramref name="searchPattern" /> and <paramref name="searchOption" />.</returns>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories. The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>Gets a value indicating whether the directory exists.</summary>
      <returns>true if the directory exists; otherwise, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>Returns the subdirectories of the current directory.</summary>
      <returns>An array of <see cref="T:System.IO.DirectoryInfo" /> objects.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the <see cref="T:System.IO.DirectoryInfo" /> object is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>Returns an array of directories in the current <see cref="T:System.IO.DirectoryInfo" /> matching the given search criteria.</summary>
      <returns>An array of type DirectoryInfo matching <paramref name="searchPattern" />.</returns>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern " />contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the DirectoryInfo object is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>Returns an array of directories in the current <see cref="T:System.IO.DirectoryInfo" /> matching the given search criteria and using a value to determine whether to search subdirectories.</summary>
      <returns>An array of type DirectoryInfo matching <paramref name="searchPattern" />.</returns>
      <param name="searchPattern">The search string to match against the names of directories.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern " />contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path encapsulated in the DirectoryInfo object is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>Returns a file list from the current directory.</summary>
      <returns>An array of type <see cref="T:System.IO.FileInfo" />.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is invalid, such as being on an unmapped drive. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>Returns a file list from the current directory matching the given search pattern.</summary>
      <returns>An array of type <see cref="T:System.IO.FileInfo" />.</returns>
      <param name="searchPattern">The search string to match against the names of files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern " />contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>Returns a file list from the current directory matching the given search pattern and using a value to determine whether to search subdirectories.</summary>
      <returns>An array of type <see cref="T:System.IO.FileInfo" />.</returns>
      <param name="searchPattern">The search string to match against the names of files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern " />contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>Returns an array of strongly typed <see cref="T:System.IO.FileSystemInfo" /> entries representing all the files and subdirectories in a directory.</summary>
      <returns>An array of strongly typed <see cref="T:System.IO.FileSystemInfo" /> entries.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is invalid (for example, it is on an unmapped drive). </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>Retrieves an array of strongly typed <see cref="T:System.IO.FileSystemInfo" /> objects representing the files and subdirectories that match the specified search criteria.</summary>
      <returns>An array of strongly typed FileSystemInfo objects matching the search criteria.</returns>
      <param name="searchPattern">The search string to match against the names of directories and files.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern " />contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>Retrieves an array of <see cref="T:System.IO.FileSystemInfo" /> objects that represent the files and subdirectories matching the specified search criteria.</summary>
      <returns>An array of file system entries that match the search criteria.</returns>
      <param name="searchPattern">The search string to match against the names of directories and filesa.  This parameter can contain a combination of valid literal path and wildcard (* and ?) characters (see Remarks), but doesn't support regular expressions. The default pattern is "*", which returns all files.</param>
      <param name="searchOption">One of the enumeration values that specifies whether the search operation should include only the current directory or all subdirectories. The default value is <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern " />contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> is not a valid <see cref="T:System.IO.SearchOption" /> value.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>Moves a <see cref="T:System.IO.DirectoryInfo" /> instance and its contents to a new path.</summary>
      <param name="destDirName">The name and path to which to move this directory. The destination cannot be another disk volume or a directory with the identical name. It can be an existing directory to which you want to add this directory as a subdirectory. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" /> is an empty string (''"). </exception>
      <exception cref="T:System.IO.IOException">An attempt was made to move a directory to a different volume. -or-<paramref name="destDirName" /> already exists.-or-You are not authorized to access this path.-or- The directory being moved and the destination directory have the same name.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The destination directory cannot be found.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>Gets the name of this <see cref="T:System.IO.DirectoryInfo" /> instance.</summary>
      <returns>The directory name.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>Gets the parent directory of a specified subdirectory.</summary>
      <returns>The parent directory, or null if the path is null or if the file path denotes a root (such as "\", "C:", or * "\\server\share").</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>Gets the root portion of the directory.</summary>
      <returns>An object that represents the root of the directory.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>Returns the original path that was passed by the user.</summary>
      <returns>Returns the original path that was passed by the user.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.File">
      <summary>Provides static methods for the creation, copying, deletion, moving, and opening of a single file, and aids in the creation of <see cref="T:System.IO.FileStream" /> objects.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Appends lines to a file, and then closes the file. If the specified file does not exist, this method creates a file, writes the specified lines to the file, and then closes the file.</summary>
      <param name="path">The file to append the lines to. The file is created if it doesn't already exist.</param>
      <param name="contents">The lines to append to the file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">Either<paramref name=" path " />or <paramref name="contents" /> is null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, the directory doesn’t exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to write to the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specifies a file that is read-only.-or-This operation is not supported on the current platform.-or-<paramref name="path" /> is a directory.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Appends lines to a file by using a specified encoding, and then closes the file. If the specified file does not exist, this method creates a file, writes the specified lines to the file, and then closes the file.</summary>
      <param name="path">The file to append the lines to. The file is created if it doesn't already exist.</param>
      <param name="contents">The lines to append to the file.</param>
      <param name="encoding">The character encoding to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">Either<paramref name=" path" />, <paramref name="contents" />, or <paramref name="encoding" /> is null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, the directory doesn’t exist or it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specifies a file that is read-only.-or-This operation is not supported on the current platform.-or-<paramref name="path" /> is a directory.-or-The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>Opens a file, appends the specified string to the file, and then closes the file. If the file does not exist, this method creates a file, writes the specified string to the file, then closes the file.</summary>
      <param name="path">The file to append the specified string to. </param>
      <param name="contents">The string to append to the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, the directory doesn’t exist or it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Appends the specified string to the file, creating the file if it does not already exist.</summary>
      <param name="path">The file to append the specified string to. </param>
      <param name="contents">The string to append to the file. </param>
      <param name="encoding">The character encoding to use. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, the directory doesn’t exist or it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>Creates a <see cref="T:System.IO.StreamWriter" /> that appends UTF-8 encoded text to an existing file, or to a new file if the specified file does not exist.</summary>
      <returns>A stream writer that appends UTF-8 encoded text to the specified file or to a new file.</returns>
      <param name="path">The path to the file to append to. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, the directory doesn’t exist or it is on an unmapped drive). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>Copies an existing file to a new file. Overwriting a file of the same name is not allowed.</summary>
      <param name="sourceFileName">The file to copy. </param>
      <param name="destFileName">The name of the destination file. This cannot be a directory or an existing file. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.-or- <paramref name="sourceFileName" /> or <paramref name="destFileName" /> specifies a directory. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified in <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> was not found. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> exists.-or- An I/O error has occurred. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>Copies an existing file to a new file. Overwriting a file of the same name is allowed.</summary>
      <param name="sourceFileName">The file to copy. </param>
      <param name="destFileName">The name of the destination file. This cannot be a directory. </param>
      <param name="overwrite">true if the destination file can be overwritten; otherwise, false. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. -or-<paramref name="destFileName" /> is read-only.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.-or- <paramref name="sourceFileName" /> or <paramref name="destFileName" /> specifies a directory. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified in <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> was not found. </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> exists and <paramref name="overwrite" /> is false.-or- An I/O error has occurred. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>Creates or overwrites a file in the specified path.</summary>
      <returns>A <see cref="T:System.IO.FileStream" /> that provides read/write access to the file specified in <paramref name="path" />.</returns>
      <param name="path">The path and name of the file to create. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.-or- <paramref name="path" /> specified a file that is read-only. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while creating the file. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>Creates or overwrites the specified file.</summary>
      <returns>A <see cref="T:System.IO.FileStream" /> with the specified buffer size that provides read/write access to the file specified in <paramref name="path" />.</returns>
      <param name="path">The name of the file. </param>
      <param name="bufferSize">The number of bytes buffered for reads and writes to the file. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.-or- <paramref name="path" /> specified a file that is read-only. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while creating the file. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>Creates or overwrites the specified file, specifying a buffer size and a <see cref="T:System.IO.FileOptions" /> value that describes how to create or overwrite the file.</summary>
      <returns>A new file with the specified buffer size.</returns>
      <param name="path">The name of the file. </param>
      <param name="bufferSize">The number of bytes buffered for reads and writes to the file. </param>
      <param name="options">One of the <see cref="T:System.IO.FileOptions" /> values that describes how to create or overwrite the file.</param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.-or- <paramref name="path" /> specified a file that is read-only. -or-<see cref="F:System.IO.FileOptions.Encrypted" /> is specified for <paramref name="options" /> and file encryption is not supported on the current platform.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while creating the file. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.-or- <paramref name="path" /> specified a file that is read-only. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.-or- <paramref name="path" /> specified a file that is read-only. </exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>Creates or opens a file for writing UTF-8 encoded text.</summary>
      <returns>A <see cref="T:System.IO.StreamWriter" /> that writes to the specified file using UTF-8 encoding.</returns>
      <param name="path">The file to be opened for writing. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>Deletes the specified file. </summary>
      <param name="path">The name of the file to be deleted. Wildcard characters are not supported.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">The specified file is in use. -or-There is an open handle on the file, and the operating system is Windows XP or earlier. This open handle can result from enumerating directories and files. For more information, see How to: Enumerate Directories and Files.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.-or- The file is an executable file that is in use.-or- <paramref name="path" /> is a directory.-or- <paramref name="path" /> specified a read-only file. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>Determines whether the specified file exists.</summary>
      <returns>true if the caller has the required permissions and <paramref name="path" /> contains the name of an existing file; otherwise, false. This method also returns false if <paramref name="path" /> is null, an invalid path, or a zero-length string. If the caller does not have sufficient permissions to read the specified file, no exception is thrown and the method returns false regardless of the existence of <paramref name="path" />.</returns>
      <param name="path">The file to check. </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>Gets the <see cref="T:System.IO.FileAttributes" /> of the file on the path.</summary>
      <returns>The <see cref="T:System.IO.FileAttributes" /> of the file on the path.</returns>
      <param name="path">The path to the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is empty, contains only white spaces, or contains invalid characters. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> represents a file and is invalid, such as being on an unmapped drive, or the file cannot be found. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> represents a directory and is invalid, such as being on an unmapped drive, or the directory cannot be found.</exception>
      <exception cref="T:System.IO.IOException">This file is being used by another process.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>Returns the creation date and time of the specified file or directory.</summary>
      <returns>A <see cref="T:System.DateTime" /> structure set to the creation date and time for the specified file or directory. This value is expressed in local time.</returns>
      <param name="path">The file or directory for which to obtain creation date and time information. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>Returns the creation date and time, in coordinated universal time (UTC), of the specified file or directory.</summary>
      <returns>A <see cref="T:System.DateTime" /> structure set to the creation date and time for the specified file or directory. This value is expressed in UTC time.</returns>
      <param name="path">The file or directory for which to obtain creation date and time information. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>Returns the date and time the specified file or directory was last accessed.</summary>
      <returns>A <see cref="T:System.DateTime" /> structure set to the date and time that the specified file or directory was last accessed. This value is expressed in local time.</returns>
      <param name="path">The file or directory for which to obtain access date and time information. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>Returns the date and time, in coordinated universal time (UTC), that the specified file or directory was last accessed.</summary>
      <returns>A <see cref="T:System.DateTime" /> structure set to the date and time that the specified file or directory was last accessed. This value is expressed in UTC time.</returns>
      <param name="path">The file or directory for which to obtain access date and time information. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>Returns the date and time the specified file or directory was last written to.</summary>
      <returns>A <see cref="T:System.DateTime" /> structure set to the date and time that the specified file or directory was last written to. This value is expressed in local time.</returns>
      <param name="path">The file or directory for which to obtain write date and time information. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>Returns the date and time, in coordinated universal time (UTC), that the specified file or directory was last written to.</summary>
      <returns>A <see cref="T:System.DateTime" /> structure set to the date and time that the specified file or directory was last written to. This value is expressed in UTC time.</returns>
      <param name="path">The file or directory for which to obtain write date and time information. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>Moves a specified file to a new location, providing the option to specify a new file name.</summary>
      <param name="sourceFileName">The name of the file to move. Can include a relative or absolute path.</param>
      <param name="destFileName">The new path and name for the file.</param>
      <exception cref="T:System.IO.IOException">The destination file already exists.-or-<paramref name="sourceFileName" /> was not found. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is a zero-length string, contains only white space, or contains invalid characters as defined in <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified in <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is invalid, (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> or <paramref name="destFileName" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>Opens a <see cref="T:System.IO.FileStream" /> on the specified path with read/write access.</summary>
      <returns>A <see cref="T:System.IO.FileStream" /> opened in the specified mode and path, with read/write access and not shared.</returns>
      <param name="path">The file to open. </param>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> value that specifies whether a file is created if one does not exist, and determines whether the contents of existing files are retained or overwritten. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. -or-<paramref name="mode" /> is <see cref="F:System.IO.FileMode.Create" /> and the specified file is a hidden file.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specified an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Opens a <see cref="T:System.IO.FileStream" /> on the specified path, with the specified mode and access.</summary>
      <returns>An unshared <see cref="T:System.IO.FileStream" /> that provides access to the specified file, with the specified mode and access.</returns>
      <param name="path">The file to open. </param>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> value that specifies whether a file is created if one does not exist, and determines whether the contents of existing files are retained or overwritten. </param>
      <param name="access">A <see cref="T:System.IO.FileAccess" /> value that specifies the operations that can be performed on the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.-or- <paramref name="access" /> specified Read and <paramref name="mode" /> specified Create, CreateNew, Truncate, or Append. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only and <paramref name="access" /> is not Read.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. -or-<paramref name="mode" /> is <see cref="F:System.IO.FileMode.Create" /> and the specified file is a hidden file.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> or <paramref name="access" /> specified an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Opens a <see cref="T:System.IO.FileStream" /> on the specified path, having the specified mode with read, write, or read/write access and the specified sharing option.</summary>
      <returns>A <see cref="T:System.IO.FileStream" /> on the specified path, having the specified mode with read, write, or read/write access and the specified sharing option.</returns>
      <param name="path">The file to open. </param>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> value that specifies whether a file is created if one does not exist, and determines whether the contents of existing files are retained or overwritten. </param>
      <param name="access">A <see cref="T:System.IO.FileAccess" /> value that specifies the operations that can be performed on the file. </param>
      <param name="share">A <see cref="T:System.IO.FileShare" /> value specifying the type of access other threads have to the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />.-or- <paramref name="access" /> specified Read and <paramref name="mode" /> specified Create, CreateNew, Truncate, or Append. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only and <paramref name="access" /> is not Read.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. -or-<paramref name="mode" /> is <see cref="F:System.IO.FileMode.Create" /> and the specified file is a hidden file.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />, <paramref name="access" />, or <paramref name="share" /> specified an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>Opens an existing file for reading.</summary>
      <returns>A read-only <see cref="T:System.IO.FileStream" /> on the specified path.</returns>
      <param name="path">The file to be opened for reading. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>Opens an existing UTF-8 encoded text file for reading.</summary>
      <returns>A <see cref="T:System.IO.StreamReader" /> on the specified path.</returns>
      <param name="path">The file to be opened for reading. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>Opens an existing file or creates a new file for writing.</summary>
      <returns>An unshared <see cref="T:System.IO.FileStream" /> object on the specified path with <see cref="F:System.IO.FileAccess.Write" /> access.</returns>
      <param name="path">The file to be opened for writing. </param>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission.-or- <paramref name="path" /> specified a read-only file or directory. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>Opens a binary file, reads the contents of the file into a byte array, and then closes the file.</summary>
      <returns>A byte array containing the contents of the file.</returns>
      <param name="path">The file to open for reading. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>Opens a text file, reads all lines of the file, and then closes the file.</summary>
      <returns>A string array containing all lines of the file.</returns>
      <param name="path">The file to open for reading. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>Opens a file, reads all lines of the file with the specified encoding, and then closes the file.</summary>
      <returns>A string array containing all lines of the file.</returns>
      <param name="path">The file to open for reading. </param>
      <param name="encoding">The encoding applied to the contents of the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>Opens a text file, reads all lines of the file, and then closes the file.</summary>
      <returns>A string containing all lines of the file.</returns>
      <param name="path">The file to open for reading. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>Opens a file, reads all lines of the file with the specified encoding, and then closes the file.</summary>
      <returns>A string containing all lines of the file.</returns>
      <param name="path">The file to open for reading. </param>
      <param name="encoding">The encoding applied to the contents of the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="path" /> was not found. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>Reads the lines of a file.</summary>
      <returns>All the lines of the file, or the lines that are the result of a query.</returns>
      <param name="path">The file to read.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specifies a file that is read-only.-or-This operation is not supported on the current platform.-or-<paramref name="path" /> is a directory.-or-The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>Read the lines of a file that has a specified encoding.</summary>
      <returns>All the lines of the file, or the lines that are the result of a query.</returns>
      <param name="path">The file to read.</param>
      <param name="encoding">The encoding that is applied to the contents of the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <paramref name="path" /> was not found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specifies a file that is read-only.-or-This operation is not supported on the current platform.-or-<paramref name="path" /> is a directory.-or-The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>Sets the specified <see cref="T:System.IO.FileAttributes" /> of the file on the specified path.</summary>
      <param name="path">The path to the file. </param>
      <param name="fileAttributes">A bitwise combination of the enumeration values. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is empty, contains only white spaces, contains invalid characters, or the file attribute is invalid. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>Sets the date and time the file was created.</summary>
      <param name="path">The file for which to set the creation date and time information. </param>
      <param name="creationTime">A <see cref="T:System.DateTime" /> containing the value to set for the creation date and time of <paramref name="path" />. This value is expressed in local time. </param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while performing the operation. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifies a value outside the range of dates, times, or both permitted for this operation. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>Sets the date and time, in coordinated universal time (UTC), that the file was created.</summary>
      <param name="path">The file for which to set the creation date and time information. </param>
      <param name="creationTimeUtc">A <see cref="T:System.DateTime" /> containing the value to set for the creation date and time of <paramref name="path" />. This value is expressed in UTC time. </param>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while performing the operation. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> specifies a value outside the range of dates, times, or both permitted for this operation. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>Sets the date and time the specified file was last accessed.</summary>
      <param name="path">The file for which to set the access date and time information. </param>
      <param name="lastAccessTime">A <see cref="T:System.DateTime" /> containing the value to set for the last access date and time of <paramref name="path" />. This value is expressed in local time. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>Sets the date and time, in coordinated universal time (UTC), that the specified file was last accessed.</summary>
      <param name="path">The file for which to set the access date and time information. </param>
      <param name="lastAccessTimeUtc">A <see cref="T:System.DateTime" /> containing the value to set for the last access date and time of <paramref name="path" />. This value is expressed in UTC time. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>Sets the date and time that the specified file was last written to.</summary>
      <param name="path">The file for which to set the date and time information. </param>
      <param name="lastWriteTime">A <see cref="T:System.DateTime" /> containing the value to set for the last write date and time of <paramref name="path" />. This value is expressed in local time. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>Sets the date and time, in coordinated universal time (UTC), that the specified file was last written to.</summary>
      <param name="path">The file for which to set the date and time information. </param>
      <param name="lastWriteTimeUtc">A <see cref="T:System.DateTime" /> containing the value to set for the last write date and time of <paramref name="path" />. This value is expressed in UTC time. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified path was not found. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> specifies a value outside the range of dates or times permitted for this operation.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>Creates a new file, writes the specified byte array to the file, and then closes the file. If the target file already exists, it is overwritten.</summary>
      <param name="path">The file to write to. </param>
      <param name="bytes">The bytes to write to the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null or the byte array is empty. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Creates a new file, writes a collection of strings to the file, and then closes the file.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The lines to write to the file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">Either<paramref name=" path " />or <paramref name="contents" /> is null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specifies a file that is read-only.-or-This operation is not supported on the current platform.-or-<paramref name="path" /> is a directory.-or-The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>Creates a new file by using the specified encoding, writes a collection of strings to the file, and then closes the file.</summary>
      <param name="path">The file to write to.</param>
      <param name="contents">The lines to write to the file.</param>
      <param name="encoding">The character encoding to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters defined by the <see cref="M:System.IO.Path.GetInvalidPathChars" /> method.</exception>
      <exception cref="T:System.ArgumentNullException">Either<paramref name=" path" />,<paramref name=" contents" />, or <paramref name="encoding" /> is null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> exceeds the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters and file names must be less than 260 characters.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specifies a file that is read-only.-or-This operation is not supported on the current platform.-or-<paramref name="path" /> is a directory.-or-The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>Creates a new file, writes the specified string to the file, and then closes the file. If the target file already exists, it is overwritten.</summary>
      <param name="path">The file to write to. </param>
      <param name="contents">The string to write to the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null or <paramref name="contents" /> is empty.  </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>Creates a new file, writes the specified string to the file using the specified encoding, and then closes the file. If the target file already exists, it is overwritten.</summary>
      <param name="path">The file to write to. </param>
      <param name="contents">The string to write to the file. </param>
      <param name="encoding">The encoding to apply to the string.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is a zero-length string, contains only white space, or contains one or more invalid characters as defined by <see cref="F:System.IO.Path.InvalidPathChars" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null or <paramref name="contents" /> is empty. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive). </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> specified a file that is read-only.-or- This operation is not supported on the current platform.-or- <paramref name="path" /> specified a directory.-or- The caller does not have the required permission. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> is in an invalid format. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>Provides properties and instance methods for the creation, copying, deletion, moving, and opening of files, and aids in the creation of <see cref="T:System.IO.FileStream" /> objects. This class cannot be inherited.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileInfo" /> class, which acts as a wrapper for a file path.</summary>
      <param name="fileName">The fully qualified name of the new file, or the relative file name. Do not end the path with the directory separator character.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">The file name is empty, contains only white spaces, or contains invalid characters. </exception>
      <exception cref="T:System.UnauthorizedAccessException">Access to <paramref name="fileName" /> is denied. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="fileName" /> contains a colon (:) in the middle of the string. </exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>Creates a <see cref="T:System.IO.StreamWriter" /> that appends text to the file represented by this instance of the <see cref="T:System.IO.FileInfo" />.</summary>
      <returns>A new StreamWriter.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>Copies an existing file to a new file, disallowing the overwriting of an existing file.</summary>
      <returns>A new file with a fully qualified path.</returns>
      <param name="destFileName">The name of the new file to copy to. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> is empty, contains only white spaces, or contains invalid characters. </exception>
      <exception cref="T:System.IO.IOException">An error occurs, or the destination file already exists. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> is null. </exception>
      <exception cref="T:System.UnauthorizedAccessException">A directory path is passed in, or the file is being moved to a different drive. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory specified in <paramref name="destFileName" /> does not exist.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contains a colon (:) within the string but does not specify the volume. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>Copies an existing file to a new file, allowing the overwriting of an existing file.</summary>
      <returns>A new file, or an overwrite of an existing file if <paramref name="overwrite" /> is true. If the file exists and <paramref name="overwrite" /> is false, an <see cref="T:System.IO.IOException" /> is thrown.</returns>
      <param name="destFileName">The name of the new file to copy to. </param>
      <param name="overwrite">true to allow an existing file to be overwritten; otherwise, false. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> is empty, contains only white spaces, or contains invalid characters. </exception>
      <exception cref="T:System.IO.IOException">An error occurs, or the destination file already exists and <paramref name="overwrite" /> is false. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> is null. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory specified in <paramref name="destFileName" /> does not exist.</exception>
      <exception cref="T:System.UnauthorizedAccessException">A directory path is passed in, or the file is being moved to a different drive. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contains a colon (:) in the middle of the string. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>Creates a file.</summary>
      <returns>A new file.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>Creates a <see cref="T:System.IO.StreamWriter" /> that writes a new text file.</summary>
      <returns>A new StreamWriter.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The file name is a directory. </exception>
      <exception cref="T:System.IO.IOException">The disk is read-only. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>Permanently deletes a file.</summary>
      <exception cref="T:System.IO.IOException">The target file is open or memory-mapped on a computer running Microsoft Windows NT.-or-There is an open handle on the file, and the operating system is Windows XP or earlier. This open handle can result from enumerating directories and files. For more information, see How to: Enumerate Directories and Files. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The path is a directory. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>Gets an instance of the parent directory.</summary>
      <returns>A <see cref="T:System.IO.DirectoryInfo" /> object representing the parent directory of this file.</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>Gets a string representing the directory's full path.</summary>
      <returns>A string representing the directory's full path.</returns>
      <exception cref="T:System.ArgumentNullException">null was passed in for the directory name. </exception>
      <exception cref="T:System.IO.PathTooLongException">The fully qualified path is 260 or more characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>Gets a value indicating whether a file exists.</summary>
      <returns>true if the file exists; false if the file does not exist or if the file is a directory.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>Gets or sets a value that determines if the current file is read only.</summary>
      <returns>true if the current file is read only; otherwise, false.</returns>
      <exception cref="T:System.IO.FileNotFoundException">The file described by the current <see cref="T:System.IO.FileInfo" /> object could not be found.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred while opening the file.</exception>
      <exception cref="T:System.UnauthorizedAccessException">This operation is not supported on the current platform.-or- The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">The user does not have write permission, but attempted to set this property to false.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>Gets the size, in bytes, of the current file.</summary>
      <returns>The size of the current file in bytes.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot update the state of the file or directory. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.-or- The Length property is called for a directory. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>Moves a specified file to a new location, providing the option to specify a new file name.</summary>
      <param name="destFileName">The path to move the file to, which can specify a different file name. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs, such as the destination file already exists or the destination device is not ready. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> is empty, contains only white spaces, or contains invalid characters. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" /> is read-only or is a directory. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> contains a colon (:) in the middle of the string. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>Gets the name of the file.</summary>
      <returns>The name of the file.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>Opens a file in the specified mode.</summary>
      <returns>A file opened in the specified mode, with read/write access and unshared.</returns>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> constant specifying the mode (for example, Open or Append) in which to open the file. </param>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The file is read-only or is a directory. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">The file is already open. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>Opens a file in the specified mode with read, write, or read/write access.</summary>
      <returns>A <see cref="T:System.IO.FileStream" /> object opened in the specified mode and access, and unshared.</returns>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> constant specifying the mode (for example, Open or Append) in which to open the file. </param>
      <param name="access">A <see cref="T:System.IO.FileAccess" /> constant specifying whether to open the file with Read, Write, or ReadWrite file access. </param>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> is read-only or is a directory. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">The file is already open. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Opens a file in the specified mode with read, write, or read/write access and the specified sharing option.</summary>
      <returns>A <see cref="T:System.IO.FileStream" /> object opened with the specified mode, access, and sharing options.</returns>
      <param name="mode">A <see cref="T:System.IO.FileMode" /> constant specifying the mode (for example, Open or Append) in which to open the file. </param>
      <param name="access">A <see cref="T:System.IO.FileAccess" /> constant specifying whether to open the file with Read, Write, or ReadWrite file access. </param>
      <param name="share">A <see cref="T:System.IO.FileShare" /> constant specifying the type of access other FileStream objects have to this file. </param>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> is read-only or is a directory. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">The file is already open. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>Creates a read-only <see cref="T:System.IO.FileStream" />.</summary>
      <returns>A new read-only <see cref="T:System.IO.FileStream" /> object.</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> is read-only or is a directory. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.IO.IOException">The file is already open. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>Creates a <see cref="T:System.IO.StreamReader" /> with UTF8 encoding that reads from an existing text file.</summary>
      <returns>A new StreamReader with UTF8 encoding.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file is not found. </exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> is read-only or is a directory. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>Creates a write-only <see cref="T:System.IO.FileStream" />.</summary>
      <returns>A write-only unshared <see cref="T:System.IO.FileStream" /> object for a new or existing file.</returns>
      <exception cref="T:System.UnauthorizedAccessException">The path specified when creating an instance of the <see cref="T:System.IO.FileInfo" /> object is read-only or is a directory.  </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path specified when creating an instance of the <see cref="T:System.IO.FileInfo" /> object is invalid, such as being on an unmapped drive. </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>Returns the path as a string.</summary>
      <returns>A string representing the path.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileOptions">
      <summary>Represents advanced options for creating a <see cref="T:System.IO.FileStream" /> object.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileOptions.Asynchronous">
      <summary>Indicates that a file can be used for asynchronous reading and writing. </summary>
    </member>
    <member name="F:System.IO.FileOptions.DeleteOnClose">
      <summary>Indicates that a file is automatically deleted when it is no longer in use.</summary>
    </member>
    <member name="F:System.IO.FileOptions.Encrypted">
      <summary>Indicates that a file is encrypted and can be decrypted only by using the same user account used for encryption.</summary>
    </member>
    <member name="F:System.IO.FileOptions.None">
      <summary>Indicates that no additional options should be used when creating a <see cref="T:System.IO.FileStream" /> object.</summary>
    </member>
    <member name="F:System.IO.FileOptions.RandomAccess">
      <summary>Indicates that the file is accessed randomly. The system can use this as a hint to optimize file caching.</summary>
    </member>
    <member name="F:System.IO.FileOptions.SequentialScan">
      <summary>Indicates that the file is to be accessed sequentially from beginning to end. The system can use this as a hint to optimize file caching. If an application moves the file pointer for random access, optimum caching may not occur; however, correct operation is still guaranteed. </summary>
    </member>
    <member name="F:System.IO.FileOptions.WriteThrough">
      <summary>Indicates that the system should write through any intermediate cache and go directly to disk.</summary>
    </member>
    <member name="T:System.IO.FileStream">
      <summary>Provides a <see cref="T:System.IO.Stream" /> for a file, supporting both synchronous and asynchronous read and write operations.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileStream" /> class for the specified file handle, with the specified read/write permission. </summary>
      <param name="handle">A file handle for the file that the current FileStream object will encapsulate. </param>
      <param name="access">A constant that sets the <see cref="P:System.IO.FileStream.CanRead" /> and <see cref="P:System.IO.FileStream.CanWrite" /> properties of the FileStream object. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="access" /> is not a field of <see cref="T:System.IO.FileAccess" />. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.IOException">An I/O error, such as a disk error, occurred.-or-The stream has been closed. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="access" /> requested is not permitted by the operating system for the specified file handle, such as when <paramref name="access" /> is Write or ReadWrite and the file handle is set for read-only access. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileStream" /> class for the specified file handle, with the specified read/write permission, and buffer size.</summary>
      <param name="handle">A file handle for the file that the current FileStream object will encapsulate. </param>
      <param name="access">A <see cref="T:System.IO.FileAccess" /> constant that sets the <see cref="P:System.IO.FileStream.CanRead" /> and <see cref="P:System.IO.FileStream.CanWrite" /> properties of the FileStream object. </param>
      <param name="bufferSize">A positive <see cref="T:System.Int32" /> value greater than 0 indicating the buffer size. The default buffer size is 4096.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="handle" /> parameter is an invalid handle.-or-The <paramref name="handle" /> parameter is a synchronous handle and it was used asynchronously. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="bufferSize" /> parameter is negative. </exception>
      <exception cref="T:System.IO.IOException">An I/O error, such as a disk error, occurred.-or-The stream has been closed.  </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="access" /> requested is not permitted by the operating system for the specified file handle, such as when <paramref name="access" /> is Write or ReadWrite and the file handle is set for read-only access. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileStream" /> class for the specified file handle, with the specified read/write permission, buffer size, and synchronous or asynchronous state.</summary>
      <param name="handle">A file handle for the file that this FileStream object will encapsulate. </param>
      <param name="access">A constant that sets the <see cref="P:System.IO.FileStream.CanRead" /> and <see cref="P:System.IO.FileStream.CanWrite" /> properties of the FileStream object. </param>
      <param name="bufferSize">A positive <see cref="T:System.Int32" /> value greater than 0 indicating the buffer size. The default buffer size is 4096.</param>
      <param name="isAsync">true if the handle was opened asynchronously (that is, in overlapped I/O mode); otherwise, false. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="handle" /> parameter is an invalid handle.-or-The <paramref name="handle" /> parameter is a synchronous handle and it was used asynchronously. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="bufferSize" /> parameter is negative. </exception>
      <exception cref="T:System.IO.IOException">An I/O error, such as a disk error, occurred.-or-The stream has been closed.  </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="access" /> requested is not permitted by the operating system for the specified file handle, such as when <paramref name="access" /> is Write or ReadWrite and the file handle is set for read-only access. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileStream" /> class with the specified path and creation mode.</summary>
      <param name="path">A relative or absolute path for the file that the current FileStream object will encapsulate. </param>
      <param name="mode">A constant that determines how to open or create the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string (""), contains only white space, or contains one or more invalid characters. -or-<paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in an NTFS environment.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in a non-NTFS environment.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found, such as when <paramref name="mode" /> is FileMode.Truncate or FileMode.Open, and the file specified by <paramref name="path" /> does not exist. The file must already exist in these modes. </exception>
      <exception cref="T:System.IO.IOException">An I/O error, such as specifying FileMode.CreateNew when the file specified by <paramref name="path" /> already exists, occurred.-or-The stream has been closed. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileStream" /> class with the specified path, creation mode, and read/write permission.</summary>
      <param name="path">A relative or absolute path for the file that the current FileStream object will encapsulate. </param>
      <param name="mode">A constant that determines how to open or create the file. </param>
      <param name="access">A constant that determines how the file can be accessed by the FileStream object. This also determines the values returned by the <see cref="P:System.IO.FileStream.CanRead" /> and <see cref="P:System.IO.FileStream.CanWrite" /> properties of the FileStream object. <see cref="P:System.IO.FileStream.CanSeek" /> is true if <paramref name="path" /> specifies a disk file. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string (""), contains only white space, or contains one or more invalid characters. -or-<paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in an NTFS environment.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in a non-NTFS environment.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found, such as when <paramref name="mode" /> is FileMode.Truncate or FileMode.Open, and the file specified by <paramref name="path" /> does not exist. The file must already exist in these modes. </exception>
      <exception cref="T:System.IO.IOException">An I/O error, such as specifying FileMode.CreateNew when the file specified by <paramref name="path" /> already exists, occurred. -or-The stream has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="access" /> requested is not permitted by the operating system for the specified <paramref name="path" />, such as when <paramref name="access" /> is Write or ReadWrite and the file or directory is set for read-only access. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileStream" /> class with the specified path, creation mode, read/write permission, and sharing permission.</summary>
      <param name="path">A relative or absolute path for the file that the current FileStream object will encapsulate. </param>
      <param name="mode">A constant that determines how to open or create the file. </param>
      <param name="access">A constant that determines how the file can be accessed by the FileStream object. This also determines the values returned by the <see cref="P:System.IO.FileStream.CanRead" /> and <see cref="P:System.IO.FileStream.CanWrite" /> properties of the FileStream object. <see cref="P:System.IO.FileStream.CanSeek" /> is true if <paramref name="path" /> specifies a disk file. </param>
      <param name="share">A constant that determines how the file will be shared by processes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string (""), contains only white space, or contains one or more invalid characters. -or-<paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in an NTFS environment.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in a non-NTFS environment.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found, such as when <paramref name="mode" /> is FileMode.Truncate or FileMode.Open, and the file specified by <paramref name="path" /> does not exist. The file must already exist in these modes. </exception>
      <exception cref="T:System.IO.IOException">An I/O error, such as specifying FileMode.CreateNew when the file specified by <paramref name="path" /> already exists, occurred. -or-The system is running Windows 98 or Windows 98 Second Edition and <paramref name="share" /> is set to FileShare.Delete.-or-The stream has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="access" /> requested is not permitted by the operating system for the specified <paramref name="path" />, such as when <paramref name="access" /> is Write or ReadWrite and the file or directory is set for read-only access. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> contains an invalid value. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileStream" /> class with the specified path, creation mode, read/write and sharing permission, and buffer size.</summary>
      <param name="path">A relative or absolute path for the file that the current FileStream object will encapsulate. </param>
      <param name="mode">A constant that determines how to open or create the file. </param>
      <param name="access">A constant that determines how the file can be accessed by the FileStream object. This also determines the values returned by the <see cref="P:System.IO.FileStream.CanRead" /> and <see cref="P:System.IO.FileStream.CanWrite" /> properties of the FileStream object. <see cref="P:System.IO.FileStream.CanSeek" /> is true if <paramref name="path" /> specifies a disk file. </param>
      <param name="share">A constant that determines how the file will be shared by processes. </param>
      <param name="bufferSize">A positive <see cref="T:System.Int32" /> value greater than 0 indicating the buffer size. The default buffer size is 4096. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string (""), contains only white space, or contains one or more invalid characters. -or-<paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in an NTFS environment.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in a non-NTFS environment.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> is negative or zero.-or- <paramref name="mode" />, <paramref name="access" />, or <paramref name="share" /> contain an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found, such as when <paramref name="mode" /> is FileMode.Truncate or FileMode.Open, and the file specified by <paramref name="path" /> does not exist. The file must already exist in these modes. </exception>
      <exception cref="T:System.IO.IOException">An I/O error, such as specifying FileMode.CreateNew when the file specified by <paramref name="path" /> already exists, occurred. -or-The system is running Windows 98 or Windows 98 Second Edition and <paramref name="share" /> is set to FileShare.Delete.-or-The stream has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="access" /> requested is not permitted by the operating system for the specified <paramref name="path" />, such as when <paramref name="access" /> is Write or ReadWrite and the file or directory is set for read-only access. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileStream" /> class with the specified path, creation mode, read/write and sharing permission, buffer size, and synchronous or asynchronous state.</summary>
      <param name="path">A relative or absolute path for the file that the current FileStream object will encapsulate. </param>
      <param name="mode">A constant that determines how to open or create the file. </param>
      <param name="access">A constant that determines how the file can be accessed by the FileStream object. This also determines the values returned by the <see cref="P:System.IO.FileStream.CanRead" /> and <see cref="P:System.IO.FileStream.CanWrite" /> properties of the FileStream object. <see cref="P:System.IO.FileStream.CanSeek" /> is true if <paramref name="path" /> specifies a disk file. </param>
      <param name="share">A constant that determines how the file will be shared by processes. </param>
      <param name="bufferSize">A positive <see cref="T:System.Int32" /> value greater than 0 indicating the buffer size. The default buffer size is 4096.. </param>
      <param name="useAsync">Specifies whether to use asynchronous I/O or synchronous I/O. However, note that the underlying operating system might not support asynchronous I/O, so when specifying true, the handle might be opened synchronously depending on the platform. When opened asynchronously, the <see cref="M:System.IO.FileStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> and <see cref="M:System.IO.FileStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> methods perform better on large reads or writes, but they might be much slower for small reads or writes. If the application is designed to take advantage of asynchronous I/O, set the <paramref name="useAsync" /> parameter to true. Using asynchronous I/O correctly can speed up applications by as much as a factor of 10, but using it without redesigning the application for asynchronous I/O can decrease performance by as much as a factor of 10. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string (""), contains only white space, or contains one or more invalid characters. -or-<paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in an NTFS environment.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in a non-NTFS environment.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> is negative or zero.-or- <paramref name="mode" />, <paramref name="access" />, or <paramref name="share" /> contain an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found, such as when <paramref name="mode" /> is FileMode.Truncate or FileMode.Open, and the file specified by <paramref name="path" /> does not exist. The file must already exist in these modes. </exception>
      <exception cref="T:System.IO.IOException">An I/O error, such as specifying FileMode.CreateNew when the file specified by <paramref name="path" /> already exists, occurred.-or- The system is running Windows 98 or Windows 98 Second Edition and <paramref name="share" /> is set to FileShare.Delete.-or-The stream has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="access" /> requested is not permitted by the operating system for the specified <paramref name="path" />, such as when <paramref name="access" /> is Write or ReadWrite and the file or directory is set for read-only access. </exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.IO.FileOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileStream" /> class with the specified path, creation mode, read/write and sharing permission, the access other FileStreams can have to the same file, the buffer size, and additional file options.</summary>
      <param name="path">A relative or absolute path for the file that the current FileStream object will encapsulate. </param>
      <param name="mode">A constant that determines how to open or create the file. </param>
      <param name="access">A constant that determines how the file can be accessed by the FileStream object. This also determines the values returned by the <see cref="P:System.IO.FileStream.CanRead" /> and <see cref="P:System.IO.FileStream.CanWrite" /> properties of the FileStream object. <see cref="P:System.IO.FileStream.CanSeek" /> is true if <paramref name="path" /> specifies a disk file. </param>
      <param name="share">A constant that determines how the file will be shared by processes. </param>
      <param name="bufferSize">A positive <see cref="T:System.Int32" /> value greater than 0 indicating the buffer size. The default buffer size is 4096.</param>
      <param name="options">A value that specifies additional file options.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> is an empty string (""), contains only white space, or contains one or more invalid characters. -or-<paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in an NTFS environment.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> refers to a non-file device, such as "con:", "com1:", "lpt1:", etc. in a non-NTFS environment.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> is negative or zero.-or- <paramref name="mode" />, <paramref name="access" />, or <paramref name="share" /> contain an invalid value. </exception>
      <exception cref="T:System.IO.FileNotFoundException">The file cannot be found, such as when <paramref name="mode" /> is FileMode.Truncate or FileMode.Open, and the file specified by <paramref name="path" /> does not exist. The file must already exist in these modes. </exception>
      <exception cref="T:System.IO.IOException">An I/O error, such as specifying FileMode.CreateNew when the file specified by <paramref name="path" /> already exists, occurred.-or-The stream has been closed.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid, such as being on an unmapped drive. </exception>
      <exception cref="T:System.UnauthorizedAccessException">The <paramref name="access" /> requested is not permitted by the operating system for the specified <paramref name="path" />, such as when <paramref name="access" /> is Write or ReadWrite and the file or directory is set for read-only access. -or-<see cref="F:System.IO.FileOptions.Encrypted" /> is specified for <paramref name="options" />, but file encryption is not supported on the current platform.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path, file name, or both exceed the system-defined maximum length. For example, on Windows-based platforms, paths must be less than 248 characters, and file names must be less than 260 characters. </exception>
    </member>
    <member name="P:System.IO.FileStream.CanRead">
      <summary>Gets a value indicating whether the current stream supports reading.</summary>
      <returns>true if the stream supports reading; false if the stream is closed or was opened with write-only access.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanSeek">
      <summary>Gets a value indicating whether the current stream supports seeking.</summary>
      <returns>true if the stream supports seeking; false if the stream is closed or if the FileStream was constructed from an operating-system handle such as a pipe or output to the console.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanWrite">
      <summary>Gets a value indicating whether the current stream supports writing.</summary>
      <returns>true if the stream supports writing; false if the stream is closed or was opened with read-only access.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.FileStream" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.IO.FileStream.Finalize">
      <summary>Ensures that resources are freed and other cleanup operations are performed when the garbage collector reclaims the FileStream.</summary>
    </member>
    <member name="M:System.IO.FileStream.Flush">
      <summary>Clears buffers for this stream and causes any buffered data to be written to the file.</summary>
      <exception cref="T:System.IO.IOException">An I/O error occurred. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Flush(System.Boolean)">
      <summary>Clears buffers for this stream and causes any buffered data to be written to the file, and also clears all intermediate file buffers.</summary>
      <param name="flushToDisk">true to flush all intermediate file buffers; otherwise, false. </param>
    </member>
    <member name="M:System.IO.FileStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Asynchronously clears all buffers for this stream, causes any buffered data to be written to the underlying device, and monitors cancellation requests. </summary>
      <returns>A task that represents the asynchronous flush operation. </returns>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
    </member>
    <member name="P:System.IO.FileStream.IsAsync">
      <summary>Gets a value indicating whether the FileStream was opened asynchronously or synchronously.</summary>
      <returns>true if the FileStream was opened asynchronously; otherwise, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Length">
      <summary>Gets the length in bytes of the stream.</summary>
      <returns>A long value representing the length of the stream in bytes.</returns>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.FileStream.CanSeek" /> for this stream is false. </exception>
      <exception cref="T:System.IO.IOException">An I/O error, such as the file being closed, occurred. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Name">
      <summary>Gets the name of the FileStream that was passed to the constructor.</summary>
      <returns>A string that is the name of the FileStream.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileStream.Position">
      <summary>Gets or sets the current position of this stream.</summary>
      <returns>The current position of this stream.</returns>
      <exception cref="T:System.NotSupportedException">The stream does not support seeking. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred. - or -The position was set to a very large value beyond the end of the stream in Windows 98 or earlier.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Attempted to set the position to a negative value. </exception>
      <exception cref="T:System.IO.EndOfStreamException">Attempted seeking past the end of a stream that does not support this. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads a block of bytes from the stream and writes the data in a given buffer.</summary>
      <returns>The total number of bytes read into the buffer. This might be less than the number of bytes requested if that number of bytes are not currently available, or zero if the end of the stream is reached.</returns>
      <param name="array">When this method returns, contains the specified byte array with the values between <paramref name="offset" /> and (<paramref name="offset" /> + <paramref name="count" /> - 1<paramref name=")" /> replaced by the bytes read from the current source. </param>
      <param name="offset">The byte offset in <paramref name="array" /> at which the read bytes will be placed. </param>
      <param name="count">The maximum number of bytes to read. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.NotSupportedException">The stream does not support reading. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> and <paramref name="count" /> describe an invalid range in <paramref name="array" />. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously reads a sequence of bytes from the current stream, advances the position within the stream by the number of bytes read, and monitors cancellation requests.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the stream has been reached. </returns>
      <param name="buffer">The buffer to write the data into.</param>
      <param name="offset">The byte offset in <paramref name="buffer" /> at which to begin writing data from the stream.</param>
      <param name="count">The maximum number of bytes to read.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.NotSupportedException">The stream does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.FileStream.ReadByte">
      <summary>Reads a byte from the file and advances the read position one byte.</summary>
      <returns>The byte, cast to an <see cref="T:System.Int32" />, or -1 if the end of the stream has been reached.</returns>
      <exception cref="T:System.NotSupportedException">The current stream does not support reading. </exception>
      <exception cref="T:System.ObjectDisposedException">The current stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.SafeFileHandle">
      <summary>Gets a <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> object that represents the operating system file handle for the file that the current <see cref="T:System.IO.FileStream" /> object encapsulates.</summary>
      <returns>An object that represents the operating system file handle for the file that the current <see cref="T:System.IO.FileStream" /> object encapsulates.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Sets the current position of this stream to the given value.</summary>
      <returns>The new position in the stream.</returns>
      <param name="offset">The point relative to <paramref name="origin" /> from which to begin seeking. </param>
      <param name="origin">Specifies the beginning, the end, or the current position as a reference point for <paramref name="offset" />, using a value of type <see cref="T:System.IO.SeekOrigin" />. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurred. </exception>
      <exception cref="T:System.NotSupportedException">The stream does not support seeking, such as if the FileStream is constructed from a pipe or console output. </exception>
      <exception cref="T:System.ArgumentException">Seeking is attempted before the beginning of the stream. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.SetLength(System.Int64)">
      <summary>Sets the length of this stream to the given value.</summary>
      <param name="value">The new length of the stream. </param>
      <exception cref="T:System.IO.IOException">An I/O error has occurred. </exception>
      <exception cref="T:System.NotSupportedException">The stream does not support both writing and seeking. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Attempted to set the <paramref name="value" /> parameter to less than 0. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes a block of bytes to the file stream.</summary>
      <param name="array">The buffer containing data to write to the stream.</param>
      <param name="offset">The zero-based byte offset in <paramref name="array" /> from which to begin copying bytes to the stream. </param>
      <param name="count">The maximum number of bytes to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> and <paramref name="count" /> describe an invalid range in <paramref name="array" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred. - or -Another thread may have caused an unexpected change in the position of the operating system's file handle. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.NotSupportedException">The current stream instance does not support writing. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously writes a sequence of bytes to the current stream, advances the current position within this stream by the number of bytes written, and monitors cancellation requests. </summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The buffer to write data from. </param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> from which to begin copying bytes to the stream.</param>
      <param name="count">The maximum number of bytes to write.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.NotSupportedException">The stream does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.FileStream.WriteByte(System.Byte)">
      <summary>Writes a byte to the current position in the file stream.</summary>
      <param name="value">A byte to write to the stream. </param>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.NotSupportedException">The stream does not support writing. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>Provides the base class for both <see cref="T:System.IO.FileInfo" /> and <see cref="T:System.IO.DirectoryInfo" /> objects.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.FileSystemInfo" /> class.</summary>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>Gets or sets the attributes for the current file or directory.</summary>
      <returns>
        <see cref="T:System.IO.FileAttributes" /> of the current <see cref="T:System.IO.FileSystemInfo" />.</returns>
      <exception cref="T:System.IO.FileNotFoundException">The specified file does not exist. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid; for example, it is on an unmapped drive. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <exception cref="T:System.ArgumentException">The caller attempts to set an invalid file attribute. -or-The user attempts to set an attribute value but does not have write permission.</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>Gets or sets the creation time of the current file or directory.</summary>
      <returns>The creation date and time of the current <see cref="T:System.IO.FileSystemInfo" /> object.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid; for example, it is on an unmapped drive.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid creation time.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>Gets or sets the creation time, in coordinated universal time (UTC), of the current file or directory.</summary>
      <returns>The creation date and time in UTC format of the current <see cref="T:System.IO.FileSystemInfo" /> object.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data. </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid; for example, it is on an unmapped drive.</exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid access time.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>Deletes a file or directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid; for example, it is on an unmapped drive.</exception>
      <exception cref="T:System.IO.IOException">There is an open handle on the file or directory, and the operating system is Windows XP or earlier. This open handle can result from enumerating directories and files. For more information, see How to: Enumerate Directories and Files.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>Gets a value indicating whether the file or directory exists.</summary>
      <returns>true if the file or directory exists; otherwise, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>Gets the string representing the extension part of the file.</summary>
      <returns>A string containing the <see cref="T:System.IO.FileSystemInfo" /> extension.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>Gets the full path of the directory or file.</summary>
      <returns>A string containing the full path.</returns>
      <exception cref="T:System.IO.PathTooLongException">The fully qualified path and file name is 260 or more characters.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission. </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>Represents the fully qualified path of the directory or file.</summary>
      <exception cref="T:System.IO.PathTooLongException">The fully qualified path is 260 or more characters.</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>Gets or sets the time the current file or directory was last accessed.</summary>
      <returns>The time that the current file or directory was last accessed.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid access time</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>Gets or sets the time, in coordinated universal time (UTC), that the current file or directory was last accessed.</summary>
      <returns>The UTC time that the current file or directory was last accessed.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid access time.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>Gets or sets the time when the current file or directory was last written to.</summary>
      <returns>The time the current file was last written.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid write time.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>Gets or sets the time, in coordinated universal time (UTC), when the current file or directory was last written to.</summary>
      <returns>The UTC time when the current file was last written to.</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> cannot initialize the data. </exception>
      <exception cref="T:System.PlatformNotSupportedException">The current operating system is not Windows NT or later.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The caller attempts to set an invalid write time.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>For files, gets the name of the file. For directories, gets the name of the last directory in the hierarchy if a hierarchy exists. Otherwise, the Name property gets the name of the directory.</summary>
      <returns>A string that is the name of the parent directory, the name of the last directory in the hierarchy, or the name of a file, including the file name extension.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>The path originally specified by the user, whether relative or absolute.</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>Refreshes the state of the object.</summary>
      <exception cref="T:System.IO.IOException">A device such as a disk drive is not ready. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>Specifies whether to search the current directory, or the current directory and all subdirectories. </summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>Includes the current directory and all its subdirectories in a search operation. This option includes reparse points such as mounted drives and symbolic links in the search.</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>Includes only the current directory in a search operation.</summary>
    </member>
  </members>
</doc>