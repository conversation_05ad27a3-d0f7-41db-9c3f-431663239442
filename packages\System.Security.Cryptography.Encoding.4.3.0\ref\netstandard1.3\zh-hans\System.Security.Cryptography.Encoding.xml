﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.Encoding</name>
  </assembly>
  <members>
    <member name="T:System.Security.Cryptography.AsnEncodedData">
      <summary>表示 Abstract Syntax Notation One (ASN.1) 编码数据。</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Byte[])">
      <summary>使用一个字节数组初始化 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 类的新实例。</summary>
      <param name="rawData">一个包含 Abstract Syntax Notation One (ASN.1) 编码数据的字节数组。</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>使用 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 类的一个实例初始化 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 类的新实例。</summary>
      <param name="asnEncodedData">
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 类的实例。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> 为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Security.Cryptography.Oid,System.Byte[])">
      <summary>使用一个 <see cref="T:System.Security.Cryptography.Oid" /> 对象和一个字节数组初始化 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 类的新实例。</summary>
      <param name="oid">
        <see cref="T:System.Security.Cryptography.Oid" /> 对象。</param>
      <param name="rawData">一个包含 Abstract Syntax Notation One (ASN.1) 编码数据的字节数组。</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.String,System.Byte[])">
      <summary>使用一个字节数组初始化 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 类的新实例。</summary>
      <param name="oid">一个表示 <see cref="T:System.Security.Cryptography.Oid" /> 信息的字符串。</param>
      <param name="rawData">一个包含 Abstract Syntax Notation One (ASN.1) 编码数据的字节数组。</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>从 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象复制信息。</summary>
      <param name="asnEncodedData">新对象基于的 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData " />为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.Format(System.Boolean)">
      <summary>将 Abstract Syntax Notation One (ASN.1) 编码数据的格式化版本作为字符串返回。</summary>
      <returns>一个表示 Abstract Syntax Notation One (ASN.1) 编码数据的格式化字符串。</returns>
      <param name="multiLine">如果返回字符串应包含回车，则为 true；否则为 false。</param>
    </member>
    <member name="P:System.Security.Cryptography.AsnEncodedData.Oid">
      <summary>获取或设置 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 对象的 <see cref="T:System.Security.Cryptography.Oid" /> 值。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.Oid" /> 对象。</returns>
    </member>
    <member name="P:System.Security.Cryptography.AsnEncodedData.RawData">
      <summary>获取或设置以字节数组表示的 Abstract Syntax Notation One (ASN.1) 编码数据。</summary>
      <returns>一个表示 Abstract Syntax Notation One (ASN.1) 编码数据的字节数组。</returns>
      <exception cref="T:System.ArgumentNullException">该值为 null。</exception>
    </member>
    <member name="T:System.Security.Cryptography.Oid">
      <summary>表示加密对象标识符。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.Security.Cryptography.Oid)">
      <summary>使用指定的 <see cref="T:System.Security.Cryptography.Oid" /> 对象初始化 <see cref="T:System.Security.Cryptography.Oid" /> 类的新实例。</summary>
      <param name="oid">用于创建新对象标识符的对象标识符信息。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oid " />为 null。</exception>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.String)">
      <summary>使用 <see cref="T:System.Security.Cryptography.Oid" /> 对象的字符串值初始化 <see cref="T:System.Security.Cryptography.Oid" /> 类的新实例。</summary>
      <param name="oid">对象标识符。</param>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.String,System.String)">
      <summary>使用指定的值和友好名称初始化 <see cref="T:System.Security.Cryptography.Oid" /> 类的新实例。</summary>
      <param name="value">标识符的圆点分隔的数字。</param>
      <param name="friendlyName">标识符的友好名称。</param>
    </member>
    <member name="P:System.Security.Cryptography.Oid.FriendlyName">
      <summary>获取或设置标识符的友好名称。</summary>
      <returns>标识符的友好名称。</returns>
    </member>
    <member name="M:System.Security.Cryptography.Oid.FromFriendlyName(System.String,System.Security.Cryptography.OidGroup)">
      <summary>通过搜索指定组从 OID 友好名称创建 <see cref="T:System.Security.Cryptography.Oid" /> 对象。</summary>
      <returns>表示指定的 OID 的对象。</returns>
      <param name="friendlyName">标识符的友好名称。</param>
      <param name="group">要在其中搜索的组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="friendlyName " /> 为 null。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">未找到 OID。</exception>
    </member>
    <member name="M:System.Security.Cryptography.Oid.FromOidValue(System.String,System.Security.Cryptography.OidGroup)">
      <summary>使用指定的 OID 值和组，创建 <see cref="T:System.Security.Cryptography.Oid" /> 对象。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.Oid" /> 对象的一个新实例。</returns>
      <param name="oidValue">OID 值。</param>
      <param name="group">要在其中搜索的组。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oidValue" /> 为 null。</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">用于 OID 值的未找到的友好名称。</exception>
    </member>
    <member name="P:System.Security.Cryptography.Oid.Value">
      <summary>获取或设置标识符的圆点分隔的数字。</summary>
      <returns>标识符的圆点分隔的数字。</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidCollection">
      <summary>表示 <see cref="T:System.Security.Cryptography.Oid" /> 对象的集合。此类不能被继承。</summary>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.#ctor">
      <summary>初始化 <see cref="T:System.Security.Cryptography.OidCollection" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.Add(System.Security.Cryptography.Oid)">
      <summary>将 <see cref="T:System.Security.Cryptography.Oid" /> 对象添加到 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象中。</summary>
      <returns>添加的 <see cref="T:System.Security.Cryptography.Oid" /> 对象的索引。</returns>
      <param name="oid">要添加到集合中的 <see cref="T:System.Security.Cryptography.Oid" /> 对象。</param>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.CopyTo(System.Security.Cryptography.Oid[],System.Int32)">
      <summary>将 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象复制到一个数组中。</summary>
      <param name="array">将 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象复制到其中的数组。</param>
      <param name="index">复制操作的起始位置。</param>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Count">
      <summary>获取集合中 <see cref="T:System.Security.Cryptography.Oid" /> 对象的数目。</summary>
      <returns>集合中 <see cref="T:System.Security.Cryptography.Oid" /> 对象的数目。</returns>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.GetEnumerator">
      <summary>返回一个 <see cref="T:System.Security.Cryptography.OidEnumerator" /> 对象，该对象可用于定位 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidEnumerator" /> 对象。</returns>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Item(System.Int32)">
      <summary>从 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象获取 <see cref="T:System.Security.Cryptography.Oid" /> 对象。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.Oid" /> 对象。</returns>
      <param name="index">
        <see cref="T:System.Security.Cryptography.Oid" /> 对象在集合中的位置。</param>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Item(System.String)">
      <summary>获取第一个 <see cref="T:System.Security.Cryptography.Oid" /> 对象，该对象包含与 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象中指定的字符串值相匹配的 <see cref="P:System.Security.Cryptography.Oid.Value" /> 属性的值或 <see cref="P:System.Security.Cryptography.Oid.FriendlyName" /> 属性的值。</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.Oid" /> 对象。</returns>
      <param name="oid">一个字符串，表示 <see cref="P:System.Security.Cryptography.Oid.Value" /> 属性或 <see cref="P:System.Security.Cryptography.Oid.FriendlyName" /> 属性。</param>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>将 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象复制到一个数组中。</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.OidCollection" /> 对象将复制到该数组中。</param>
      <param name="index">复制操作的起始位置。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> 不能为多维数组。- 或 -<paramref name="array" /> 的长度是无效偏移量长度。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 的值超出范围。</exception>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.OidCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.OidCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个 <see cref="T:System.Security.Cryptography.OidEnumerator" /> 对象，该对象可用于定位 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象。</summary>
      <returns>可用于定位集合的 <see cref="T:System.Security.Cryptography.OidEnumerator" /> 对象。</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidEnumerator">
      <summary>提供在 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象中导航的能力。此类不能被继承。</summary>
    </member>
    <member name="P:System.Security.Cryptography.OidEnumerator.Current">
      <summary>在 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象中获取当前 <see cref="T:System.Security.Cryptography.Oid" /> 对象。</summary>
      <returns>集合中当前的 <see cref="T:System.Security.Cryptography.Oid" /> 对象。</returns>
    </member>
    <member name="M:System.Security.Cryptography.OidEnumerator.MoveNext">
      <summary>前移到 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象中的下一个 <see cref="T:System.Security.Cryptography.Oid" /> 对象。</summary>
      <returns>如果枚举数成功前移到下一个元素，则为 true；如果枚举数已超过集合末尾，则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="M:System.Security.Cryptography.OidEnumerator.Reset">
      <summary>将枚举数设置到它的初始位置。</summary>
      <exception cref="T:System.InvalidOperationException">在创建了枚举数后集合被修改了。</exception>
    </member>
    <member name="P:System.Security.Cryptography.OidEnumerator.System#Collections#IEnumerator#Current">
      <summary>在 <see cref="T:System.Security.Cryptography.OidCollection" /> 对象中获取当前 <see cref="T:System.Security.Cryptography.Oid" /> 对象。</summary>
      <returns>当前的 <see cref="T:System.Security.Cryptography.Oid" /> 对象。</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidGroup">
      <summary>标识 Windows 加密对象标识符 (OID) 团队。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.All">
      <summary>所有组。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Attribute">
      <summary>由 CRYPT_RDN_ATTR_OID_GROUP_ID 表示的 Windows 组。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.EncryptionAlgorithm">
      <summary>由 CRYPT_ENCRYPT_ALG_OID_GROUP_ID 表示的 Windows 组。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.EnhancedKeyUsage">
      <summary>由 CRYPT_ENHKEY_USAGE_OID_GROUP_ID 表示的 Windows 组。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.ExtensionOrAttribute">
      <summary>由 CRYPT_EXT_OR_ATTR_OID_GROUP_ID 表示的 Windows 组。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.HashAlgorithm">
      <summary>由 CRYPT_HASH_ALG_OID_GROUP_ID 表示的 Windows 团队。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.KeyDerivationFunction">
      <summary>由 CRYPT_KDF_OID_GROUP_ID 表示的 Windows 组。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Policy">
      <summary>由 CRYPT_POLICY_OID_GROUP_ID 表示的 Windows 组。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.PublicKeyAlgorithm">
      <summary>由 CRYPT_PUBKEY_ALG_OID_GROUP_ID 表示的 Windows 组。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.SignatureAlgorithm">
      <summary>由 CRYPT_SIGN_ALG_OID_GROUP_ID 表示的 Windows 团队。</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Template">
      <summary>由 CRYPT_TEMPLATE_OID_GROUP_ID 表示的 Windows 组。</summary>
    </member>
  </members>
</doc>