﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.ServiceContracts
{
    public interface IVoiceRecService
    {
        bool SaveVoiceRecDBCallsInSurveyDB(string callIds, int userId, int surveyId, int tenantId, int revsyncSurveyId); //Delete This Contract when Running Single Database
        bool SaveVoiceRecDBCallsInSurveyDBOnRecorder(string callIds, int userId, int surveyId, Recorder recorder, int tenantId);
        VoiceRecResponse CreateAndGetCallSearchResults(VoiceRecRequest voiceRecRequest);

        VoiceRecResponse GetCallSearchResults(VoiceRecRequest voiceRecRequest);
        VoiceRecResponse GetCallSearchResultsPaged(VoiceRecRequest voiceRecRequest);
        VoiceRecResponse GetCallsByLocation(VoiceRecRequest voiceRecRequest);
        //List<CallInfoSearchResultDTO> GetCallSearchResults(int numberOfRows, int pageIndex, out int totalPages);
        List<CallInfo> GetCallsByIds(string callIds, int tenantId);
        VoiceRecResponse GetCallInfoById(string callId, int tenantId);
        VoiceRecResponse GetCallInfosByCommaSeperatedIds(string callIds, int tenantId);

        bool UpdateCallInfoCustomFields(VoiceRecRequest voiceRecRequest);
        VoiceRecResponse UpdateCallInfoFields(string callIds, string fieldName, string fieldText, int userId, int tenantId);
        VoiceRecResponse UpdateCallInfoRetainValue(string callId, bool retainValue, int userId, int tenantId);

        //bool SaveBookMark(VoiceRecRequest voiceRecRequest);
        VoiceRecResponse SaveBookMark(VoiceRecRequest voiceRecRequest);

        VoiceRecResponse GetMonitorChannels(VoiceRecRequest voiceRecRequest);

        //VoiceRecResponse GetSearchPageData(VoiceRecRequest voiceRecRequest);
        //VoiceRecResponse GetIRFullPageData(VoiceRecRequest voiceRecRequest);



        
        VoiceRecResponse GetSimpleUserSearchValues(VoiceRecRequest voiceRecRequest);

        #region Playlist
        //VoiceRecResponse SavePlaylist(VoiceRecRequest voiceRecRequest);
        //bool DeletePlaylist(int playlistId);
        //List<Playlist> GetPlaylist(int userId);
        //VoiceRecResponse GetPlaylist(int id, int userId);
        //VoiceRecResponse AddTracksInsidePlaylist(int playlistId, string callIds, int userId,int MaxItems);
        //List<PlaylistDetail> GetTracksInsidePlaylist(int playlistId);
        //bool DeletePlaylistDetails(int playlistId);
        //bool DeletePlaylistDetail(int playlistDetailId);
        #endregion

        #region Enterprise Configuration

        #endregion
        VoiceRecResponse GetCallsByLocationFromRecorder(VoiceRecRequest voiceRecRequest);

        #region Onsite Contact Information

        VoiceRecResponse UpdateOnsiteContactInfoConfirmation(int UserNum, string Comments, int tenantId);
        VoiceRecResponse GetOnsiteContactInfoConfirmation(int tenantId);

        #endregion
    }
}
