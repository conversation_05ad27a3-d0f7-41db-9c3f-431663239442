﻿using Newtonsoft.Json;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.IQ3ConditionalLogic;
using RevCord.DataContracts.IQ3InspectionEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace RevCord.DataAccess
{
    public class InspectionDAL
    {
        private int _tenantId;
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 300);

        public InspectionDAL(int tenantId)
        {
            _tenantId = tenantId;
        }

        public List<InspectionTemplate> FetchInspectionTemplates(int userId, bool IsCommon = false, bool OnlyDefaultTemplates = false)
        {
            List<InspectionTemplate> inspectionTemplates = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_GETALL;
                    cmd.Parameters.AddWithValue("@UserNum", userId);
                    cmd.Parameters.AddWithValue("@IsCommon", IsCommon);
                    cmd.Parameters.AddWithValue("@OnlyDefaultTemplates", OnlyDefaultTemplates);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "FetchInspectionTemplates", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        inspectionTemplates = ORMapper.GetAllInspectionTemplates(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return inspectionTemplates;
        }

        public InspectionTemplate GetInspectionTemplateById(int inspectionTemplateId, bool viewOnly = false)
        {
            InspectionTemplate inspectionTemplate = new InspectionTemplate();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_GET_BY_ID;
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetInspectionTemplateById", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        inspectionTemplate = ORMapper.GetInspectionTemplateById(dr);
                    }
                }

                if (inspectionTemplate != null)
                {
                    inspectionTemplate.PreInspections = this.GetAllPreInspections(inspectionTemplate.Id);
                    inspectionTemplate.PreInspectionGroupFields = this.GetPreInspectionGroupsByTemplateId(inspectionTemplate.Id, false);

                    inspectionTemplate.TemplateSections = this.GetAllSections(inspectionTemplate.Id);

                    if (inspectionTemplate.TemplateSections != null)
                    {
                        foreach (var section in inspectionTemplate.TemplateSections)
                        {
                            if (viewOnly)
                            {
                                section.Markers = this.GetAllMarkersForView(section.Id);
                            }
                            else
                            {
                                section.Markers = this.GetAllMarkers(section.Id);
                            }

                            if (section.Markers != null)
                            {
                                foreach (var marker in section.Markers)
                                {
                                    marker.MarkerOptions = this.GetAllMarkerOptions(Convert.ToInt32(marker.Id));
                                    marker.MarkerLogics = this.GetMarkerLogics(Convert.ToInt32(marker.Id));
                                    if (marker.MarkerTypeId == 5)
                                    {
                                        marker.GraphicMarkerDetail = this.GetGraphicMarkerDetailsById(marker.Id);
                                    }
                                }
                            }
                        }
                    }
                }

            }
            catch (Exception ex) { throw ex; }
            return inspectionTemplate;
        }



        #region Templates
        public InspectionTemplate addTemplate(InspectionTemplate inspectionTemplate, bool AddDefaultSection = true)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_INSERT;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Title", inspectionTemplate.Title);
                    cmd.Parameters.AddWithValue("@Description", inspectionTemplate.Description);
                    cmd.Parameters.AddWithValue("@TemplateType", inspectionTemplate.TemplateType);
                    cmd.Parameters.AddWithValue("@DisableCopyMarker", inspectionTemplate.DisableCopyMarker);
                    cmd.Parameters.AddWithValue("@IsPassFailRequired", inspectionTemplate.IsPassFailRequired);
                    cmd.Parameters.AddWithValue("@InspectionTypeID", inspectionTemplate.InspectionTypeID);
                    cmd.Parameters.AddWithValue("@HasSections", inspectionTemplate.HasSections);
                    cmd.Parameters.AddWithValue("@CreatedBy", inspectionTemplate.CreatedBy);
                    cmd.Parameters.AddWithValue("@AddDefaultSection", AddDefaultSection);

                    cmd.Parameters.Add("@SectionId", SqlDbType.Int).Direction = ParameterDirection.Output;

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "addTemplate", _tenantId));

                    inspectionTemplate.Id = Convert.ToInt32(cmd.ExecuteScalar());

                    if (AddDefaultSection)
                    {
                        inspectionTemplate.TemplateSections = new List<Section>();
                        inspectionTemplate.TemplateSections.Add(new Section { Id = (int)cmd.Parameters["@SectionId"].Value });
                    }

                    tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return inspectionTemplate;
        }

        public bool updateTemplate(InspectionTemplate inspectionTemplate)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_UPDATE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Id", inspectionTemplate.Id);
                    cmd.Parameters.AddWithValue("@Title", inspectionTemplate.Title);
                    cmd.Parameters.AddWithValue("@TemplateType", inspectionTemplate.TemplateType);
                    cmd.Parameters.AddWithValue("@DisableCopyMarker", inspectionTemplate.DisableCopyMarker);
                    cmd.Parameters.AddWithValue("@IsPassFailRequired", inspectionTemplate.IsPassFailRequired);
                    cmd.Parameters.AddWithValue("@InspectionTypeID", inspectionTemplate.InspectionTypeID);
                    cmd.Parameters.AddWithValue("@Description", inspectionTemplate.Description);
                    cmd.Parameters.AddWithValue("@HasSections", inspectionTemplate.HasSections);
                    cmd.Parameters.AddWithValue("@ModifiedBy", inspectionTemplate.ModifiedBy);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "updateTemplate", _tenantId));
                    var result = Convert.ToBoolean(cmd.ExecuteScalar());
                    tran.Commit();

                    return result;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<PreInspection> addPreInspectionData(int inspectionTemplateId, List<PreInspection> preInspections, string customFieldIds)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_PREINSPECTION_INSERT;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    foreach (var preInspection in preInspections)
                    {
                        cmd.Parameters.AddWithValue("@InspectionTemplateId", preInspection.InspectionTemplateId);
                        cmd.Parameters.AddWithValue("@Title", preInspection.Title);
                        cmd.Parameters.Add("@Id", SqlDbType.Int).Direction = ParameterDirection.Output;

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "addPreInspectionData", _tenantId));
                        preInspection.Id = (int)cmd.ExecuteScalar();
                        cmd.Parameters.Clear();
                    }

                    // CustomFieldIds Save
                    if (customFieldIds != null)
                    {
                        cmd.CommandText = DBConstants.UserPreInspection.IQ3_INSPECTION_TEMPLATE_USER_PREINSPECTION_UPDATE;
                        cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                        cmd.Parameters.AddWithValue("@UserPreInspection", customFieldIds);

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "addPreInspectionData->CustomFieldIds", _tenantId));
                        int count = Convert.ToInt32(cmd.ExecuteNonQuery());
                    }

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "addPreInspectionData", _tenantId));
                    tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return preInspections;
        }

        public bool updatePreInspectionData(int inspectionTemplateId, List<PreInspection> preInspections, string customFieldIds)
        {
            var bUpdated = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_PREINSPECTION_UPDATE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    foreach (var preInspection in preInspections)
                    {
                        cmd.Parameters.AddWithValue("@Id", preInspection.Id);
                        cmd.Parameters.AddWithValue("@Title", preInspection.Title);

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "updatePreInspectionData", _tenantId));
                        bUpdated = Convert.ToBoolean(cmd.ExecuteNonQuery());
                        cmd.Parameters.Clear();
                    }

                    // CustomFieldIds Save
                    if (customFieldIds != null)
                    {
                        cmd.CommandText = DBConstants.UserPreInspection.IQ3_INSPECTION_TEMPLATE_USER_PREINSPECTION_UPDATE;
                        cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                        cmd.Parameters.AddWithValue("@UserPreInspection", customFieldIds);

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "addPreInspectionData->CustomFieldIds", _tenantId));
                        int count = Convert.ToInt32(cmd.ExecuteNonQuery());
                        if (count > 0)
                        {
                            bUpdated = true;
                        }
                    }

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "updatePreInspectionData", _tenantId));
                    tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return bUpdated;
        }
        #endregion

        #region Markers
        public int SaveMarker(Marker marker)
        {
            try
            {
                XElement markerOptionsXML = this.CreateMarkerOptionsXML(marker.MarkerOptions);
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_MARKER_INSERT;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", marker.InspectionTemplateId);
                    cmd.Parameters.AddWithValue("@SectionId", marker.SectionId);
                    cmd.Parameters.AddWithValue("@Title", marker.Title);
                    cmd.Parameters.AddWithValue("@Description", marker.Description);
                    cmd.Parameters.AddWithValue("@MarkerTypeId", marker.MarkerTypeId);
                    cmd.Parameters.AddWithValue("@IsPhotoAllowed", marker.IsPhotoAllowed);
                    cmd.Parameters.AddWithValue("@IsRequired", marker.IsRequired);
                    cmd.Parameters.AddWithValue("@IsMultiSection", marker.IsMultiSection);
                    cmd.Parameters.AddWithValue("@IsRepeating", marker.IsRepeating);
                    cmd.Parameters.AddWithValue("@Ordering", marker.Ordering);
                    cmd.Parameters.AddWithValue("@MarkerOptionsXML", markerOptionsXML.ToString());
                    cmd.Parameters.AddWithValue("@CreatedDate", marker.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", marker.IsDeleted);
                    cmd.Parameters.AddWithValue("@MarkerId", 0);
                    cmd.Parameters.AddWithValue("@ParentId", marker.ParentId);
                    cmd.Parameters["@MarkerId"].Direction = ParameterDirection.Output;

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "SaveMarker", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (marker.MarkerOptions != null && marker.MarkerOptions.Count != 0)
                        {
                            while (dr.Read())
                            {
                                foreach (var item in marker.MarkerOptions)
                                {
                                    if (item.Ordering == (int)dr["Ordering"])
                                        item.Id = (long)dr["Id"];
                                }
                            }
                        }                        
                    }
                    marker.MarkerId = marker.Id;
                    marker.Id = Convert.ToInt32(cmd.Parameters["@MarkerId"].Value.ToString());

                    tran.Commit();
                    return marker.Id;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion

        #region Section

        public List<Section> GetSectionsByTemplateId(int inspectionTemplateId)
        {
            List<Section> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.SECTION_GETBYTEMPLATEID;
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "GetSectionsByTemplateId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapInspectionSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        public List<Section> CreateAndGetSections(Section section)
        {
            List<Section> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.SECTION_INSERT_AND_GET;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Title", section.Title);
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", section.InspectionTemplateId);
                    cmd.Parameters.AddWithValue("@SectionType", (byte)section.SectionType);
                    cmd.Parameters.AddWithValue("@SectionCategory", (byte)section.SectionCategory);
                    cmd.Parameters.AddWithValue("@AllowCopySection", section.AllowCopySection);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "CreateAndGetSections", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapInspectionSections(dr);
                    }
                    tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        public int CreateSection(Section section)
        {
            int sectionId = 0;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.SECTION_INSERT;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Title", section.Title);
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", section.InspectionTemplateId);
                    cmd.Parameters.AddWithValue("@IsDefaultSection", section.IsDefaultSection);
                    cmd.Parameters.AddWithValue("@AllowCopySection", section.AllowCopySection);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "CreateSection", _tenantId));

                    sectionId = Convert.ToInt32(cmd.ExecuteScalar());
                    tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return sectionId;
        }

        public List<Section> UpateAndGetSections(Section section)
        {
            List<Section> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.SECTION_UPDATE_AND_GET;
                    cmd.Parameters.AddWithValue("@Id", section.Id);
                    cmd.Parameters.AddWithValue("@Title", section.Title);
                    cmd.Parameters.AddWithValue("@SectionCategory", (byte)section.SectionCategory);
                    cmd.Parameters.AddWithValue("@AllowCopySection", section.AllowCopySection);
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", section.InspectionTemplateId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "UpateAndGetSections", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapInspectionSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        public List<Section> DeleteAndGetSections(int sectionId, int inspectionTemplateId)
        {
            List<Section> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.SECTION_DELETE_AND_GET;
                    cmd.Parameters.AddWithValue("@Id ", sectionId);
                    cmd.Parameters.AddWithValue("@InspectionTemplateId ", inspectionTemplateId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "DeleteAndGetSections", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapInspectionSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        public List<Marker> GetMarkersByInspectionTemplateId(int inspectionTemplateId)
        {
            List<Marker> markers = new List<Marker>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.MARKER_GETBY_INSPECTIONTEMPLATEID;
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetMarkersByInspectionTemplateId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markers = ORMapper.MapMarkers(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markers;
        }

        public List<Section> UpdateMarkerSectionAndGetAllSections(int inspectionTemplateId, int sectionId, string assignedMarkers, string unassignedMarkers)
        {
            List<Section> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.MARKER_UPDATE_SECTION;
                    cmd.Parameters.AddWithValue("InspectionTemplateId", inspectionTemplateId);
                    cmd.Parameters.AddWithValue("SectionId", sectionId);
                    cmd.Parameters.AddWithValue("AssignedMarkers", assignedMarkers);
                    cmd.Parameters.AddWithValue("UnassignedMarkers", unassignedMarkers);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateMarkerSectionAndGetAllSections", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapInspectionSections(dr);
                    }
                    return sections;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Private Functions
        private List<Section> GetAllSections(int inspectionTemlateId)
        {
            List<Section> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM iq3Section WHERE InspectionTemplateId = @InspectionTemplateId and IsDeleted = 0 AND SectionType = 1 AND IsCopySection = 0;";
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemlateId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetAllSections", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.GetAllSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        private List<Marker> GetAllMarkers(int sectionId)
        {
            List<Marker> markers = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from iq3Marker where SectionId = @SectionId and IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@SectionId", sectionId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetAllMarkers", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markers = ORMapper.GetAllMarkers(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markers;
        }

        public List<Marker> GetMarkersByInspectionTemplate(int inspectionTemplateId)
        {
            List<Marker> markers = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from iq3Marker where InspectionTemplateId = @InspectionTemplateId and IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetMarkersByInspectionTemplate", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markers = ORMapper.GetAllMarkers(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markers;
        }

        private List<Marker> GetAllMarkersForView(int sectionId)
        {
            List<Marker> markers = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from iq3Marker where (SectionId = @SectionId and IsDeleted = 0) OR Id IN (SELECT MarkerId FROM iq3MarkerSections WHERE SectionId = @SectionId AND IsDeleted = 0);";
                    cmd.Parameters.AddWithValue("@SectionId", sectionId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetAllMarkers", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markers = ORMapper.GetAllMarkers(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markers;
        }

        private List<MarkerOption> GetAllMarkerOptions(int markerId)
        {
            List<MarkerOption> markerOptions = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from iq3MarkerOption where MarkerId = @MarkerId and IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@MarkerId", markerId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetAllMarkerOptions", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markerOptions = ORMapper.GetAllMarkerOptions(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markerOptions;
        }
        
        private List<PreInspection> GetAllPreInspections(int inspectionTemplateId)
        {
            List<PreInspection> preInspections = new List<PreInspection>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_PREINSPECTION_GETALL;
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetAllPreInspections", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        preInspections = ORMapper.GetAllPreInspections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return preInspections;
        }

        private XElement CreateMarkerOptionsXML(List<MarkerOption> markerOptions)
        {
            try
            {
                XElement options = new XElement("Options");
                foreach (var option in markerOptions)
                {
                    options.Add(new XElement("Option",
                                    new XAttribute("Id", option.Id),
                                    new XAttribute("MarkerId", option.MarkerId),
                                    new XAttribute("Title", option.Title),
                                    new XAttribute("Ordering", option.Ordering),
                                    new XAttribute("CreatedDate", DateTime.Now),
                                    new XAttribute("IsDeleted", option.IsDeleted)
                                ));
                }
                return options;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }        
        #endregion

        public Inspection GetInspectionByEventId(string eventId)
        {
            Inspection inspection = null;
            int AssetId = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_GET_BY_EVENTID;
                    cmd.Parameters.AddWithValue("@EventId", eventId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetInspectionByEventId", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        inspection = ORMapper.MapInspection(dr);

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            if (dr.Read())
                            {
                                AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToInt32(dr["AssetId"]) : 0;
                            }
                        }
                    }
                }

                if (inspection != null)
                {
                    try
                    {
                        if (AssetId > 0)
                        {
                            inspection.IQ3Asset = new CustomerDBDAL(_tenantId).GetIQ3AssetById(AssetId);
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return inspection;
        }

        public int DeleteTemplate(int id, int RevSyncServerID)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                conn.Open();

                SqlTransaction tran = conn.BeginTransaction();
                cmd.Transaction = tran;

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_DELETE;
                cmd.Parameters.AddWithValue("@TemplateId", id);

                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "DeleteTemplate", _tenantId));

                int rowsaffected = cmd.ExecuteNonQuery();

                tran.Commit();

                /*
                 * Commenting for now
                if (SiteConfig.RevSyncEnabled && RevSyncServerID > 0)
                {
                    bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "InspectionDAL", "DeleteTemplate", JsonConvert.SerializeObject(RevSyncServerID)));
                    if (bReturn) tran.Commit(); else tran.Rollback();
                }
                else if (SiteConfig.IsMTEnable)
                {
                    bool isSent = DALHelper.SendMessageToHub(_tenantId, "InspectionDAL", "DeleteTemplate", JsonConvert.SerializeObject(id));
                    if (isSent) tran.Commit(); else tran.Rollback();
                }
                else tran.Commit();
                */

                return rowsaffected;
            }
        }

        public int ShareTemplate(int id, string SelectedUsers, int UserNum)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                conn.Open();

                SqlTransaction tran = conn.BeginTransaction();
                cmd.Transaction = tran;

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_SHARE;
                cmd.Parameters.AddWithValue("@TemplateId", id);
                cmd.Parameters.AddWithValue("@SelectedUsers", SelectedUsers);
                cmd.Parameters.AddWithValue("@UserNum", UserNum);

                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "ShareTemplate", _tenantId));

                int rowsaffected = cmd.ExecuteNonQuery();

                tran.Commit();

                /*
                 * Commenting for now
                if (SiteConfig.RevSyncEnabled && RevSyncServerID > 0)
                {
                    bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "InspectionDAL", "DeleteTemplate", JsonConvert.SerializeObject(RevSyncServerID)));
                    if (bReturn) tran.Commit(); else tran.Rollback();
                }
                else if (SiteConfig.IsMTEnable)
                {
                    bool isSent = DALHelper.SendMessageToHub(_tenantId, "InspectionDAL", "DeleteTemplate", JsonConvert.SerializeObject(id));
                    if (isSent) tran.Commit(); else tran.Rollback();
                }
                else tran.Commit();
                */

                return rowsaffected;
            }
        }

        public int UnshareTemplate(int id, int UserNum)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                conn.Open();

                SqlTransaction tran = conn.BeginTransaction();
                cmd.Transaction = tran;

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_UNSHARE;
                cmd.Parameters.AddWithValue("@TemplateId", id);

                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Playlist, "UnshareTemplate", _tenantId));

                int rowsaffected = cmd.ExecuteNonQuery();

                tran.Commit();

                /*
                 * Commenting for now
                if (SiteConfig.RevSyncEnabled && RevSyncServerID > 0)
                {
                    bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "InspectionDAL", "DeleteTemplate", JsonConvert.SerializeObject(RevSyncServerID)));
                    if (bReturn) tran.Commit(); else tran.Rollback();
                }
                else if (SiteConfig.IsMTEnable)
                {
                    bool isSent = DALHelper.SendMessageToHub(_tenantId, "InspectionDAL", "DeleteTemplate", JsonConvert.SerializeObject(id));
                    if (isSent) tran.Commit(); else tran.Rollback();
                }
                else tran.Commit();
                */

                return rowsaffected;
            }
        }

        public InspectionTemplate CopyTemplate(int UserId, InspectionTemplate inspectionTemplate)
        {
            inspectionTemplate.IsShared = false;
            inspectionTemplate.CreatedBy = UserId;

            var insertedInspectionTemplate = this.addTemplate(inspectionTemplate, false);

            foreach (var preInspectiondata in inspectionTemplate.PreInspections)
            {
                preInspectiondata.InspectionTemplateId = insertedInspectionTemplate.Id;
            }

            this.addPreInspectionData(inspectionTemplate.Id, inspectionTemplate.PreInspections, string.Empty);

            foreach (var section in inspectionTemplate.TemplateSections)
            {
                section.InspectionTemplateId = insertedInspectionTemplate.Id;
                var sectionId = this.CreateSection(section);

                if (inspectionTemplate.DisableCopyMarker == false)
                {
                    foreach (var marker in section.Markers)
                    {
                        if (marker.MarkerTypeId == 4)
                        {
                            // Copy Graphic Marker
                            marker.InspectionTemplateId = insertedInspectionTemplate.Id;
                            marker.SectionId = sectionId;
                            var markerId = this.SaveMarker(marker);
                            marker.Id = markerId;

                            var graphicMarker = this.GetGraphicMarkerDetailsById(marker.Id);
                            foreach (var graphicSection in graphicMarker.Sections)
                            {
                                // Create Graphic Marker Section for Data Items
                                graphicSection.InspectionTemplateId = insertedInspectionTemplate.Id;
                                var newSectionId = this.SaveGraphicMarkerSection(graphicSection, graphicMarker.Id);
                                foreach (var graphicMarkerItem in graphicSection.Markers)
                                {
                                    // Create Markers / Data Items under Each Section.
                                    graphicMarkerItem.InspectionTemplateId = insertedInspectionTemplate.Id;
                                    graphicMarkerItem.SectionId = newSectionId;
                                    var mrkrId = this.SaveMarker(graphicMarkerItem);
                                    graphicMarkerItem.Id = mrkrId;
                                }
                            }
                        }
                        else
                        {
                            marker.InspectionTemplateId = insertedInspectionTemplate.Id;
                            marker.SectionId = sectionId;
                            var markerId = this.SaveMarker(marker);
                            marker.Id = markerId;
                        }
                    }
                }
            }

            return GetInspectionTemplateById(insertedInspectionTemplate.Id);
        }

        public int DeletePreInspection(int preInspectionId)
        {
            using (var conn = DALHelper.GetConnection(_tenantId))
            using (var cmd = conn.CreateCommand())
            {
                conn.Open();

                SqlTransaction tran = conn.BeginTransaction();
                cmd.Transaction = tran;

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_PRE_INSPECTION_DELETE;
                cmd.Parameters.AddWithValue("@PreInspectionId", preInspectionId);

                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "DeletePreInspection", _tenantId));

                int rowsaffected = cmd.ExecuteNonQuery();

                tran.Commit();

                return rowsaffected;
            }
        }

        public Marker UpdateMarker(Marker marker)
        {
            try
            {
                XElement markerOptionsXML = this.CreateMarkerOptionsXML(marker.MarkerOptions);
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_MARKER_UPDATE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Id", marker.Id);
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", marker.InspectionTemplateId);
                    cmd.Parameters.AddWithValue("@SectionId", marker.SectionId);
                    cmd.Parameters.AddWithValue("@Title", marker.Title);
                    cmd.Parameters.AddWithValue("@Description", marker.Description);
                    cmd.Parameters.AddWithValue("@MarkerTypeId", marker.MarkerTypeId);
                    cmd.Parameters.AddWithValue("@IsPhotoAllowed", marker.IsPhotoAllowed);
                    cmd.Parameters.AddWithValue("@IsRepeating", marker.IsRepeating);
                    cmd.Parameters.AddWithValue("@IsRequired", marker.IsRequired);
                    cmd.Parameters.AddWithValue("@IsMultiSection", marker.IsMultiSection);
                    cmd.Parameters.AddWithValue("@Ordering", marker.Ordering);
                    cmd.Parameters.AddWithValue("@MarkerOptionsXML", markerOptionsXML.ToString());
                    cmd.Parameters.AddWithValue("@CreatedDate", marker.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", marker.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "UpdateMarker", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (marker.MarkerOptions != null && marker.MarkerOptions.Count != 0)
                        {
                            while (dr.Read())
                            {
                                foreach (var item in marker.MarkerOptions)
                                {
                                    if (item.Ordering == (int)dr["Ordering"])
                                        item.Id = (long)dr["Id"];
                                }
                            }
                        }
                    }
                    marker.MarkerId = marker.Id;
                    marker.Id = Convert.ToInt32(cmd.Parameters["@Id"].Value.ToString());

                    tran.Commit();
                    return marker;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int DeleteMarker(int markerId)
        {
            int rowsaffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_MARKER_DELETE;
                    cmd.Parameters.AddWithValue("@MarkerId", markerId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "DeleteMarker", _tenantId));
                    rowsaffected = cmd.ExecuteNonQuery();
                    return rowsaffected;
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public InspectionTitle LoadTitleDetails()
        {
            InspectionTitle _InspectionTitle = new InspectionTitle();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM iq3InspectionTitle;";
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "LoadTitleDetails", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            _InspectionTitle.Address1 = dr["Address1"].ToString();
                            _InspectionTitle.Address2 = dr["Address2"].ToString();
                            _InspectionTitle.Address3 = dr["Address3"].ToString();
                            _InspectionTitle.City = dr["City"].ToString();
                            _InspectionTitle.State = dr["State"].ToString();
                            _InspectionTitle.Zip = dr["Zip"].ToString();
                            _InspectionTitle.Country = dr["Country"].ToString();
                            _InspectionTitle.CustomerName = dr["CustomerName"].ToString();
                            _InspectionTitle.CustomerContact = dr["CustomerContact"].ToString();
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return _InspectionTitle;
        }

        public bool SaveTitleDetails(InspectionTitle _InspectionTitle)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandText = "UPDATE [dbo].[iq3InspectionTitle] SET [Address1] = @Address1 ,[Address2] = @Address2,[Address3] = @Address3 ,[City] = @City ,[State] = @State ,[Zip] = @Zip ,[Country] = @Country ,[CustomerName] = @CustomerName ,[CustomerContact] = @CustomerContact; ";
                    cmd.CommandType = CommandType.Text;
                    cmd.Parameters.AddWithValue("@Address1", _InspectionTitle.Address1);
                    cmd.Parameters.AddWithValue("@Address2", _InspectionTitle.Address2);
                    cmd.Parameters.AddWithValue("@Address3", _InspectionTitle.Address3);
                    cmd.Parameters.AddWithValue("@City", _InspectionTitle.City);
                    cmd.Parameters.AddWithValue("@State", _InspectionTitle.State);
                    cmd.Parameters.AddWithValue("@Zip", _InspectionTitle.Zip);
                    cmd.Parameters.AddWithValue("@Country", _InspectionTitle.Country);
                    cmd.Parameters.AddWithValue("@CustomerName", _InspectionTitle.CustomerName);
                    cmd.Parameters.AddWithValue("@CustomerContact", _InspectionTitle.CustomerContact);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "SaveTitleDetails", 0));
                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #region Multi Section Markers
        public List<Marker> GetMultiSectionMarkers(int inspectionTemplateId)
        {
            List<Marker> markers = new List<Marker>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.MARKER_GET_MULTISECTION_MARKERS;
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetMultiSectionMarkers", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markers = ORMapper.MapMarkers(dr);
                    }
                }

                if (markers != null)
                {
                    foreach (var marker in markers)
                    {
                        marker.MarkerSections = this.GetMarkerSectionsByMarkerId(marker.Id);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markers;
        }

        private List<MarkerSection> GetMarkerSectionsByMarkerId(int markerId)
        {
            List<MarkerSection> markerSections = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.MARKER_GET_MARKER_SECTIONS;
                    cmd.Parameters.AddWithValue("@MarkerId", markerId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetMarkerSectionsByMarkerId", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markerSections = ORMapper.GetMarkerSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markerSections;
        }

        public List<MarkerSection> SaveMarkerSections(int inspectionTemplateId, List<MarkerSection> markerSections)
        {
            bool FirstSection = true;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.MARKER_UPDATE_MARKER_SECTIONS;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    foreach (var markerSection in markerSections)
                    {
                        cmd.Parameters.AddWithValue("@InspectionTemplateId", markerSection.InspectionTemplateId);
                        cmd.Parameters.AddWithValue("@SectionId", markerSection.SectionId);
                        cmd.Parameters.AddWithValue("@MarkerId", markerSection.MarkerId);
                        if (FirstSection)
                        {
                            cmd.Parameters.AddWithValue("@FirstSection", FirstSection);
                            FirstSection = false;
                        }
                        //cmd.Parameters.Add("@Id", SqlDbType.Int).Direction = ParameterDirection.Output;

                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inquire, "SaveMarkerSections", _tenantId));
                        //markerSection.Id = (int)
                        cmd.ExecuteScalar();
                        cmd.Parameters.Clear();
                    }

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "SaveMarkerSections", _tenantId));
                    tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }

            return markerSections;
        }
        #endregion

        public bool CreateSectionAndMoveMarkers(int defaultSectionId, string sectionTitle, int inspectionTemplateId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_SECTION_CREATE_AND_MOVE_MARKER;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    cmd.Parameters.AddWithValue("@DefaultSectionId", defaultSectionId);
                    cmd.Parameters.AddWithValue("@SectionTitle", sectionTitle);
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "CreateSectionAndMoveMarkers", _tenantId));
                    var result = Convert.ToBoolean(cmd.ExecuteScalar());
                    cmd.Parameters.Clear();

                    tran.Commit();
                    return result;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<Marker> GetDefaultSectionMarkers(int defaultSectionId)
        {
            List<Marker> markers = new List<Marker>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.MARKER_GETBY_DEFAULTSECTION;
                    cmd.Parameters.AddWithValue("@DefaultSectionId", defaultSectionId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "GetDefaultSectionMarkers", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markers = ORMapper.MapMarkers(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markers;
        }

        public int SaveGraphicMarkerSection(Section section, int graphicMarkerId)
        {
            int sectionId = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.SAVE_GRAPHIC_MARKER_SECTION;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Title", section.Title);
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", section.InspectionTemplateId);
                    cmd.Parameters.AddWithValue("@IsDefaultSection", section.IsDefaultSection);
                    cmd.Parameters.AddWithValue("@SectionType", (int)section.SectionType);
                    cmd.Parameters.AddWithValue("@GraphicMarkerId", graphicMarkerId);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "SaveGraphicMarkerSection", _tenantId));

                    sectionId = Convert.ToInt32(cmd.ExecuteScalar());
                    tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return sectionId;
        }

        public GraphicMarkerDetail GetGraphicMarkerDetailsById(int graphicMarkerId)
        {
            GraphicMarkerDetail graphicMarker = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.GRAPHIC_MARKER_GET_BY_ID;
                    cmd.Parameters.AddWithValue("@GraphicMarkerId", graphicMarkerId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "GetGraphicMarkerDetailsById", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        graphicMarker = ORMapper.GetGraphicMarker(dr);
                    }
                }

                if (graphicMarker != null)
                {
                    graphicMarker.Sections = this.GetSectionsByGraphicMarkerId(graphicMarkerId);

                    if (graphicMarker.Sections != null)
                    {
                        foreach (var section in graphicMarker.Sections)
                        {
                            section.Markers = this.GetAllMarkers(section.Id);

                            if (section.Markers != null)
                            {
                                foreach (var marker in section.Markers)
                                {
                                    marker.MarkerOptions = this.GetAllMarkerOptions(Convert.ToInt32(marker.Id));
                                }
                            }
                        }
                    }
                }

            }
            catch (Exception ex) { throw ex; }
            return graphicMarker;
        }

        private List<Section> GetSectionsByGraphicMarkerId(int graphicMarkerId)
        {
            List<Section> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM iq3Section WHERE IsDeleted = 0 AND SectionType = 2 AND Id IN ( SELECT SectionId FROM iq3GraphicMarkerSection WHERE GraphicMarkerId = @GraphicMarkerId AND IsDeleted = 0 );";
                    cmd.Parameters.AddWithValue("@GraphicMarkerId", graphicMarkerId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "GetSectionsByGraphicMarkerId", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.GetAllSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        #region Graphic Marker Section
        public int DeleteGraphicMarkerDetailsSection(int sectionId)
        {
            int rowsaffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.GRAPHIC_MARKER_DETAILS_SECTION_DELETE;
                    cmd.Parameters.AddWithValue("@SectionId", sectionId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "DeleteGraphicMarkerDetailsSection", _tenantId));
                    rowsaffected = cmd.ExecuteNonQuery();
                    return rowsaffected;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public bool UpdateSectionName(int sectionId, string sectionTitle)
        {
            var returnValue = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE iq3Section SET Title = @Title WHERE Id = @Id";
                    cmd.Parameters.AddWithValue("@Id", sectionId);
                    cmd.Parameters.AddWithValue("@Title", sectionTitle);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "UpdateSectionName", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    returnValue = count > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return returnValue;
        }
        #endregion

        public bool UpdatePdfFileNameForGraphicMarker(string fileName, int graphicMarkerId)
        {
            var returnValue = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE iq3Marker SET Description = @FileName WHERE Id = @Id";
                    cmd.Parameters.AddWithValue("@FileName", fileName);
                    cmd.Parameters.AddWithValue("@Id", graphicMarkerId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "UpdatePdfFileNameForGraphicMarker", _tenantId));

                    int count = (int)cmd.ExecuteNonQuery();
                    returnValue = count > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return returnValue;
        }

        public List<IQ3InspectionParameter> GetIQ3InspectionParameters(int userId, IQ3InspectionParameterType parameterType, string StartDateTime, string EndDateTime)
        {
            List<IQ3InspectionParameter> iQ3InspectionParameters = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_GET_PARAMETERS;
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@ParameterType", (int)parameterType);
                    cmd.Parameters.AddWithValue("@StartDateTime", StartDateTime);
                    cmd.Parameters.AddWithValue("@EndDateTime", EndDateTime);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "GetIQ3InspectionParameters", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        iQ3InspectionParameters = ORMapper.GetAllInspectionParameters(dr);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return iQ3InspectionParameters;
        }

        public RVIMessage LoadRVIMessage()
        {
            RVIMessage rviMessage = new RVIMessage();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM iq3RVIMessage;";
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "LoadRVIMessage", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            rviMessage.RVISMS = Convert.ToString(dr["RVISMS"]);
                            rviMessage.ScheduledRVISMS = Convert.ToString(dr["ScheduledRVISMS"]);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return rviMessage;
        }

        public bool SaveRVIMessage(RVIMessage rviMessage)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandText = "UPDATE [dbo].[iq3RVIMessage] SET [RVISMS] = @RVISMS, [ScheduledRVISMS] = @ScheduledRVISMS; ";
                    cmd.CommandType = CommandType.Text;
                    cmd.Parameters.AddWithValue("@RVISMS", rviMessage.RVISMS);
                    cmd.Parameters.AddWithValue("@ScheduledRVISMS", rviMessage.ScheduledRVISMS);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "SaveRVIMessage", 0));
                    return Convert.ToInt32(cmd.ExecuteNonQuery()) > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }


        #region User - Custom Fields

        public List<PreInspectionGroup> SearchAllPreInspectionGroup(string SearchText, int UserNum, bool IncludePreInspections = true)
        {
            List<PreInspectionGroup> groupList = new List<PreInspectionGroup>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_GROUP_GET_SEARCH_ALL;

                    cmd.Parameters.AddWithValue("@SearchText", SearchText);
                    cmd.Parameters.AddWithValue("@UserNum", UserNum);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "SearchAllPreInspectionGroup", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            groupList = ORMapper.GetAllUserPreInspectionGroup(dr);
                        }
                    }
                }


                if (IncludePreInspections)
                {
                    try
                    {
                        foreach(var group in groupList)
                        {
                            group.UserPreInspections = GetAllUserPreInspectionsByGroupId(group.Id);
                        }
                    }
                    catch (Exception ex)
                    {
                        
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return groupList;
        }

        public List<PreInspectionGroup> GetAllPreInspectionGroup(int UserNum, bool IncludePreInspections = true)
        {
            List<PreInspectionGroup> groupList = new List<PreInspectionGroup>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_GROUP_GET_ALL;

                    //cmd.Parameters.AddWithValue("@UserNum", UserNum);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "GetAllPreInspectionGroup", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            groupList = ORMapper.GetAllUserPreInspectionGroup(dr);
                        }
                    }
                }


                if (IncludePreInspections)
                {
                    try
                    {
                        foreach(var group in groupList)
                        {
                            group.UserPreInspections = GetAllUserPreInspectionsByGroupId(group.Id);
                        }
                    }
                    catch (Exception ex)
                    {
                        
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return groupList;
        }

        private List<PreInspection> GetPreInspectionGroupsByTemplateId(int inspectionTemplateId, bool IncludePreInspections)
        {
            List<PreInspection> groupList = new List<PreInspection>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_INSPECTION_TEMPLATE_GET_USER_PREINSPECTION_GROUP;
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        groupList = ORMapper.GetAllUserPreInspectionGroupField(dr);
                    }
                }

                /*if (IncludePreInspections)
                {
                    try
                    {
                        foreach (var group in groupList)
                        {
                            group.UserPreInspections = GetAllUserPreInspectionsByGroupId(group.Id);
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                }*/
            }
            catch (Exception ex) { throw ex; }
            return groupList;
        }

        public bool AddPreInspectionGroup(PreInspectionGroup group)
        {
            bool isAdded = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_GROUP_ADD;
                    cmd.Parameters.AddWithValue("@UserNum", group.UserNum);
                    cmd.Parameters.AddWithValue("@Name", group.Name);
                    cmd.Parameters.AddWithValue("@Description", group.Description);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "AddPreInspectionGroup", _tenantId));
                    int customFieldId = Convert.ToInt32(cmd.ExecuteScalar());
                    isAdded = (customFieldId > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isAdded;
        }

        public bool UpdatePreInspectionGroup(PreInspectionGroup group)
        {
            bool isUpdated = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_GROUP_UPDATE;
                    cmd.Parameters.AddWithValue("@Id", group.Id);
                    cmd.Parameters.AddWithValue("@Name", group.Name);
                    cmd.Parameters.AddWithValue("@Description", group.Description);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "UpdatePreInspectionGroup", _tenantId));
                    int count = cmd.ExecuteNonQuery();
                    isUpdated = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isUpdated;
        }

        public bool DeletePreInspectionGroup(int GroupId)
        {
            bool isDeleted = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_GROUP_DELETE;
                    cmd.Parameters.AddWithValue("@GroupId", GroupId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "DeletePreInspectionGroup", _tenantId));
                    int count = cmd.ExecuteNonQuery();
                    isDeleted = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isDeleted;
        }

        public List<PreInspection> GetAllUserPreInspectionsByGroupId(int PreInspectionGroupId)
        {
            List<PreInspection> customFieldsList = new List<PreInspection>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_GET_ALL_BYGROUPID;
                    cmd.Parameters.AddWithValue("@PreInspectionGroupId", PreInspectionGroupId);

                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "GetAllPreInspectionsByGroup", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            customFieldsList = ORMapper.GetAllUserPreInspections(dr);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return customFieldsList;
        }

        public List<PreInspection> SearchAllCustomFields(string SearchText, int UserNum)
        {
            List<PreInspection> customFieldsList = new List<PreInspection>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_GET_SEARCH_ALL;
                    cmd.Parameters.AddWithValue("@SearchText", SearchText);
                    cmd.Parameters.AddWithValue("@UserNum", UserNum);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "SearchAllCustomFields", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            customFieldsList = ORMapper.GetAllUserPreInspections(dr);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return customFieldsList;
        }

        public List<PreInspection> GetAllCustomFields(int UserNum)
        {
            List<PreInspection> customFieldsList = new List<PreInspection>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_GET_ALL;
                    cmd.Parameters.AddWithValue("@UserNum", UserNum);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "GetAllCustomFields", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            customFieldsList = ORMapper.GetAllUserPreInspections(dr);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return customFieldsList;
        }

        public bool AddCustomField(PreInspection CustomField, int UserNum)
        {
            bool isAdded = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_ADD;
                    cmd.Parameters.AddWithValue("@UserNum", UserNum);
                    cmd.Parameters.AddWithValue("@Title", CustomField.Title);
                    cmd.Parameters.AddWithValue("@Description", CustomField.Description);
                    cmd.Parameters.AddWithValue("@GroupId", CustomField.GroupId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "AddCustomField", _tenantId));
                    int customFieldId = Convert.ToInt32(cmd.ExecuteScalar());
                    isAdded = (customFieldId > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isAdded;
        }

        public bool UpdateCustomField(PreInspection CustomField)
        {
            bool isUpdated = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_UPDATE;
                    cmd.Parameters.AddWithValue("@Id", CustomField.Id);
                    cmd.Parameters.AddWithValue("@Title", CustomField.Title);
                    cmd.Parameters.AddWithValue("@Description", CustomField.Description);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "UpdateCustomField", _tenantId));
                    int count = cmd.ExecuteNonQuery();
                    isUpdated = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isUpdated;
        }

        public bool DeleteCustomField(int CustomFieldId)
        {
            bool isDeleted = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_DELETE;
                    cmd.Parameters.AddWithValue("@CustomFieldId", CustomFieldId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "DeleteCustomField", _tenantId));
                    int count = cmd.ExecuteNonQuery();
                    isDeleted = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isDeleted;
        }

        public bool UnDeleteCustomField(string CustomFieldIds)
        {
            bool isDeleted = false;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserPreInspection.IQ3_USER_PREINSPECTION_UNDELETE;
                    cmd.Parameters.AddWithValue("@CustomFieldIds", CustomFieldIds);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CustomerDB, "UnDeleteCustomField", _tenantId));
                    int count = cmd.ExecuteNonQuery();
                    isDeleted = (count > 0) ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
            return isDeleted;
        }

        #endregion User - Custom Fields


        public List<AutoReportRecipient> FetchAutoReportRecipients(int createdBy)
        {
            List<AutoReportRecipient> autoReportRecipients = new List<AutoReportRecipient>();
            AutoReportRecipient autoReportRecipient = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM iq3AutoReportRecipient WHERE IsDeleted = 0;";
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Inspection, "LoadTitleDetails", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                autoReportRecipient = new AutoReportRecipient();
                                autoReportRecipient.Id = Convert.ToInt32(dr["Id"]);
                                autoReportRecipient.Name = Convert.ToString(dr["Name"]);
                                autoReportRecipient.EmailAddress = Convert.ToString(dr["EmailAddress"]);
                                autoReportRecipient.UserNum = Convert.ToInt32(dr["UserNum"]);
                                autoReportRecipient.IsActive = Convert.ToBoolean(dr["IsActive"]);
                                autoReportRecipient.CreatedBy = Convert.ToInt32(dr["CreatedBy"]);
                                autoReportRecipient.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                autoReportRecipient.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                                autoReportRecipients.Add(autoReportRecipient);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return autoReportRecipients;
        }

        public int UpdateAutoReportRecipientActiveStatus(int id, bool isActive)
        {
            try
            {
                int rowsAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.AutoReportRecipient.AUTOREPORTRECIPIENT_ACTIVE_STATUS_UPDATE;
                    cmd.Parameters.AddWithValue("@Id", id);
                    cmd.Parameters.AddWithValue("@IsActive", isActive);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.AutoReportRecipient, "UpdateAutoReportRecipientActiveStatus", _tenantId));
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public int SaveAutoReportRecipient(AutoReportRecipient autoReportRecipient)
        {
            int arrId = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.AutoReportRecipient.AUTOREPORTRECIPIENT_SAVE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Name", autoReportRecipient.Name);
                    cmd.Parameters.AddWithValue("@EmailAddress", autoReportRecipient.EmailAddress);
                    cmd.Parameters.AddWithValue("@UserNum", autoReportRecipient.UserNum);
                    cmd.Parameters.AddWithValue("@IsActive", autoReportRecipient.IsActive);
                    cmd.Parameters.AddWithValue("@CreatedBy", autoReportRecipient.CreatedBy);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InspectionTemplate, "SaveAutoReportRecipients", _tenantId));
                    arrId = Convert.ToInt32(cmd.ExecuteScalar());
                    autoReportRecipient.Id = arrId;
                    autoReportRecipient.CreatedDate = DateTime.Now;
                    tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return arrId;
        }

        public bool DeleteAutoReportRecipient(int arRecipientId)
        {
            try
            {
                var returnValue = false;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.AutoReportRecipient.AUTOREPORTRECIPIENT_DELETE;
                    cmd.Parameters.AddWithValue("@Id", arRecipientId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.AutoReportRecipient, "DeleteAutoReportRecipient", _tenantId));
                    returnValue = Convert.ToBoolean(cmd.ExecuteNonQuery());
                    tran.Commit();
                    return returnValue;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public List<MarkerLogic> GetMarkerLogics(int markerId)
        {
            List<MarkerLogic> markerLogics = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from iq3MarkerLogic where MarkerId = @MarkerId and IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@MarkerId", markerId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markerLogics = ORMapper.GetAllMarkerLogics(dr);
                    }
                }

                if (markerLogics != null)
                {
                    foreach (var markerLogic in markerLogics)
                    {
                        using (var conn = DALHelper.GetConnection(_tenantId))
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.CommandText = "select * from iq3LogicTrigger where LogicId = @LogicId and IsDeleted = 0;";
                            cmd.Parameters.AddWithValue("@LogicId", markerLogic.Id);
                            conn.Open();
                            using (SqlDataReader dr = cmd.ExecuteReader())
                            {
                                markerLogic.LogicTriggers = ORMapper.GetLogicTriggers(dr);
                            }
                        }

                        if (markerLogic.LogicTriggers != null && markerLogic.LogicTriggers.Count > 0)
                        {
                            foreach (var logicTrigger in markerLogic.LogicTriggers)
                            {
                                using (var conn = DALHelper.GetConnection(_tenantId))
                                using (var cmd = conn.CreateCommand())
                                {
                                    cmd.CommandType = CommandType.Text;
                                    cmd.CommandText = "select * from iq3TriggerAction where TriggerId = @TriggerId and IsDeleted = 0;";
                                    cmd.Parameters.AddWithValue("@TriggerId", logicTrigger.Id);
                                    conn.Open();
                                    using (SqlDataReader dr = cmd.ExecuteReader())
                                    {
                                        logicTrigger.TriggerAction = ORMapper.GetTriggerAction(dr);
                                    }
                                }
                            }

                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markerLogics;
        }
    }
}
