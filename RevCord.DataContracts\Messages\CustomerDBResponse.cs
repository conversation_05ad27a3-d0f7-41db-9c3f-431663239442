﻿using RevCord.DataContracts.CustomerDBEntities;
using RevCord.DataContracts.MessageBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class CustomerDBResponse : ResponseBase
    {
        public List<IQ3AssetModel> IQ3AssetsModel { get; set; }
        public List<Object> IQ3Assets { get; set; }
        public Object IQ3Asset { get; set; }
        
        public bool FlagStatus { get; set; }

        public List<IQ3AssetHistory> IQ3AssetHistory { get; set; }


        public Asset Asset { get; set; }
        public List<Asset> Assets { get; set; }

        public List<AssetDetail> AssetDetails { get; set; }
    }
}
