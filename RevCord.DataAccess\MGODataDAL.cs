﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.MGODataEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataAccess
{
    public class MGODataDAL
    {
        private int _tenantId;
        public MGODataDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        public MGOTempData GetMGOTempDataById(int Id)
        {
            MGOTempData data = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MGOData.MGO_TEMP_DATA_GET_BY_ID;
                    cmd.Parameters.AddWithValue("@Id", Id);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.MGOData, "GetMGOTempDataById", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            data = ORMapper.GetMGOTempData(dr);
                        }
                    }
                }

                return data;
            }
            catch (Exception ex) { throw ex; }
        }

        public int SaveMGOTempData(MGOTempData data)
        {
            int Id = 0;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MGOData.MGO_TEMP_DATA_ADD;
                    cmd.Parameters.AddWithValue("@WorkOrderID", data.WorkOrderID);
                    cmd.Parameters.AddWithValue("@JurisdictionID", data.JurisdictionID);
                    cmd.Parameters.AddWithValue("@Notes", data.Notes);
                    cmd.Parameters.AddWithValue("@ContractorName", data.ContractorName);
                    cmd.Parameters.AddWithValue("@ContractorEmail", data.ContractorEmail);
                    cmd.Parameters.AddWithValue("@ContractorPhoneNumber", data.ContractorPhoneNumber);
                    cmd.Parameters.AddWithValue("@PermitID", data.PermitID);
                    cmd.Parameters.AddWithValue("@Address", data.Address);
                    cmd.Parameters.AddWithValue("@ScheduledDate", data.ScheduledDate);
                    cmd.Parameters.AddWithValue("@Latitude", data.Latitude);
                    cmd.Parameters.AddWithValue("@Longitude", data.Longitude);

                    cmd.Parameters.AddWithValue("@ProjectTypeID", data.ProjectTypeID);
                    cmd.Parameters.AddWithValue("@InspectionTypeID", data.InspectionTypeID);


                    //cmd.Parameters.AddWithValue("@MGOInspectionName", data.MGOInspectionName);
                    //cmd.Parameters.AddWithValue("@MGOInspectionTypeID", data.MGOInspectionTypeID);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.MGOData, "SaveMGOTempData", _tenantId));

                    Id = Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }

            return Id;
        }

        public MGOReportData GetMGOReportDataByEventId(string EventId)
        {
            MGOReportData reportData = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MGOData.MGO_REPORT_DATA_GET_BY_EVENTID;
                    cmd.Parameters.AddWithValue("@EventId", EventId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.MGOData, "GetMGOReportDataByEventId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            reportData = ORMapper.GetMGOReportData(dr);
                        }
                    }
                }

                return reportData;
            }
            catch (Exception ex) { throw ex; }
        }

        public int SaveMGOReportData(MGOReportData data)
        {
            int Id = 0;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MGOData.MGO_REPORT_DATA_ADD;
                    cmd.Parameters.AddWithValue("@EventId", data.EventId);
                    cmd.Parameters.AddWithValue("@WorkOrderID", data.WorkOrderID);
                    cmd.Parameters.AddWithValue("@ProjectTypeID", data.ProjectTypeID);
                    cmd.Parameters.AddWithValue("@ContractorName", data.ContractorName);
                    cmd.Parameters.AddWithValue("@ContractorEmail", data.ContractorEmail);
                    cmd.Parameters.AddWithValue("@ContractorPhoneNumber", data.ContractorPhoneNumber);
                    cmd.Parameters.AddWithValue("@PermitID", data.PermitID);
                    cmd.Parameters.AddWithValue("@Address", data.Address);
                    cmd.Parameters.AddWithValue("@Latitude", data.Latitude);
                    cmd.Parameters.AddWithValue("@Longitude", data.Longitude);
                    cmd.Parameters.AddWithValue("@InspectionTypeID", data.InspectionTypeID);
                    cmd.Parameters.AddWithValue("@InspectionType", data.InspectionType);
                    cmd.Parameters.AddWithValue("@CategoryID", data.CategoryID);
                    cmd.Parameters.AddWithValue("@Category", data.Category);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.MGOData, "SaveMGOReportData", _tenantId));

                    Id = Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }

            return Id;
        }

        public int UpdateMGOEventStatus(MGOReportData data)
        {
            int AffectedRows = 0;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MGOData.MGO_STATUS_PASS_FAIL_UPDATE;
                    cmd.Parameters.AddWithValue("@EventId", data.EventId);
                    cmd.Parameters.AddWithValue("@InspectionOptionID", data.InspectionOptionID);
                    cmd.Parameters.AddWithValue("@InspectionOption", data.InspectionOption);
                    cmd.Parameters.AddWithValue("@InspectionOptionNotes", data.InspectionOptionNotes);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.MGOData, "UpdateMGOEventStatus", _tenantId));

                    AffectedRows = Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }

            return AffectedRows;
        }

        #region MGO - Inspection Type

        public List<MGOInspectionType> GetMGOInspectionTypes()
        {
            List<MGOInspectionType> inspectionTypes = new List<MGOInspectionType>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MGOData.MGO_INSPECTION_TYPES_GET;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.MGOData, "GetMGOInspectionTypes", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            inspectionTypes = ORMapper.GetMGOInspectionTypes(dr);
                        }
                    }
                }

                return inspectionTypes;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<MGOInspectionType> GetMGOInspectionTypesByProjectTypeID(int ProjectTypeID)
        {
            List<MGOInspectionType> inspectionTypes = new List<MGOInspectionType>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MGOData.MGO_INSPECTION_TYPES_GET_BY_PROJECT_TYPE_ID;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.MGOData, "GetMGOInspectionTypesByProjectTypeID", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            inspectionTypes = ORMapper.GetMGOInspectionTypes(dr);
                        }
                    }
                }

                return inspectionTypes;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<MGOInspectionOption> GetMGOInspectionOptions()
        {
            List<MGOInspectionOption> inspectionOptions = new List<MGOInspectionOption>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MGOData.MGO_INSPECTION_OPTIONS_GET;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.MGOData, "GetMGOInspectionOptions", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            inspectionOptions = ORMapper.GetMGOInspectionOptions(dr);
                        }
                    }
                }

                return inspectionOptions;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<MGOInspectionOption> GetMGOInspectionOptionsByProjectTypeID(int ProjectTypeID)
        {
            List<MGOInspectionOption> inspectionOptions = new List<MGOInspectionOption>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.MGOData.MGO_INSPECTION_OPTIONS_GET_BY_PROJECT_TYPE_ID;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.MGOData, "GetMGOInspectionOptionsByProjectTypeID", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            inspectionOptions = ORMapper.GetMGOInspectionOptions(dr);
                        }
                    }
                }

                return inspectionOptions;
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion MGO - Inspection Type
    }
}
