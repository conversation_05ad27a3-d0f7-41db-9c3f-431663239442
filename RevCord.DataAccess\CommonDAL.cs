﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.SqlClient;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.CommonEntities;
using System.Threading.Tasks;
using RevCord.Util;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;

namespace RevCord.DataAccess
{
    public class CommonDAL
    {
        private int _tenantId;

        public CommonDAL(int tenantId)
        {
            _tenantId = tenantId;
        }
        public List<string> GetFieldValues(string FieldName, string TableName, string SearchCriteria, string RoleId, string Criteria)
        {
            try
            {
                List<string> strList;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.SearchCriteria.GENERIC_GETAUTOCOMPLETE;
                    cmd.Parameters.AddWithValue("@Table_Name", TableName);
                    cmd.Parameters.AddWithValue("@Field_Name", FieldName);
                    cmd.Parameters.AddWithValue("@Search_Text", SearchCriteria);
                    cmd.Parameters.AddWithValue("@RoleId", RoleId);
                    cmd.Parameters.AddWithValue("@Criteria", Criteria);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "GetFieldValues", _tenantId));
                    strList = new List<string>();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read()) //SurveyTable
                        {
                            strList.Add(dr[0].ToString());
                        }
                    }
                    return strList;

                }
            }
            catch (Exception ex) { throw ex; }

        }


        public string GetLicenceInformation()
        {
            try
            {
                string strLicence = string.Empty;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.PRODUCT_LICENCE_INFORMATION;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "GetLicenceInformation", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            strLicence = dr["SerialKey"].ToString();
                        }
                    }
                    return strLicence;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public string GetLicenceInfoAndAdInfo(out int noOfDomainUsers)
        {
            noOfDomainUsers = 0;
            try
            {
                string strLicence = string.Empty;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.PRODUCT_LICENCE_INFORMATION;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "GetLicenceInfoAndAdInfo", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            strLicence = dr["SerialKey"].ToString();
                        }
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                noOfDomainUsers = Convert.ToInt32(dr["NoOfDomainUser"]);
                            }
                        }
                    }
                    return strLicence;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<IPCameraSetting> GetAllIPCameraSettings(int userNum) {
            List<IPCameraSetting> ipCameraSettings = new List<IPCameraSetting>();
            IPCameraSetting ipCameraSetting = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.GET_IPCAMINFO_GetAll;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "GetAllIPCameraSettings", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            ipCameraSetting = new IPCameraSetting();
                            ipCameraSetting.Id = Convert.ToInt32(dr["Id"]);
                            ipCameraSetting.CompanyName = Convert.ToString(dr["CompanyName"]);
                            ipCameraSetting.UserName = Convert.ToString(dr["UserName"]);
                            ipCameraSetting.Password = Convert.ToString(dr["Password"]);
                            ipCameraSetting.Url = Convert.ToString(dr["Url"]);
                            ipCameraSetting.IsEnable = Convert.ToBoolean(dr["IsEnable"]);
                            ipCameraSetting.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            ipCameraSetting.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                            ipCameraSettings.Add(ipCameraSetting);
                        }
                    }
                    return ipCameraSettings;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public IPCameraSetting GetIPCameraSettingsById(int id)
        {
            IPCameraSetting ipCameraSetting = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.GET_IPCAMINFO_BY_ID;
                    cmd.Parameters.AddWithValue("Id", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "GetIPCameraSettingsById", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            ipCameraSetting = new IPCameraSetting();
                            ipCameraSetting.Id = Convert.ToInt32(dr["Id"]);
                            ipCameraSetting.CompanyName = Convert.ToString(dr["CompanyName"]);
                            ipCameraSetting.UserName = Convert.ToString(dr["UserName"]);
                            ipCameraSetting.Password = Convert.ToString(dr["Password"]);
                            ipCameraSetting.Url = Convert.ToString(dr["Url"]);
                            ipCameraSetting.IsEnable = Convert.ToBoolean(dr["IsEnable"]);
                            ipCameraSetting.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                            ipCameraSetting.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                        }
                    }
                    return ipCameraSetting;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public int SaveIPCameraSettings(int userNum, string companyName, string userName, string password, string url)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.ADD_IPCAMERA_SETTING;
                    cmd.Parameters.AddWithValue("@CompanyName", companyName);
                    cmd.Parameters.AddWithValue("@UserName", userName);
                    cmd.Parameters.AddWithValue("@Password", password);
                    cmd.Parameters.AddWithValue("@Url", url);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "SaveIPCameraSettings", _tenantId));
                    conn.Open();
                    rowsAffected =  Convert.ToInt32(cmd.ExecuteScalar());
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateIPCameraSettings(int camSettingsId, string companyName, string userName, string password, string url)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.UPDATE_IPCAMERA_SETTING;
                    cmd.Parameters.AddWithValue("@Id", camSettingsId);
                    cmd.Parameters.AddWithValue("@CompanyName", companyName);
                    cmd.Parameters.AddWithValue("@UserName", userName);
                    cmd.Parameters.AddWithValue("@Password", password);
                    cmd.Parameters.AddWithValue("@Url", url);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "UpdateIPCameraSettings", _tenantId));
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public int DeleteIPCameraSettings(int camSettingsId)
        {
            try
            {
                int rowsAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DELETE_IPCAMERA_SETTING;
                    cmd.Parameters.AddWithValue("@Id", camSettingsId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "DeleteIPCameraSettings", _tenantId));
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public int UpdateIPCameraStatus(int camSettingsId, bool bEnable)
        {
            try
            {
                int rowsAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.UPDATE_IPCAMERA_STATUS;
                    cmd.Parameters.AddWithValue("@Id", camSettingsId);
                    cmd.Parameters.AddWithValue("@IsEnable", bEnable);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "UpdateIPCameraStatus", _tenantId));
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }
        public List<IPCamUserAssociation> GetUsersAssociatedWithIpCam(int camSettingsId)
        {
            try
            {
                List<IPCamUserAssociation> _lIpcamUserAssociated = new List<IPCamUserAssociation>();
                List<int> _IpcamAssociatedUsernum = new List<int>();
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.GET_IPCamAssociated_GetAllUser;
                    cmd.Parameters.AddWithValue("@Id", camSettingsId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "GetUsersandIpCamAssociated", _tenantId));
                    //rowsAffected = cmd.ExecuteNonQuery();

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                            _IpcamAssociatedUsernum.Add(Convert.ToInt32(dr["UserNum"]));

                        dr.NextResult();
                        while (dr.Read())
                        {
                            IPCamUserAssociation _IPCamUserAssociation = new IPCamUserAssociation();
                            _IPCamUserAssociation.UserNum = Convert.ToInt32(dr["UserNum"]);
                            _IPCamUserAssociation.Ext = Convert.ToInt32(dr["Ext"]);
                            _IPCamUserAssociation.UserName = Convert.ToString(dr["UserName"]);
                            _IPCamUserAssociation.IsIpCamAssociated = _IpcamAssociatedUsernum.Contains(_IPCamUserAssociation.UserNum) ? true : false;

                            _lIpcamUserAssociated.Add(_IPCamUserAssociation);
                        }
                    }
                }
                return _lIpcamUserAssociated;
            }
            catch (Exception ex) { throw ex; }
        }
        public int saveUsersAssociatedWithIpCam(int camSettingsId, string UserAssociated)
        {
            try
            {
                int rowsAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.GET_IPCamAssociated_SaveUser;
                    cmd.Parameters.AddWithValue("@Id", camSettingsId);
                    cmd.Parameters.AddWithValue("@UserAssociated", UserAssociated);
                    
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "GetUsersandIpCamAssociated", _tenantId));
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        public RevcordConfiguration FetchConfiguration()
        {
            RevcordConfiguration revcordConfiguration = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT TOP 1 * FROM mtConfiguration;";
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "FetchConfiguration", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            revcordConfiguration = new RevcordConfiguration();
                            revcordConfiguration.Id = Convert.ToInt32(dr["Id"]);
                            revcordConfiguration.NextGenRecorder = Convert.ToBoolean(dr["NextGenRecorder"]);
                        }
                    }
                    return revcordConfiguration;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool UpdateConfiguration(bool nextGenRecorder)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE mtConfiguration SET NextGenRecorder = @NextGenRecorder";
                    cmd.Parameters.AddWithValue("@NextGenRecorder", nextGenRecorder);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Common, "UpdateConfiguration", _tenantId));
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected > 0 ? true : false;
            }
            catch (Exception ex) { throw ex; }
        }
    }
}
