﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IWBEntities
{
    public class IwbMTR
    {
        public int Id { get; set; }
        public string CompanyName { get; set; }
        public string CertificateName { get; set; }
        public string OrderNo { get; set; }
        public string HeatPieceNo { get; set; }
        public DateTime? Date { get; set; }
        public string AddressSoldTo { get; set; }
        public string AddressShipTo { get; set; }
        public string ComplianceStatus { get; set; }
        public string Remarks { get; set; }
        public string ASME { get; set; }
        public string ASTM { get; set; }
        public string Uns { get; set; }
        public string Grade { get; set; }

        public List<IwbMTRSection> MtrSections { get; set; }
    }
    public class IwbMTRSection
    {
        public int Id { get; set; }
        public int MtrId { get; set; }
        public string Name { get; set; }
        public List<IwbMTRSectionField> Elements { get; set; }
    }
    public class IwbMTRSectionField
    {
        public int Id { get; set; }
        public int MtrId { get; set; }
        public int MtrSectionId { get; set; }
        public string Name { get; set; }
        public string Symbol { get; set; }
        public string Input { get; set; }
        public string Standard { get; set; }
        public string Difference { get; set; }
        public string Status { get; set; }
        public string Reason { get; set; }
    }
}