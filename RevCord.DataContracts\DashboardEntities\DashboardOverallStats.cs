﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.DataContracts.DashboardEntities
{
    public class DashboardOverallStats
    {
        public List<CallSummary> CallSummary { get; set; }

        public List<Tuple<int,int>> SevenDayCallCount { get; set; }
        
        public int TotalChannels { get; set; }

        public int TotalCalls { get; set; }

        public int AudioCount { get; set; }

        public int ScreenCount { get; set; }
        public int Text911Count { get; set; }

        public int TotalEvents { get; set; }

        public int IQ3EventsCount { get; set; }

        public int PictureEventsCount { get; set; }

        public int VirtualInspectionEventsCount { get; set; }

        public int TotalSurveyForms { get; set; }

        public int PublishedSurveyCount { get; set; }

        public int UnPublishedSurveyCount { get; set; }

        public int SharedEvals { get; set; }

        public int UnsharedEvals { get; set; }

        public int TotalActiveUsers { get; set; }

        public int TotalDeletedUsers { get; set; }

        public int AdminUserCount { get; set; }

        public int AdditionalUserCount { get; set; }

        public int SimpleUserCount { get; set; }

        public int TotalIQ3Users { get; set; }
        public int IQ3ActiveUsers { get; set; }
        public int IQ3ActiveViewers { get; set; }

        public List<Playlist> Playlists { get; set; }

        public List<CallInfoLite> CallInfoLite { get; set; }

        #region Reload Dashboard Data
        public List<int> EvalStatus { get; set; }
        public List<CallSummaryLite> CallSummaryLite { get; set; }
        #endregion

    }

    public class CallSummary
    {
        public int Date { get; set; }
        public int Ext { get; set; }
        public string ExtName { get; set; }
        public int CallDate { get; set; }
        public int NoOfCalls { get; set; }
        public Int64 TotalDuration { get; set; }
    }
    public class CallSummaryLite {
        public string ChannelName { get; set; }
        public List<int> DaywiseCallData { get; set; }
    }
}
