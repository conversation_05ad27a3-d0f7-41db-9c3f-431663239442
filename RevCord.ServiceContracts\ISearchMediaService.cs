﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.DTO;

namespace RevCord.ServiceContracts
{
    public interface ISearchMediaService
    {
        Task<DALMediaResponse> PerformDefaultSearch(VRRequest request);
        Task<DALMediaResponse> PerformDefaultSearchByEventId(VRRequest request);

        Task<DALMediaResponse> PerformAdvanceSearch(VRRequest request);


        Task<DALMediaResponse> PerformQADefaultSearch(VRRequest request);

        Task<DALMediaResponse> PerformQAAdvanceSearch(VRRequest request);

        Task<DALMediaResponse> PerformDefaultSearchEC(VRRequest request);
        Task<DALMediaResponse> PerformAdvanceSearchEC(VRRequest request);

    }
}
