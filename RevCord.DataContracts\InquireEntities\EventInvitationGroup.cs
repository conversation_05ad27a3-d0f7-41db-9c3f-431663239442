﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.InquireEntities
{

    public class EventInvitationGroup
    {
        #region Properties

        public int Id { get; set; }
        public string Name { get; set; }
        public string Comments { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
        public DateTime DateExpired { get; set; }
        public DateTime DateAccepted { get; set; }
        public string SentToEmail { get; set; }
        public string AccessCode { get; set; }
        public int NoOfEvents
        {
            get
            {
                return EventDetails.Count;
            }
        }


        #endregion

        #region Associations

        public int UserId { get; set; }
        public InvitationStatus Status { get; set; }

        public List<EventInvitationDetail> EventDetails { get; set; }

        public List<EventInvitee> Invitees{ get; set; }

        #endregion


    }
}
