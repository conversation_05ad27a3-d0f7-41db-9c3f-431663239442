﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.ByteArrayContent">
      <summary>根據位元組陣列提供 HTTP 內容。</summary>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[])">
      <summary>初始化 <see cref="T:System.Net.Http.ByteArrayContent" /> 類別的新執行個體。</summary>
      <param name="content">用來初始化 <see cref="T:System.Net.Http.ByteArrayContent" /> 的內容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> 參數為 null。</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>初始化 <see cref="T:System.Net.Http.ByteArrayContent" /> 類別的新執行個體。</summary>
      <param name="content">用來初始化 <see cref="T:System.Net.Http.ByteArrayContent" /> 的內容。</param>
      <param name="offset">
        <paramref name="content" /> 參數中用來初始化 <see cref="T:System.Net.Http.ByteArrayContent" /> 的位移 (以位元組為單位)。</param>
      <param name="count">
        <paramref name="content" />中的位元組數目（從用來初始化<see cref="T:System.Net.Http.ByteArrayContent" /> 的<paramref name="offset" />參數開始）。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> 參數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 參數小於零。-或-<paramref name="offset" /> 參數大於 <paramref name="content" /> 參數指定的內容長度。-或-<paramref name="count " /> 參數小於零。-或-<paramref name="count" /> 參數大於 <paramref name="content" /> 參數指定的內容長度，減去 <paramref name="offset" /> 參數。</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.CreateContentReadStreamAsync">
      <summary>建立 HTTP 內容資料流做為非同步讀取作業，其支援存放區是在 <see cref="T:System.Net.Http.ByteArrayContent" /> 中的記憶體</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>進行序列化，並以非同步方式將建構函式中提供的位元組陣列寫入到 HTTP 內容資料流。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task" />。工作物件，表示非同步作業。</returns>
      <param name="stream">目標資料流。</param>
      <param name="context">傳輸的相關資訊，如通道繫結語彙基元。這個參數可以是 null。</param>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.TryComputeLength(System.Int64@)">
      <summary>判斷位元組陣列的長度 (以位元組為單位) 是否有效。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="length" /> 是有效長度則為 true，否則為 false。</returns>
      <param name="length">位元組陣列的長度，以位元組為單位。</param>
    </member>
    <member name="T:System.Net.Http.ClientCertificateOption">
      <summary>指定如何提供用戶端憑證。</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Automatic">
      <summary>
        <see cref="T:System.Net.Http.HttpClientHandler" /> 會嘗試自動提供所有可用的用戶端憑證。</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Manual">
      <summary>應用程式會以手動方式提供用戶端憑證給 <see cref="T:System.Net.Http.WebRequestHandler" />。這個值為預設值。</summary>
    </member>
    <member name="T:System.Net.Http.DelegatingHandler">
      <summary>HTTP 處理常式的類型，這些處理常式會將 HTTP 回應訊息的處理委派給另一個處理常式，也稱為內部處理常式。</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor">
      <summary>建立 <see cref="T:System.Net.Http.DelegatingHandler" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>使用特定的內部處理常式，建立 <see cref="T:System.Net.Http.DelegatingHandler" /> 類別的新執行個體。</summary>
      <param name="innerHandler">負責處理 HTTP 回應訊息的內部處理常式。</param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Net.Http.DelegatingHandler" /> 所使用的 Unmanaged 資源，並選擇性處置 Managed 資源。</summary>
      <param name="disposing">true，表示釋放 Managed 和 Unmanaged 資源；false，表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="P:System.Net.Http.DelegatingHandler.InnerHandler">
      <summary>取得或設定處理 HTTP 回應訊息的內部處理常式。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpMessageHandler" />。HTTP 回應訊息的內部處理常式。</returns>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>將 HTTP 要求傳送到內部處理常式，以非同步作業方式傳送到伺服器。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="request">要傳送到伺服器的 HTTP 要求訊息。</param>
      <param name="cancellationToken">要取消作業的取消語彙基元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> 為 null。</exception>
    </member>
    <member name="T:System.Net.Http.FormUrlEncodedContent">
      <summary>使用 application/x-www-form-urlencoded MIME 類型編碼之名稱/值 Tuple 的容器。</summary>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>使用特定名稱/值組集合，初始化 <see cref="T:System.Net.Http.FormUrlEncodedContent" /> 類別的新執行個體。</summary>
      <param name="nameValueCollection">名稱/值組的集合。</param>
    </member>
    <member name="T:System.Net.Http.HttpClient">
      <summary>提供基底類別，用來傳送 HTTP 要求，以及從 URI 所識別的資源接收 HTTP 回應。</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor">
      <summary>初始化 <see cref="T:System.Net.Http.HttpClient" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>使用特定處理常式，初始化 <see cref="T:System.Net.Http.HttpClient" /> 類別的新執行個體。</summary>
      <param name="handler">要用來傳送要求的 HTTP 處理常式堆疊。</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>使用特定處理常式，初始化 <see cref="T:System.Net.Http.HttpClient" /> 類別的新執行個體。</summary>
      <param name="handler">負責處理 HTTP 回應訊息的 <see cref="T:System.Net.Http.HttpMessageHandler" />。</param>
      <param name="disposeHandler">如果內部處理常式應由 Dispose() 處置則為 true，如果您想要重複使用內部處理常式則為 false。</param>
    </member>
    <member name="P:System.Net.Http.HttpClient.BaseAddress">
      <summary>取得或設定傳送要求時所使用之網際網路資源的統一資源識別元 (URI) 基底位址。</summary>
      <returns>傳回 <see cref="T:System.Uri" />。傳送要求時所使用之網際網路資源的統一資源識別元 (URI) 基底位址。</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.CancelPendingRequests">
      <summary>取消這個執行個體上的所有暫止要求。</summary>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultRequestHeaders">
      <summary>取得應該在每個要求中傳送的標頭。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />。應該藉由每個要求傳送的標頭。</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String)">
      <summary>以非同步作業的方式，將 DELETE 要求傳送至指定的 URI。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">要求訊息已由<see cref="T:System.Net.Http.HttpClient" />執行個體傳送。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 DELETE 要求和取消權杖至指定的 Uri。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒用來接收取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">要求訊息已由<see cref="T:System.Net.Http.HttpClient" />執行個體傳送。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri)">
      <summary>以非同步作業的方式，將 DELETE 要求傳送至指定的 URI。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">要求訊息已由<see cref="T:System.Net.Http.HttpClient" />執行個體傳送。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 DELETE 要求和取消權杖至指定的 Uri。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒用來接收取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">要求訊息已由<see cref="T:System.Net.Http.HttpClient" />執行個體傳送。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Net.Http.HttpClient" /> 所使用的 Unmanaged 資源，並選擇性處置 Managed 資源。</summary>
      <param name="disposing">true 表示會同時釋放 Managed 和 Unmanaged 資源；false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String)">
      <summary>以非同步作業的方式，將 GET 要求傳送至指定的 URI。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption)">
      <summary>以非同步作業的方式，傳送 GET 要求和 HTTP 完成選項至指定的 Uri。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="completionOption">HTTP 完成選項值，表示作業應該被視為已完成。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 GET 要求和 HTTP 完成選項以及取消語彙基元至指定的 Uri。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="completionOption">HTTP 完成選項值，表示作業應該被視為已完成。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒用來接收取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 GET 要求和取消權杖至指定的 Uri。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒用來接收取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri)">
      <summary>以非同步作業的方式，將 GET 要求傳送至指定的 URI。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption)">
      <summary>以非同步作業的方式，傳送 GET 要求和 HTTP 完成選項至指定的 Uri。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="completionOption">HTTP 完成選項值，表示作業應該被視為已完成。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 GET 要求和 HTTP 完成選項以及取消語彙基元至指定的 Uri。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="completionOption">HTTP 完成選項值，表示作業應該被視為已完成。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒用來接收取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 GET 要求和取消權杖至指定的 Uri。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒用來接收取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.String)">
      <summary>將 GET 要求傳送至指定的 URI，並透過非同步作業，以位元組陣列形式傳回回應內容。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.Uri)">
      <summary>將 GET 要求傳送至指定的 URI，並透過非同步作業，以位元組陣列形式傳回回應內容。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.String)">
      <summary>將 GET 要求傳送至指定的 URI，並透過非同步作業，以資料流形式傳回回應內容。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.Uri)">
      <summary>將 GET 要求傳送至指定的 URI，並透過非同步作業，以資料流形式傳回回應內容。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.String)">
      <summary>將 GET 要求傳送至指定的 URI，並透過非同步作業，以字串形式傳回回應內容。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.Uri)">
      <summary>將 GET 要求傳送至指定的 URI，並透過非同步作業，以字串形式傳回回應內容。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.MaxResponseContentBufferSize">
      <summary>取得或設定讀取回應內容時要緩衝處理的位元組數目上限。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。讀取回應內容時要緩衝處理的位元組數目上限。此屬性的預設值是 2 GB。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">指定的大小小於或等於零。</exception>
      <exception cref="T:System.InvalidOperationException">作業已經在目前的執行個體上啟動。</exception>
      <exception cref="T:System.ObjectDisposedException">已經處置目前的執行個體。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent)">
      <summary>以非同步作業的方式，將 POST 要求傳送至指定的 URI。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="content">傳送至伺服器的 HTTP 要求內容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 POST 要求和取消語彙基元。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="content">傳送至伺服器的 HTTP 要求內容。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒用來接收取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>以非同步作業的方式，將 POST 要求傳送至指定的 URI。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="content">傳送至伺服器的 HTTP 要求內容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 POST 要求和取消語彙基元。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="content">傳送至伺服器的 HTTP 要求內容。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒用來接收取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent)">
      <summary>以非同步作業的方式，將 PUT 要求傳送至指定的 URI。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="content">傳送至伺服器的 HTTP 要求內容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 PUT 要求和取消語彙基元。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="content">傳送至伺服器的 HTTP 要求內容。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒用來接收取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>以非同步作業的方式，將 PUT 要求傳送至指定的 URI。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="content">傳送至伺服器的 HTTP 要求內容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 PUT 要求和取消語彙基元。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="requestUri">傳送要求的目標 URI。</param>
      <param name="content">傳送至伺服器的 HTTP 要求內容。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒用來接收取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage)">
      <summary>以非同步作業的方式，傳送 HTTP 要求。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="request">要傳送的 HTTP 要求訊息。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">要求訊息已由<see cref="T:System.Net.Http.HttpClient" />執行個體傳送。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption)">
      <summary>以非同步作業的方式，傳送 HTTP 要求。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="request">要傳送的 HTTP 要求訊息。</param>
      <param name="completionOption">當作業應該完成時 (可取得回應時或讀取整個回應內容之後)。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">要求訊息已由<see cref="T:System.Net.Http.HttpClient" />執行個體傳送。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 HTTP 要求。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="request">要傳送的 HTTP 要求訊息。</param>
      <param name="completionOption">當作業應該完成時 (可取得回應時或讀取整個回應內容之後)。</param>
      <param name="cancellationToken">用於取消作業的取消語彙基元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">要求訊息已由<see cref="T:System.Net.Http.HttpClient" />執行個體傳送。</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 HTTP 要求。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="request">要傳送的 HTTP 要求訊息。</param>
      <param name="cancellationToken">用於取消作業的取消語彙基元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">要求訊息已由<see cref="T:System.Net.Http.HttpClient" />執行個體傳送。</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.Timeout">
      <summary>取得或設定要求逾時前等候的時間長度。</summary>
      <returns>傳回 <see cref="T:System.TimeSpan" />。要求逾時前等候的時間長度。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">指定的逾時小於或等於零，並且不是 <see cref="F:System.Threading.Timeout.InfiniteTimeSpan" />。</exception>
      <exception cref="T:System.InvalidOperationException">作業已經在目前的執行個體上啟動。</exception>
      <exception cref="T:System.ObjectDisposedException">已經處置目前的執行個體。</exception>
    </member>
    <member name="T:System.Net.Http.HttpClientHandler">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" /> 所使用的預設訊息處理常式。</summary>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.#ctor">
      <summary>建立 <see cref="T:System.Net.Http.HttpClientHandler" /> 類別的執行個體。</summary>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect">
      <summary>取得或設定值，指出處理常式是否應該緊接在重新導向回應之後。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果處理常式應該接在重新導向回應之後，則為 true，否則為 false。預設值是 true。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AutomaticDecompression">
      <summary>取得或設定處理常式用來自動解壓縮 HTTP 內容回應的解壓縮方法型別。</summary>
      <returns>傳回 <see cref="T:System.Net.DecompressionMethods" />。處理常式所使用的自動解壓縮方法。預設值是 <see cref="F:System.Net.DecompressionMethods.None" />。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.ClientCertificateOptions">
      <summary>取得或設定與這個處理常式相關聯的安全憑證集合。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.ClientCertificateOption" />。與這個處理常式相關聯的安全憑證的集合。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.CookieContainer">
      <summary>取得或設定處理常式用來儲存伺服器 cookie 的 cookie 容器。</summary>
      <returns>傳回 <see cref="T:System.Net.CookieContainer" />。處理常式用來儲存伺服器 cookie 的 cookie 容器。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Credentials">
      <summary>取得或設定這個處理常式所使用的驗證資訊。</summary>
      <returns>傳回 <see cref="T:System.Net.ICredentials" />。與處理常式相關聯的驗證認證。預設值為 null。</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Net.Http.HttpClientHandler" /> 所使用的 Unmanaged 資源，並選擇性處置 Managed 資源。</summary>
      <param name="disposing">true，表示釋放 Managed 和 Unmanaged 資源；false，表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections">
      <summary>取得或設定處理常式追蹤的最大重新導向數目。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。處理常式追蹤的最大重新導向回應數目。預設值為 50。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxRequestContentBufferSize">
      <summary>取得或設定處理常式所使用的要求內容緩衝區大小上限。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。要求內容緩衝區的大小上限，以位元組為單位。預設值為 2 GB。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.PreAuthenticate">
      <summary>取得或設定值，指出處理常式是否隨要求傳送授權標頭。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。true 表示在進行驗證後，處理常式隨同要求傳送 HTTP 驗證標頭，否則為 false。預設值為 false。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Proxy">
      <summary>取得或設定處理常式使用的 Proxy 資訊。</summary>
      <returns>傳回 <see cref="T:System.Net.IWebProxy" />。處理常式所使用的 proxy 資訊。預設值是 null。</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>根據 <see cref="T:System.Net.Http.HttpRequestMessage" /> 中提供的資訊，建立 <see cref="T:System.Net.Http.HttpResponseMessage" /> 的執行個體成為不會封鎖的作業。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="request">HTTP 要求訊息。</param>
      <param name="cancellationToken">要取消作業的取消語彙基元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> 為 null。</exception>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsAutomaticDecompression">
      <summary>取得值，指出處理常式是否支援自動回應內容解壓縮。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果處理常式支援自動回應內容解壓縮，則為 true，否則為 false。預設值是 true。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsProxy">
      <summary>取得值，指出處理常式是否支援 Proxy 設定。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果處理常式支援 Proxy 設定，則為 true，否則為 false。預設值是 true。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsRedirectConfiguration">
      <summary>取得值，這個值指出處理常式是否支援 <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> 和 <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> 屬性的組態設定。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果處理常式支援 <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> 和 <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" /> 屬性的組態設定，則為 true，否則為 false。預設值是 true。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseCookies">
      <summary>取得或設定值，指出處理常式是否使用 <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> 屬性，以儲存伺服器 cookie，並在傳送要求時使用這些 cookie。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果處理常式支援使用 <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> 屬性儲存伺服器 cookie，並在傳送要求時使用這些 cookie，則為 true，否則為 false。預設值是 true。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseDefaultCredentials">
      <summary>取得或設定值，該值會控制是否隨著處理常式的要求傳送預設認證。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果使用預設認證則為 true，否則為 false。預設值是 false。</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseProxy">
      <summary>取得或設定值，指出處理常式是否對要求使用 Proxy。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果處理常式應該使用 proxy 來處理要求，則為 true，否則為 false。預設值是 true。</returns>
    </member>
    <member name="T:System.Net.Http.HttpCompletionOption">
      <summary>表示 <see cref="T:System.Net.Http.HttpClient" /> 作業應該在取得回應之後立即視為已完成，或在讀取整個回應訊息 (包括內容) 之後視為已完成。</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseContentRead">
      <summary>在讀取包括內容的完整回應之後，操作應該完成。</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseHeadersRead">
      <summary>一旦回應可使用而標頭也已讀取後，就應完成作業。尚未讀取內容。</summary>
    </member>
    <member name="T:System.Net.Http.HttpContent">
      <summary>基底類別，表示 HTTP 實體內容與內容標題。</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.#ctor">
      <summary>初始化 <see cref="T:System.Net.Http.HttpContent" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream)">
      <summary>將 HTTP 內容序列化成位元組資料流，然後複製到 <paramref name="stream" /> 參數所提供的資料流物件。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task" />。工作物件，表示非同步作業。</returns>
      <param name="stream">目標資料流。</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>將 HTTP 內容序列化成位元組資料流，然後複製到 <paramref name="stream" /> 參數所提供的資料流物件。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task" />。工作物件，表示非同步作業。</returns>
      <param name="stream">目標資料流。</param>
      <param name="context">傳輸的相關資訊 (例如通道繫結語彙基元)。這個參數可以是 null。</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CreateContentReadStreamAsync">
      <summary>以非同步作業方式將 HTTP 內容序列化至記憶體資料流。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose">
      <summary>釋放 Unmanaged 資源，並處置 <see cref="T:System.Net.Http.HttpContent" /> 所使用的 Managed 資源。</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Net.Http.HttpContent" /> 所使用的 Unmanaged 資源，並選擇性處置 Managed 資源。</summary>
      <param name="disposing">true，表示釋放 Managed 和 Unmanaged 資源；false，表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="P:System.Net.Http.HttpContent.Headers">
      <summary>取得 HTTP 內容標頭，如 RFC 2616 中所定義。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpContentHeaders" />。RFC 2616 中所定義的標頭。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync">
      <summary>以非同步作業方式將 HTTP 內容序列化至記憶體緩衝區。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task" />。工作物件，表示非同步作業。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync(System.Int64)">
      <summary>以非同步作業方式將 HTTP 內容序列化至記憶體緩衝區。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task" />。工作物件，表示非同步作業。</returns>
      <param name="maxBufferSize">要使用的緩衝區的大小上限，以位元組為單位。</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsByteArrayAsync">
      <summary>以非同步作業的方式將 HTTP 內容寫入至位元組陣列。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStreamAsync">
      <summary>將 HTTP 內容序列化，並以非同步作業的方式傳回表示內容的資料流。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStringAsync">
      <summary>以非同步作業方式將 HTTP 內容序列化至字串。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>以非同步作業方式將 HTTP 內容序列化至資料流。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task" />。工作物件，表示非同步作業。</returns>
      <param name="stream">目標資料流。</param>
      <param name="context">傳輸的相關資訊 (例如通道繫結語彙基元)。這個參數可以是 null。</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.TryComputeLength(System.Int64@)">
      <summary>判斷 HTTP 內容的長度 (以位元組為單位) 是否有效。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="length" /> 是有效長度則為 true，否則為 false。</returns>
      <param name="length">HTTP 內容的長度，以位元組為單位。</param>
    </member>
    <member name="T:System.Net.Http.HttpMessageHandler">
      <summary>HTTP 訊息處理常式的基底型別。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.#ctor">
      <summary>初始化 <see cref="T:System.Net.Http.HttpMessageHandler" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose">
      <summary>釋放 Unmanaged 資源，並處置 <see cref="T:System.Net.Http.HttpMessageHandler" /> 所使用的 Managed 資源。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Net.Http.HttpMessageHandler" /> 所使用的 Unmanaged 資源，並選擇性處置 Managed 資源。</summary>
      <param name="disposing">true，表示釋放 Managed 和 Unmanaged 資源；false，表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 HTTP 要求。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="request">要傳送的 HTTP 要求訊息。</param>
      <param name="cancellationToken">用於取消作業的取消語彙基元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> 為 null。</exception>
    </member>
    <member name="T:System.Net.Http.HttpMessageInvoker">
      <summary>特定類別，允許應用程式呼叫 HTTP 處理常式鏈結的 <see cref="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)" /> 方法。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>使用特定 <see cref="T:System.Net.Http.HttpMessageHandler" />，初始化 <see cref="T:System.Net.Http.HttpMessageInvoker" /> 類別的執行個體。</summary>
      <param name="handler">負責處理 HTTP 回應訊息的 <see cref="T:System.Net.Http.HttpMessageHandler" />。</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>使用特定 <see cref="T:System.Net.Http.HttpMessageHandler" />，初始化 <see cref="T:System.Net.Http.HttpMessageInvoker" /> 類別的執行個體。</summary>
      <param name="handler">負責處理 HTTP 回應訊息的 <see cref="T:System.Net.Http.HttpMessageHandler" />。</param>
      <param name="disposeHandler">如果內部處理常式應由 Dispose() 處置則為 true，如果您想要重複使用內部處理常式則為 false。</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose">
      <summary>釋放 Unmanaged 資源，並處置 <see cref="T:System.Net.Http.HttpMessageInvoker" /> 所使用的 Managed 資源。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Net.Http.HttpMessageInvoker" /> 所使用的 Unmanaged 資源，並選擇性處置 Managed 資源。</summary>
      <param name="disposing">true，表示釋放 Managed 和 Unmanaged 資源；false，表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>以非同步作業的方式，傳送 HTTP 要求。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="request">要傳送的 HTTP 要求訊息。</param>
      <param name="cancellationToken">用於取消作業的取消語彙基元。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> 為 null。</exception>
    </member>
    <member name="T:System.Net.Http.HttpMethod">
      <summary>協助程式類別，用於擷取和比較標準 HTTP 方法，以及建立新的 HTTP 方法。</summary>
    </member>
    <member name="M:System.Net.Http.HttpMethod.#ctor(System.String)">
      <summary>使用特定 HTTP 方法，初始化 <see cref="T:System.Net.Http.HttpMethod" /> 類別的新執行個體。</summary>
      <param name="method">HTTP 方法。</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Delete">
      <summary>表示 HTTP DELETE 通訊協定方法。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpMethod" />。</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Net.Http.HttpMethod)">
      <summary>判斷指定的 <see cref="T:System.Net.Http.HttpMethod" /> 和目前的 <see cref="T:System.Object" /> 是否相等。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的物件等於目前的物件，則為 true，否則為 false。</returns>
      <param name="other">要與目前專案比較的 HTTP 方法。</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 和目前的 <see cref="T:System.Object" /> 是否相等。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的物件等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Get">
      <summary>表示 HTTP GET 通訊協定方法。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpMethod" />。</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.GetHashCode">
      <summary>做為此型別的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前 <see cref="T:System.Object" /> 的雜湊程式碼。</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Head">
      <summary>表示 HTTP HEAD 通訊協定方法。HEAD 方法與 GET 相同，除了伺服器只在回應中傳回訊息標頭，不含訊息主體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpMethod" />。</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Method">
      <summary>HTTP 方法。</summary>
      <returns>傳回 <see cref="T:System.String" />。以 <see cref="T:System.String" /> 形式表示的 HTTP 方法。</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Equality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>等號比較運算子，用來比較兩個 <see cref="T:System.Net.Http.HttpMethod" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <paramref name="left" /> 和 <paramref name="right" /> 參數相等，則為 true，否則為 false。</returns>
      <param name="left">等號比較運算子左邊的 <see cref="T:System.Net.Http.HttpMethod" />。</param>
      <param name="right">等號比較運算子右邊的 <see cref="T:System.Net.Http.HttpMethod" />。</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Inequality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>不等比較運算子，用來比較兩個 <see cref="T:System.Net.Http.HttpMethod" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <paramref name="left" /> 和 <paramref name="right" /> 參數不相等，則為 true，否則為 false。</returns>
      <param name="left">不等比較運算子左邊的 <see cref="T:System.Net.Http.HttpMethod" />。</param>
      <param name="right">不等比較運算子右邊的 <see cref="T:System.Net.Http.HttpMethod" />。</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Options">
      <summary>表示 HTTP OPTIONS 通訊協定方法。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpMethod" />。</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Post">
      <summary>表示 HTTP POST 通訊協定方法，用來公佈新實體至 URI 做為新增項目。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpMethod" />。</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Put">
      <summary>表示 HTTP PUT 通訊協定方法，用來取代 URI 所識別的實體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpMethod" />。</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.ToString">
      <summary>傳回表示目前物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Trace">
      <summary>表示 HTTP TRACE 通訊協定方法。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpMethod" />。</returns>
    </member>
    <member name="T:System.Net.Http.HttpRequestException">
      <summary>
        <see cref="T:System.Net.Http.HttpClient" /> 及 <see cref="T:System.Net.Http.HttpMessageHandler" /> 類別所擲回之例外狀況的基底類別。</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor">
      <summary>初始化 <see cref="T:System.Net.Http.HttpRequestException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String)">
      <summary>使用描述目前例外狀況的特定訊息，初始化 <see cref="T:System.Net.Http.HttpRequestException" /> 類別的新執行個體。</summary>
      <param name="message">描述目前例外狀況的訊息。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String,System.Exception)">
      <summary>使用描述目前例外狀況和內部例外狀況的特定訊息，初始化 <see cref="T:System.Net.Http.HttpRequestException" /> 類別的新執行個體。</summary>
      <param name="message">描述目前例外狀況的訊息。</param>
      <param name="inner">內部例外狀況。</param>
    </member>
    <member name="T:System.Net.Http.HttpRequestMessage">
      <summary>表示 HTTP 要求訊息。</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor">
      <summary>初始化 <see cref="T:System.Net.Http.HttpRequestMessage" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.String)">
      <summary>使用 HTTP 方法和要求 <see cref="T:System.Uri" />，初始化 <see cref="T:System.Net.Http.HttpRequestMessage" /> 類別的新執行個體。</summary>
      <param name="method">HTTP 方法。</param>
      <param name="requestUri">字串，表示要求 <see cref="T:System.Uri" />。</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.Uri)">
      <summary>使用 HTTP 方法和要求 <see cref="T:System.Uri" />，初始化 <see cref="T:System.Net.Http.HttpRequestMessage" /> 類別的新執行個體。</summary>
      <param name="method">HTTP 方法。</param>
      <param name="requestUri">要求的 <see cref="T:System.Uri" />。</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Content">
      <summary>取得或設定 HTTP 訊息的內容。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpContent" />。訊息內容。</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose">
      <summary>釋放 Unmanaged 資源，並處置 <see cref="T:System.Net.Http.HttpRequestMessage" /> 所使用的 Managed 資源。</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Net.Http.HttpRequestMessage" /> 所使用的 Unmanaged 資源，並選擇性處置 Managed 資源。</summary>
      <param name="disposing">true，表示釋放 Managed 和 Unmanaged 資源；false，表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Headers">
      <summary>取得 HTTP 要求標頭的集合。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />。HTTP 要求標頭的集合。</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Method">
      <summary>取得或設定 HTTP 要求訊息所使用的 HTTP 方法。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpMethod" />。要求訊息所使用的 HTTP 方法。預設為 GET 方法。</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Properties">
      <summary>取得 HTTP 要求的屬性集。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.IDictionary`2" />。</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.RequestUri">
      <summary>取得或設定用於 HTTP 要求的 <see cref="T:System.Uri" />。</summary>
      <returns>傳回 <see cref="T:System.Uri" />。用於 HTTP 要求的 <see cref="T:System.Uri" />。</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.ToString">
      <summary>傳回表示目前物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。目前物件的字串表示。</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Version">
      <summary>取得或設定 HTTP 訊息版本。</summary>
      <returns>傳回 <see cref="T:System.Version" />。HTTP 訊息版本。預設值為 1.1。</returns>
    </member>
    <member name="T:System.Net.Http.HttpResponseMessage">
      <summary>表示包含狀態碼及資料的 HTTP 回應訊息。</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor">
      <summary>初始化 <see cref="T:System.Net.Http.HttpResponseMessage" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor(System.Net.HttpStatusCode)">
      <summary>使用特定 <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" />，初始化 <see cref="T:System.Net.Http.HttpResponseMessage" /> 類別的新執行個體。</summary>
      <param name="statusCode">HTTP 回應的狀態碼。</param>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Content">
      <summary>取得或設定 HTTP 回應訊息的內容。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpContent" />。HTTP 回應訊息的內容。</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose">
      <summary>釋放 Unmanaged 資源，並處置 <see cref="T:System.Net.Http.HttpResponseMessage" /> 所使用的 Unmanaged 資源。</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Net.Http.HttpResponseMessage" /> 所使用的 Unmanaged 資源，並選擇性處置 Managed 資源。</summary>
      <param name="disposing">true，表示釋放 Managed 和 Unmanaged 資源；false，表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode">
      <summary>如果 HTTP 回應的 <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode" /> 屬性為 false，會擲回例外狀況。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpResponseMessage" />。HTTP 回應訊息（如果該呼叫成功）。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Headers">
      <summary>取得 HTTP 回應標頭的集合。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpResponseHeaders" />。HTTP 回應標頭的集合。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode">
      <summary>取得值，指出 HTTP 回應是否成功。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。表示 HTTP 回應是否成功的值。如果 <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> 在範圍 200-299 中，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.ReasonPhrase">
      <summary>取得或設定原因片語，通常由伺服器將它與狀態碼一起傳送。</summary>
      <returns>傳回 <see cref="T:System.String" />。伺服器所傳送之原因詞彙。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.RequestMessage">
      <summary>取得或設定造成此回應訊息的要求訊息。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpRequestMessage" />。造成此回應訊息的要求訊息。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.StatusCode">
      <summary>取得或設定 HTTP 回應的狀態碼。</summary>
      <returns>傳回 <see cref="T:System.Net.HttpStatusCode" />。HTTP 回應的狀態碼。</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.ToString">
      <summary>傳回表示目前物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。目前物件的字串表示。</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Version">
      <summary>取得或設定 HTTP 訊息版本。</summary>
      <returns>傳回 <see cref="T:System.Version" />。HTTP 訊息版本。預設值為 1.1。</returns>
    </member>
    <member name="T:System.Net.Http.MessageProcessingHandler">
      <summary>處理常式的基底型別，這些處理常式僅對要求和 (或) 回應訊息執行一些小型處理。</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor">
      <summary>建立 <see cref="T:System.Net.Http.MessageProcessingHandler" /> 類別的執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>使用特定的內部處理常式，建立 <see cref="T:System.Net.Http.MessageProcessingHandler" /> 類別的執行個體。</summary>
      <param name="innerHandler">負責處理 HTTP 回應訊息的內部處理常式。</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>執行對傳送至伺服器之每個要求的處理。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpRequestMessage" />。處理的 HTTP 要求訊息。</returns>
      <param name="request">要處理的 HTTP 要求訊息。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒使用以接收的取消通知。</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessResponse(System.Net.Http.HttpResponseMessage,System.Threading.CancellationToken)">
      <summary>執行對伺服器每個回應的處理。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.HttpResponseMessage" />。已處理的 HTTP 回應訊息。</returns>
      <param name="response">要處理的 HTTP 回應訊息。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒使用以接收的取消通知。</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>將 HTTP 要求傳送到內部處理常式，以非同步作業方式傳送到伺服器。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
      <param name="request">要傳送到伺服器的 HTTP 要求訊息。</param>
      <param name="cancellationToken">取消語彙基元，可由其他物件或執行緒使用以接收的取消通知。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="request" /> 為 null。</exception>
    </member>
    <member name="T:System.Net.Http.MultipartContent">
      <summary>提供使用 multipart/* 內容類型規格進行序列化之 <see cref="T:System.Net.Http.HttpContent" /> 物件的集合。</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor">
      <summary>建立 <see cref="T:System.Net.Http.MultipartContent" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String)">
      <summary>建立 <see cref="T:System.Net.Http.MultipartContent" /> 類別的新執行個體。</summary>
      <param name="subtype">多部分內容的子型別。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="subtype" /> 為 null 或僅包含空白字元。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String,System.String)">
      <summary>建立 <see cref="T:System.Net.Http.MultipartContent" /> 類別的新執行個體。</summary>
      <param name="subtype">多部分內容的子型別。</param>
      <param name="boundary">多重內容的界限字串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="subtype" /> 為 null，或是空字串。<paramref name="boundary" /> 為 null 或僅包含空白字元。-或-<paramref name="boundary" /> 以空白字元結束。</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="boundary" /> 的長度大於 70。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Add(System.Net.Http.HttpContent)">
      <summary>將多重部分 HTTP 內容加入至使用 multipart/* 內容類型規格進行序列化之 <see cref="T:System.Net.Http.HttpContent" /> 物件的集合。</summary>
      <param name="content">要新增至集合中的 HTTP 內容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Net.Http.MultipartContent" /> 所使用的 Unmanaged 資源，並選擇性處置 Managed 資源。</summary>
      <param name="disposing">true，表示釋放 Managed 和 Unmanaged 資源；false，表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.GetEnumerator">
      <summary>傳回列舉程式，此列舉程式可以逐一查看 <see cref="T:System.Net.Http.HttpContent" /> 物件的集合，該集合會使用多重/* 內容型別規格進行序烈化。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.IEnumerator`1" />。用於逐一查看集合的物件。</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>以非同步作業方式將多個 HTTP 內容序列化至資料流。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task" />。工作物件，表示非同步作業。</returns>
      <param name="stream">目標資料流。</param>
      <param name="context">傳輸的相關資訊 (例如通道繫結語彙基元)。這個參數可以是 null。</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="M:System.Net.Http.MultipartContent.GetEnumerator" /> 方法的明確實作。</summary>
      <returns>傳回 <see cref="T:System.Collections.IEnumerator" />。用於逐一查看集合的物件。</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.TryComputeLength(System.Int64@)">
      <summary>判斷 HTTP 多部分內容的長度 (以位元組為單位) 是否有效。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="length" /> 是有效長度則為 true，否則為 false。</returns>
      <param name="length">HHTP 內容的長度，以位元組為單位。</param>
    </member>
    <member name="T:System.Net.Http.MultipartFormDataContent">
      <summary>提供使用多重/表單資料 MIME 類型編碼內容的容器。</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor">
      <summary>建立 <see cref="T:System.Net.Http.MultipartFormDataContent" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor(System.String)">
      <summary>建立 <see cref="T:System.Net.Http.MultipartFormDataContent" /> 類別的新執行個體。</summary>
      <param name="boundary">多重表單資料內容的界限字串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="boundary" /> 為 null 或僅包含空白字元。-或-<paramref name="boundary" /> 以空白字元結束。</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="boundary" /> 的長度大於 70。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent)">
      <summary>將 HTTP 內容加入至 <see cref="T:System.Net.Http.HttpContent" /> 物件的集合，會序列化為 multipart/form-data MIME 類型。</summary>
      <param name="content">要新增至集合中的 HTTP 內容。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String)">
      <summary>將 HTTP 內容加入至 <see cref="T:System.Net.Http.HttpContent" /> 物件的集合，會序列化為 multipart/form-data MIME 類型。</summary>
      <param name="content">要新增至集合中的 HTTP 內容。</param>
      <param name="name">要為 HTTP 內容加入的名稱。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> 為 null 或僅包含空白字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> 為 null。</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String,System.String)">
      <summary>將 HTTP 內容加入至 <see cref="T:System.Net.Http.HttpContent" /> 物件的集合，會序列化為 multipart/form-data MIME 類型。</summary>
      <param name="content">要新增至集合中的 HTTP 內容。</param>
      <param name="name">要為 HTTP 內容加入的名稱。</param>
      <param name="fileName">要加入至集合中的 HTTP 內容的檔案名稱。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> 為 null 或僅包含空白字元。-或-<paramref name="fileName" /> 為 null 或僅包含空白字元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> 為 null。</exception>
    </member>
    <member name="T:System.Net.Http.StreamContent">
      <summary>根據資料流提供 HTTP 內容。</summary>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream)">
      <summary>建立 <see cref="T:System.Net.Http.StreamContent" /> 類別的新執行個體。</summary>
      <param name="content">用來初始化 <see cref="T:System.Net.Http.StreamContent" /> 的內容。</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream,System.Int32)">
      <summary>建立 <see cref="T:System.Net.Http.StreamContent" /> 類別的新執行個體。</summary>
      <param name="content">用來初始化 <see cref="T:System.Net.Http.StreamContent" /> 的內容。</param>
      <param name="bufferSize">
        <see cref="T:System.Net.Http.StreamContent" /> 的緩衝區大小，以位元組為單位。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="content" /> 為 null。</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="bufferSize" /> 小於或等於零值。</exception>
    </member>
    <member name="M:System.Net.Http.StreamContent.CreateContentReadStreamAsync">
      <summary>將 HTTP 資料流內容寫入至記憶體資料流，做為非同步作業。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task`1" />。工作物件，表示非同步作業。</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Net.Http.StreamContent" /> 所使用的 Unmanaged 資源，並選擇性處置 Managed 資源。</summary>
      <param name="disposing">true，表示釋放 Managed 和 Unmanaged 資源；false，表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>以非同步作業方式將 HTTP 內容序列化至資料流。</summary>
      <returns>傳回 <see cref="T:System.Threading.Tasks.Task" />。工作物件，表示非同步作業。</returns>
      <param name="stream">目標資料流。</param>
      <param name="context">傳輸的相關資訊 (例如通道繫結語彙基元)。這個參數可以是 null。</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.TryComputeLength(System.Int64@)">
      <summary>判斷資料流內容的長度 (以位元組為單位) 是否有效。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="length" /> 是有效長度則為 true，否則為 false。</returns>
      <param name="length">資料流內容的長度，以位元組為單位。</param>
    </member>
    <member name="T:System.Net.Http.StringContent">
      <summary>根據字串提供 HTTP 內容。</summary>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String)">
      <summary>建立 <see cref="T:System.Net.Http.StringContent" /> 類別的新執行個體。</summary>
      <param name="content">用來初始化 <see cref="T:System.Net.Http.StringContent" /> 的內容。</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding)">
      <summary>建立 <see cref="T:System.Net.Http.StringContent" /> 類別的新執行個體。</summary>
      <param name="content">用來初始化 <see cref="T:System.Net.Http.StringContent" /> 的內容。</param>
      <param name="encoding">要用於內容的編碼方式。</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding,System.String)">
      <summary>建立 <see cref="T:System.Net.Http.StringContent" /> 類別的新執行個體。</summary>
      <param name="content">用來初始化 <see cref="T:System.Net.Http.StringContent" /> 的內容。</param>
      <param name="encoding">要用於內容的編碼方式。</param>
      <param name="mediaType">要用於內容的媒體類型。</param>
    </member>
    <member name="T:System.Net.Http.Headers.AuthenticationHeaderValue">
      <summary>表示 Authorization、ProxyAuthorization、WWW-Authenticate 和 Proxy 驗證標頭值中的驗證資訊。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 類別的新執行個體。</summary>
      <param name="scheme">用於授權的配置。</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String,System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 類別的新執行個體。</summary>
      <param name="scheme">用於授權的配置。</param>
      <param name="parameter">認證，包含所要求資源的使用者代理程式驗證資訊。</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Parameter">
      <summary>取得認證，包含所要求資源的使用者代理程式驗證資訊。</summary>
      <returns>傳回 <see cref="T:System.String" />。包含驗證資訊的認證。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />。<see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 執行個體。</returns>
      <param name="input">表示驗證標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的驗證標頭值資訊。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Scheme">
      <summary>取得用於授權的配置。</summary>
      <returns>傳回 <see cref="T:System.String" />。用於授權的配置。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.TryParse(System.String,System.Net.Http.Headers.AuthenticationHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.CacheControlHeaderValue">
      <summary>表示 Cache-Control標頭的值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.#ctor">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Extensions">
      <summary>快取擴充權杖，其中每個權杖都有選擇性指派的值。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。快取擴充語彙基元的集合，其中每個語彙基元都有選擇性指派的值。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxAge">
      <summary>HTTP 用戶端願意接受回應的最長保留期限（以秒為單位來指定）。</summary>
      <returns>傳回 <see cref="T:System.TimeSpan" />。秒數。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStale">
      <summary>否 HTTP 用戶端願意接受已經超過其到期時間的回應。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 HTTP 用戶端願意接受已經超過到期時間的回應，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStaleLimit">
      <summary>HTTP 用戶端願意接受已經超過其到期時間的回應的最長時間（以秒為單位）。</summary>
      <returns>傳回 <see cref="T:System.TimeSpan" />。秒數。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MinFresh">
      <summary>HTTP 用戶端願意接受回應的有效期限存留期（以秒為單位）。</summary>
      <returns>傳回 <see cref="T:System.TimeSpan" />。秒數。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MustRevalidate">
      <summary>當快取項目變成過時，在接續的使用中，原始伺服器是否需要重新驗證快取項目。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果當快取項目變成過時，在後續的使用中，原始伺服器需要重新驗證快取項目，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCache">
      <summary>是否 HTTP 用戶端願意接受快取的回應。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 HTTP 用戶端願意接受快取的回應，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCacheHeaders">
      <summary>在 HTTP 回應上，Cache-Control 標頭欄位之 "no-cache" 指示詞中的 fieldnames 集合。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。欄位名稱的集合。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoStore">
      <summary>是否快取絕不可以儲存 HTTP 要求訊息或任何回應的任何組成部分。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果快取中不能儲存 HTTP 要求訊息或任何回應的任何組成部分，則為 true，否則為false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoTransform">
      <summary>是否快取或 Proxy 絕不可以變更實體主體的任何層面。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果快取或 Proxy 絕不可以變更實體主體的任何層面，則為 true，否則為false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.OnlyIfCached">
      <summary>是否快取應該使用符合 HTTP 要求的其他約束條件的快取項目來回應，或是以 504 (閘道逾時) 狀態來回應。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果快取必須使用符合 HTTP 要求的其他條件約束之已快取項目來做回應，或使用 504 (Gateway Timeout) 狀態做回應，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />。<see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 執行個體。</returns>
      <param name="input">表示快取控制項標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的快取控制標頭值資訊。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Private">
      <summary>是否整個或部分的 HTTP 回應訊息僅供單一使用者使用，或絕不可由共用快取來快取。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 HTTP 回應訊息僅供單一使用者使用，而且不可由共用快取來快取，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.PrivateHeaders">
      <summary>在 HTTP 回應上，Cache-Control 標頭欄位之 "private" 指示詞中的 fieldnames 集合。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。欄位名稱的集合。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.ProxyRevalidate">
      <summary>當共用使用者代理快取的快取項目變成過時，在接續的使用中，原始伺服器是否需要重新驗證快取項目。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果當共用使用者代理快取的快取項目變成過時，在後續的使用中，原始伺服器需要重新驗證快取項目，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Public">
      <summary>是否 HTTP 回應可以由任何快取來快取，即使它通常不可快取，或只可以在非共用的快取中快取。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 HTTP 回應可以由任何快取來快取，即使它通常不可快取，或只可以在非共用的快取中快取，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.SharedMaxAge">
      <summary>HTTP 回應中的共用最長使用期限（以秒為單位），會覆蓋快取控制標頭或共用快取的到期日標頭中的 "max-age" 指示詞。</summary>
      <returns>傳回 <see cref="T:System.TimeSpan" />。秒數。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.TryParse(System.String,System.Net.Http.Headers.CacheControlHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentDispositionHeaderValue">
      <summary>表示 Content-Disposition 標頭的值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.Net.Http.Headers.ContentDispositionHeaderValue)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 類別的新執行個體。</summary>
      <param name="source">
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 類別的新執行個體。</summary>
      <param name="dispositionType">包含 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 的字串。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.CreationDate">
      <summary>建立檔案的日期。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。檔案建立日期。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.DispositionType">
      <summary>內容內文部分的配置類型。</summary>
      <returns>傳回 <see cref="T:System.String" />。配置類型。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileName">
      <summary>對於如何建構要在實體已中斷連結且儲存於不同檔案時用來儲存訊息承載之檔案名稱的建議。</summary>
      <returns>傳回 <see cref="T:System.String" />。建議的檔案名稱。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileNameStar">
      <summary>對於如何建構要在實體已中斷連結且儲存於不同檔案時用來儲存訊息承載之檔案名稱的建議。</summary>
      <returns>傳回 <see cref="T:System.String" />。表單檔案名稱的建議檔案名稱*。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ModificationDate">
      <summary>上次修改檔案的日期。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。檔案修改日期。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Name">
      <summary>內容主體組件的名稱。</summary>
      <returns>傳回 <see cref="T:System.String" />。內容主體組件的名稱。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Parameters">
      <summary>一組包含 Content-Disposition 標頭的參數。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。參數的集合。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />。<see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 執行個體。</returns>
      <param name="input">表示內容配置標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的內容配置標頭值資訊。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ReadDate">
      <summary>上次讀取檔案的日期。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。上次讀取日期。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Size">
      <summary>檔案的約略大小大小，以位元組為單位。</summary>
      <returns>傳回 <see cref="T:System.Int64" />。約略大小，以位元組為單位。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentDispositionHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentRangeHeaderValue">
      <summary>表示 Content-Range 標頭的值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 類別的新執行個體。</summary>
      <param name="length">範圍的起始和結束點 (以位元組表示)。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 類別的新執行個體。</summary>
      <param name="from">要開始傳送資料的位置 (以位元組表示)。</param>
      <param name="to">要停止傳送資料的位置 (以位元組表示)。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64,System.Int64)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 類別的新執行個體。</summary>
      <param name="from">要開始傳送資料的位置 (以位元組表示)。</param>
      <param name="to">要停止傳送資料的位置 (以位元組表示)。</param>
      <param name="length">範圍的起始和結束點 (以位元組表示)。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Equals(System.Object)">
      <summary>判斷指定的物件是否等於目前的 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.From">
      <summary>取得要開始傳送資料的位置。</summary>
      <returns>傳回 <see cref="T:System.Int64" />。要開始傳送資料的位置 (以位元組表示)。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasLength">
      <summary>取得 Content-Range 標頭是否有指定的長度。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 Content-Range 有指定長度則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasRange">
      <summary>取得 Content-Range 是否有指定的範圍。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 Content-Range 有指定範圍則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Length">
      <summary>取得完整實體內容的長度。</summary>
      <returns>傳回 <see cref="T:System.Int64" />。完整實體內容的長度。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />。<see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 執行個體。</returns>
      <param name="input">表示內容範圍標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的內容範圍標頭值資訊。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.To">
      <summary>取得要停止傳送資料的位置。</summary>
      <returns>傳回 <see cref="T:System.Int64" />。要停止傳送資料的位置。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentRangeHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> 版本。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Unit">
      <summary>使用的範圍單位。</summary>
      <returns>傳回 <see cref="T:System.String" />。包含範圍單位的 <see cref="T:System.String" />。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.EntityTagHeaderValue">
      <summary>表示實體標記標頭值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 類別的新執行個體。</summary>
      <param name="tag">包含 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 的字串。</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String,System.Boolean)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 類別的新執行個體。</summary>
      <param name="tag">包含 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 的字串。</param>
      <param name="isWeak">表示這個實體標記標頭是否為弱式驗證程式的值。如果實體標記標頭是弱式驗證程式，則應該將 <paramref name="isWeak" /> 設定為 true。如果實體標記標頭是強式驗證程式，則應該將 <paramref name="isWeak" /> 設定為 false。</param>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Any">
      <summary>取得實體標記標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.IsWeak">
      <summary>取得實體標記的前端是否有弱點指標。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果實體標記有弱式指標做為開頭則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />。<see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 執行個體。</returns>
      <param name="input">表示實體標記標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的實體標記標頭值資訊。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Tag">
      <summary>取得不透明的引號括住的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。不透明的引號括住的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.TryParse(System.String,System.Net.Http.Headers.EntityTagHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpContentHeaders">
      <summary>表示內容標頭集合，如 RFC 2616 中的定義。</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Allow">
      <summary>取得 HTTP 回應的 Allow 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。HTTP 回應的 Allow 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentDisposition">
      <summary>取得 HTTP 回應的 Content-Disposition 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />。HTTP 回應的 Content-Disposition 內容標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentEncoding">
      <summary>取得 HTTP 回應的 Content-Encoding 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。HTTP 回應的 Content-Encoding 內容標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLanguage">
      <summary>取得 HTTP 回應的 Content-Language 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。HTTP 回應的 Content-Language 內容標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLength">
      <summary>取得或設定 HTTP 回應之 Content-Length 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Int64" />。HTTP 回應的 Content-Length 內容標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLocation">
      <summary>取得或設定 HTTP 回應之 Content-Location 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Uri" />。HTTP 回應的 Content-Location 內容標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentMD5">
      <summary>取得或設定 HTTP 回應之 Content-MD5 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Byte" />。HTTP 回應的 Content-MD5 內容標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentRange">
      <summary>取得或設定 HTTP 回應之 Content-Range 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />。HTTP 回應的 Content-Range 內容標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentType">
      <summary>取得或設定 HTTP 回應之 Content-Type 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />。HTTP 回應的 Content-Type 內容標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Expires">
      <summary>取得或設定 HTTP 回應之 Expires 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。HTTP 回應的 Expires 內容標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.LastModified">
      <summary>取得或設定 HTTP 回應之 Last-Modified 內容標頭的值。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。HTTP 回應的 Last-Modified 內容標頭的值。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaders">
      <summary>標頭及其值的集合，如 RFC 2616 中所定義的。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.#ctor">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>將指定的標頭及其值加入至 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 集合中。</summary>
      <param name="name">要加入至集合的標頭。</param>
      <param name="values">要加入至集合的標頭值清單。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.String)">
      <summary>將指定的標頭及其值加入至 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 集合中。</summary>
      <param name="name">要加入至集合的標頭。</param>
      <param name="value">標頭的內容。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Clear">
      <summary>移除 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 集合中的所有標頭。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Contains(System.String)">
      <summary>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 集合中是否有特定標頭。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。true 為指定的標頭存在於集合中，否則為 false。</returns>
      <param name="name">特定的標頭。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetEnumerator">
      <summary>傳回可以逐一查看 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 執行個體的列舉值。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.IEnumerator`1" />。<see cref="T:System.Net.Http.Headers.HttpHeaders" /> 的列舉值。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetValues(System.String)">
      <summary>傳回儲存在 <see cref="T:System.Net.Http.Headers.HttpHeaders" />集合中的指定標頭的所有標頭值。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.IEnumerable`1" />。標頭字串的陣列。</returns>
      <param name="name">要為其傳回值的指定標頭。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Remove(System.String)">
      <summary>從 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 集合中移除指定的標頭。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。</returns>
      <param name="name">要從集合中移除的標頭名稱。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回可以逐一查看 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 的列舉程式。</summary>
      <returns>傳回 <see cref="T:System.Collections.IEnumerator" />。可以逐一查看 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 的 <see cref="T:System.Collections.IEnumerator" /> 實作執行個體。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>傳回值，這個值表示指定的標頭及其值是否已在沒有驗證所提供之資訊的情況下加入至 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 集合。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的標頭 <paramref name="name" /> 和 <paramref name="values" /> 可加入至集合中，則為 true，否則為 false。</returns>
      <param name="name">要加入至集合的標頭。</param>
      <param name="values">標頭的值。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.String)">
      <summary>傳回值，這個值表示指定的標頭及其值是否已在沒有驗證所提供之資訊的情況下加入至 <see cref="T:System.Net.Http.Headers.HttpHeaders" /> 集合。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的標頭 <paramref name="name" /> 和 <paramref name="value" /> 可加入至集合中，則為 true，否則為 false。</returns>
      <param name="name">要加入至集合的標頭。</param>
      <param name="value">標頭的內容。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
      <summary>傳回指定的標頭和指定的值是否儲存在<see cref="T:System.Net.Http.Headers.HttpHeaders" />集合中。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的標頭 <paramref name="name" /> 和 values 儲存在集合中，則為 true，否則為 false。</returns>
      <param name="name">指定的標頭。</param>
      <param name="values">指定的標頭值。</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaderValueCollection`1">
      <summary>表示標頭值的集合。</summary>
      <typeparam name="T">標頭集合類型。</typeparam>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Add(`0)">
      <summary>將項目加入至 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。</summary>
      <param name="item">要加入至標頭集合中的項目。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Clear">
      <summary>移除 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 的所有項目。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Contains(`0)">
      <summary>判斷 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 是否包含項目。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 執行個體中包含項目，則為 true，否則為 false。</returns>
      <param name="item">要在標頭集合中尋找的項目。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.CopyTo(`0[],System.Int32)">
      <summary>從目標陣列的指定索引開始，複製整個 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 至相容的一維 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.Count">
      <summary>取得 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 中的標頭數目。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。集合中的標頭數。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 中逐一查看的列舉值。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.IEnumerator`1" />。<see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 執行個體的列舉程式。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.IsReadOnly">
      <summary>取得值，該值指出 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 執行個體是否唯讀。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 執行個體是唯讀，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ParseAdd(System.String)">
      <summary>剖析項目並將其加入至 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。</summary>
      <param name="input">要加入的項目。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Remove(`0)">
      <summary>從 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 中移除指定項目。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="item" /> 已成功從 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 執行個體中移除，則為 true，否則為 false。</returns>
      <param name="item">要移除的項目。</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回在 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 中逐一查看的列舉值。</summary>
      <returns>傳回 <see cref="T:System.Collections.IEnumerator" />。<see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 執行個體的列舉程式。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.TryParseAdd(System.String)">
      <summary>判斷輸入是否可加以剖析並加入至 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果可以剖析 <paramref name="input" /> 並將其加入至 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> 執行個體則為 true，否則為 false</returns>
      <param name="input">要驗證的項目。</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpRequestHeaders">
      <summary>表示要求標頭集合，如 RFC 2616 中的定義。</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Accept">
      <summary>取得 HTTP 要求的 Accept 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Accept 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptCharset">
      <summary>取得 HTTP 要求的 Accept-Charset 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Accept-Charset 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptEncoding">
      <summary>取得 HTTP 要求的 Accept-Encoding 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Accept-Encoding 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptLanguage">
      <summary>取得 HTTP 要求的 Accept-Language 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Accept-Language 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Authorization">
      <summary>取得或設定 HTTP 要求之 Authorization 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />。HTTP 要求的 Authorization 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.CacheControl">
      <summary>取得或設定 HTTP 要求之 Cache-Control 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />。HTTP 要求的 Cache-Control 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Connection">
      <summary>取得 HTTP 要求的 Connection 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Connection 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ConnectionClose">
      <summary>取得或設定值，指出 HTTP 要求的 Connection 標頭是否包含 Close。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 Connection 標頭包含 Close 則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Date">
      <summary>取得或設定 HTTP 要求之 Date 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。HTTP 要求的 Date 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Expect">
      <summary>取得 HTTP 要求的 Expect 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Expect 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ExpectContinue">
      <summary>取得或設定值，指出 HTTP 要求的 Expect 標頭是否包含 Continue。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 Expect 標頭包含 Continue 則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.From">
      <summary>取得或設定 HTTP 要求之 From 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.String" />。HTTP 要求的 From 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Host">
      <summary>取得或設定 HTTP 要求之 Host 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.String" />。HTTP 要求的 Host 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfMatch">
      <summary>取得 HTTP 要求的 If-Match 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 If-Match 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfModifiedSince">
      <summary>取得或設定 HTTP 要求之 If-Modified-Since 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。HTTP 要求的 If-Modified-Since 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfNoneMatch">
      <summary>取得 HTTP 要求的 If-None-Match 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。取得 HTTP 要求的 If-None-Match 標頭值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfRange">
      <summary>取得或設定 HTTP 要求之 If-Range 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />。HTTP 要求的 If-Range 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfUnmodifiedSince">
      <summary>取得或設定 HTTP 要求之 If-Unmodified-Since 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。HTTP 要求的 If-Unmodified-Since 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.MaxForwards">
      <summary>取得或設定 HTTP 要求之 Max-Forwards 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。HTTP 要求的 Max-Forwards 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Pragma">
      <summary>取得 HTTP 要求的 Pragma 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Pragma 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ProxyAuthorization">
      <summary>取得或設定 HTTP 要求之 Proxy-Authorization 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />。HTTP 要求的 Proxy-Authorization 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Range">
      <summary>取得或設定 HTTP 要求之 Range 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />。HTTP 要求的 Range 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Referrer">
      <summary>取得或設定 HTTP 要求之 Referer 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Uri" />。HTTP 要求的 Referer 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TE">
      <summary>取得 HTTP 要求的 TE 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 TE 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Trailer">
      <summary>取得 HTTP 要求的 Trailer 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Trailer 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncoding">
      <summary>取得 HTTP 要求的 Transfer-Encoding 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Transfer-Encoding 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncodingChunked">
      <summary>取得或設定值，指出 HTTP 要求的 Transfer-Encoding 標頭是否包含 chunked。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 Transfer-Encoding 標頭包含 chunked 則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Upgrade">
      <summary>取得 HTTP 要求的 Upgrade 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Upgrade 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.UserAgent">
      <summary>取得 HTTP 要求的 User-Agent 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 User-Agent 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Via">
      <summary>取得 HTTP 要求的 Via 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Via 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Warning">
      <summary>取得 HTTP 要求的 Warning 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 要求的 Warning 標頭的值。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpResponseHeaders">
      <summary>表示回應標頭集合，如 RFC 2616 中的定義。</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.AcceptRanges">
      <summary>取得 HTTP 回應的 Accept-Ranges 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Accept-Ranges 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Age">
      <summary>取得或設定 HTTP 回應之 Age 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.TimeSpan" />。HTTP 回應的 Age 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.CacheControl">
      <summary>取得或設定 HTTP 回應之 Cache-Control 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />。HTTP 回應的 Cache-Control 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Connection">
      <summary>取得 HTTP 回應的 Connection 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Connection 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ConnectionClose">
      <summary>取得或設定值，指出 HTTP 回應的 Connection 標頭是否包含 Close。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 Connection 標頭包含 Close 則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Date">
      <summary>取得或設定 HTTP 回應之 Date 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。HTTP 回應的 Date 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ETag">
      <summary>取得或設定 HTTP 回應之 ETag 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />。HTTP 回應的 ETag 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Location">
      <summary>取得或設定 HTTP 回應之 Location 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Uri" />。HTTP 回應的 Location 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Pragma">
      <summary>取得 HTTP 回應的 Pragma 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Pragma 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ProxyAuthenticate">
      <summary>取得 HTTP 回應的 Proxy-Authenticate 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Proxy-Authenticate 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.RetryAfter">
      <summary>取得或設定 HTTP 回應之 Retry-After 標頭的值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />。HTTP 回應的 Retry-After 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Server">
      <summary>取得 HTTP 回應的 Server 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Server 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Trailer">
      <summary>取得 HTTP 回應的 Trailer 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Trailer 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncoding">
      <summary>取得 HTTP 回應的 Transfer-Encoding 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Transfer-Encoding 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncodingChunked">
      <summary>取得或設定值，指出 HTTP 回應的 Transfer-Encoding 標頭是否包含 chunked。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 Transfer-Encoding 標頭包含 chunked 則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Upgrade">
      <summary>取得 HTTP 回應的 Upgrade 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Upgrade 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Vary">
      <summary>取得 HTTP 回應的 Vary 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Vary 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Via">
      <summary>取得 HTTP 回應的 Via 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Via 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Warning">
      <summary>取得 HTTP 回應的 Warning 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 Warning 標頭的值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.WwwAuthenticate">
      <summary>取得 HTTP 回應的 WWW-Authenticate 標頭值。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />。HTTP 回應的 WWW-Authenticate 標頭的值。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeHeaderValue">
      <summary>表示用於依 RFC 2616 所定義之內容類型標頭的媒體類型。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 類別的新執行個體。</summary>
      <param name="source"> 用來初始化新執行個體的 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 類別的新執行個體。</summary>
      <param name="mediaType">表示為字串以初始化新執行個體的來源。</param>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.CharSet">
      <summary>取得或設定字元集。</summary>
      <returns>傳回 <see cref="T:System.String" />。字元集。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.MediaType">
      <summary>取得或設定媒體型別標頭值。</summary>
      <returns>傳回 <see cref="T:System.String" />。媒體類型標頭值。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.Parameters">
      <summary>取得或設定媒體類型標頭值參數。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。媒體類型標頭值參數。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />。<see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 執行個體。</returns>
      <param name="input">表示媒體類型標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的媒體類型標頭值資訊。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue">
      <summary>表示具有用於內容類型標頭之其他品質係數的媒體類型。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 類別的新執行個體。</summary>
      <param name="mediaType">
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />，表示為字串以初始化新執行個體。</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 類別的新執行個體。</summary>
      <param name="mediaType">
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />，表示為字串以初始化新執行個體。</param>
      <param name="quality">與這個標頭值相關聯的品質。</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />。<see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 執行個體。</returns>
      <param name="input">表示媒體類型的字串，含有品質標頭值資訊。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是具有品質標頭值資訊的有效媒體類型。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Quality">
      <summary>取得或設定 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 的品質值。</summary>
      <returns>傳回 <see cref="T:System.Double" />。<see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 物件的品質值。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeWithQualityHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueHeaderValue">
      <summary>表示用於各種依 RFC 2616 所定義之標頭的名稱/值組。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.Net.Http.Headers.NameValueHeaderValue)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 類別的新執行個體。</summary>
      <param name="source">用來初始化新執行個體的 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 類別的新執行個體。</summary>
      <param name="name">標頭名稱。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String,System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 類別的新執行個體。</summary>
      <param name="name">標頭名稱。</param>
      <param name="value">標頭值。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Name">
      <summary>取得標頭名稱。</summary>
      <returns>傳回 <see cref="T:System.String" />。標頭名稱。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />。<see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 執行個體。</returns>
      <param name="input">表示名稱值標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的名稱值標頭值資訊。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> 版本。</param>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Value">
      <summary>取得標頭值。</summary>
      <returns>傳回 <see cref="T:System.String" />。標頭值。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue">
      <summary>表示搭配參數用於各種依 RFC 2616 所定義之標頭的名稱/值組。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.Net.Http.Headers.NameValueWithParametersHeaderValue)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 類別的新執行個體。</summary>
      <param name="source">用來初始化新執行個體的 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 類別的新執行個體。</summary>
      <param name="name">標頭名稱。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String,System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 類別的新執行個體。</summary>
      <param name="name">標頭名稱。</param>
      <param name="value">標頭值。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parameters">
      <summary>從 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 物件取得參數。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。包含參數的集合。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />。<see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 執行個體。</returns>
      <param name="input">表示名稱值的字串，含有參數標頭值資訊。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是具有參數標頭值資訊的有效名稱值。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueWithParametersHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.ProductHeaderValue">
      <summary>表示使用者代理程式標頭中的產品語彙基元值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 類別的新執行個體。</summary>
      <param name="name">產品名稱。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String,System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 類別的新執行個體。</summary>
      <param name="name">產品名稱值。</param>
      <param name="version">產品版本值。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Name">
      <summary>取得產品語彙基元的名稱。</summary>
      <returns>傳回 <see cref="T:System.String" />。產品語彙基元的名稱。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />。<see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 執行個體。</returns>
      <param name="input">表示產品標頭值資訊的字串。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> 版本。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Version">
      <summary>取得產品語彙基元的版本。</summary>
      <returns>傳回 <see cref="T:System.String" />。產品語彙基元的版本。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductInfoHeaderValue">
      <summary>代表一個值，它可以是 User-Agent 標頭中的一項產品或註解。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.Net.Http.Headers.ProductHeaderValue)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 類別的新執行個體。</summary>
      <param name="product">用來初始化新執行個體的 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 類別的新執行個體。</summary>
      <param name="comment">註解值。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String,System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 類別的新執行個體。</summary>
      <param name="productName">產品名稱值。</param>
      <param name="productVersion">產品版本值。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Comment">
      <summary>從 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 物件取得註解。</summary>
      <returns>傳回 <see cref="T:System.String" />。這個 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 的註解值。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />。<see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 執行個體。</returns>
      <param name="input">表示產品資訊標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的產品資訊標頭值資訊。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Product">
      <summary>從 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 物件取得產品。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />。這個 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 中的產品值。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductInfoHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeConditionHeaderValue">
      <summary>表示 If-Range 標頭值，它可以是日期/時間或是實體標記值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 類別的新執行個體。</summary>
      <param name="date">用來初始化新執行個體的日期值。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.Net.Http.Headers.EntityTagHeaderValue)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 類別的新執行個體。</summary>
      <param name="entityTag">用來初始化新執行個體的 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> 物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 類別的新執行個體。</summary>
      <param name="entityTag">實體標記，表示為用來初始化新執行個體的字串。</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.Date">
      <summary>從 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 物件取得日期。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。<see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 物件中的日期。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.EntityTag">
      <summary>從 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 物件取得實體標記。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />。<see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 物件中的實體標記。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />。<see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 執行個體。</returns>
      <param name="input">表示範圍條件標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的範圍條件標頭值資訊。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeConditionHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeHeaderValue">
      <summary>表示 Range 標頭值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>使用位元組範圍初始化 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 類別的新執行個體。</summary>
      <param name="from">要開始傳送資料的位置。</param>
      <param name="to">要停止傳送資料的位置。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> 大於 <paramref name="to" />-或-<paramref name="from" /> 或 <paramref name="to" /> 小於 0</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />。<see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 執行個體。</returns>
      <param name="input">表示範圍標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的範圍標頭值資訊。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Ranges">
      <summary>從 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 物件中取得指定的範圍。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。<see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 物件提供的範圍。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 版本。</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Unit">
      <summary>從 <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 物件取得單位。</summary>
      <returns>傳回 <see cref="T:System.String" />。<see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> 物件中的單位。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeItemHeaderValue">
      <summary>表示 Range 標頭值中的位元組範圍。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> 類別的新執行個體。</summary>
      <param name="from">要開始傳送資料的位置。</param>
      <param name="to">要停止傳送資料的位置。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> 大於 <paramref name="to" />-或-<paramref name="from" /> 或 <paramref name="to" /> 小於 0</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.From">
      <summary>取得要開始傳送資料的位置。</summary>
      <returns>傳回 <see cref="T:System.Int64" />。要開始傳送資料的位置。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.To">
      <summary>取得要停止傳送資料的位置。</summary>
      <returns>傳回 <see cref="T:System.Int64" />。要停止傳送資料的位置。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RetryConditionHeaderValue">
      <summary>表示 Retry-After 標頭值，它可以是日期/時間或是 timespan 值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 類別的新執行個體。</summary>
      <param name="date">用來初始化新執行個體的日期及時間位移。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.TimeSpan)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 類別的新執行個體。</summary>
      <param name="delta">用來初始化新執行個體的差異，以秒為單位。</param>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Date">
      <summary>取得與 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 物件相差的日期及時間。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。與 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 物件相差的日期及時間。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Delta">
      <summary>從 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 物件取得差異，以秒為單位。</summary>
      <returns>傳回 <see cref="T:System.TimeSpan" />。<see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 物件中的差異，以秒為單位。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />。<see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 執行個體。</returns>
      <param name="input">表示重試條件標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的重試條件標頭值資訊。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RetryConditionHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.StringWithQualityHeaderValue">
      <summary>表示含選擇性品質的字串標頭值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 類別的新執行個體。</summary>
      <param name="value">用來初始化新執行個體的字串。</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 類別的新執行個體。</summary>
      <param name="value">用來初始化新執行個體的字串。</param>
      <param name="quality">用來初始化新執行個體的品質因素。</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Equals(System.Object)">
      <summary>判斷指定的物件是否等於目前的 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />。<see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 執行個體。</returns>
      <param name="input">表示品質標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是具有品質標頭值資訊的有效字串。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Quality">
      <summary>從 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 物件取得品質係數。</summary>
      <returns>傳回 <see cref="T:System.Double" />。<see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 物件中的品質係數。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.StringWithQualityHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 版本。</param>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Value">
      <summary>從 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 物件取得字串值。</summary>
      <returns>傳回 <see cref="T:System.String" />。取自 <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> 物件的字串值。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingHeaderValue">
      <summary>表示 accept-encoding 標頭值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.Net.Http.Headers.TransferCodingHeaderValue)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 類別的新執行個體。</summary>
      <param name="source">用來初始化新執行個體的 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 類別的新執行個體。</summary>
      <param name="value">用來初始化新執行個體的字串。</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Equals(System.Object)">
      <summary>判斷指定的物件是否等於目前的 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Parameters">
      <summary>取得傳輸編碼參數。</summary>
      <returns>傳回 <see cref="T:System.Collections.Generic.ICollection`1" />。傳輸編碼參數。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />。<see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> 執行個體。</returns>
      <param name="input">表示傳輸編碼標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的傳輸編碼標頭值資訊。</exception>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> 版本。</param>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Value">
      <summary>取得傳輸編碼值。</summary>
      <returns>傳回 <see cref="T:System.String" />。傳輸編碼值。</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue">
      <summary>表示含選擇性品質係數的 Accept-Encoding 標頭值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 類別的新執行個體。</summary>
      <param name="value">用來初始化新執行個體的字串。</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 類別的新執行個體。</summary>
      <param name="value">用來初始化新執行個體的字串。</param>
      <param name="quality">品質係數的值。</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />。<see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 執行個體。</returns>
      <param name="input">表示傳輸編碼值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是具有品質標頭值資訊的有效傳輸編碼。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Quality">
      <summary>從 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 取得品質係數。</summary>
      <returns>傳回 <see cref="T:System.Double" />。<see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 中的品質係數。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingWithQualityHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.ViaHeaderValue">
      <summary>表示 Via 標頭的值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 類別的新執行個體。</summary>
      <param name="protocolVersion">接收的通訊協定的通訊協定版本。</param>
      <param name="receivedBy">已收到要求或回應的主機及連接埠。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 類別的新執行個體。</summary>
      <param name="protocolVersion">接收的通訊協定的通訊協定版本。</param>
      <param name="receivedBy">已收到要求或回應的主機及連接埠。</param>
      <param name="protocolName">接收的通訊協定的通訊協定名稱。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String,System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 類別的新執行個體。</summary>
      <param name="protocolVersion">接收的通訊協定的通訊協定版本。</param>
      <param name="receivedBy">已收到要求或回應的主機及連接埠。</param>
      <param name="protocolName">接收的通訊協定的通訊協定名稱。</param>
      <param name="comment">用來識別收件者 Proxy 或閘道之軟體的註解欄位。</param>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.Comment">
      <summary>取得用來識別收件者 Proxy 或閘道之軟體的註解欄位。</summary>
      <returns>傳回 <see cref="T:System.String" />。用來識別收件者 Proxy 或閘道之軟體的註解欄位。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 是否等於目前的 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。傳回目前物件的雜湊程式碼。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />。<see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 執行個體。</returns>
      <param name="input">表示 via 標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的 via 標頭值資訊。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolName">
      <summary>取得接收的通訊協定的通訊協定名稱。</summary>
      <returns>傳回 <see cref="T:System.String" />。通訊協定名稱。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolVersion">
      <summary>取得接收的通訊協定的通訊協定版本。</summary>
      <returns>傳回 <see cref="T:System.String" />。通訊協定版本。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ReceivedBy">
      <summary>取得收到要求或回應的主機和連接埠。</summary>
      <returns>傳回 <see cref="T:System.String" />。已收到要求或回應的主機及連接埠。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.TryParse(System.String,System.Net.Http.Headers.ViaHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> 版本。</param>
    </member>
    <member name="T:System.Net.Http.Headers.WarningHeaderValue">
      <summary>表示警告標頭所用的警告值。</summary>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 類別的新執行個體。</summary>
      <param name="code">特定警告碼。</param>
      <param name="agent">已附加警告的主機。</param>
      <param name="text">含有警告文字的以引號括住的字串。</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String,System.DateTimeOffset)">
      <summary>初始化 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 類別的新執行個體。</summary>
      <param name="code">特定警告碼。</param>
      <param name="agent">已附加警告的主機。</param>
      <param name="text">含有警告文字的以引號括住的字串。</param>
      <param name="date">警告的日期/時間戳記。</param>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Agent">
      <summary>取得已附加警告的主機。</summary>
      <returns>傳回 <see cref="T:System.String" />。已附加警告的主機。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Code">
      <summary>取得特定警告碼。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。特定警告碼。</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Date">
      <summary>取得警告的日期/時間戳記。</summary>
      <returns>傳回 <see cref="T:System.DateTimeOffset" />。警告的日期/時間戳記。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Equals(System.Object)">
      <summary>判斷指定的 <see cref="T:System.Object" /> 物件是否等於目前的 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 物件。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果指定的 <see cref="T:System.Object" /> 等於目前的物件，則為 true，否則為 false。</returns>
      <param name="obj">要與目前物件比較的物件。</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.GetHashCode">
      <summary>做為 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 物件的雜湊函式。</summary>
      <returns>傳回 <see cref="T:System.Int32" />。目前物件的雜湊碼。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Parse(System.String)">
      <summary>將字串轉換為 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 執行個體。</summary>
      <returns>傳回 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 執行個體。</returns>
      <param name="input">表示驗證標頭值資訊的字串。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 為 null 參考。</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> 不是有效的驗證標頭值資訊。</exception>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Text">
      <summary>取得包含警告文字的以引號括住的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。含有警告文字的以引號括住的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.ToString">
      <summary>傳回表示目前 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 物件的字串。</summary>
      <returns>傳回 <see cref="T:System.String" />。表示目前物件的字串。</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.TryParse(System.String,System.Net.Http.Headers.WarningHeaderValue@)">
      <summary>判斷字串是否為有效的 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 資訊。</summary>
      <returns>傳回 <see cref="T:System.Boolean" />。如果 <paramref name="input" /> 為有效的 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 資訊，則為 true，否則為 false。</returns>
      <param name="input">要驗證的字串。</param>
      <param name="parsedValue">字串的 <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> 版本。</param>
    </member>
  </members>
</doc>