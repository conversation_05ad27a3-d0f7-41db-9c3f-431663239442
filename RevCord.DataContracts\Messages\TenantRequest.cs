﻿using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.TenantEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class TenantRequest : RequestBase
    {
        public List<TenantColumnModel> TenantColumnsModel { get; set; }
        public List<TenantCustomConfiguration> TenantCustomConfigurations { get; set; }
        public List<int> TenantConfigurationCriteria { get; set; }
        public List<int> TenantIdCriteria { get; set; }
        public int UserNum { get; set; }
        public bool IsOnlyIQ3ModeEnabled { get; set; }
    }
}
