﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace RevCord.Util
{
    public class QueryHelper
    {
        //private static readonly string _groupExtOrCaluse = @"(AG.GroupNum = {0} AND A.Ext = {1})";

        private static readonly string _groupExtOrCaluse = @"({0}.GroupNum = {1} AND {2}.Ext = {3})";
        //private static readonly string _groupExtCallTypeOrCaluse = @"({0}.GroupNum = {1} AND {2}.Ext = {3} AND {4}.CallType = {5})";
        //private static readonly string _groupExtScreenRecOrCaluse = @"({0}.GroupNum = {1} AND {2}.Ext = {3} AND {4}.Screen_Rec_File IS NOT NULL)";
        private static readonly char _groupExtSeparator = 'E';

        private static readonly string _groupOrCaluse = @"({0}.GroupNum = {1})";
        //private static readonly string _groupCallTypeOrCaluse = @"({0}.GroupNum = {1} AND {2}.CallType = {3})";
        //private static readonly string _groupScreenRecOrCaluse = @"({0}.GroupNum = {1} AND {2}.Screen_Rec_File IS NOT NULL)";

        private static readonly char _groupUserSeparator = 'A'; //E;
        private static readonly string _groupUserWhereClause = @" AND ceo.{0} IN ( {1} ) "; //ExI
        
        /// <summary>
        /// Overloaded function for Audio call types.
        /// </summary>
        /// <param name="extensionGroups"></param>
        /// <param name="grpAlies"></param>
        /// <param name="extAlies"></param>
        /// <param name="defaultUser"></param>
        /// <returns></returns>
        public static string BuildGroupExtWhereClause(List<string> extensionGroups, string grpAlies, string extAlies, int defaultUser=0)
        {
            StringBuilder sb = new StringBuilder();

            if (extensionGroups == null || extensionGroups.Count == 0 || String.IsNullOrEmpty(grpAlies) || String.IsNullOrEmpty( extAlies))
                return "";
            //sb.Append(" 1=1 ");
            foreach (string extGrp in extensionGroups)
            {
                //if (extGrp.Length > 6 && extGrp.Contains('E'))
                if (extGrp.Contains('E'))
                {
                    sb.Append(String.Format(_groupExtOrCaluse, grpAlies, extGrp.Split(_groupExtSeparator)[0], extAlies, extGrp.Split(_groupExtSeparator)[1]));
                    sb.Append(" OR ");
                }
                else //only group selected
                {
                    sb.Append(String.Format(_groupOrCaluse, grpAlies, extGrp));
                    sb.Append(" OR ");
                }
                //else if(defaultUser=1)
                //{
                //}
            }
            return sb.RemoveLast(" OR ").ToString();
        }

        
        public static string BuildGroupUserWhereClause(List<string> groupUsers,string columnName)
        {
            StringBuilder sb = new StringBuilder();
            
            foreach (string grpUser in groupUsers)
            {
                if (grpUser.Contains('E'))
                {
                    sb.Append(grpUser.Split(_groupExtSeparator)[1]);
                    sb.Append(" , ");
                }
                else if (grpUser.Contains('A'))
                {
                    sb.Append(grpUser.Split(_groupUserSeparator)[1]);
                    sb.Append(" , ");
                }
            }
            sb.RemoveLast(" , ").ToString();

            return String.Format(_groupUserWhereClause, columnName, sb.ToString());
        }
    }
}
