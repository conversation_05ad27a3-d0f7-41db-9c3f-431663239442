﻿using RevCord.DataContracts.RoleManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class RoleManagementRequest
    {
        public int TenantId { get; set; }
        public Role Role { get; set; }
        public List<RolePermission> RolePermissions { get; set; }
        public RoleChannelPermission RoleChannelPermission { get; set; }
        public List<RoleChannelPermission> RoleChannelPermissions { get; set; }
    }
}
