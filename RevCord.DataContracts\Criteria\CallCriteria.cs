﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.DTO;
using System.Runtime.Serialization;
using RevCord.DataContracts.ReportEntities;

namespace RevCord.DataContracts.Criteria
{
    [DataContract]
    public class CallCriteria
    {
        //public CallSearchCriteriaDTO CallSearchCriteriaDTO { get; set; }
        [DataMember]
        public DateTime StartDate { get; set; }
        [DataMember]
        public DateTime EndDate { get; set; }
        [DataMember]
        public TimeSpan StartTime { get; set; }
        [DataMember]
        public TimeSpan EndTime { get; set; }
        [DataMember]
        public TimeSpan StartDuration { get; set; }
        [DataMember]
        public TimeSpan EndDuration { get; set; }
        [DataMember]
        public int RestrictionInHours { get; set; }
        [DataMember]
        public bool IsCustomSearch { get; set; }
        [DataMember]
        public string CustomText { get; set; }
        [DataMember]
        public string CustomSearchOperator { get; set; }
        [DataMember]
        public SearchType SearchType { get; set; }
        [DataMember]
        public EvalSearchType EvalSearchType { get; set; }
        [DataMember]
        public bool IsRandom { get; set; }
        [DataMember]
        public int NoOfCalls { get; set; }
        [DataMember]
        public bool IsPercentage { get; set; }
        [DataMember]
        public bool IsNotAuditCallsRequest { get; set; }
        [DataMember]
        public bool IsAgentBasedSearch { get; set; }
        [DataMember]
        public string AgentName { get; set; }
        [DataMember]
        public string RecorderName { get; set; }
        [DataMember]
        public string RadioTalkGroup { get; set; }
        [DataMember]
        public string ArchiveGroup { get; set; }
        [DataMember]
        public int RecId { get; set; }
        [DataMember]
        public string TimeFormat { get; set; }
        [DataMember]
        public List<CategoryGroupExtension> CategoryGroupExtensions { get; set; }
        [DataMember]
        public List<RecorderCategoryGroupExtension> RecorderCategoryGroupExtensions { get; set; }
        [DataMember]
        public string SelectedDemoExtensions { get; set; }
        [DataMember]
        public bool IsOnlyDemoSearch { get; set; }
        [DataMember]
        public bool IsAvrisView { get; set; }
        [DataMember]
        public bool IsGroupBasedSearchOnReport { get; set; }
        [DataMember]
        public int ReportGroupId { get; set; }


        [DataMember]
        public bool IsCustomDataSearch { get; set; }
        [DataMember]
        public CustomSearchType CustomSearchType { get; set; }
        [DataMember]
        public int InspectionTemplateId { get; set; }
        [DataMember]
        public int BookmarkId { get; set; }
        [DataMember]
        public string BookmarkText { get; set; }
        [DataMember]
        public string BookmarkNotes { get; set; }
        [DataMember]
        public string PreInspectionTitle { get; set; }
        [DataMember]
        public string PreInspectionData { get; set; }
        [DataMember]
        public string JOINString { get; set; }
    }
    public class RecorderCategoryGroupExtension
    {
        public int RecorderId { get; set; }
        public string RecorderIp { get; set; }
        public string RecorderName { get; set; }
        public List<CategoryGroupExtension> CategoryGroupExtensions { get; set; }
        public string RadioTalkGroup { get; set; }
    }

    //[Serializable]
    public class CategoryGroupExtension
    {
        public GroupType GroupType { get; set; }
        public List<GroupExtension> GroupExtensions { get; set; }
    }

    public class GroupExtension
    {
        public int GroupId { get; set; }
        public string ExtensionIdsCSV { get; set; }
        public List<string> ExtensionIds
        {
            get
            {
                if (!string.IsNullOrEmpty(this.ExtensionIdsCSV))
                {
                    //return ExtensionIdsCSV.Split(',').ToList();
                    return ExtensionIdsCSV.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                }
                return null;
            }
        }

        public override string ToString()
        {
            //return string.Format(@"({0}.GroupNum = {1} AND {0}.Ext IN ({2}))", "CI", this.GroupId, this.ExtensionIdsCSV);
            return base.ToString();
        }
    }
}