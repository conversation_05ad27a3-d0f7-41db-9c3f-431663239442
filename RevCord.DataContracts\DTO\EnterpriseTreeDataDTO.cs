﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.DTO
{
    //public class EnterpriseRecorderTreeDataDTO
    //{
    //    public int RecId { get; set; }
    //    public string RecName { get; set; }
    //    public EnterpriseTreeData EnterpriseTreeData { get; set; }
    //}

    public class EnterpriseRecorderTreeData
    {
        public int RecId { get; set; }
        public List<ModuleNode> ModuleNodes { get; set; }
    }

    public class ModuleNode
    {
        public ModuleBit ModuleBit { get; set; }
        public List<EnterpriseTreeData> TreeData { get; set; }
    }

    public class EnterpriseTreeData
    {
        public int RecId { get; set; }
        public ModuleBit ModuleBit { get; set; }
        public string NodeId { get; set; }
        public string NodeCaption { get; set; }
    }

    public enum ModuleBit : short
    {
        Unknown = 0,
        Setup = 1,
        IRLite = 2,
        Monitor = 3,
        Search = 4,
        Evaluation = 5,
        Dashboard = 6,
        QAEvaluationReports = 7,
        AdvancedReports = 8,
        IRFull = 9,
        SaveNEmail = 10,
    }

    public static class ModuleAccessRight
    {
        public const string Setup = "1000000000";
        public const string IRLite = "0100000000";
        public const string Monitor = "0010000000";
        public const string Search = "0001000000";
        public const string Evaluation = "0000100000";
        public const string Dashboard = "0000010000";
        public const string QAEvaluationReports = "0000001000";
        public const string AdvancedReports = "0000000100";
        public const string IRFull = "0000000010";
        public const string SaveNEmail = "0000000001";
    }

    public class EnterpriseNodeDTO
    {
        public int RecId { get; set; }
        public ModuleBit ModuleBit { get; set; }
        public int NodeId { get; set; }
        public bool DbStatus { get; set; }
    }

    public class EnterpriseGroupRightDTO
    {
        public int UserId { get; set; }
        public string Permission { get; set; }
        public List<EnterpriseRecorderGroup> EnterpriseRecorderGroups { get; set; }
    }

    public class EnterpriseRecorderGroup
    {
        public int RecId { get; set; }
        public int GroupId { get; set; }
        public bool IsActive { get; set; }
    }
}
