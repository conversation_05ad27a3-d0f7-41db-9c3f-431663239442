﻿<%@ Page Title="MMS::My WPQ" Language="C#" MasterPageFile="~/MasterPages/2.0/2Columns.Master" AutoEventWireup="true" CodeBehind="~/Reports/ContractReport.aspx.cs"
    Inherits="RevCord.VoiceRec.WebUIClient.Reports.ContractReport" %>

<%@ Import Namespace="RevCord.Util" %>
<%@ Register Assembly="Microsoft.ReportViewer.WebForms, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" Namespace="Microsoft.Reporting.WebForms" TagPrefix="rsweb" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphFilterContents" runat="server">
  <div>
    <div class="date-row">
        <label for="startDate">Start Date:</label>
        <input type="date" id="startDate" name="startDate" runat="server" onfocus="this.showPicker()" />

        <label for="endDate">End Date:</label>
        <input type="date" id="endDate" name="endDate" runat="server" onfocus="this.showPicker()" />

        <div id="isAdminView" runat="server" style="display: flex; align-items: center; gap: 10px;">
            <select id="ddlReportType" onchange="getUserListByUserType(this)" runat="server" class="ddlReportType" autopostback="false">
                <option value="0">Select Report</option>
                <option value="7">Contractor</option>
                <option value="8">Welder</option>
                <option value="6">Owner</option>
                <option value="11">Insurance</option>
                <option value="9">Testing</option>
            </select>

            <select id="reportUsers" runat="server" class="ddlSelectedUser" onchange="setSelectedUserType(this)">
            </select>

            <asp:HiddenField ID="hfSelectedUser" runat="server"/>
            <asp:HiddenField ID="hfSelectedReport" runat="server"/>
        </div>
    </div>

    <div id="validationMessage" style="color: red;"></div>

    <div class="btn-container">
        <asp:Button ID="btnGenerateReport" Text="Generate Report" ClientIDMode="Static" CssClass="btn btn-primary ml hide"
            runat="server" ValidationGroup="FormValidation"
            OnClick="btnGenerateReport_Click" OnClientClick="return validateReportForm();" />
    </div>
</div>

   <style>
    .date-row {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
    }

    .date-row label,
    .date-row input,
    .date-row select {
        margin-right: 10px;
    }

    .date-row input,
    .date-row select {
        height: 30px;
    }

    .btn-container {
        display: flex;
        align-items: center;
    }

    /* Ensure custom date display inputs match the original styling */
    input[type="text"][placeholder="mm-dd-yyyy"] {
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 14px;
        line-height: 1.42857143;
        color: #555;
        background-color: #fff;
        background-image: none;
        cursor: pointer;
    }

    input[type="text"][placeholder="mm-dd-yyyy"]:focus {
        border-color: #66afe9;
        outline: 0;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
    }

</style>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cphPageContents" runat="server">
    <asp:ScriptManager ID="ScriptManager1" runat="server" ScriptMode="Release" AsyncPostBackTimeout="56000"></asp:ScriptManager>

    <rsweb:ReportViewer ID="reportViewerExcel" runat="server" Width="100%" Height="100%"
        Font-Names="Verdana" Font-Size="8pt" AsyncRendering="False" ProcessingMode="Local">
        <LocalReport ReportPath="" EnableHyperlinks="true"></LocalReport>
    </rsweb:ReportViewer>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cphPageScripts" runat="server">
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/common.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/report.js") %>" type="text/javascript"></script>
    
    <script>
        const siteBaseUrl = "<%= SiteConfig.WebURL %>";
        getUserListByUserType(document.getElementById('<%= ddlReportType.ClientID %>'));

        // Function to format date from yyyy-mm-dd to mm-dd-yyyy
        function formatDateDisplay(dateValue) {
            if (!dateValue) return '';
            var parts = dateValue.split('-');
            if (parts.length === 3) {
                return parts[1] + '-' + parts[2] + '-' + parts[0]; // mm-dd-yyyy
            }
            return dateValue;
        }

        // Function to format date from mm-dd-yyyy back to yyyy-mm-dd for form submission
        function formatDateForSubmission(dateValue) {
            if (!dateValue) return '';
            var parts = dateValue.split('-');
            if (parts.length === 3 && parts[0].length === 2) {
                return parts[2] + '-' + parts[0] + '-' + parts[1]; // yyyy-mm-dd
            }
            return dateValue;
        }

        // Initialize date format display on page load
        document.addEventListener('DOMContentLoaded', function() {
            var startDateInput = document.getElementById('<%= startDate.ClientID %>');
            var endDateInput = document.getElementById('<%= endDate.ClientID %>');

            // Set up custom display for date inputs
            setupDateFormatting(startDateInput);
            setupDateFormatting(endDateInput);
        });

        function setupDateFormatting(dateInput) {
            if (!dateInput) return;

            // Create a display input that shows the formatted date
            var displayInput = document.createElement('input');
            displayInput.type = 'text';
            displayInput.placeholder = 'mm-dd-yyyy';
            displayInput.style.cssText = dateInput.style.cssText;
            displayInput.className = dateInput.className;

            // Hide the original date input
            dateInput.style.display = 'none';

            // Insert the display input after the original
            dateInput.parentNode.insertBefore(displayInput, dateInput.nextSibling);

            // Update display when original input changes
            dateInput.addEventListener('change', function() {
                displayInput.value = formatDateDisplay(this.value);
            });

            // Handle manual input in display field
            displayInput.addEventListener('blur', function() {
                var formattedValue = formatDateForSubmission(this.value);
                if (isValidDate(formattedValue)) {
                    dateInput.value = formattedValue;
                } else if (this.value) {
                    // Try to parse various date formats
                    var parsedDate = parseFlexibleDate(this.value);
                    if (parsedDate) {
                        dateInput.value = parsedDate;
                        this.value = formatDateDisplay(parsedDate);
                    }
                }
            });

            // Show date picker when clicking on display input
            displayInput.addEventListener('click', function() {
                dateInput.focus();
                dateInput.click();
            });

            // Initialize display if there's already a value
            if (dateInput.value) {
                displayInput.value = formatDateDisplay(dateInput.value);
            }
        }

        function isValidDate(dateString) {
            var date = new Date(dateString);
            return date instanceof Date && !isNaN(date);
        }

        function parseFlexibleDate(input) {
            if (!input) return null;

            // Try mm-dd-yyyy format
            var parts = input.split('-');
            if (parts.length === 3) {
                var month = parseInt(parts[0]);
                var day = parseInt(parts[1]);
                var year = parseInt(parts[2]);

                if (month >= 1 && month <= 12 && day >= 1 && day <= 31 && year >= 1900) {
                    var date = new Date(year, month - 1, day);
                    if (date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day) {
                        return year + '-' + String(month).padStart(2, '0') + '-' + String(day).padStart(2, '0');
                    }
                }
            }

            return null;
        }

        function validateReportForm() {
            var reportType = document.getElementById('<%= ddlReportType.ClientID %>');
            var reportUsers = document.getElementById('<%= reportUsers.ClientID %>');
            var startDate = document.getElementById('<%= startDate.ClientID %>');
            var endDate = document.getElementById('<%= endDate.ClientID %>');
            var errorDiv = document.getElementById("validationMessage");

            errorDiv.innerHTML = "";

            if (!reportType || reportType.value === "0") {
                errorDiv.innerHTML = "Please select a Report Type.";
                reportType.focus();
                return false;
            }

            if (!startDate || startDate.value === "") {
                errorDiv.innerHTML = "Please select a Start Date.";
                startDate.focus();
                return false;
            }

            if (!endDate || endDate.value === "") {
                errorDiv.innerHTML = "Please select an End Date.";
                endDate.focus();
                return false;
            }

            if (new Date(startDate.value) > new Date(endDate.value)) {
                errorDiv.innerHTML = "Start Date cannot be after End Date.";
                startDate.focus();
                return false;
            }

            return true;
        }
    </script>
</asp:Content>

