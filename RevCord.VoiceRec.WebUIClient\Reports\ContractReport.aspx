<%@ Page Title="MMS::My WPQ" Language="C#" MasterPageFile="~/MasterPages/2.0/2Columns.Master" AutoEventWireup="true" CodeBehind="~/Reports/ContractReport.aspx.cs"
    Inherits="RevCord.VoiceRec.WebUIClient.Reports.ContractReport" %>

<%@ Import Namespace="RevCord.Util" %>
<%@ Register Assembly="Microsoft.ReportViewer.WebForms, Version=12.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" Namespace="Microsoft.Reporting.WebForms" TagPrefix="rsweb" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphFilterContents" runat="server">
  <div>
    <div class="date-row">
        <label for="startDate">Start Date:</label>
        <input type="date" id="startDate" name="startDate" runat="server" onfocus="this.showPicker()" />

        <label for="endDate">End Date:</label>
        <input type="date" id="endDate" name="endDate" runat="server" onfocus="this.showPicker()" />

        <div id="isAdminView" runat="server" style="display: flex; align-items: center; gap: 10px;">
            <select id="ddlReportType" onchange="getUserListByUserType(this)" runat="server" class="ddlReportType" autopostback="false">
                <option value="0">Select Report</option>
                <option value="7">Contractor</option>
                <option value="8">Welder</option>
                <option value="6">Owner</option>
                <option value="11">Insurance</option>
                <option value="9">Testing</option>
            </select>

            <select id="reportUsers" runat="server" class="ddlSelectedUser" onchange="setSelectedUserType(this)">
            </select>

            <asp:HiddenField ID="hfSelectedUser" runat="server"/>
            <asp:HiddenField ID="hfSelectedReport" runat="server"/>
        </div>
    </div>

    <div id="validationMessage" style="color: red;"></div>

    <div class="btn-container">
        <asp:Button ID="btnGenerateReport" Text="Generate Report" ClientIDMode="Static" CssClass="btn btn-primary ml hide"
            runat="server" ValidationGroup="FormValidation"
            OnClick="btnGenerateReport_Click" OnClientClick="return validateReportForm();" />
    </div>
</div>

   <style>
    .date-row {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
    }

    .date-row label,
    .date-row input,
    .date-row select {
        margin-right: 10px;
    }

    .date-row input,
    .date-row select {
        height: 30px;
    }

    .btn-container {
        display: flex;
        align-items: center;
    }

    /* Style for the date display overlay */
    .date-display {
        font-family: inherit;
    }

</style>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cphPageContents" runat="server">
    <asp:ScriptManager ID="ScriptManager1" runat="server" ScriptMode="Release" AsyncPostBackTimeout="56000"></asp:ScriptManager>

    <rsweb:ReportViewer ID="reportViewerExcel" runat="server" Width="100%" Height="100%"
        Font-Names="Verdana" Font-Size="8pt" AsyncRendering="False" ProcessingMode="Local">
        <LocalReport ReportPath="" EnableHyperlinks="true"></LocalReport>
    </rsweb:ReportViewer>
</asp:Content>
<asp:Content ID="Content4" ContentPlaceHolderID="cphPageScripts" runat="server">
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/common.js") %>" type="text/javascript"></script>
    <script src="<%= Page.ResolveClientUrl("~/assets/js/pages/report.js") %>" type="text/javascript"></script>
    
    <script>
        const siteBaseUrl = "<%= SiteConfig.WebURL %>";
        getUserListByUserType(document.getElementById('<%= ddlReportType.ClientID %>'));

        // Simple function to format date display only
        function formatDateToDisplay(dateInput) {
            if (!dateInput || !dateInput.value) return;

            var parts = dateInput.value.split('-'); // yyyy-mm-dd
            if (parts.length === 3) {
                var formattedDate = parts[1] + '-' + parts[2] + '-' + parts[0]; // mm-dd-yyyy

                // Create a temporary span to show the formatted date
                var span = dateInput.nextElementSibling;
                if (!span || !span.classList.contains('date-display')) {
                    span = document.createElement('span');
                    span.className = 'date-display';
                    span.style.position = 'absolute';
                    span.style.left = '8px';
                    span.style.top = '50%';
                    span.style.transform = 'translateY(-50%)';
                    span.style.pointerEvents = 'none';
                    span.style.color = '#555';
                    span.style.fontSize = '14px';
                    span.style.backgroundColor = 'white';
                    span.style.padding = '0 2px';

                    // Make the parent container relative
                    dateInput.parentNode.style.position = 'relative';
                    dateInput.parentNode.style.display = 'inline-block';

                    dateInput.parentNode.appendChild(span);
                }
                span.textContent = formattedDate;

                // Hide the span when the input is focused (to show native date picker)
                dateInput.addEventListener('focus', function() {
                    span.style.display = 'none';
                });

                dateInput.addEventListener('blur', function() {
                    if (this.value) {
                        span.style.display = 'block';
                        formatDateToDisplay(this);
                    } else {
                        span.style.display = 'none';
                    }
                });
            }
        }

        // Initialize date formatting on page load
        document.addEventListener('DOMContentLoaded', function() {
            var startDateInput = document.getElementById('<%= startDate.ClientID %>');
            var endDateInput = document.getElementById('<%= endDate.ClientID %>');

            if (startDateInput) {
                startDateInput.addEventListener('change', function() {
                    formatDateToDisplay(this);
                });
                formatDateToDisplay(startDateInput); // Format if already has value
            }

            if (endDateInput) {
                endDateInput.addEventListener('change', function() {
                    formatDateToDisplay(this);
                });
                formatDateToDisplay(endDateInput); // Format if already has value
            }
        });

        function validateReportForm() {
            var reportType = document.getElementById('<%= ddlReportType.ClientID %>');
            var reportUsers = document.getElementById('<%= reportUsers.ClientID %>');
            var startDate = document.getElementById('<%= startDate.ClientID %>');
            var endDate = document.getElementById('<%= endDate.ClientID %>');
            var errorDiv = document.getElementById("validationMessage");

            errorDiv.innerHTML = "";

            if (!reportType || reportType.value === "0") {
                errorDiv.innerHTML = "Please select a Report Type.";
                reportType.focus();
                return false;
            }

            if (!startDate || startDate.value === "") {
                errorDiv.innerHTML = "Please select a Start Date.";
                startDate.focus();
                return false;
            }

            if (!endDate || endDate.value === "") {
                errorDiv.innerHTML = "Please select an End Date.";
                endDate.focus();
                return false;
            }

            if (new Date(startDate.value) > new Date(endDate.value)) {
                errorDiv.innerHTML = "Start Date cannot be after End Date.";
                startDate.focus();
                return false;
            }

            return true;
        }
    </script>
</asp:Content>

