﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.FileSystem</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeFileHandle">
      <summary>表示文件句柄的包装类。</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeFileHandle.#ctor(System.IntPtr,System.Boolean)">
      <summary>初始化 <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> 类的新实例。</summary>
      <param name="preexistingHandle">
        <see cref="T:System.IntPtr" /> 对象，表示要使用的预先存在的句柄。</param>
      <param name="ownsHandle">如果为 true，则在完成阶段可靠地释放句柄；如果为 false，则阻止可靠释放（建议不要这样做）。</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeFileHandle.IsInvalid"></member>
    <member name="T:System.IO.Directory">
      <summary>公开用于通过目录和子目录进行创建、移动和枚举的静态方法。此类不能被继承。若要浏览此类型的.NET Framework 源代码，请参阅参考源。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Directory.CreateDirectory(System.String)">
      <summary>在指定路径中创建所有目录和子目录，除非它们已经存在。</summary>
      <returns>一个表示在指定路径的目录的对象。无论指定路径的目录是否已经存在，都将返回此对象。</returns>
      <param name="path">要创建的目录。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 指定的目录是个文件。- 或 -网络名称未知。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 -<paramref name="path" /> 带有冒号字符 (:) 前缀，或仅包含一个冒号字符 (:)。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 包含一个冒号字符 (:)，该冒号字符不是驱动器标签（“C:\”）的一部分。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String)">
      <summary>从指定路径删除空目录。</summary>
      <param name="path">要移除的空目录的名称。此目录必须可写且为空。</param>
      <exception cref="T:System.IO.IOException">存在具有相同名称和 <paramref name="path" /> 指定的位置的文件。- 或 -该目录为应用程序的当前工作目录。- 或 -由 <paramref name="path" /> 指定的目录不为空。- 或 -该目录是只读的，或者包含只读文件。- 或 -该目录正被另一个进程使用。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 不存在或未能找到。- 或 -指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Delete(System.String,System.Boolean)">
      <summary>删除指定的目录并（如果指示）删除该目录中的所有子目录和文件。</summary>
      <param name="path">要移除的目录的名称。 </param>
      <param name="recursive">若要移除 <paramref name="path" /> 中的目录、子目录和文件，则为 true；否则为 false。</param>
      <exception cref="T:System.IO.IOException">存在具有相同名称和 <paramref name="path" /> 指定的位置的文件。- 或 -<paramref name="path" /> 指定的目录是只读的，或者 <paramref name="recursive" /> 是 false 并且 <paramref name="path" /> 不是空目录。- 或 -该目录为应用程序的当前工作目录。- 或 -目录中包含一个只读文件。- 或 -该目录正被另一个进程使用。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 不存在或未能找到。- 或 -指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String)">
      <summary>返回指定路径中的目录名的可枚举集合。</summary>
      <returns>一个可枚举集合，它包含 <paramref name="path" /> 指定的目录中的目录的完整名称（包括路径）。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />是一个零长度字符串、仅包含空白或者包含无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效，比如引用未映射的驱动器。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String)">
      <summary>返回指定路径中与搜索模式匹配的目录名的可枚举集合。</summary>
      <returns>一个可枚举集合，它包含 <paramref name="path" /> 指定的目录中与指定的搜索模式匹配的目录的完整名称（包括路径）。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的目录名称匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />是一个零长度字符串、仅包含空白或者包含无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 -<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。- 或 -<paramref name="searchPattern" /> 为 null。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效，比如引用未映射的驱动器。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>返回指定路径中与搜索模式匹配的目录名称的可枚举集合，还可以搜索子目录。</summary>
      <returns>一个可枚举集合，它包含 <paramref name="path" /> 指定的目录中与指定的搜索模式和选项匹配的目录的完整名称（包括路径）。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的目录名称匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。</param>
      <param name="searchOption">指定搜索操作是应仅包含当前目录还是应包含所有子目录的枚举值之一。默认值为 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />是一个零长度字符串、仅包含空白或者包含无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 -<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。- 或 -<paramref name="searchPattern" /> 为 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效，比如引用未映射的驱动器。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String)">
      <summary>返回指定路径中的文件名的可枚举集合。</summary>
      <returns>一个可枚举集合，它包含 <paramref name="path" /> 指定的目录中的文件的完整名称（包括路径）。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />是一个零长度字符串、仅包含空白或者包含无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效，比如引用未映射的驱动器。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String)">
      <summary>返回指定路径中与搜索模式匹配的文件名称的可枚举集合。</summary>
      <returns>一个可枚举集合，它包含 <paramref name="path" /> 指定的目录中与指定的搜索模式匹配的文件的完整名称（包括路径）。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的文件名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />是一个零长度字符串、仅包含空白或者包含无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 -<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。- 或 -<paramref name="searchPattern" /> 为 null。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效，比如引用未映射的驱动器。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>返回指定路径中与搜索模式匹配的文件名称的可枚举集合，还可以搜索子目录。</summary>
      <returns>一个可枚举集合，它包含 <paramref name="path" /> 指定的目录中与指定的搜索模式和选项匹配的文件的完整名称（包括路径）。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的文件名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。</param>
      <param name="searchOption">指定搜索操作是应仅包含当前目录还是应包含所有子目录的枚举值之一。默认值为 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />是一个零长度字符串、仅包含空白或者包含无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 -<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。- 或 -<paramref name="searchPattern" /> 为 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效，比如引用未映射的驱动器。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String)">
      <summary>返回指定路径中的文件名和目录名的可枚举集合。</summary>
      <returns>由 <paramref name="path" /> 指定的目录中的文件系统项的可枚举集合。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />是一个零长度字符串、仅包含空白或者包含无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效，比如引用未映射的驱动器。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String)">
      <summary>返回指定路径中与搜索模式匹配的文件名和目录名的可枚举集合。</summary>
      <returns>由 <paramref name="path" /> 指定的目录中与指定搜索模式匹配的文件系统项的可枚举集合。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的文件系统项的名称匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />是一个零长度字符串、仅包含空白或者包含无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 -<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。- 或 -<paramref name="searchPattern" /> 为 null。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效，比如引用未映射的驱动器。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.Directory.EnumerateFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>返回指定路径中与搜索模式匹配的文件名称和目录名的可枚举集合，还可以搜索子目录。</summary>
      <returns>由 <paramref name="path" /> 指定的目录中与指定搜索模式和选项匹配的文件系统项的可枚举集合。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的文件系统项匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。</param>
      <param name="searchOption">指定搜索操作是应仅包含当前目录还是应包含所有子目录的枚举值之一。默认值为 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />是一个零长度字符串、仅包含空白或者包含无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 -<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。- 或 -<paramref name="searchPattern" /> 为 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效，比如引用未映射的驱动器。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.Directory.Exists(System.String)">
      <summary>确定给定路径是否引用磁盘上的现有目录。</summary>
      <returns>如果 <paramref name="path" /> 指向现有目录，则为 true；如果该目录不存在或者在确定指定文件是否存在时出错，则为 false。</returns>
      <param name="path">要测试的路径。 </param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTime(System.String)">
      <summary>获取目录的创建日期和时间。</summary>
      <returns>一个设置为指定目录的创建日期和时间的结构。该值用本地时间表示。</returns>
      <param name="path">目录的路径。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCreationTimeUtc(System.String)">
      <summary>获取目录创建的日期和时间，其格式为协调通用时 (UTC)。</summary>
      <returns>一个设置为指定目录的创建日期和时间的结构。该值用 UTC 时间表示。</returns>
      <param name="path">目录的路径。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetCurrentDirectory">
      <summary>获取应用程序的当前工作目录。</summary>
      <returns>包含当前工作目录的路径且不以反斜杠 (\) 结尾的字符串。</returns>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">操作系统为 Windows CE，该系统不具有当前目录功能。此方法在 .NET Compact Framework 中可用，但是当前并不支持。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String)">
      <summary>返回指定目录中的子目录的名称（包括其路径）。</summary>
      <returns>指定路径中子目录的完整名称（包含路径）的数组；如果未找到任何目录，则为空数组。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String)">
      <summary>返回指定目录中与指定的搜索模式匹配的子目录的名称（包括其路径）。</summary>
      <returns>指定目录中与搜索模式匹配的子目录的完整名称（包含路径）的数组；如果未找到任何文件，则为空数组。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的子目录的名称匹配的搜索字符串。此参数可以包含有效文本和通配符的组合（请参见“备注”），但不支持正则表达式。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 来查询无效字符。- 或 - <paramref name="searchPattern" /> 不包含有效模式。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 或 <paramref name="searchPattern" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetDirectories(System.String,System.String,System.IO.SearchOption)">
      <summary>返回与在指定目录中的指定搜索模式匹配的子目录的名称（包括其路径），还可以选择地搜索子目录。</summary>
      <returns>与指定条件匹配的子目录的完整名称（包含路径）的数组；如果未找到任何目录，则为空数组。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的子目录的名称匹配的搜索字符串。此参数可以包含有效文本和通配符的组合（请参见“备注”），但不支持正则表达式。</param>
      <param name="searchOption">用于指定搜索操作是应包含所有子目录还是仅包含当前目录的枚举值之一。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 - <paramref name="searchPattern" /> 不包含有效模式。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 或 <paramref name="searchPattern" /> 为 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
    </member>
    <member name="M:System.IO.Directory.GetDirectoryRoot(System.String)">
      <summary>返回指定路径的卷信息、根信息或两者同时返回。</summary>
      <returns>包含指定路径的卷信息、根信息或同时包括这两者的字符串。</returns>
      <param name="path">文件或目录的路径。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String)">
      <summary>返回指定目录中文件的名称（包括其路径）。</summary>
      <returns>一个包含指定目录中的文件的完整名称（包含路径）的数组，如果未找到任何文件，则为空数组。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。- 或 -发生了网络错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径找不到或者无效（例如，它位于未映射的驱动器上）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String)">
      <summary>返回指定目录中与指定的搜索模式匹配的文件的名称（包含其路径）。</summary>
      <returns>指定目录中与指定的搜索模式匹配的文件的完整名称（包含路径）的数组；如果未找到任何文件，则为空数组。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的文件名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。- 或 -发生了网络错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 来查询无效字符。- 或 - <paramref name="searchPattern" /> 不包含有效模式。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 或 <paramref name="searchPattern" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径找不到或者无效（例如，它位于未映射的驱动器上）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFiles(System.String,System.String,System.IO.SearchOption)">
      <summary>返回指定目录中与指定的搜索模式匹配的文件的名称（包含其路径），使用某个值确定是否要搜索子目录。</summary>
      <returns>指定目录中与指定的搜索模式和选项匹配的文件的完整名称（包含路径）的数组；如果未找到任何文件，则为空数组。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的文件名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。</param>
      <param name="searchOption">用于指定搜索操作是应包含所有子目录还是仅包含当前目录的枚举值之一。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 - <paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 或 <paramref name="searchpattern" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径找不到或者无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。- 或 -发生了网络错误。</exception>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String)">
      <summary>返回指定路径中的所有文件和子目录的名称。</summary>
      <returns>指定目录中的文件和子目录的名称的数组；如果找不到任何文件或子目录，则为空数组。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String)">
      <summary>返回与指定路径中搜索模式匹配的文件名和目录名的数组。</summary>
      <returns>与指定的搜索条件匹配的文件名和目录名的数组；如果找不到任何文件或目录，则为空数组。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的文件和目录的名称匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 - <paramref name="searchPattern" /> 不包含有效模式。 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 或 <paramref name="searchPattern" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetFileSystemEntries(System.String,System.String,System.IO.SearchOption)">
      <summary>返回指定路径中与搜索模式匹配的所有文件名和目录名的数组，还可以搜索子目录。</summary>
      <returns>与指定的搜索条件匹配的文件名和目录名的数组；如果找不到任何文件或目录，则为空数组。</returns>
      <param name="path">要搜索的目录的相对或绝对路径。此字符串不区分大小写。</param>
      <param name="searchPattern">要与 <paramref name="path" /> 中的文件和目录的名称匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。</param>
      <param name="searchOption">指定搜索操作是应仅包含当前目录还是应包含所有子目录的枚举值之一。默认值为 <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path " />是一个零长度字符串、仅包含空白或者包含无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。- 或 -<paramref name="searchPattern" /> 不包含有效模式。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。- 或 -<paramref name="searchPattern" /> 为 null。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" /> 不是有效的 <see cref="T:System.IO.SearchOption" /> 值。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效，比如引用未映射的驱动器。 </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 是一个文件名。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTime(System.String)">
      <summary>返回上次访问指定文件或目录的日期和时间。</summary>
      <returns>一个结构，它被设置为上次访问指定文件或目录的日期和时间。该值用本地时间表示。</returns>
      <param name="path">要获取其访问日期和时间信息的文件或目录。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 参数的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastAccessTimeUtc(System.String)">
      <summary>返回上次访问指定文件或目录的日期和时间，其格式为协调通用时 (UTC)。</summary>
      <returns>一个结构，它被设置为上次访问指定文件或目录的日期和时间。该值用 UTC 时间表示。</returns>
      <param name="path">要获取其访问日期和时间信息的文件或目录。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 参数的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTime(System.String)">
      <summary>返回上次写入指定文件或目录的日期和时间。</summary>
      <returns>一个结构，它被设置为上次写入指定文件或目录的日期和时间。该值用本地时间表示。</returns>
      <param name="path">要获取其修改日期和时间信息的文件或目录。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetLastWriteTimeUtc(System.String)">
      <summary>返回上次写入指定文件或目录的日期和时间，其格式为协调通用时 (UTC)。</summary>
      <returns>一个结构，它被设置为上次写入指定文件或目录的日期和时间。该值用 UTC 时间表示。</returns>
      <param name="path">要获取其修改日期和时间信息的文件或目录。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.GetParent(System.String)">
      <summary>检索指定路径的父目录，包括绝对路径和相对路径。</summary>
      <returns>父目录；或者如果 <paramref name="path" /> 是根目录，包括 UNC 服务器或共享名的根，则为 null。</returns>
      <param name="path">用于检索父目录的路径。</param>
      <exception cref="T:System.IO.IOException">
        <paramref name="path" /> 指定的目录是只读的。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">未找到指定的路径。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.Move(System.String,System.String)">
      <summary>将文件或目录及其内容移到新位置。</summary>
      <param name="sourceDirName">要移动的文件或目录的路径。</param>
      <param name="destDirName">指向 <paramref name="sourceDirName" /> 的新位置的路径。如果 <paramref name="sourceDirName" /> 是一个文件，则 <paramref name="destDirName" /> 也必须是一个文件名。</param>
      <exception cref="T:System.IO.IOException">试图将一个目录移到不同的卷。- 或 - <paramref name="destDirName" /> 已存在。- 或 -<paramref name="sourceDirName" /> 和 <paramref name="destDirName" /> 参数引用相同的文件或目录。- 或 -另一个进程正在使用的目录或在其中一个文件。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirName" /> or <paramref name="destDirName" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirName" /> 或 <paramref name="destDirName" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">由 <paramref name="sourceDirName" /> 指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTime(System.String,System.DateTime)">
      <summary>为指定的文件或目录设置创建日期和时间。</summary>
      <param name="path">要设置其创建日期和时间信息的文件或目录。</param>
      <param name="creationTime">上次写入到文件或目录的日期和时间。该值用本地时间表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> 指定超出该操作允许的日期或时间范围的值。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>设置指定文件或目录的创建日期和时间，其格式为协调通用时 (UTC)。</summary>
      <param name="path">要设置其创建日期和时间信息的文件或目录。</param>
      <param name="creationTimeUtc">目录或文件的创建日期和时间。该值用本地时间表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> 指定超出该操作允许的日期或时间范围的值。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetCurrentDirectory(System.String)">
      <summary>将应用程序的当前工作目录设置为指定的目录。</summary>
      <param name="path">设置为当前工作目录的路径。</param>
      <exception cref="T:System.IO.IOException">发生了 I/O 错误。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有访问未委托的代码所需的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">未找到指定的目录。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTime(System.String,System.DateTime)">
      <summary>设置上次访问指定文件或目录的日期和时间。</summary>
      <param name="path">要设置其访问日期和时间信息的文件或目录。</param>
      <param name="lastAccessTime">一个对象，它包含要为 <paramref name="path" /> 的访问日期和时间设置的值。该值用本地时间表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> 指定超出该操作允许的日期或时间范围的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>设置上次访问指定文件或目录的日期和时间，其格式为协调通用时 (UTC)。</summary>
      <param name="path">要设置其访问日期和时间信息的文件或目录。</param>
      <param name="lastAccessTimeUtc">一个对象，它包含要为 <paramref name="path" /> 的访问日期和时间设置的值。该值用 UTC 时间表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> 指定超出该操作允许的日期或时间范围的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTime(System.String,System.DateTime)">
      <summary>设置上次写入目录的日期和时间。</summary>
      <param name="path">目录的路径。</param>
      <param name="lastWriteTime">上次写入目录的日期和时间。该值用本地时间表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> 指定超出该操作允许的日期或时间范围的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Directory.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>设置上次写入某个目录的日期和时间，其格式为协调通用时 (UTC)。</summary>
      <param name="path">目录的路径。</param>
      <param name="lastWriteTimeUtc">上次写入目录的日期和时间。该值用 UTC 时间表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个无效字符。可以使用 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法来查询无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> 指定超出该操作允许的日期或时间范围的值。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.DirectoryInfo">
      <summary>公开用于通过目录和子目录进行创建、移动和枚举的实例方法。此类不能被继承。若要浏览此类型的.NET Framework 源代码，请参阅
                                引用源.
                            </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.#ctor(System.String)">
      <summary>初始化
<see cref="T:System.IO.DirectoryInfo" />指定路径上的类。
                            </summary>
      <param name="path">一个字符串，指定在其上创建路径
                                    DirectoryInfo.
                                </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />是
                                        null.
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 包含无效字符，如 "、&lt;、&gt; 或 |.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。指定的路径或文件名太长，或者两者都太长。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.Create">
      <summary>创建目录。</summary>
      <exception cref="T:System.IO.IOException">不能创建该目录。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.CreateSubdirectory(System.String)">
      <summary>在指定路径上创建一个或多个子目录。指定的路径可以是相对于此实例
<see cref="T:System.IO.DirectoryInfo" />类。
                        </summary>
      <returns>中指定的最后一个目录
                                <paramref name="path" />.
                            </returns>
      <param name="path">指定的路径。它不能是另一个磁盘卷或通用命名约定 (UNC) 名称。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" />未指定有效的文件路径或包含无效
DirectoryInfo字符。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.IO.IOException">不能创建子目录。- 或 -文件或目录已具有指定的名称
                                        <paramref name="path" />.
                                    </exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。指定的路径或文件名太长，或者两者都太长。</exception>
      <exception cref="T:System.Security.SecurityException">调用方不具有创建目录的代码访问权限。- 或 -调用方没有代码访问权限以读取所描述的返回目录
<see cref="T:System.IO.DirectoryInfo" /> 对象。
                                发生这种情况时
<paramref name="path" />参数描述的现有目录。
                                </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 包含一个冒号字符 (:)，该冒号字符不是驱动器标签（“C:\”）的一部分。
                                    </exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete">
      <summary>这将删除
<see cref="T:System.IO.DirectoryInfo" />如果它为空。
                            </summary>
      <exception cref="T:System.UnauthorizedAccessException">目录中包含一个只读文件。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">此描述的目录
<see cref="T:System.IO.DirectoryInfo" />对象不存在或找不到。
                                    </exception>
      <exception cref="T:System.IO.IOException">目录不为空。- 或 -该目录为应用程序的当前工作目录。- 或 -对于目录有打开句柄，并且操作系统是 Windows XP 或更早版本。此打开句柄可能是由于枚举目录导致的。有关详细信息，请参阅
                                    如何：枚举目录和文件.
                                </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.Delete(System.Boolean)">
      <summary>删除此实例
<see cref="T:System.IO.DirectoryInfo" />指定是否删除子目录和文件。
                            </summary>
      <param name="recursive">true若要删除此目录、 及其子目录中，和所有文件 ；否则为
                                    false.
                                </param>
      <exception cref="T:System.UnauthorizedAccessException">目录中包含一个只读文件。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">此描述的目录
<see cref="T:System.IO.DirectoryInfo" />对象不存在或找不到。
                                    </exception>
      <exception cref="T:System.IO.IOException">目录为只读。- 或 -该目录包含一个或多个文件或子目录和
<paramref name="recursive" />是
                                        false.
                                    - 或 -该目录为应用程序的当前工作目录。- 或 -对于目录或其文件之一有打开句柄，并且操作系统是 Windows XP 或更早版本。此打开句柄可能是由于枚举目录和文件导致的。有关详细信息，请参阅
                                    如何：枚举目录和文件.
                                </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories">
      <summary>返回当前目录中目录信息的可枚举集合。</summary>
      <returns>当前目录中目录的可枚举集合。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
<see cref="T:System.IO.DirectoryInfo" />对象无效 （例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String)">
      <summary>返回与指定的搜索模式匹配的目录信息的可枚举集合。</summary>
      <returns>匹配的目录的可枚举集合
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">要与目录名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
<see cref="T:System.IO.DirectoryInfo" />对象无效 （例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateDirectories(System.String,System.IO.SearchOption)">
      <summary>返回与指定的搜索模式和搜索子目录选项匹配的目录信息的可枚举集合。</summary>
      <returns>匹配的目录的可枚举集合
<paramref name="searchPattern" />和
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">要与目录名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <param name="searchOption">指定搜索操作是应仅包含当前目录还是应包含所有子目录的枚举值之一。默认值是
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" />值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
<see cref="T:System.IO.DirectoryInfo" />对象无效 （例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles">
      <summary>返回当前目录中的文件信息的可枚举集合。</summary>
      <returns>当前目录中的文件的可枚举集合。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
<see cref="T:System.IO.DirectoryInfo" />对象无效 （例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String)">
      <summary>返回与搜索模式匹配的文件信息的可枚举集合。</summary>
      <returns>匹配的文件的可枚举集合
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">要与文件名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
<see cref="T:System.IO.DirectoryInfo" />对象无效，（例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFiles(System.String,System.IO.SearchOption)">
      <summary>返回与指定的搜索模式和搜索子目录选项匹配的文件信息的可枚举集合。</summary>
      <returns>匹配的文件的可枚举集合
<paramref name="searchPattern" />和
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">要与文件名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <param name="searchOption">指定搜索操作是应仅包含当前目录还是应包含所有子目录的枚举值之一。默认值是
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" />值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
<see cref="T:System.IO.DirectoryInfo" />对象无效 （例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos">
      <summary>返回当前目录中的文件系统信息的可枚举集合。</summary>
      <returns>当前目录中的文件系统信息的可枚举集合。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
<see cref="T:System.IO.DirectoryInfo" />对象无效 （例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String)">
      <summary>返回与指定的搜索模式匹配的文件系统信息的可枚举集合。</summary>
      <returns>匹配的文件系统信息对象的可枚举集合
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">要与目录名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
<see cref="T:System.IO.DirectoryInfo" />对象无效 （例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.EnumerateFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>返回与指定的搜索模式和搜索子目录选项匹配的文件系统信息的可枚举集合。</summary>
      <returns>匹配的文件系统信息对象的可枚举集合
<paramref name="searchPattern" />和
                                <paramref name="searchOption" />.
                            </returns>
      <param name="searchPattern">要与目录名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <param name="searchOption">指定搜索操作是应仅包含当前目录还是应包含所有子目录的枚举值之一。默认值是
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" />值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
<see cref="T:System.IO.DirectoryInfo" />对象无效 （例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="P:System.IO.DirectoryInfo.Exists">
      <summary>获取指示目录是否存在的值。</summary>
      <returns>true如果该目录存在） ；否则为
                                false.
                            </returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories">
      <summary>返回当前目录的子目录。</summary>
      <returns>一个数组
<see cref="T:System.IO.DirectoryInfo" /> 对象。
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
<see cref="T:System.IO.DirectoryInfo" />对象是无效的例如，位于未映射的驱动器上。
                                    </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String)">
      <summary>返回一个数组中当前的目录
<see cref="T:System.IO.DirectoryInfo" />匹配给定的搜索条件。
                            </summary>
      <returns>类型的数组
DirectoryInfo匹配
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">要与目录名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含由定义的一个或多个无效字符
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
DirectoryInfo对象无效 （例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetDirectories(System.String,System.IO.SearchOption)">
      <summary>返回一个数组中当前的目录
<see cref="T:System.IO.DirectoryInfo" />与给定的搜索条件相匹配，并使用一个值以确定是否搜索子目录。
                            </summary>
      <returns>类型的数组
DirectoryInfo匹配
                                <paramref name="searchPattern" />.
                            </returns>
      <param name="searchPattern">要与目录名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <param name="searchOption">指定搜索操作是应仅包含当前目录还是应包含所有子目录的枚举值之一。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含由定义的一个或多个无效字符
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" />值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径封装在
DirectoryInfo对象无效 （例如，它位于未映射的驱动器上）。
                                    </exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles">
      <summary>返回当前目录的文件列表。</summary>
      <returns>类型的数组
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">该路径无效，比如在未映射的驱动器上。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String)">
      <summary>返回当前目录中与给定的搜索模式匹配的文件列表。</summary>
      <returns>类型的数组
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">要与文件名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含由定义的一个或多个无效字符
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">路径无效（例如，在未映射的驱动器上）。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFiles(System.String,System.IO.SearchOption)">
      <summary>返回当前目录的文件列表，该列表与给定的搜索模式匹配并且使用某个值确定是否搜索子目录。</summary>
      <returns>类型的数组
                                <see cref="T:System.IO.FileInfo" />.
                            </returns>
      <param name="searchPattern">要与文件名匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <param name="searchOption">指定搜索操作是应仅包含当前目录还是应包含所有子目录的枚举值之一。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含由定义的一个或多个无效字符
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" />值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">路径无效（例如，在未映射的驱动器上）。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos">
      <summary>返回强类型的数组
<see cref="T:System.IO.FileSystemInfo" />表示所有文件和目录的子目录中的项。
                            </summary>
      <returns>强类型的数组
<see cref="T:System.IO.FileSystemInfo" />条目。
                            </returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">路径无效（例如，在未映射的驱动器上）。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String)">
      <summary>检索数组的强类型化
<see cref="T:System.IO.FileSystemInfo" />对象表示的文件和与指定的搜索条件匹配的子目录。
                            </summary>
      <returns>强类型的数组
FileSystemInfo与搜索条件匹配的对象。
                            </returns>
      <param name="searchPattern">要与目录和文件的名称匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含由定义的一个或多个无效字符
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.GetFileSystemInfos(System.String,System.IO.SearchOption)">
      <summary>检索的数组
<see cref="T:System.IO.FileSystemInfo" />表示的文件和子目录的指定的搜索条件匹配的对象。
                            </summary>
      <returns>与搜索条件匹配的文件系统项的数组。</returns>
      <param name="searchPattern">要与目录和文件的名称匹配的搜索字符串。此参数可以包含有效文本路径和通配符（* 和 ?）的组合（请参见“备注”），但不支持正则表达式。默认模式为“*”，该模式返回所有文件。</param>
      <param name="searchOption">指定搜索操作是应仅包含当前目录还是应包含所有子目录的枚举值之一。默认值是
                                <see cref="F:System.IO.SearchOption.TopDirectoryOnly" />.
                            </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="searchPattern" />包含由定义的一个或多个无效字符
<see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。
                                    </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="searchPattern" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="searchOption" />不是有效
<see cref="T:System.IO.SearchOption" />值。
                                    </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.DirectoryInfo.MoveTo(System.String)">
      <summary>将移动
<see cref="T:System.IO.DirectoryInfo" />实例，并且其内容进行新的路径。
                            </summary>
      <param name="destDirName">要将此目录移动到的目标位置的名称和路径。目标不能是另一个具有相同名称的磁盘卷或目录。它可以是你要将此目录作为子目录添加到某个现有目录。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destDirName" />是
                                        null.
                                    </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destDirName" /> 是空字符串 ("")。
                                    </exception>
      <exception cref="T:System.IO.IOException">试图将一个目录移到不同的卷。- 或 -<paramref name="destDirName" /> 已存在。
                                    - 或 -您无权访问此路径。- 或 -被移动的目录与目标目录同名。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">找不到此目标目录。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Name">
      <summary>获取此名称
<see cref="T:System.IO.DirectoryInfo" />实例。
                            </summary>
      <returns>目录名称。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.DirectoryInfo.Parent">
      <summary>获取指定的子目录的父目录。</summary>
      <returns>父目录中，或
null如果该路径为 null，或文件路径表示根 （例如"\"，"c:"或 *"\\server\share"）。
                            </returns>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.DirectoryInfo.Root">
      <summary>获取目录的根部分。</summary>
      <returns>一个表示目录的根目录的对象。</returns>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.DirectoryInfo.ToString">
      <summary>返回用户所传递的原始路径。</summary>
      <returns>返回用户所传递的原始路径。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.File">
      <summary>提供用于创建、复制、删除、移动和打开单一文件的静态方法，并协助创建 <see cref="T:System.IO.FileStream" /> 对象。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>向一个文件中追加行，然后关闭该文件。如果指定文件不存在，此方法会创建一个文件，向其中写入指定的行，然后关闭该文件。</summary>
      <param name="path">要向其中追加行的文件。如果文件尚不存在，则创建该文件。</param>
      <param name="contents">要追加到文件中的行。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法中已定义的一个或多个无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">要么<paramref name=" path " />或 <paramref name="contents" /> 是 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效（例如，目录不存在或位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到 <paramref name="path" /> 指定的文件。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超过了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有写入到文件的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 -当前平台不支持此操作。- 或 -<paramref name="path" /> 是一个目录。</exception>
    </member>
    <member name="M:System.IO.File.AppendAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>使用指定的编码向一个文件中追加行，然后关闭该文件。如果指定文件不存在，此方法会创建一个文件，向其中写入指定的行，然后关闭该文件。</summary>
      <param name="path">要向其中追加行的文件。如果文件尚不存在，则创建该文件。</param>
      <param name="contents">要追加到文件中的行。</param>
      <param name="encoding">要使用的字符编码。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法中已定义的一个或多个无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name=" path" />、<paramref name="contents" /> 或 <paramref name="encoding" /> 为 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效（例如，目录不存在或位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到 <paramref name="path" /> 指定的文件。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超过了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 -当前平台不支持此操作。- 或 -<paramref name="path" /> 是一个目录。- 或 -调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String)">
      <summary>打开一个文件，向其中追加指定的字符串，然后关闭该文件。如果文件不存在，此方法将创建一个文件，将指定的字符串写入文件，然后关闭该文件。</summary>
      <param name="path">要将指定的字符串追加到的文件。</param>
      <param name="contents">要追加到文件中的字符串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定路径无效（例如，目录不存在或位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendAllText(System.String,System.String,System.Text.Encoding)">
      <summary>将指定的字符串追加到文件中，如果文件还不存在则创建该文件。</summary>
      <param name="path">要将指定的字符串追加到的文件。</param>
      <param name="contents">要追加到文件中的字符串。</param>
      <param name="encoding">要使用的字符编码。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定路径无效（例如，目录不存在或位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.AppendText(System.String)">
      <summary>创建一个 <see cref="T:System.IO.StreamWriter" />，它将 UTF-8 编码文本追加到现有文件或新文件（如果指定文件不存在）。</summary>
      <returns>一个流写入器，它将 UTF-8 编码文本追加到指定文件或新文件。</returns>
      <param name="path">要向其中追加内容的文件的路径。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定路径无效（例如，目录不存在或位于未映射的驱动器上）。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String)">
      <summary>将现有文件复制到新文件。不允许覆盖同名的文件。</summary>
      <param name="sourceFileName">要复制的文件。</param>
      <param name="destFileName">目标文件的名称。它不能是一个目录或现有文件。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。- 或 - <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 指定目录。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在 <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 中指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" />。</exception>
      <exception cref="T:System.IO.IOException">已存在 <paramref name="destFileName" />。- 或 - 出现 I/O 错误。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Copy(System.String,System.String,System.Boolean)">
      <summary>将现有文件复制到新文件。允许覆盖同名的文件。</summary>
      <param name="sourceFileName">要复制的文件。</param>
      <param name="destFileName">目标文件的名称。不能是目录。</param>
      <param name="overwrite">如果可以覆盖目标文件，则为 true；否则为 false。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。- 或 -<paramref name="destFileName" /> 为只读。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。- 或 - <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 指定目录。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">在 <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 中指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" />。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destFileName" /> 存在并且 <paramref name="overwrite" /> 为 false.- 或 - 出现 I/O 错误。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String)">
      <summary>在指定路径中创建或覆盖文件。</summary>
      <returns>一个 <see cref="T:System.IO.FileStream" />，它提供对 <paramref name="path" /> 中指定的文件的读/写访问。</returns>
      <param name="path">要创建的文件的路径及名称。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。- 或 - <paramref name="path" /> 指定了一个只读文件。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">创建文件时发生 I/O 错误。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32)">
      <summary>创建或覆盖指定的文件。</summary>
      <returns>一个具有指定缓冲区大小的 <see cref="T:System.IO.FileStream" />，它提供对 <paramref name="path" /> 中指定的文件的读/写访问。</returns>
      <param name="path">文件的名称。</param>
      <param name="bufferSize">用于读取和写入到文件的已放入缓冲区的字节数。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。- 或 - <paramref name="path" /> 指定了一个只读文件。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">创建文件时发生 I/O 错误。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Create(System.String,System.Int32,System.IO.FileOptions)">
      <summary>创建或覆盖指定的文件，指定缓冲区大小和一个描述如何创建或覆盖该文件的 <see cref="T:System.IO.FileOptions" /> 值。</summary>
      <returns>具有指定缓冲区大小的新文件。</returns>
      <param name="path">文件的名称。</param>
      <param name="bufferSize">用于读取和写入到文件的已放入缓冲区的字节数。</param>
      <param name="options">
        <see cref="T:System.IO.FileOptions" /> 值之一，它描述如何创建或覆盖该文件。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。- 或 - <paramref name="path" /> 指定了一个只读文件。- 或 -为 <see cref="F:System.IO.FileOptions.Encrypted" /> 指定了 <paramref name="options" />，但当前平台不支持文件加密。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">创建文件时发生 I/O 错误。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。- 或 - <paramref name="path" /> 指定了一个只读文件。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。- 或 - <paramref name="path" /> 指定了一个只读文件。</exception>
    </member>
    <member name="M:System.IO.File.CreateText(System.String)">
      <summary>创建或打开用于写入 UTF-8 编码文本的文件。</summary>
      <returns>一个 <see cref="T:System.IO.StreamWriter" />，它使用 UTF-8 编码写入到指定的文件。</returns>
      <param name="path">要打开以进行写入的文件。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Delete(System.String)">
      <summary>删除指定的文件。</summary>
      <param name="path">要删除的文件的名称。该指令不支持通配符。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">指定的文件正在使用中。- 或 -对于文件有打开句柄，并且操作系统是 Windows XP 或更早版本。此打开句柄可能是由于枚举目录和文件导致的。有关详细信息，请参阅如何：枚举目录和文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。- 或 - 该文件是正在使用的可执行文件。- 或 - <paramref name="path" /> 是一个目录。- 或 - <paramref name="path" /> 指定一个只读文件。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Exists(System.String)">
      <summary>确定指定的文件是否存在。</summary>
      <returns>如果调用方具有要求的权限并且 true 包含现有文件的名称，则为 <paramref name="path" />；否则为 false。如果 false 为 <paramref name="path" />（一个无效路径或零长度字符串）,则此方法也将返回 null。如果调用方不具有读取指定文件所需的足够权限，则不引发异常并且该方法返回 false，这与 <paramref name="path" /> 是否存在无关。</returns>
      <param name="path">要检查的文件。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetAttributes(System.String)">
      <summary>获取在此路径上的文件的 <see cref="T:System.IO.FileAttributes" />。</summary>
      <returns>路径上文件的 <see cref="T:System.IO.FileAttributes" />。</returns>
      <param name="path">文件的路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 为空，仅包含空白，或包含无效字符。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="path" /> 表示一个文件且它是无效的，例如，位于未映射的驱动器上或无法找到文件。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 表示一个目录且它是无效的，例如，位于未映射的驱动器上或无法找到目录。</exception>
      <exception cref="T:System.IO.IOException">此文件正由另一个进程使用。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTime(System.String)">
      <summary>返回指定文件或目录的创建日期和时间。</summary>
      <returns>一个 <see cref="T:System.DateTime" /> 结构，它被设置为指定文件或目录的创建日期和时间。该值用本地时间表示。</returns>
      <param name="path">要获取其创建日期和时间信息的文件或目录。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetCreationTimeUtc(System.String)">
      <summary>返回指定的文件或目录的创建日期及时间，其格式为协调通用时 (UTC)。</summary>
      <returns>一个 <see cref="T:System.DateTime" /> 结构，它被设置为指定文件或目录的创建日期和时间。该值用 UTC 时间表示。</returns>
      <param name="path">要获取其创建日期和时间信息的文件或目录。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTime(System.String)">
      <summary>返回上次访问指定文件或目录的日期和时间。</summary>
      <returns>一个 <see cref="T:System.DateTime" /> 结构，它被设置为上次访问指定文件或目录的日期和时间。该值用本地时间表示。</returns>
      <param name="path">要获取其访问日期和时间信息的文件或目录。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastAccessTimeUtc(System.String)">
      <summary>返回上次访问指定的文件或目录的日期及时间，其格式为协调通用时 (UTC)。</summary>
      <returns>一个 <see cref="T:System.DateTime" /> 结构，它被设置为上次访问指定文件或目录的日期和时间。该值用 UTC 时间表示。</returns>
      <param name="path">要获取其访问日期和时间信息的文件或目录。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTime(System.String)">
      <summary>返回上次写入指定文件或目录的日期和时间。</summary>
      <returns>一个 <see cref="T:System.DateTime" /> 结构，它被设置为上次写入指定文件或目录的日期和时间。该值用本地时间表示。</returns>
      <param name="path">要获取其写入日期和时间信息的文件或目录。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.GetLastWriteTimeUtc(System.String)">
      <summary>返回上次写入指定的文件或目录的日期和时间，其格式为协调通用时 (UTC)。</summary>
      <returns>一个 <see cref="T:System.DateTime" /> 结构，它被设置为上次写入指定文件或目录的日期和时间。该值用 UTC 时间表示。</returns>
      <param name="path">要获取其写入日期和时间信息的文件或目录。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Move(System.String,System.String)">
      <summary>将指定文件移到新位置，提供要指定新文件名的选项。</summary>
      <param name="sourceFileName">要移动的文件的名称。可以包括相对或绝对路径。</param>
      <param name="destFileName">文件的新路径和名称。</param>
      <exception cref="T:System.IO.IOException">目标文件已经存在。- 或 -<paramref name="sourceFileName" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 是零长度字符串、只包含空白或者包含在 <see cref="F:System.IO.Path.InvalidPathChars" /> 中定义的无效字符。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 中指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 或 <paramref name="destFileName" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode)">
      <summary>以读/写访问权限打开指定路径上的 <see cref="T:System.IO.FileStream" />。</summary>
      <returns>以读/写访问与不共享权限打开的指定模式和路径上的 <see cref="T:System.IO.FileStream" />。</returns>
      <param name="path">要打开的文件。</param>
      <param name="mode">
        <see cref="T:System.IO.FileMode" /> 值，用于指定在文件不存在时是否创建该文件，并确定是保留还是覆盖现有文件的内容。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。- 或 -<paramref name="mode" /> 为 <see cref="F:System.IO.FileMode.Create" />，指定文件为隐藏文件。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 指定了一个无效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="path" /> 中指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>以指定的模式和访问权限打开指定路径上的 <see cref="T:System.IO.FileStream" />。</summary>
      <returns>一个非共享的 <see cref="T:System.IO.FileStream" />，它提供对指定文件的访问，并且具有指定的模式和访问权限。</returns>
      <param name="path">要打开的文件。</param>
      <param name="mode">
        <see cref="T:System.IO.FileMode" /> 值，用于指定在文件不存在时是否创建该文件，并确定是保留还是覆盖现有文件的内容。</param>
      <param name="access">一个 <see cref="T:System.IO.FileAccess" /> 值，它指定可以对文件执行的操作。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。- 或 - <paramref name="access" /> 指定了 Read，而 <paramref name="mode" /> 指定了 Create、CreateNew、Truncate 或 Append。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件，而 <paramref name="access" /> 不为 Read。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。- 或 -<paramref name="mode" /> 为 <see cref="F:System.IO.FileMode.Create" />，指定文件为隐藏文件。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 或 <paramref name="access" /> 指定了一个无效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="path" /> 中指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.Open(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>打开指定路径上的 <see cref="T:System.IO.FileStream" />，具有带读、写或读/写访问的指定模式和指定的共享选项。</summary>
      <returns>指定路径上的 <see cref="T:System.IO.FileStream" />，具有带读、写或读/写访问的指定模式以及指定的共享选项。</returns>
      <param name="path">要打开的文件。</param>
      <param name="mode">
        <see cref="T:System.IO.FileMode" /> 值，用于指定在文件不存在时是否创建该文件，并确定是保留还是覆盖现有文件的内容。</param>
      <param name="access">一个 <see cref="T:System.IO.FileAccess" /> 值，它指定可以对文件执行的操作。</param>
      <param name="share">一个 <see cref="T:System.IO.FileShare" /> 值，它指定其他线程所具有的对该文件的访问类型。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。- 或 - <paramref name="access" /> 指定了 Read，而 <paramref name="mode" /> 指定了 Create、CreateNew、Truncate 或 Append。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件，而 <paramref name="access" /> 不为 Read。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。- 或 -<paramref name="mode" /> 为 <see cref="F:System.IO.FileMode.Create" />，指定文件为隐藏文件。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" />、<paramref name="access" /> 或 <paramref name="share" /> 指定了一个无效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="path" /> 中指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenRead(System.String)">
      <summary>打开现有文件以进行读取。</summary>
      <returns>指定路径上的只读 <see cref="T:System.IO.FileStream" />。</returns>
      <param name="path">要打开以进行读取的文件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="path" /> 中指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenText(System.String)">
      <summary>打开现有 UTF-8 编码文本文件以进行读取。</summary>
      <returns>指定路径上的 <see cref="T:System.IO.StreamReader" />。</returns>
      <param name="path">要打开以进行读取的文件。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="path" /> 中指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.OpenWrite(System.String)">
      <summary>打开一个现有文件或创建一个新文件以进行写入。</summary>
      <returns>指定路径上具有 <see cref="T:System.IO.FileStream" /> 访问权限的非共享的 <see cref="F:System.IO.FileAccess.Write" /> 对象。</returns>
      <param name="path">要打开以进行写入的文件。</param>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。- 或 - <paramref name="path" /> 指定了只读文件或目录。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllBytes(System.String)">
      <summary>打开一个二进制文件，将文件的内容读入一个字节数组，然后关闭该文件。</summary>
      <returns>包含文件内容的字节数组。</returns>
      <param name="path">要打开以进行读取的文件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="path" /> 中指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String)">
      <summary>打开一个文本文件，读取文件的所有行，然后关闭该文件。</summary>
      <returns>包含文件所有行的字符串数组。</returns>
      <param name="path">要打开以进行读取的文件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="path" /> 中指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllLines(System.String,System.Text.Encoding)">
      <summary>打开一个文件，使用指定的编码读取文件的所有行，然后关闭该文件。</summary>
      <returns>包含文件所有行的字符串数组。</returns>
      <param name="path">要打开以进行读取的文件。</param>
      <param name="encoding">应用到文件内容的编码。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="path" /> 中指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String)">
      <summary>打开一个文本文件，读取文件的所有行，然后关闭该文件。</summary>
      <returns>包含文件所有行的字符串。</returns>
      <param name="path">要打开以进行读取的文件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="path" /> 中指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadAllText(System.String,System.Text.Encoding)">
      <summary>打开一个文件，使用指定的编码读取文件的所有行，然后关闭该文件。</summary>
      <returns>包含文件所有行的字符串。</returns>
      <param name="path">要打开以进行读取的文件。</param>
      <param name="encoding">应用到文件内容的编码。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="path" /> 中指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String)">
      <summary>读取文件的行。</summary>
      <returns>该文件的所有行或查询结果所示的行。</returns>
      <param name="path">要读取的文件。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白，或者包含由定义的一个或多个无效字符 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效（例如，在未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到 <paramref name="path" /> 指定的文件。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超过了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 -当前平台不支持此操作。- 或 -<paramref name="path" /> 是一个目录。- 或 -调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.File.ReadLines(System.String,System.Text.Encoding)">
      <summary>读取具有指定编码的文件的行。</summary>
      <returns>该文件的所有行或查询结果所示的行。</returns>
      <param name="path">要读取的文件。</param>
      <param name="encoding">应用到文件内容的编码。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法中定义的一个或多个无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效（例如，在未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到 <paramref name="path" /> 指定的文件。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超过了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 -当前平台不支持此操作。- 或 -<paramref name="path" /> 是一个目录。- 或 -调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.File.SetAttributes(System.String,System.IO.FileAttributes)">
      <summary>获取指定路径上的文件的指定 <see cref="T:System.IO.FileAttributes" />。</summary>
      <param name="path">文件的路径。</param>
      <param name="fileAttributes">枚举值的按位组合。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 为空、只包含空白、包含无效字符或文件属性无效。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.FileNotFoundException">无法找到该文件。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTime(System.String,System.DateTime)">
      <summary>设置创建该文件的日期和时间。</summary>
      <param name="path">要设置其创建日期和时间信息的文件。</param>
      <param name="creationTime">一个 <see cref="T:System.DateTime" />，它包含要为 <paramref name="path" /> 的创建日期和时间设置的值。该值用本地时间表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.IOException">执行操作时发生 I/O 错误。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> 指定的值超出了该操作所允许的日期范围或时间范围，或同时超出了日期范围和时间范围。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetCreationTimeUtc(System.String,System.DateTime)">
      <summary>设置文件创建的日期和时间，其格式为协调通用时 (UTC)。</summary>
      <param name="path">要设置其创建日期和时间信息的文件。</param>
      <param name="creationTimeUtc">一个 <see cref="T:System.DateTime" />，它包含要为 <paramref name="path" /> 的创建日期和时间设置的值。该值用 UTC 时间表示。</param>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.IOException">执行操作时发生 I/O 错误。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationTime" /> 指定的值超出了该操作所允许的日期范围或时间范围，或同时超出了日期范围和时间范围。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTime(System.String,System.DateTime)">
      <summary>设置上次访问指定文件的日期和时间。</summary>
      <param name="path">要设置其访问日期和时间信息的文件。</param>
      <param name="lastAccessTime">一个 <see cref="T:System.DateTime" />，它包含要为 <paramref name="path" /> 的上次访问日期和时间设置的值。该值用本地时间表示。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTime" /> 指定超出该操作允许的日期或时间范围的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastAccessTimeUtc(System.String,System.DateTime)">
      <summary>设置上次访问指定的文件的日期和时间，其格式为协调通用时 (UTC)。</summary>
      <param name="path">要设置其访问日期和时间信息的文件。</param>
      <param name="lastAccessTimeUtc">一个 <see cref="T:System.DateTime" />，它包含要为 <paramref name="path" /> 的上次访问日期和时间设置的值。该值用 UTC 时间表示。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastAccessTimeUtc" /> 指定超出该操作允许的日期或时间范围的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTime(System.String,System.DateTime)">
      <summary>设置上次写入指定文件的日期和时间。</summary>
      <param name="path">要设置其日期和时间信息的文件。</param>
      <param name="lastWriteTime">一个 <see cref="T:System.DateTime" />，它包含要为 <paramref name="path" /> 的上次写入日期和时间设置的值。该值用本地时间表示。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTime" /> 指定超出该操作允许的日期或时间范围的值。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.SetLastWriteTimeUtc(System.String,System.DateTime)">
      <summary>设置上次写入指定的文件的日期和时间，其格式为协调通用时 (UTC)。</summary>
      <param name="path">要设置其日期和时间信息的文件。</param>
      <param name="lastWriteTimeUtc">一个 <see cref="T:System.DateTime" />，它包含要为 <paramref name="path" /> 的上次写入日期和时间设置的值。该值用 UTC 时间表示。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到指定的路径。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="lastWriteTimeUtc" /> 指定超出该操作允许的日期或时间范围的值。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllBytes(System.String,System.Byte[])">
      <summary>创建一个新文件，在其中写入指定的字节数组，然后关闭该文件。如果目标文件已存在，则覆盖该文件。</summary>
      <param name="path">要写入的文件。</param>
      <param name="bytes">要写入文件的字节。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null 或字节数组为空。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>创建一个新文件，向其中写入一个字符串集合，然后关闭该文件。</summary>
      <param name="path">要写入的文件。</param>
      <param name="contents">要写入到文件中的行。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白，或者包含由定义的一个或多个无效字符 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。</exception>
      <exception cref="T:System.ArgumentNullException">要么<paramref name=" path " />或 <paramref name="contents" /> 是 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效（例如，在未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超过了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 -当前平台不支持此操作。- 或 -<paramref name="path" /> 是一个目录。- 或 -调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.File.WriteAllLines(System.String,System.Collections.Generic.IEnumerable{System.String},System.Text.Encoding)">
      <summary>使用指定的编码创建一个新文件，向其中写入一个字符串集合，然后关闭该文件。</summary>
      <param name="path">要写入的文件。</param>
      <param name="contents">要写入到文件中的行。</param>
      <param name="encoding">要使用的字符编码。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白，或者包含由定义的一个或多个无效字符 <see cref="M:System.IO.Path.GetInvalidPathChars" /> 方法。</exception>
      <exception cref="T:System.ArgumentNullException">要么<paramref name=" path" />,，<paramref name=" contents" />, ，或 <paramref name="encoding" /> 是 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="path" /> 无效（例如，在未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="path" /> 超过了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 -当前平台不支持此操作。- 或 -<paramref name="path" /> 是一个目录。- 或 -调用方没有所要求的权限。</exception>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String)">
      <summary>创建一个新文件，向其中写入指定的字符串，然后关闭文件。如果目标文件已存在，则覆盖该文件。</summary>
      <param name="path">要写入的文件。</param>
      <param name="contents">要写入文件的字符串。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null 或 <paramref name="contents" /> 为空。 </exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.File.WriteAllText(System.String,System.String,System.Text.Encoding)">
      <summary>创建一个新文件，使用指定编码向其中写入指定的字符串，然后关闭文件。如果目标文件已存在，则覆盖该文件。</summary>
      <param name="path">要写入的文件。</param>
      <param name="contents">要写入文件的字符串。</param>
      <param name="encoding">应用于字符串的编码。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null 或 <paramref name="contents" /> 为空。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 指定了一个只读文件。- 或 - 当前平台不支持此操作。- 或 - <paramref name="path" /> 指定了一个目录。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 的格式无效。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.IO.FileInfo">
      <summary>提供用于创建、复制、删除、移动和打开文件的属性和实例方法，并且帮助创建 <see cref="T:System.IO.FileStream" /> 对象。此类不能被继承。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.#ctor(System.String)">
      <summary>初始化作为文件路径的包装的 <see cref="T:System.IO.FileInfo" /> 类的新实例。</summary>
      <param name="fileName">新文件的完全限定名或相对文件名。路径不要以目录分隔符结尾。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> 为 null。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">文件名为空，只包含空白，或包含无效字符。</exception>
      <exception cref="T:System.UnauthorizedAccessException">对 <paramref name="fileName" /> 的访问被拒绝。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="fileName" /> 字符串中间有一个冒号 (:)。</exception>
    </member>
    <member name="M:System.IO.FileInfo.AppendText">
      <summary>创建一个 <see cref="T:System.IO.StreamWriter" />，它向 <see cref="T:System.IO.FileInfo" /> 的此实例表示的文件追加文本。</summary>
      <returns>一个新的 StreamWriter。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String)">
      <summary>将现有文件复制到新文件，不允许覆盖现有文件。</summary>
      <returns>带有完全限定路径的新文件。</returns>
      <param name="destFileName">要复制到的新文件的名称。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> 为空，仅包含空白，或包含无效字符。</exception>
      <exception cref="T:System.IO.IOException">发生错误或目标文件已经存在。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> 为 null。</exception>
      <exception cref="T:System.UnauthorizedAccessException">传入了一个目录路径，或者正在将文件移动到另一个驱动器。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="destFileName" /> 中指定的目录不存在。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> 在字符串内包含一个冒号 (:)，但未指定卷。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CopyTo(System.String,System.Boolean)">
      <summary>将现有文件复制到新文件，允许覆盖现有文件。</summary>
      <returns>为新文件；如果 <paramref name="overwrite" /> 是 true，则为现有文件的覆盖。如果文件存在且 <paramref name="overwrite" /> 为 false，则引发 <see cref="T:System.IO.IOException" />。</returns>
      <param name="destFileName">要复制到的新文件的名称。</param>
      <param name="overwrite">如果允许覆盖现有文件，则为 true；否则为 false。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> 为空，仅包含空白，或包含无效字符。</exception>
      <exception cref="T:System.IO.IOException">发生错误，或者目标文件已经存在，并且 <paramref name="overwrite" /> 为 false。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> 为 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="destFileName" /> 中指定的目录不存在。</exception>
      <exception cref="T:System.UnauthorizedAccessException">传入了一个目录路径，或者正在将文件移动到另一个驱动器。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> 字符串中间有一个冒号 (:)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Create">
      <summary>创建文件。</summary>
      <returns>新文件。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.CreateText">
      <summary>创建写入新文本文件的 <see cref="T:System.IO.StreamWriter" />。</summary>
      <returns>一个新的 StreamWriter。</returns>
      <exception cref="T:System.UnauthorizedAccessException">文件名为目录。</exception>
      <exception cref="T:System.IO.IOException">磁盘为只读。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Delete">
      <summary>永久删除文件。</summary>
      <exception cref="T:System.IO.IOException">目标文件已打开或内存映射到运行 Microsoft Windows NT 的计算机上。- 或 -对于文件有打开句柄，并且操作系统是 Windows XP 或更早版本。此打开句柄可能是由于枚举目录和文件导致的。有关详细信息，请参阅如何：枚举目录和文件。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">路径是目录。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Directory">
      <summary>获取父目录的实例。</summary>
      <returns>表示此文件父目录的 <see cref="T:System.IO.DirectoryInfo" /> 对象。</returns>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.DirectoryName">
      <summary>获取表示目录的完整路径的字符串。</summary>
      <returns>表示目录的完整路径的字符串。</returns>
      <exception cref="T:System.ArgumentNullException">为目录名传入 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">完全限定路径为 260 或更多字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Exists">
      <summary>获取指示文件是否存在的值。</summary>
      <returns>如果该文件存在，则为 true；如果文件不存在或文件即是目录，则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.IsReadOnly">
      <summary>获取或设置确定当前文件是否为只读的值。</summary>
      <returns>如果当前文件为只读，则为 true；否则为 false。</returns>
      <exception cref="T:System.IO.FileNotFoundException">找不到当前 <see cref="T:System.IO.FileInfo" /> 对象所描述的文件。</exception>
      <exception cref="T:System.IO.IOException">打开文件时发生 I/O 错误。</exception>
      <exception cref="T:System.UnauthorizedAccessException">当前平台不支持此操作。- 或 - 调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">用户没有写入权限，但尝试设置属性为 false。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileInfo.Length">
      <summary>获取当前文件的大小（以字节为单位）。</summary>
      <returns>当前文件的大小（以字节为单位）。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 无法更新文件或目录的状态。</exception>
      <exception cref="T:System.IO.FileNotFoundException">文件不存在。- 或 - 为一个目录调用 Length 属性。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.MoveTo(System.String)">
      <summary>将指定文件移到新位置，提供要指定新文件名的选项。</summary>
      <param name="destFileName">要将文件移动到的路径，可以指定另一个文件名。</param>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，如目标文件已经存在或目标设备未准备好。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destFileName" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="destFileName" /> 为空，仅包含空白，或包含无效字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destFileName" /> 为只读，或者是一个目录。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到该文件。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destFileName" /> 字符串中间有一个冒号 (:)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileInfo.Name">
      <summary>获取文件名。</summary>
      <returns>文件的名称。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode)">
      <summary>在指定的模式中打开文件。</summary>
      <returns>在指定模式中打开、具有读/写访问权限且不共享的文件。</returns>
      <param name="mode">一个 <see cref="T:System.IO.FileMode" /> 常数，它指定打开文件所采用的模式（例如 Open 或 Append）。</param>
      <exception cref="T:System.IO.FileNotFoundException">找不到该文件。</exception>
      <exception cref="T:System.UnauthorizedAccessException">此文件是只读文件，或者是一个目录。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.IO.IOException">文件已经处于打开状态。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess)">
      <summary>用读、写或读/写访问权限在指定模式下打开文件。</summary>
      <returns>用指定模式和访问权限打开的且不共享的 <see cref="T:System.IO.FileStream" /> 对象。</returns>
      <param name="mode">一个 <see cref="T:System.IO.FileMode" /> 常数，它指定打开文件所采用的模式（例如 Open 或 Append）。</param>
      <param name="access">一个 <see cref="T:System.IO.FileAccess" /> 常数，它指定是使用 Read、Write 还是 ReadWrite 文件访问来打开文件。</param>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到该文件。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 为只读，或者是一个目录。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.IO.IOException">文件已经处于打开状态。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.Open(System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>用读、写或读/写访问权限和指定的共享选项在指定的模式中打开文件。</summary>
      <returns>用指定的模式、访问权限和共享选项打开的 <see cref="T:System.IO.FileStream" /> 对象。</returns>
      <param name="mode">一个 <see cref="T:System.IO.FileMode" /> 常数，它指定打开文件所采用的模式（例如 Open 或 Append）。</param>
      <param name="access">一个 <see cref="T:System.IO.FileAccess" /> 常数，它指定是使用 Read、Write 还是 ReadWrite 文件访问来打开文件。</param>
      <param name="share">一个 <see cref="T:System.IO.FileShare" /> 常数，它指定其他 FileStream 对象对此文件拥有的访问类型。</param>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到该文件。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 为只读，或者是一个目录。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.IO.IOException">文件已经处于打开状态。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenRead">
      <summary>创建一个只读的 <see cref="T:System.IO.FileStream" />。</summary>
      <returns>一个新的只读的 <see cref="T:System.IO.FileStream" /> 对象。</returns>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 为只读，或者是一个目录。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.IO.IOException">文件已经处于打开状态。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenText">
      <summary>创建使用从现有文本文件中读取的 UTF8 编码的 <see cref="T:System.IO.StreamReader" />。</summary>
      <returns>使用 UTF8 编码的新的 StreamReader。</returns>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到该文件。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="path" /> 为只读，或者是一个目录。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.OpenWrite">
      <summary>创建一个只写的 <see cref="T:System.IO.FileStream" />。</summary>
      <returns>新的或现有文件的只写非共享的 <see cref="T:System.IO.FileStream" /> 对象。</returns>
      <exception cref="T:System.UnauthorizedAccessException">路径指定创建 <see cref="T:System.IO.FileInfo" /> 对象的实例何时是只读或者是目录。 </exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">路径指定创建 <see cref="T:System.IO.FileInfo" /> 对象的实例何时无效，例如在未映射的驱动器上时。</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileInfo.ToString">
      <summary>以字符串形式返回路径。</summary>
      <returns>一个表示该路径的字符串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileOptions">
      <summary>表示用于创建 <see cref="T:System.IO.FileStream" /> 对象的高级选项。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileOptions.Asynchronous">
      <summary>指示文件可用于异步读取和写入。</summary>
    </member>
    <member name="F:System.IO.FileOptions.DeleteOnClose">
      <summary>指示当不再使用某个文件时，自动删除该文件。</summary>
    </member>
    <member name="F:System.IO.FileOptions.Encrypted">
      <summary>指示文件是加密的，只能通过用于加密的同一用户帐户来解密。</summary>
    </member>
    <member name="F:System.IO.FileOptions.None">
      <summary>指示在生成 <see cref="T:System.IO.FileStream" /> 对象时，不应使用其他选项。</summary>
    </member>
    <member name="F:System.IO.FileOptions.RandomAccess">
      <summary>指示随机访问文件。系统可将此选项用作优化文件缓存的提示。</summary>
    </member>
    <member name="F:System.IO.FileOptions.SequentialScan">
      <summary>指示按从头到尾的顺序访问文件。系统可将此选项用作优化文件缓存的提示。如果应用程序移动用于随机访问的文件指针，可能不发生优化缓存，但仍然保证操作的正确性。</summary>
    </member>
    <member name="F:System.IO.FileOptions.WriteThrough">
      <summary>指示系统应通过任何中间缓存、直接写入磁盘。</summary>
    </member>
    <member name="T:System.IO.FileStream">
      <summary>为文件提供 <see cref="T:System.IO.Stream" />，既支持同步读写操作，也支持异步读写操作。若要浏览此类型的.NET Framework 源代码，请参阅 Reference Source。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess)">
      <summary>使用指定的读/写权限为指定的文件句柄初始化 <see cref="T:System.IO.FileStream" /> 类的新实例。</summary>
      <param name="handle">当前 FileStream 对象将封装的文件的文件句柄。</param>
      <param name="access">一个常数，用于设置 FileStream 对象的 <see cref="P:System.IO.FileStream.CanRead" /> 和 <see cref="P:System.IO.FileStream.CanWrite" /> 属性。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="access" /> 不是 <see cref="T:System.IO.FileAccess" /> 的字段。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，如磁盘错误。- 或 -流已关闭。</exception>
      <exception cref="T:System.UnauthorizedAccessException">对于指定的文件句柄，操作系统不允许所请求的 <paramref name="access" />，例如，当 <paramref name="access" /> 为 Write 或 ReadWrite 而文件句柄设置为只读访问时。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32)">
      <summary>使用指定的读/写权限和缓冲区大小为指定的文件句柄初始化 <see cref="T:System.IO.FileStream" /> 类的新实例。</summary>
      <param name="handle">当前 FileStream 对象将封装的文件的文件句柄。</param>
      <param name="access">一个 <see cref="T:System.IO.FileAccess" /> 常数，它设置 FileStream 对象的 <see cref="P:System.IO.FileStream.CanRead" /> 和 <see cref="P:System.IO.FileStream.CanWrite" /> 属性。</param>
      <param name="bufferSize">一个大于零的正 <see cref="T:System.Int32" /> 值，表示缓冲区大小。默认缓冲区大小为 4096。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" /> 参数是无效的句柄。- 或 -<paramref name="handle" /> 参数是同步句柄，但被异步使用。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 参数为负数。</exception>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，如磁盘错误。- 或 -流已关闭。 </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">对于指定的文件句柄，操作系统不允许所请求的 <paramref name="access" />，例如，当 <paramref name="access" /> 为 Write 或 ReadWrite 而文件句柄设置为只读访问时。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(Microsoft.Win32.SafeHandles.SafeFileHandle,System.IO.FileAccess,System.Int32,System.Boolean)">
      <summary>使用指定的读/写权限、缓冲区大小和同步或异步状态为指定的文件句柄初始化 <see cref="T:System.IO.FileStream" /> 类的新实例。</summary>
      <param name="handle">此 FileStream 对象将封装的文件的文件句柄。</param>
      <param name="access">一个常数，用于设置 FileStream 对象的 <see cref="P:System.IO.FileStream.CanRead" /> 和 <see cref="P:System.IO.FileStream.CanWrite" /> 属性。</param>
      <param name="bufferSize">一个大于零的正 <see cref="T:System.Int32" /> 值，表示缓冲区大小。默认缓冲区大小为 4096。</param>
      <param name="isAsync">如果异步打开句柄（即以重叠的 I/O 模式），则为 true；否则为 false。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" /> 参数是无效的句柄。- 或 -<paramref name="handle" /> 参数是同步句柄，但被异步使用。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 参数为负数。</exception>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，如磁盘错误。- 或 -流已关闭。 </exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.UnauthorizedAccessException">对于指定的文件句柄，操作系统不允许所请求的 <paramref name="access" />，例如，当 <paramref name="access" /> 为 Write 或 ReadWrite 而文件句柄设置为只读访问时。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode)">
      <summary>使用指定的路径和创建模式初始化 <see cref="T:System.IO.FileStream" /> 类的新实例。</summary>
      <param name="path">当前 FileStream 对象将封装的文件的相对路径或绝对路径。</param>
      <param name="mode">一个确定如何打开或创建文件的常数。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 为空字符串 ("")，只包含空格，或者包含一个或多个无效字符。- 或 -<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到文件，比如当 <paramref name="mode" /> 是 FileMode.Truncate 或 FileMode.Open 而 <paramref name="path" /> 指定的文件不存在时。文件必须已经以这些模式存在。</exception>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，比如指定 FileMode.CreateNew 而 <paramref name="path" /> 指定的文件已存在。- 或 -流已关闭。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 包含无效值。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess)">
      <summary>使用指定的路径、创建模式和读/写权限初始化 <see cref="T:System.IO.FileStream" /> 类的新实例。</summary>
      <param name="path">当前 FileStream 对象将封装的文件的相对路径或绝对路径。</param>
      <param name="mode">一个确定如何打开或创建文件的常数。</param>
      <param name="access">一个常数，用于确定 FileStream 对象访问文件的方式。该常数还可以确定由 FileStream 对象的 <see cref="P:System.IO.FileStream.CanRead" /> 和 <see cref="P:System.IO.FileStream.CanWrite" /> 属性返回的值。如果 <paramref name="path" /> 指定磁盘文件，则 <see cref="P:System.IO.FileStream.CanSeek" /> 为 true。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 为空字符串 ("")，只包含空格，或者包含一个或多个无效字符。- 或 -<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到文件，比如当 <paramref name="mode" /> 是 FileMode.Truncate 或 FileMode.Open 而 <paramref name="path" /> 指定的文件不存在时。文件必须已经以这些模式存在。</exception>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，比如指定 FileMode.CreateNew 而 <paramref name="path" /> 指定的文件已存在。- 或 -流已关闭。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.UnauthorizedAccessException">对于指定的 <paramref name="access" /> 操作系统不允许所请求的 <paramref name="path" />，比如当 <paramref name="access" /> 是 Write 或 ReadWrite 而文件或目录设置为只读访问时。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 包含无效值。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)">
      <summary>使用指定的路径、创建模式、读/写权限和共享权限创建 <see cref="T:System.IO.FileStream" /> 类的新实例。</summary>
      <param name="path">当前 FileStream 对象将封装的文件的相对路径或绝对路径。</param>
      <param name="mode">一个确定如何打开或创建文件的常数。</param>
      <param name="access">一个常数，用于确定 FileStream 对象访问文件的方式。该常数还可以确定由 FileStream 对象的 <see cref="P:System.IO.FileStream.CanRead" /> 和 <see cref="P:System.IO.FileStream.CanWrite" /> 属性返回的值。如果 <paramref name="path" /> 指定磁盘文件，则 <see cref="P:System.IO.FileStream.CanSeek" /> 为 true。</param>
      <param name="share">一个常数，确定文件将如何由进程共享。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 为空字符串 ("")，只包含空格，或者包含一个或多个无效字符。- 或 -<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到文件，比如当 <paramref name="mode" /> 是 FileMode.Truncate 或 FileMode.Open 而 <paramref name="path" /> 指定的文件不存在时。文件必须已经以这些模式存在。</exception>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，比如指定 FileMode.CreateNew 而 <paramref name="path" /> 指定的文件已存在。- 或 -系统正在运行 Windows 98 或 Windows 98 Second Edition，并且 <paramref name="share" /> 设置为 FileShare.Delete。- 或 -流已关闭。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.UnauthorizedAccessException">对于指定的 <paramref name="access" /> 操作系统不允许所请求的 <paramref name="path" />，比如当 <paramref name="access" /> 是 Write 或 ReadWrite 而文件或目录设置为只读访问时。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> 包含无效值。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32)">
      <summary>用指定的路径、创建模式、读/写及共享权限和缓冲区大小初始化 <see cref="T:System.IO.FileStream" /> 类的新实例。</summary>
      <param name="path">当前 FileStream 对象将封装的文件的相对路径或绝对路径。</param>
      <param name="mode">一个确定如何打开或创建文件的常数。</param>
      <param name="access">一个常数，用于确定 FileStream 对象访问文件的方式。该常数还可以确定由 FileStream 对象的 <see cref="P:System.IO.FileStream.CanRead" /> 和 <see cref="P:System.IO.FileStream.CanWrite" /> 属性返回的值。如果 <paramref name="path" /> 指定磁盘文件，则 <see cref="P:System.IO.FileStream.CanSeek" /> 为 true。</param>
      <param name="share">一个常数，确定文件将如何由进程共享。</param>
      <param name="bufferSize">一个大于零的正 <see cref="T:System.Int32" /> 值，表示缓冲区大小。默认缓冲区大小为 4096。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 为空字符串 ("")，只包含空格，或者包含一个或多个无效字符。- 或 -<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 为负数或零。- 或 - <paramref name="mode" />、<paramref name="access" /> 或 <paramref name="share" /> 包含无效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到文件，比如当 <paramref name="mode" /> 是 FileMode.Truncate 或 FileMode.Open 而 <paramref name="path" /> 指定的文件不存在时。文件必须已经以这些模式存在。</exception>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，比如指定 FileMode.CreateNew 而 <paramref name="path" /> 指定的文件已存在。- 或 -系统正在运行 Windows 98 或 Windows 98 Second Edition，并且 <paramref name="share" /> 设置为 FileShare.Delete。- 或 -流已关闭。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.UnauthorizedAccessException">对于指定的 <paramref name="access" /> 操作系统不允许所请求的 <paramref name="path" />，比如当 <paramref name="access" /> 是 Write 或 ReadWrite 而文件或目录设置为只读访问时。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.Boolean)">
      <summary>使用指定的路径、创建模式、读/写和共享权限、缓冲区大小和同步或异步状态初始化 <see cref="T:System.IO.FileStream" /> 类的新实例。</summary>
      <param name="path">当前 FileStream 对象将封装的文件的相对路径或绝对路径。</param>
      <param name="mode">一个确定如何打开或创建文件的常数。</param>
      <param name="access">一个常数，用于确定 FileStream 对象访问文件的方式。该常数还可以确定由 FileStream 对象的 <see cref="P:System.IO.FileStream.CanRead" /> 和 <see cref="P:System.IO.FileStream.CanWrite" /> 属性返回的值。如果 <paramref name="path" /> 指定磁盘文件，则 <see cref="P:System.IO.FileStream.CanSeek" /> 为 true。</param>
      <param name="share">一个常数，确定文件将如何由进程共享。</param>
      <param name="bufferSize">一个大于零的正 <see cref="T:System.Int32" /> 值，表示缓冲区大小。默认缓冲区大小为 4096。</param>
      <param name="useAsync">指定使用异步 I/O 还是同步 I/O。但是，请注意，基础操作系统可能不支持异步 I/O，因此在指定 true 后，根据所用平台，句柄可能同步打开。当异步打开时，<see cref="M:System.IO.FileStream.BeginRead(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> 和 <see cref="M:System.IO.FileStream.BeginWrite(System.Byte[],System.Int32,System.Int32,System.AsyncCallback,System.Object)" /> 方法在执行大量读或写时效果更好，但对于少量的读/写，这些方法速度可能要慢得多。如果应用程序打算利用异步 I/O，将 <paramref name="useAsync" /> 参数设置为 true。正确使用异步 I/O 可以使应用程序的速度加快 10 倍，但是如果在没有为异步 I/O 重新设计应用程序的情况下使用异步 I/O，则可能使性能降低 10 倍。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 为空字符串 ("")，只包含空格，或者包含一个或多个无效字符。- 或 -<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 为负数或零。- 或 - <paramref name="mode" />、<paramref name="access" /> 或 <paramref name="share" /> 包含无效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到文件，比如当 <paramref name="mode" /> 是 FileMode.Truncate 或 FileMode.Open 而 <paramref name="path" /> 指定的文件不存在时。文件必须已经以这些模式存在。</exception>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，比如指定 FileMode.CreateNew 而 <paramref name="path" /> 指定的文件已存在。- 或 - 系统正在运行 Windows 98 或 Windows 98 Second Edition，并且 <paramref name="share" /> 设置为 FileShare.Delete。- 或 -流已关闭。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.UnauthorizedAccessException">对于指定的 <paramref name="access" /> 操作系统不允许所请求的 <paramref name="path" />，比如当 <paramref name="access" /> 是 Write 或 ReadWrite 而文件或目录设置为只读访问时。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
    </member>
    <member name="M:System.IO.FileStream.#ctor(System.String,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,System.Int32,System.IO.FileOptions)">
      <summary>使用指定的路径、创建模式、读/写和共享权限、其他 FileStreams 可以具有的对此文件的访问权限、缓冲区大小和附加文件选项初始化 <see cref="T:System.IO.FileStream" /> 类的新实例。</summary>
      <param name="path">当前 FileStream 对象将封装的文件的相对路径或绝对路径。</param>
      <param name="mode">一个确定如何打开或创建文件的常数。</param>
      <param name="access">一个常数，用于确定 FileStream 对象访问文件的方式。该常数还可以确定由 FileStream 对象的 <see cref="P:System.IO.FileStream.CanRead" /> 和 <see cref="P:System.IO.FileStream.CanWrite" /> 属性返回的值。如果 <paramref name="path" /> 指定磁盘文件，则 <see cref="P:System.IO.FileStream.CanSeek" /> 为 true。</param>
      <param name="share">一个常数，确定文件将如何由进程共享。</param>
      <param name="bufferSize">一个大于零的正 <see cref="T:System.Int32" /> 值，表示缓冲区大小。默认缓冲区大小为 4096。</param>
      <param name="options">一个指定附加文件选项的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="path" /> 为空字符串 ("")，只包含空格，或者包含一个或多个无效字符。- 或 -<paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在 NTFS 环境中。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="path" /> 指非文件设备，如"con："、"com1："，"lpt1:"，等等。在非 NTFS 环境中。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> 为负数或零。- 或 - <paramref name="mode" />、<paramref name="access" /> 或 <paramref name="share" /> 包含无效值。</exception>
      <exception cref="T:System.IO.FileNotFoundException">找不到文件，比如当 <paramref name="mode" /> 是 FileMode.Truncate 或 FileMode.Open 而 <paramref name="path" /> 指定的文件不存在时。文件必须已经以这些模式存在。</exception>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，比如指定 FileMode.CreateNew 而 <paramref name="path" /> 指定的文件已存在。- 或 -流已关闭。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效，比如在未映射的驱动器上。</exception>
      <exception cref="T:System.UnauthorizedAccessException">对于指定的 <paramref name="access" /> 操作系统不允许所请求的 <paramref name="path" />，比如当 <paramref name="access" /> 是 Write 或 ReadWrite 而文件或目录设置为只读访问时。- 或 -为 <see cref="F:System.IO.FileOptions.Encrypted" /> 指定了 <paramref name="options" />，但是当前平台不支持文件加密。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径必须小于 248 个字符，文件名必须小于 260 个字符。</exception>
    </member>
    <member name="P:System.IO.FileStream.CanRead">
      <summary>获取一个值，该值指示当前流是否支持读取。</summary>
      <returns>如果流支持读取，则为 true；如果流已关闭或是通过只写访问方式打开的，则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanSeek">
      <summary>获取一个值，该值指示当前流是否支持查找。</summary>
      <returns>如果流支持查找，则为 true；如果流已关闭或者如果 FileStream 是从操作系统句柄（如管道或到控制台的输出）构造的，则为 false。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.CanWrite">
      <summary>获取一个值，该值指示当前流是否支持写入。</summary>
      <returns>如果流支持写入，则为 true；如果流已关闭或是通过只读访问方式打开的，则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Dispose(System.Boolean)">
      <summary>释放由 <see cref="T:System.IO.FileStream" /> 占用的非托管资源，还可以另外再释放托管资源。</summary>
      <param name="disposing">若要释放托管资源和非托管资源，则为 true；若仅释放非托管资源，则为 false。</param>
    </member>
    <member name="M:System.IO.FileStream.Finalize">
      <summary>确保垃圾回收器回收 FileStream 时释放资源并执行其他清理操作。</summary>
    </member>
    <member name="M:System.IO.FileStream.Flush">
      <summary>清除此流的缓冲区，使得所有缓冲数据都写入到文件中。</summary>
      <exception cref="T:System.IO.IOException">发生了 I/O 错误。</exception>
      <exception cref="T:System.ObjectDisposedException">流已关闭。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Flush(System.Boolean)">
      <summary>清除此流的缓冲区，将所有缓冲数据都写入到文件中，并且也清除所有中间文件缓冲区。</summary>
      <param name="flushToDisk">如果刷新所有中间文件缓冲区，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.IO.FileStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>异步清理这个流的所有缓冲区，并使所有缓冲数据写入基础设备，并且监控取消请求。</summary>
      <returns>表示异步刷新操作的任务。</returns>
      <param name="cancellationToken">要监视取消请求的标记。</param>
      <exception cref="T:System.ObjectDisposedException">流已被释放。</exception>
    </member>
    <member name="P:System.IO.FileStream.IsAsync">
      <summary>获取一个值，该值指示 FileStream 是异步还是同步打开的。</summary>
      <returns>如果 FileStream 是异步打开的，则为 true，否则为 false。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Length">
      <summary>获取用字节表示的流长度。</summary>
      <returns>表示流长度（以字节为单位）的长值。</returns>
      <exception cref="T:System.NotSupportedException">该流的 <see cref="P:System.IO.FileStream.CanSeek" /> 为 false。</exception>
      <exception cref="T:System.IO.IOException">发生 I/O 错误，如文件被关闭。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.Name">
      <summary>获取传递给构造函数的 FileStream 的名称。</summary>
      <returns>一个字符串，它是 FileStream 的名称。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileStream.Position">
      <summary>获取或设置此流的当前位置。</summary>
      <returns>此流的当前位置。</returns>
      <exception cref="T:System.NotSupportedException">流不支持查找。</exception>
      <exception cref="T:System.IO.IOException">发生了 I/O 错误。- 或 -在 Windows 98 或较早版本中，该位置被设置为超出流的末尾的很大的一个值。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">试图将位置设置为负值。</exception>
      <exception cref="T:System.IO.EndOfStreamException">试图在流的末尾之外查找，而流不支持此操作。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>从流中读取字节块并将该数据写入给定缓冲区中。</summary>
      <returns>读入缓冲区中的总字节数。如果字节数当前不可用，则总字节数可能小于所请求的字节数；如果已到达流结尾，则为零。</returns>
      <param name="array">此方法返回时包含指定的字节数组，数组中 <paramref name="offset" /> 和 (<paramref name="offset" /> + <paramref name="count" /> - 1<paramref name=")" /> 之间的值由从当前源中读取的字节替换。</param>
      <param name="offset">
        <paramref name="array" /> 中的字节偏移量，将在此处放置读取的字节。</param>
      <param name="count">最多读取的字节数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="count" /> 为负。</exception>
      <exception cref="T:System.NotSupportedException">流不支持读取。</exception>
      <exception cref="T:System.IO.IOException">发生了 I/O 错误。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 和 <paramref name="count" /> 描述 <paramref name="array" /> 中的无效范围。</exception>
      <exception cref="T:System.ObjectDisposedException">在流关闭后调用方法。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>从当前流异步读取字节的序列，将流中的位置提升读取的字节数，并监视取消请求。</summary>
      <returns>表示异步读取操作的任务。<paramref name="TResult" /> 参数的值包含读入缓冲区的总字节数。如果当前可用字节数少于所请求的字节数，则该结果值可小于所请求的字节数；如果已到达流结尾时，则为 0（零）。</returns>
      <param name="buffer">数据写入的缓冲区。</param>
      <param name="offset">
        <paramref name="buffer" /> 中的字节偏移量，从该偏移量开始写入从流中读取的数据。</param>
      <param name="count">最多读取的字节数。</param>
      <param name="cancellationToken">要监视取消请求的标记。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="count" /> 为负。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 与 <paramref name="count" /> 的和大于缓冲区长度。</exception>
      <exception cref="T:System.NotSupportedException">流不支持读取。</exception>
      <exception cref="T:System.ObjectDisposedException">流已被释放。</exception>
      <exception cref="T:System.InvalidOperationException">该流正在由其前一次读取操作使用。</exception>
    </member>
    <member name="M:System.IO.FileStream.ReadByte">
      <summary>从文件中读取一个字节，并将读取位置提升一个字节。</summary>
      <returns>强制转换为 <see cref="T:System.Int32" /> 的字节；或者如果已到达流的末尾，则为 -1。</returns>
      <exception cref="T:System.NotSupportedException">当前流不支持读取。</exception>
      <exception cref="T:System.ObjectDisposedException">当前流已关闭。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileStream.SafeFileHandle">
      <summary>获取 <see cref="T:Microsoft.Win32.SafeHandles.SafeFileHandle" /> 对象，它代表当前 <see cref="T:System.IO.FileStream" /> 对象所封装的文件的操作系统文件句柄。</summary>
      <returns>一个对象，该对象表示当前 <see cref="T:System.IO.FileStream" /> 对象封装的文件的操作系统文件句柄。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>将该流的当前位置设置为给定值。</summary>
      <returns>流中的新位置。</returns>
      <param name="offset">相对于 <paramref name="origin" /> 的点，从此处开始查找。</param>
      <param name="origin">使用 <see cref="T:System.IO.SeekOrigin" /> 类型的值，将开始位置、结束位置或当前位置指定为 <paramref name="offset" /> 的参考点。</param>
      <exception cref="T:System.IO.IOException">发生了 I/O 错误。</exception>
      <exception cref="T:System.NotSupportedException">流不支持查找，例如，如果 FileStream 是由管道或控制台输出构造的。</exception>
      <exception cref="T:System.ArgumentException">试图在流的开始位置之前查找。</exception>
      <exception cref="T:System.ObjectDisposedException">在流关闭后调用方法。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.SetLength(System.Int64)">
      <summary>将该流的长度设置为给定值。</summary>
      <param name="value">流的新长度。</param>
      <exception cref="T:System.IO.IOException">出现 I/O 错误。</exception>
      <exception cref="T:System.NotSupportedException">流不同时支持写入和查找。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">试图将 <paramref name="value" /> 参数设置为小于 0。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>将字节块写入文件流。</summary>
      <param name="array">包含要写入该流的数据的缓冲区。</param>
      <param name="offset">
        <paramref name="array" /> 中的从零开始的字节偏移量，从此处开始将字节复制到该流。</param>
      <param name="count">最多写入的字节数。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> 为 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 和 <paramref name="count" /> 描述 <paramref name="array" /> 中的无效范围。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="count" /> 为负。</exception>
      <exception cref="T:System.IO.IOException">发生了 I/O 错误。- 或 -另一个线程可能已导致操作系统的文件句柄位置发生意外更改。</exception>
      <exception cref="T:System.ObjectDisposedException">流已关闭。</exception>
      <exception cref="T:System.NotSupportedException">当前流实例不支持写入。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.FileStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>将字节的序列异步写入当前流，将该流中的当前位置向前移动写入的字节数，并监视取消请求。</summary>
      <returns>表示异步写入操作的任务。</returns>
      <param name="buffer">从中写入数据的缓冲区。</param>
      <param name="offset">
        <paramref name="buffer" /> 中的从零开始的字节偏移量，从此处开始将字节复制到该流。</param>
      <param name="count">最多写入的字节数。</param>
      <param name="cancellationToken">要监视取消请求的标记。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 或 <paramref name="count" /> 为负。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 与 <paramref name="count" /> 的和大于缓冲区长度。</exception>
      <exception cref="T:System.NotSupportedException">流不支持写入。</exception>
      <exception cref="T:System.ObjectDisposedException">流已被释放。</exception>
      <exception cref="T:System.InvalidOperationException">该流正在由其前一次写入操作使用。</exception>
    </member>
    <member name="M:System.IO.FileStream.WriteByte(System.Byte)">
      <summary>一个字节写入文件流中的当前位置。</summary>
      <param name="value">要写入流的字节。</param>
      <exception cref="T:System.ObjectDisposedException">流已关闭。</exception>
      <exception cref="T:System.NotSupportedException">流不支持写入。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.FileSystemInfo">
      <summary>为 <see cref="T:System.IO.FileInfo" /> 和 <see cref="T:System.IO.DirectoryInfo" /> 对象提供基类。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.FileSystemInfo.#ctor">
      <summary>初始化 <see cref="T:System.IO.FileSystemInfo" /> 类的新实例。</summary>
    </member>
    <member name="P:System.IO.FileSystemInfo.Attributes">
      <summary>获取或设置当前文件或目录的特性。</summary>
      <returns>当前 <see cref="T:System.IO.FileSystemInfo" /> 的 <see cref="T:System.IO.FileAttributes" />。</returns>
      <exception cref="T:System.IO.FileNotFoundException">指定的文件不存在。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效：例如，它位于未映射的驱动器上。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <exception cref="T:System.ArgumentException">调用方试图设置无效的文件属性。- 或 -用户尝试设置属性值，但没有写入权限。</exception>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 不能初始化数据。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTime">
      <summary>获取或设置当前文件或目录的创建时间。</summary>
      <returns>当前 <see cref="T:System.IO.FileSystemInfo" /> 对象的创建日期和时间。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 不能初始化数据。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效：例如，它位于未映射的驱动器上。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">调用方试图设置无效的创建时间。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.CreationTimeUtc">
      <summary>获取或设置当前文件或目录的创建时间，其格式为协调世界时 (UTC)。</summary>
      <returns>当前 <see cref="T:System.IO.FileSystemInfo" /> 对象的创建日期及时间（UTC 格式）。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 不能初始化数据。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效：例如，它位于未映射的驱动器上。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">调用方试图设置无效的访问时间。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.FileSystemInfo.Delete">
      <summary>删除文件或目录。</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效：例如，它位于未映射的驱动器上。</exception>
      <exception cref="T:System.IO.IOException">对于文件或目录有打开句柄，并且操作系统是 Windows XP 或更早版本。此打开句柄可能是由于枚举目录和文件导致的。有关详细信息，请参阅如何：枚举目录和文件。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Exists">
      <summary>获取指示文件或目录是否存在的值。</summary>
      <returns>如果文件或目录存在，则为 true；否则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.Extension">
      <summary>获取表示文件扩展名部分的字符串。</summary>
      <returns>包含 <see cref="T:System.IO.FileSystemInfo" /> 扩展名的字符串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.FileSystemInfo.FullName">
      <summary>获取目录或文件的完整目录。</summary>
      <returns>包含完整目录的字符串。</returns>
      <exception cref="T:System.IO.PathTooLongException">完全限定路径或文件名为 260 或更多字符。</exception>
      <exception cref="T:System.Security.SecurityException">调用方没有所要求的权限。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="F:System.IO.FileSystemInfo.FullPath">
      <summary>表示目录或文件的完全限定目录。</summary>
      <exception cref="T:System.IO.PathTooLongException">完全限定路径为 260 或更多字符。</exception>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTime">
      <summary>获取或设置上次访问当前文件或目录的时间。</summary>
      <returns>上次访问当前文件或目录的时间。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 不能初始化数据。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">调用方试图设置无效的访问时间</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastAccessTimeUtc">
      <summary>获取或设置上次访问当前文件或目录的时间，其格式为协调世界时 (UTC)。</summary>
      <returns>上次访问当前文件或目录的 UTC 时间。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 不能初始化数据。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">调用方试图设置无效的访问时间。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTime">
      <summary>获取或设置上次写入当前文件或目录的时间。</summary>
      <returns>上次写入当前文件的时间。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 不能初始化数据。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">调用方试图设置无效的写入时间。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.LastWriteTimeUtc">
      <summary>获取或设置上次写入当前文件或目录的时间，其格式为协调世界时 (UTC)。</summary>
      <returns>上次写入当前文件的 UTC 时间。</returns>
      <exception cref="T:System.IO.IOException">
        <see cref="M:System.IO.FileSystemInfo.Refresh" /> 不能初始化数据。</exception>
      <exception cref="T:System.PlatformNotSupportedException">当前操作系统不是 Windows NT 或更高版本。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">调用方试图设置无效的写入时间。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.FileSystemInfo.Name">
      <summary>对于文件，获取该文件的名称。对于目录，如果存在层次结构，则获取层次结构中最后一个目录的名称。否则，Name 属性获取该目录的名称。</summary>
      <returns>一个字符串，它是父目录的名称、层次结构中最后一个目录的名称或文件的名称（包括文件扩展名）。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.FileSystemInfo.OriginalPath">
      <summary>最初由用户指定的目录（不论是相对目录还是绝对目录）。</summary>
    </member>
    <member name="M:System.IO.FileSystemInfo.Refresh">
      <summary>刷新对象的状态。</summary>
      <exception cref="T:System.IO.IOException">设备（如磁盘驱动器）未准备好。 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.IO.SearchOption">
      <summary>指定是搜索当前目录，还是搜索当前目录及其所有子目录。</summary>
    </member>
    <member name="F:System.IO.SearchOption.AllDirectories">
      <summary>在搜索操作中包括当前目录和所有它的子目录。此选项在搜索中包括重解析点，比如安装的驱动器和符号链接。</summary>
    </member>
    <member name="F:System.IO.SearchOption.TopDirectoryOnly">
      <summary>仅在搜索操作中包括当前目录。</summary>
    </member>
  </members>
</doc>