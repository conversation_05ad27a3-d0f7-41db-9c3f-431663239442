﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.BusinessLogic
{
    public class CustomTemplateBuilder
    {
        public Dictionary<string, string> Data { get; set; }

        public string ReadTemplateContent(string templateData)
        {
            string body = string.Empty;
            try
            {
                if (templateData != "")
                {
                    byte[] byteArray = Encoding.ASCII.GetBytes(templateData);
                    MemoryStream stream = new MemoryStream(byteArray);

                    using (StreamReader template = new StreamReader(stream))
                    {
                        body = BuildMessageBody(template, true);
                        template.Close();
                    }
                }
            }
            catch (Exception)
            {

            }

            return body;
        }

        public string BuildMessageBody(TextReader streamToRead, bool isHtml)
        {
            StringBuilder body = new StringBuilder();
            StringBuilder nextTag = new StringBuilder();
            bool inTag = false;
            char nextCharacter;
            char tagStart = '$';

            while (streamToRead.Peek() >= 0)
            {
                nextCharacter = Convert.ToChar(streamToRead.Peek());
                if (nextCharacter.Equals(tagStart)) inTag = !inTag;

                if (inTag)
                {
                    nextTag.Append(Convert.ToChar(streamToRead.Read()));
                    if (nextTag.Length >= 50)
                    {
                        body.Append(nextTag.ToString());
                        nextTag.Length = 0;
                        inTag = false;
                    }
                }
                else if (nextTag.Length > 0)
                {
                    if (nextCharacter.Equals(tagStart)) nextTag.Append(Convert.ToChar(streamToRead.Read()));
                    body.Append(ReplaceEmailTags(nextTag.ToString()));
                    nextTag.Length = 0;
                }
                else
                {
                    body.Append(Convert.ToChar(streamToRead.Read()));
                }
            }

            return body.ToString();
        }

        private string ReplaceEmailTags(string tag)
        {
            string returnValue = string.Empty;
            tag = tag.Trim();
            try
            {
                returnValue = Data[tag];
            }
            catch (Exception ex)
            {
                returnValue = "";
            }
            return returnValue;
        }
    }
}
