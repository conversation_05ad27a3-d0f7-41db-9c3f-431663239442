﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts;
using RevCord.DataContracts.EMREntities;
using RevCord.DataContracts.InquireEntities;
using RevCord.DataAccess.Util;
using RevCord.Util;
using RevCord.DataContracts.VoiceRecEntities;
using System.Data.SqlClient;
using RevCord.DataContracts.IQ3InspectionEntities;
using RevCord.DataContracts.CustomerDBEntities;
using RevCord.DataContracts.MGODataEntities;

namespace RevCord.DataAccess
{
    public class InquireRxDAL
    {
        private int _tenantId;
        public InquireRxDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }


        //public Case GetEventDetailsById_old(string eventId)
        //{
        //    try
        //    {
        //        Case rxCase = null;
        //        Event iq3event = null;
        //        Patient patient = null;
        //        List<Medication> medications = null;
        //        List<string> medicationImages = null;
        //        List<Drug> drugs = null;
        //        List<string> drugImages = null;
        //        List<Electroencephalogram> eegs = null;
        //        List<string> eegImages = null;
        //        //Dictionary<VitalSign, string> patientVitalSigns = null;
        //        List<PatientVital> patientVitals = null;
        //        string qbDialogId = string.Empty;
        //        using (var conn = DALHelper.GetConnection(_tenantId))
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            cmd.CommandText = DBConstants.InquireRx.CASE_DETAILS_GETBYID;
        //            cmd.Parameters.AddWithValue("@EventId", eventId);
        //            conn.Open();
        //            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InquireRx, "GetEventDetailsById_old", _tenantId));
        //            using (var dr = cmd.ExecuteReader())
        //            {
        //                // 1.Case ResultSet
        //                if (dr.HasRows)
        //                {
        //                    while (dr.Read())
        //                    {
        //                        rxCase = new Case();
        //                        rxCase.FlightNo = Convert.ToString(dr["FlightNumber"]);
        //                        rxCase.PrimaryComplaint = Convert.ToString(dr["PrimaryComplaint"]);
        //                        rxCase.Comments = Convert.ToString(dr["Comments"]);
        //                        rxCase.CreatedDate = dr["CreatedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["CreatedDate"]);
        //                        rxCase.LastModifiedDate = dr["LastModifiedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["LastModifiedDate"]);
        //                    }
        //                }
        //                //2. Interview ResultSet
        //                dr.NextResult();
        //                if (dr.HasRows)
        //                {
        //                    while (dr.Read())
        //                    {
        //                        if (rxCase == null)
        //                            rxCase = new Case();
        //                        iq3event = new Event();
        //                        iq3event.Id = Convert.ToString(dr["InterviewId"]);
        //                        iq3event.DateTime = Convert.ToString(dr["DateTime"]);
        //                        iq3event.GPS = Convert.ToString(dr["GPS"]);
        //                        iq3event.Interviewer = Convert.ToString(dr["Interviewer"]);
        //                        iq3event.Interviewee = Convert.ToString(dr["Interviewee"]);
        //                        iq3event.Notes = Convert.ToString(dr["Notes"]);
        //                        iq3event.TotalTime = Convert.ToString(dr["TotalTime"]);
        //                        iq3event.InterviewType = Convert.ToString(dr["InterviewType"]);
        //                        iq3event.FilePath = Convert.ToString(dr["FilePath"]);
        //                        iq3event.Thumb = Convert.ToString(dr["Thumb"]);
        //                        iq3event.IsRecorded = Convert.ToString(dr["IsRecorded"]);
        //                        iq3event.PrimaryArea = Convert.ToString(dr["PrimaryArea"]);
        //                    }
        //                }
        //                //3. Patient ResultSet
        //                dr.NextResult();
        //                if (dr.HasRows)
        //                {
        //                    patient = new Patient();
        //                    while (dr.Read())
        //                    {
        //                        patient.Id = (int)dr["Id"];
        //                        patient.FirstName = Convert.ToString(dr["FirstName"]);
        //                        patient.LastName = Convert.ToString(dr["LastName"]);
        //                        patient.Gender = dr["Gender"] == DBNull.Value ? Gender.NotSpecified : (Gender)Enum.Parse(typeof(Gender), Convert.ToString(dr["Gender"]));
        //                        patient.DOB = dr["DateOfBirth"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["DateOfBirth"]);
        //                        patient.Height = Convert.ToString(dr["Height"]);
        //                        patient.Weight = Convert.ToString(dr["Weight"]);
        //                        patient.BloodGroup = Convert.ToString(dr["BloodGroup"]);
        //                        patient.SocialSecurityNumber = Convert.ToString(dr["SocialSecurityNumber"]);
        //                        patient.TelephoneNo = Convert.ToString(dr["TelephoneNo"]);
        //                        patient.CellNo = Convert.ToString(dr["CellNo"]);
        //                        patient.Address = Convert.ToString(dr["Address"]);
        //                        patient.City = Convert.ToString(dr["City"]);
        //                        patient.State = Convert.ToString(dr["State"]);
        //                        patient.ZipCode = Convert.ToString(dr["ZipCode"]);
        //                        patient.Comments = Convert.ToString(dr["Comments"]);
        //                        patient.CreatedDate = dr["CreatedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["CreatedDate"]);
        //                        patient.LastModifiedDate = dr["LastModifiedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["LastModifiedDate"]);
        //                    }
        //                }
        //                //4. Patient Medications ResultSet
        //                dr.NextResult();
        //                if (dr.HasRows)
        //                {
        //                    medications = new List<Medication>();
        //                    while (dr.Read())
        //                    {
        //                        if (dr["MedicationId"] != DBNull.Value)
        //                        {
        //                            medications.Add(new Medication
        //                            {
        //                                Id = (int)dr["Id"],
        //                                Name = Convert.ToString(dr["Name"])
        //                            });
        //                        }
        //                        else
        //                        {
        //                            if (medicationImages == null) medicationImages = new List<string>();
        //                            medicationImages.Add(Convert.ToString(dr["MedicationImageName"]));
        //                        }
        //                    }
        //                }
        //                //5. Patient Drugs ResultSet
        //                dr.NextResult();
        //                if (dr.HasRows)
        //                {
        //                    drugs = new List<Drug>();
        //                    while (dr.Read())
        //                    {
        //                        if (dr["DrugId"] != DBNull.Value)
        //                        {
        //                            drugs.Add(new Drug
        //                            {
        //                                Id = (int)dr["Id"],
        //                                Name = Convert.ToString(dr["Name"])
        //                            });
        //                        }
        //                        else
        //                        {
        //                            if (drugImages == null) drugImages = new List<string>();
        //                            drugImages.Add(Convert.ToString(dr["DrugImageName"]));
        //                        }
        //                    }
        //                }
        //                //6. Patient EEG ResultSet
        //                dr.NextResult();
        //                if (dr.HasRows)
        //                {
        //                    eegs = new List<Electroencephalogram>();
        //                    while (dr.Read())
        //                    {
        //                        if (dr["EEGId"] != DBNull.Value)
        //                        {
        //                            eegs.Add(new Electroencephalogram
        //                            {
        //                                Id = (int)dr["Id"],
        //                                Name = Convert.ToString(dr["Name"])
        //                            });
        //                        }
        //                        else
        //                        {
        //                            if (eegImages == null) eegImages = new List<string>();
        //                            eegImages.Add(Convert.ToString(dr["EEGImageName"]));
        //                        }
        //                    }
        //                }
        //                //7. Patient Vitals ResultSet
        //                dr.NextResult();
        //                if (dr.HasRows)
        //                {
        //                    //patientVitalSigns = new Dictionary<VitalSign, string>();
        //                    patientVitals = new List<PatientVital>();
        //                    while (dr.Read())
        //                    {
        //                        PatientVital pv = new PatientVital();
        //                        pv.VitalSign = new VitalSign
        //                        {
        //                            Id = (int)dr["Id"],
        //                            Name = Convert.ToString(dr["Name"])
        //                        };
        //                        pv.Reading = Convert.ToString(dr["Reading"]);
        //                        //pv.Comments = Convert.ToString(dr["Comments"]);
        //                        pv.CreatedDate = dr["CreatedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["CreatedDate"]);
        //                        //pv.LastModifiedDate = dr["LastModifiedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["LastModifiedDate"]);
        //                        patientVitals.Add(pv);
        //                    }
        //                }
        //                //8. QB Dialog ResultSet
        //                dr.NextResult();
        //                if (dr.HasRows)
        //                {
        //                    if (rxCase == null)
        //                        rxCase = new Case();
        //                    if (dr.Read())
        //                    {
        //                        qbDialogId = Convert.ToString(dr["DialogId"]);
        //                    }
        //                }
        //            }
        //        }
        //        if (rxCase != null || iq3event != null)
        //        {
        //            rxCase.Event = iq3event;
        //            rxCase.Patient = patient;
        //            rxCase.Drugs = drugs;
        //            rxCase.DrugImages = drugImages;
        //            rxCase.Medications = medications;
        //            rxCase.MedicationImages = medicationImages;
        //            rxCase.EEGs = eegs;
        //            rxCase.EEGImages = eegImages;
        //            rxCase.PatientVitals = patientVitals;
        //            rxCase.QBDialogId = qbDialogId;
        //        }
        //        return rxCase;
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}

        public Case GetEventDetailsById(string eventId)
        {
            try
            {
                Case rxCase = null;
                Event iq3event = null;
                Patient patient = null;
                List<Medication> medications = null;
                List<string> medicationImages = null;
                List<Drug> drugs = null;
                List<string> drugImages = null;
                List<Electroencephalogram> eegs = null;
                List<string> eegImages = null;
                //Dictionary<VitalSign, string> patientVitalSigns = null;
                List<PatientVital> patientVitals = null;
                List<CustomField> CustomFields = null;
                List<ChatTranscript> chatTranscriptList = null;
                Inspection _Inspection = null;
                string qbDialogId = string.Empty;

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.InquireRx.CASE_DETAILS_GETBYID;
                    cmd.Parameters.AddWithValue("@EventId", eventId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InquireRx, "GetEventDetailsById", _tenantId));
                    rxCase = new Case();
                    using (var dr = cmd.ExecuteReader())
                    {
                        //1. Case ResultSet
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                rxCase.FlightNo = Convert.ToString(dr["FlightNumber"]);
                                rxCase.PrimaryComplaint = Convert.ToString(dr["PrimaryComplaint"]);
                                rxCase.Comments = Convert.ToString(dr["Comments"]);
                                rxCase.CreatedDate = dr["CreatedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["CreatedDate"]);
                                rxCase.LastModifiedDate = dr["LastModifiedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["LastModifiedDate"]);
                            }
                        }
                        //2. Interview ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                iq3event = new Event();
                                iq3event.Id = Convert.ToString(dr["InterviewId"]);
                                iq3event.DateTime = Convert.ToString(dr["DateTime"]);
                                iq3event.GPS = Convert.ToString(dr["GPS"]);
                                iq3event.Interviewer = Convert.ToString(dr["Interviewer"]);
                                iq3event.Interviewee = Convert.ToString(dr["Interviewee"]);
                                iq3event.Notes = Convert.ToString(dr["Notes"]);
                                iq3event.TotalTime = Convert.ToString(dr["TotalTime"]);
                                iq3event.InterviewType = Convert.ToString(dr["InterviewType"]);
                                iq3event.FilePath = Convert.ToString(dr["FilePath"]);
                                iq3event.Thumb = Convert.ToString(dr["Thumb"]);
                                iq3event.IsRecorded = Convert.ToString(dr["IsRecorded"]);
                                iq3event.UserEmail = DBRecordExtensions.HasColumn(dr, "UserEmail") ? Convert.ToString(dr["UserEmail"]) : string.Empty;
                                iq3event.UserPhone = DBRecordExtensions.HasColumn(dr, "UserPhone") ? Convert.ToString(dr["UserPhone"]) : string.Empty;
                                iq3event.PrimaryArea = DBRecordExtensions.HasColumn(dr, "PrimaryArea") ? (Convert.ToString(dr["PrimaryArea"]) == string.Empty ? "N/A"  : Convert.ToString(dr["PrimaryArea"]))  : "N/A";
                                iq3event.IsVirtualInspection = DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? Convert.ToBoolean(dr["IsVirtualInspection"]) : false;
                                iq3event.ReportName = DBRecordExtensions.HasColumn(dr, "ReportName") ? Convert.ToString(dr["ReportName"]) : string.Empty;
                                iq3event.FileName = DBRecordExtensions.HasColumn(dr, "FileName") ? Convert.ToString(dr["FileName"]) : string.Empty;
                                iq3event.AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty;
                            }
                        }
                        //3. Patient ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            patient = new Patient();
                            while (dr.Read())
                            {
                                patient.Id = (int)dr["Id"];
                                patient.FirstName = Convert.ToString(dr["FirstName"]);
                                patient.LastName = Convert.ToString(dr["LastName"]);
                                patient.Gender = dr["Gender"] == DBNull.Value ? Gender.NotSpecified : (Gender)Enum.Parse(typeof(Gender), Convert.ToString(dr["Gender"]));
                                patient.DOB = dr["DateOfBirth"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["DateOfBirth"]);
                                patient.Height = Convert.ToString(dr["Height"]);
                                patient.Weight = Convert.ToString(dr["Weight"]);
                                patient.BloodGroup = Convert.ToString(dr["BloodGroup"]);
                                patient.SocialSecurityNumber = Convert.ToString(dr["SocialSecurityNumber"]);
                                patient.TelephoneNo = Convert.ToString(dr["TelephoneNo"]);
                                patient.CellNo = Convert.ToString(dr["CellNo"]);
                                patient.Address = Convert.ToString(dr["Address"]);
                                patient.City = Convert.ToString(dr["City"]);
                                patient.State = Convert.ToString(dr["State"]);
                                patient.ZipCode = Convert.ToString(dr["ZipCode"]);
                                patient.Comments = Convert.ToString(dr["Comments"]);
                                patient.CreatedDate = dr["CreatedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["CreatedDate"]);
                                patient.LastModifiedDate = dr["LastModifiedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["LastModifiedDate"]);
                            }
                        }
                        //4. Patient Medications ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            medications = new List<Medication>();
                            while (dr.Read())
                            {
                                if (dr["MedicationId"] != DBNull.Value && Convert.ToInt32(dr["MedicationId"]) != 0)
                                {
                                    medications.Add(new Medication
                                    {
                                        Id = (int)dr["Id"],
                                        Name = Convert.ToString(dr["Name"])
                                    });
                                }
                                else
                                {
                                    if (medicationImages == null) medicationImages = new List<string>();
                                    medicationImages.Add(Convert.ToString(dr["MedicationImageName"]));
                                }
                            }
                        }
                        //5. Patient Drugs ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            drugs = new List<Drug>();
                            while (dr.Read())
                            {
                                if (dr["DrugId"] != DBNull.Value && Convert.ToInt32(dr["DrugId"]) != 0)
                                {
                                    drugs.Add(new Drug
                                    {
                                        Id = (int)dr["Id"],
                                        Name = Convert.ToString(dr["Name"])
                                    });
                                }
                                else
                                {
                                    if (drugImages == null) drugImages = new List<string>();
                                    drugImages.Add(Convert.ToString(dr["DrugImageName"]));
                                }
                            }
                        }
                        //6. Patient EEG ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            eegs = new List<Electroencephalogram>();
                            while (dr.Read())
                            {
                                if (dr["EEGId"] != DBNull.Value && Convert.ToInt32(dr["EEGId"]) != 0)
                                {
                                    eegs.Add(new Electroencephalogram
                                    {
                                        Id = (int)dr["Id"],
                                        Name = Convert.ToString(dr["Name"])
                                    });
                                }
                                else
                                {
                                    if (eegImages == null) eegImages = new List<string>();
                                    eegImages.Add(Convert.ToString(dr["EEGImageName"]));
                                }
                            }
                        }
                        //7. Patient Vitals ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            //patientVitalSigns = new Dictionary<VitalSign, string>();
                            patientVitals = new List<PatientVital>();
                            while (dr.Read())
                            {
                                PatientVital pv = new PatientVital();
                                pv.VitalSign = new VitalSign
                                {
                                    Id = (int)dr["Id"],
                                    Name = Convert.ToString(dr["Name"])
                                };
                                pv.Reading = Convert.ToString(dr["Reading"]);
                                //pv.Comments = Convert.ToString(dr["Comments"]);
                                pv.CreatedDate = dr["CreatedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["CreatedDate"]);
                                //pv.LastModifiedDate = dr["LastModifiedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(dr["LastModifiedDate"]);

                                patientVitals.Add(pv);

                            }
                        }
                        //8. QB Dialog ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            if (dr.Read())
                                qbDialogId = Convert.ToString(dr["DialogId"]);
                        }
                        //9. Custom Fields ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            CustomFields = new List<CustomField>();
                            while (dr.Read())
                            {
                                CustomField cf = new CustomField();
                                cf.CustomFieldHeader = Convert.ToString(dr["Title"]);
                                cf.CustomFieldData = Convert.ToString(dr["CFText"]);
                                
                                CustomFields.Add(cf);

                            }
                        }

                        //10. Chat Transcript ResultSet
                        dr.NextResult();
                        chatTranscriptList = new List<ChatTranscript>();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                ChatTranscript chatTranscript = new ChatTranscript();

                                chatTranscript.Id = Convert.ToInt32(dr["Id"]);
                                chatTranscript.EventId = Convert.ToString(dr["EventId"]);
                                chatTranscript.ChannelSid = Convert.ToString(dr["ChannelSid"]);
                                chatTranscript.Transcript = Convert.ToString(dr["Transcript"]);
                                chatTranscript.ParticipantIdsCSV = Convert.ToString(dr["ParticipantIdsCSV"]);
                                chatTranscript.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                chatTranscript.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);

                                chatTranscriptList.Add(chatTranscript);
                            }
                        }

                        //11. Inquire Inspection
                        dr.NextResult();
                        _Inspection = new Inspection();
                        if (dr.HasRows)
                        {
                            dr.Read();
                            _Inspection.Id = Convert.ToInt32(dr["Id"]);
                            _Inspection.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                            _Inspection.EventId = eventId.Trim();
                            _Inspection.InspectorId = Convert.ToInt32(dr["InspectorId"]);
                            _Inspection.Title = Convert.ToString(dr["Title"]);
                            _Inspection.Comments = Convert.ToString(dr["Comments"]);
                            _Inspection.Remarks = Convert.ToString(dr["Remarks"]);
                            _Inspection.IsPassFailRequired = Convert.ToBoolean(dr["IsPassFailRequired"]);
                            _Inspection.IsPassed = Convert.ToBoolean(dr["IsPassed"]);
                            _Inspection.FailReason = Convert.ToString(dr["FailReason"]);
                            _Inspection.InspectionStatus = Convert.ToString(dr["InspectionStatus"]);
                            _Inspection.InspectionDate = Convert.ToDateTime(dr["InspectionDate"]);
                            _Inspection.NoOfMarkers = Convert.ToInt32(dr["NoOfMarkers"]);
                            _Inspection.NoOfSections = Convert.ToInt32(dr["NoOfSections"]);
                            _Inspection.InspectorName = Convert.ToString(dr["InspectorName"]);
                            _Inspection.Description = Convert.ToString(dr["Description"]);
                            _Inspection.Duration = Convert.ToString(dr["Duration"]);
                        }

                        dr.NextResult();
                        List<Section> TemplateSections = new List<Section>();
                        if (dr.HasRows)
                        {
                            
                            while (dr.Read())
                            {
                                //Id EventId InspectionTemplateId SectionId   MarkerId MarkerTypeId    MarkerOptionId Title   MarkerAnswer MarkerPosition  Description Note    MarkerMeasurement IsSelected  IsRepeating IsPhotoAllowed  IsPhotoUploaded PhotoFileName   Ordering CreatedDate IsDeleted EventDateTime   VideoFileName
                                Section _section = new Section();
                                _section.Id = Convert.ToInt32(dr["Id"]);
                                _section.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                                _section.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                                _section.Title = Convert.ToString(dr["Title"]);
                                _section.IsDefaultSection = Convert.ToBoolean(dr["IsDefaultSection"]);
                                _section.Markers = new List<Marker>();
                                TemplateSections.Add(_section);
                            }
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                Marker _marker = new Marker();
                                _marker.Id = Convert.ToInt32(dr["Id"]);
                                _marker.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                                _marker.SectionId = Convert.ToInt32(dr["SectionId"]);
                                _marker.MarkerId = Convert.ToInt32(dr["MarkerId"]);
                                _marker.MarkerTypeId = Convert.ToInt32(dr["MarkerTypeId"]);
                                _marker.MarkerOptionId = Convert.ToInt16(dr["MarkerOptionId"]);
                                _marker.Title = Convert.ToString(dr["Title"]);
                                _marker.MarkerAnswer = Convert.ToString(dr["MarkerAnswer"]);
                                _marker.Note = dr["Note"] == DBNull.Value ? "" : Convert.ToString(dr["Note"]);
                                _marker.Description = dr["Description"] == DBNull.Value ? "" : Convert.ToString(dr["Description"]);
                                _marker.MarkerMeasurement = dr["MarkerMeasurement"] == DBNull.Value ? "" : Convert.ToString(dr["MarkerMeasurement"]);
                                _marker.Ordering = dr["Ordering"] == DBNull.Value ? 0 : Convert.ToInt32(dr["Ordering"]);
                                _marker.MarkerPosition = dr["MarkerPosition"] == DBNull.Value ? 0 : Convert.ToInt32(dr["MarkerPosition"]);
                                _marker.IsPhotoUploaded = dr["IsPhotoUploaded"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsPhotoUploaded"]);
                                _marker.BookmarkFlag = dr["BookmarkFlag"] == DBNull.Value ? 0 : Convert.ToInt32(dr["BookmarkFlag"]);
                                _marker.BookmarkFlagColorID = dr["FlagColorID"] == DBNull.Value ? "" : Convert.ToString(dr["FlagColorID"]);
                                _marker.BookmarkFlagName = dr["FlagName"] == DBNull.Value ? "" : Convert.ToString(dr["FlagName"]);
                                _marker.PhotoFileName = dr["PhotoFileName"] == DBNull.Value ? "" : Convert.ToString(dr["PhotoFileName"]);
                                _marker.IsVideoUploaded = !DBRecordExtensions.HasColumn(dr, "IsVideoUploaded") ? false : dr["IsVideoUploaded"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsVideoUploaded"]);
                                _marker.MarkerVideoFileName = !DBRecordExtensions.HasColumn(dr, "MarkerVideoFileName") ? "" : dr["MarkerVideoFileName"] == DBNull.Value ? "" : Convert.ToString(dr["MarkerVideoFileName"]);
                                _marker.EventDateTime = dr["EventDateTime"] == DBNull.Value ? "" : Convert.ToString(dr["EventDateTime"]);
                                _marker.VideoFileName = dr["VideoFileName"] == DBNull.Value ? "" : Convert.ToString(dr["VideoFileName"]);
                                _marker.IsRepeating = Convert.ToBoolean(dr["IsRepeating"]); ;
                                _marker.IsPhotoAllowed = Convert.ToBoolean(dr["IsPhotoAllowed"]);
                                _marker.IsRequired = dr["IsRequired"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsRequired"]);
                                _marker.IsMultiSection = dr["IsMultiSection"] == DBNull.Value ? false : Convert.ToBoolean(dr["IsMultiSection"]);
                                
                                _marker.IsSelected = Convert.ToBoolean(dr["IsSelected"]); ;
                                //Id EventId InspectionTemplateId SectionId   MarkerId MarkerTypeId    MarkerOptionId Title   MarkerAnswer MarkerPosition  Description Note    MarkerMeasurement IsSelected  IsRepeating IsPhotoAllowed  IsPhotoUploaded PhotoFileName   Ordering CreatedDate IsDeleted EventDateTime   VideoFileName
                                foreach (var _Sec in TemplateSections)
                                {
                                    if(_Sec.Id == _marker.SectionId)
                                    {
                                        var CanInsert = true;
                                        if(_marker.MarkerTypeId == 3)
                                        {
                                            foreach (var marker in _Sec.Markers)
                                            {
                                                if (marker.MarkerTypeId == _marker.MarkerTypeId && marker.MarkerPosition == _marker.MarkerPosition)
                                                {
                                                    marker.MarkerAnswer = marker.MarkerAnswer + ", " + _marker.MarkerAnswer;
                                                    CanInsert = false;
                                                }
                                                    
                                            }
                                        }
                                        if(CanInsert) _Sec.Markers.Add(_marker);
                                    }
                                          
                                }
                            }
                        }

                        List<PreInspectionData> lPreInspectionData = new List<PreInspectionData>();
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                PreInspectionData _PreInspectionData = new PreInspectionData();
                                _PreInspectionData.PreInspectionTitle = Convert.ToString(dr["PreInspectionTitle"]);
                                _PreInspectionData.PreInspectionText = Convert.ToString(dr["PreInspectionText"]);
                                _PreInspectionData.PreInspectionDescription = Convert.ToString(dr["PreInspectionDescription"]);
                                lPreInspectionData.Add(_PreInspectionData);
                            }
                        }
                        _Inspection.Sections = TemplateSections;
                        _Inspection.PreInspectionDataList = lPreInspectionData;

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            iq3event.AssetModel = new List<AssetModel>();
                            while (dr.Read())
                            {
                                var assetModel = new AssetModel();
                                assetModel.Id = Convert.ToInt32(dr["Id"]);
                                assetModel.Name = Convert.ToString(dr["Name"]);
                                assetModel.Caption = Convert.ToString(dr["Caption"]);
                                iq3event.AssetModel.Add(assetModel);
                            }
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                iq3event.AssetDictionary = Enumerable.Range(0, dr.FieldCount).ToDictionary(dr.GetName, dr.GetValue);
                            }
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            iq3event.IQ3AssetDetails = new List<IQ3AssetDetail>();
                            while (dr.Read())
                            {
                                IQ3AssetDetail iq3AssetDetail = new IQ3AssetDetail();

                                iq3AssetDetail.Id = Convert.ToInt32(dr["Id"]);
                                iq3AssetDetail.Name = Convert.ToString(dr["Name"]);
                                iq3AssetDetail.Value = Convert.ToString(dr["Value"]);

                                iq3event.IQ3AssetDetails.Add(iq3AssetDetail);
                            }
                        }

                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            iq3event.MGOReportData = ORMapper.GetMGOReportData(dr);
                        }

                    }
                }

                if (rxCase != null)
                {
                    rxCase.Event = iq3event;
                    rxCase.Patient = patient;
                    rxCase.Drugs = drugs;
                    rxCase.DrugImages = drugImages;
                    rxCase.Medications = medications;
                    rxCase.MedicationImages = medicationImages;
                    rxCase.EEGs = eegs;
                    //rxCase.EEGImages = eegImages;
                    rxCase.EEGImages = (eegs != null && eegImages == null) ? eegs.Select(e => e.Name).ToList() : eegImages;  //new List<string>() //(eegImages ?? new List<string>()).Where(e => e != null).ToList();
                    rxCase.PatientVitals = patientVitals;
                    rxCase.QBDialogId = qbDialogId;
                    rxCase.CustomFields = CustomFields;
                    rxCase.ChatTranscript = chatTranscriptList;
                    rxCase.Inspection = _Inspection;
                }
                return rxCase;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public List<InspectionNotesHistory> FetchInspectionNotesHistory(int InspectionTemplateId, string TemplateName, string userEmail)
        {
            if ((TemplateName == null || TemplateName.Trim().Length == 0) && InspectionTemplateId > 0) TemplateName = GetTemplateNameByTemplateId(InspectionTemplateId);
            List<InspectionNotesHistory> inspectionHistoryList = new List<InspectionNotesHistory>();
            try
            {
                var eventHistory = FetchEventHistory(TemplateName, userEmail);
                foreach (var item in eventHistory)
                {
                    try
                    {
                        InspectionNotesHistory inspectionHistory = new InspectionNotesHistory();

                        using (var conn = DALHelper.GetConnection(_tenantId))
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = "iq3_GetInspection_MarkerNotesHistory";
                            cmd.Parameters.AddWithValue("@EventId", item.EventId);
                            conn.Open();
                            using (SqlDataReader dr = cmd.ExecuteReader())
                            {
                                if (dr.HasRows)
                                {
                                    inspectionHistory.Inspection = new Inspection();

                                    if (dr.HasRows)
                                    {
                                        dr.Read();
                                        inspectionHistory.Inspection.Id = Convert.ToInt32(dr["Id"]);
                                        inspectionHistory.Inspection.InspectionTemplateId = Convert.ToInt32(dr["InspectionTemplateId"]);
                                        inspectionHistory.Inspection.EventId = item.EventId;
                                        inspectionHistory.Inspection.InspectorId = Convert.ToInt32(dr["InspectorId"]);
                                        inspectionHistory.Inspection.Title = Convert.ToString(dr["Title"]);
                                        inspectionHistory.Inspection.Comments = Convert.ToString(dr["Comments"]);
                                        inspectionHistory.Inspection.Remarks = Convert.ToString(dr["Remarks"]);
                                        inspectionHistory.Inspection.InspectionDate = Convert.ToDateTime(dr["InspectionDate"]);
                                        //inspectionHistory.Inspection.InspectionDateStr = inspectionHistory.Inspection.InspectionDate.ToString("MMM dd, yyyy HH:mm:ss");
                                    }

                                    List<PreInspectionData> lPreInspectionData = new List<PreInspectionData>();
                                    dr.NextResult();
                                    if (dr.HasRows)
                                    {
                                        while (dr.Read())
                                        {
                                            PreInspectionData _PreInspectionData = new PreInspectionData();
                                            _PreInspectionData.PreInspectionTitle = Convert.ToString(dr["PreInspectionTitle"]);
                                            _PreInspectionData.PreInspectionText = Convert.ToString(dr["PreInspectionText"]);
                                            lPreInspectionData.Add(_PreInspectionData);
                                        }
                                    }

                                    inspectionHistory.Inspection.PreInspectionDataList = lPreInspectionData;

                                    List<Marker> markerNotes = new List<Marker>();
                                    Marker marker = null;

                                    dr.NextResult();
                                    if (dr.HasRows)
                                    {
                                        while (dr.Read())
                                        {
                                            marker = new Marker();
                                            marker.MarkerId = Convert.ToInt32(dr["markerid"]);
                                            marker.SectionId = Convert.ToInt32(dr["sectionid"]);
                                            marker.MarkerTypeId = Convert.ToInt32(dr["MarkerTypeId"]);
                                            marker.Note = Convert.ToString(dr["Note"]);
                                            marker.MarkerPosition = Convert.ToInt32(dr["MarkerPosition"]);
                                            markerNotes.Add(marker);
                                        }
                                    }

                                    inspectionHistory.MarkerNotes = markerNotes;

                                    inspectionHistoryList.Add(inspectionHistory);
                                }
                            }
                        }
                    }
                    catch { }
                }
            }
            catch { }

            return inspectionHistoryList;
        }

        public List<EventHistory> FetchEventHistory(string TemplateName, string userEmail)
        {
            List<EventHistory> eventHistoryList = new List<EventHistory>();
            try {
                foreach (var tenant in DALHelper.GetTenantsByEmail(userEmail)) {
                    try {
                        using (var conn = DALHelper.GetConnection(tenant))
                        using (var cmd = conn.CreateCommand()) {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.CommandText = "iq3_GetAll_EventHistory";
                            cmd.Parameters.AddWithValue("@TemplateName", TemplateName);
                            cmd.Parameters.AddWithValue("@UserEmail", userEmail);
                            conn.Open();

                            using (SqlDataReader dr = cmd.ExecuteReader()) {
                                while (dr.Read()) {
                                    EventHistory eventHistory = new EventHistory();
                                    eventHistory.EventId = Convert.ToString(dr["CallId"]);
                                    eventHistory.TenantId = tenant;
                                    eventHistory.StartTime = Convert.ToString(dr["StartTime"]);
                                    
                                    eventHistoryList.Add(eventHistory);
                                }
                            }
                        }
                    }
                    catch { }
                }
                eventHistoryList = eventHistoryList.OrderByDescending(item => item.StartTime).ToList();
                if (eventHistoryList.Count > 10) eventHistoryList = eventHistoryList.Take(10).ToList();
            }
            catch { }

            return eventHistoryList;
        }

        public List<InspectionTemplate> GetInspectionTemplateById(int inspectionTemplateId, string userEmail)
        {
            List<InspectionTemplate> inspectionTemplates = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "iq3_InspectionTemplate_GetById_RevRec";
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                    cmd.Parameters.AddWithValue("@UserEmail", userEmail);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader()) {
                        inspectionTemplates = ORMapper.GetAllInspectionTemplates(dr);
                    }
                }

                if (inspectionTemplates != null)
                {
                    foreach (var inspectionTemplate in inspectionTemplates)
                    {
                        inspectionTemplate.PreInspections = this.GetPreInspectionsByTemplateId(inspectionTemplate.Id);
                        inspectionTemplate.TemplateSections = this.GetSectionsByTemplateId(inspectionTemplate.Id);

                        if (inspectionTemplate.TemplateSections != null)
                        {
                            foreach (var section in inspectionTemplate.TemplateSections)
                            {
                                section.Markers = this.GetMarkersBySectionId(section.Id);

                                if (section.Markers != null)
                                {
                                    foreach (var marker in section.Markers)
                                    {
                                        marker.MarkerOptions = this.GetMarkerOptions(Convert.ToInt32(marker.Id));
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return inspectionTemplates;
        }

        private List<PreInspection> GetPreInspectionsByTemplateId(int inspectionTemplateId)
        {
            List<PreInspection> preInspections = new List<PreInspection>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Inspection.INSPECTION_TEMPLATE_PREINSPECTION_GETALL;
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemplateId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        preInspections = ORMapper.GetAllPreInspections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return preInspections;
        }

        private string GetTemplateNameByTemplateId(int inspectionTemlateId)
        {
            string TemplateName = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM iq3InspectionTemplate WHERE InspectionTemplateId = @InspectionTemplateId and IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemlateId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            TemplateName = Convert.ToString(dr["Title"]);
                        }
                    }
                }
            }
            catch (Exception ex) { return null; }
            return TemplateName;
        }

        private List<Section> GetSectionsByTemplateId(int inspectionTemlateId)
        {
            List<Section> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM iq3Section WHERE InspectionTemplateId = @InspectionTemplateId and IsDeleted = 0 AND SectionType = 1;";
                    cmd.Parameters.AddWithValue("@InspectionTemplateId", inspectionTemlateId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.GetAllSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        private List<Marker> GetMarkersBySectionId(int sectionId)
        {
            List<Marker> markers = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from iq3Marker where SectionId = @SectionId and IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@SectionId", sectionId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markers = ORMapper.GetAllMarkers(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markers;
        }

        private List<MarkerOption> GetMarkerOptions(int markerId)
        {
            List<MarkerOption> markerOptions = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "select * from iq3MarkerOption where MarkerId = @MarkerId and IsDeleted = 0;";
                    cmd.Parameters.AddWithValue("@MarkerId", markerId);
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        markerOptions = ORMapper.GetAllMarkerOptions(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return markerOptions;
        }

        public CaseImages GetEventImagesById(string eventId)
        {
            try
            {
                CaseImages rxCaseImages = null;
                List<string> medicationImages = null;
                List<string> drugImages = null;
                List<string> eegImages = null;

                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.InquireRx.CASE_IMAGES_GETBYID;
                    cmd.Parameters.AddWithValue("@EventId", eventId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.InquireRx, "GetEventImagesById", _tenantId));
                    rxCaseImages = new CaseImages();
                    using (var dr = cmd.ExecuteReader())
                    {
                        //CaseImages
                        //1. Patient MedicationsImages ResultSet
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                if (dr["MedicationId"] == DBNull.Value || Convert.ToInt32(dr["MedicationId"]) == 0)
                                {
                                    if (medicationImages == null) medicationImages = new List<string>();
                                    medicationImages.Add(Convert.ToString(dr["MedicationImageName"]));
                                }
                            }
                        }
                        //2. Patient DrugsImages ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                if (dr["DrugId"] == DBNull.Value || Convert.ToInt32(dr["DrugId"]) == 0)
                                {
                                    if (drugImages == null) drugImages = new List<string>();
                                    drugImages.Add(Convert.ToString(dr["DrugImageName"]));
                                }
                            }
                        }
                        //3. Patient EEGImages ResultSet
                        dr.NextResult();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                if (dr["EEGId"] == DBNull.Value || Convert.ToInt32(dr["EEGId"]) == 0)
                                {
                                    if (eegImages == null) eegImages = new List<string>();
                                    eegImages.Add(Convert.ToString(dr["EEGImageName"]));
                                }
                            }
                        }
                    }
                }
                if (rxCaseImages != null)
                {
                    rxCaseImages.DrugImages = drugImages;
                    rxCaseImages.MedicationImages = medicationImages;
                    rxCaseImages.EEGImages = eegImages;
                }
                return rxCaseImages;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public int SaveCustomFieldsData(int userNum, List<CustomField> customFields)
        {
            int dbId = -1;
            try
            {
                foreach (var customField in customFields)
                {
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = "iq3AddCustomFieldData";
                        cmd.Parameters.AddWithValue("@CustomFieldId", customField.CustomFieldId);
                        cmd.Parameters.AddWithValue("@CustomFieldText", customField.CustomFieldData);
                        cmd.Parameters.AddWithValue("@EventId", customField.EventId);
                        conn.Open();
                        dbId = Convert.ToInt32(cmd.ExecuteScalar());

                        conn.Close();
                    }
                }

            }
            catch (Exception ex) { throw ex; }
            return dbId;
        }
    }
}
