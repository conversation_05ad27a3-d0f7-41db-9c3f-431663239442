﻿using RevCord.DataContracts.ReportEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Criteria
{
    public class SavedReportCriteria
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public int UserId { get; set; }

        public string ReportName { get; set; }

        public List<ReportCategory> ReportCategories { get; set; }

        public List<TypeOfReport> ReportTypes { get; set; }

        public int PageSize { get; set; }
        public int PageIndex { get; set; }

        public bool? IsFavorite { get; set; }
        public bool? IsShared { get; set; }
        public bool? IsScheduled { get; set; }
        public bool LoadGroupBasedReports { get; set; }
        public SavedReportMode SavedReportMode { get; set; }


    }
}
