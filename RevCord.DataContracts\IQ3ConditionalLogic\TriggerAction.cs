﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3ConditionalLogic
{
    public class TriggerAction
    {
        public int Id { get; set; }
        public int MarkerId { get; set; }
        public int MarkerLogicId { get; set; }
        public int TriggerId { get; set; }
        public TriggerType TriggerType { get; set; }
        public string CSVEmail { get; set; }
        public string CSVPhoneNumber { get; set; }
        public string CSVHideMarkerIds { get; set; }
        //public int FocusMarkerId { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }

        //public MarkerToFocus MarkerToFocus { get; set; }
        //public List<MarkerToHide> MarkersToHide { get; set; }
        //public List<MarkerToUnHide> MarkersToUnHide { get; set; }

        public string MarkerToFocusJSON { get; set; }
        public string MarkersToHideJSON { get; set; }
        public string MarkersToUnHideJSON { get; set; }
    }

    public class MarkerToFocus {
        public int MarkerId { get; set; }
        public int SectionId { get; set; }
    }

    public class MarkerToHide
    {
        public int MarkerId { get; set; }
        public int SectionId { get; set; }
    }

    public class MarkerToUnHide
    {
        public int MarkerId { get; set; }
        public int SectionId { get; set; }
    }

    public class MultiMatch
    {
        public string MatchText { get; set; }
        public int MatchID { get; set; }
    }
}