﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.ReportEntities
{
    public class SharedReportInfo
    {
        public int Id { get; set; }
        public int ReportId { get; set; }
        public int SharedBy { get; set; }
        public List<int> SharedWith { get; set; }
        public DateTime? CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
        public bool IsDeleted { get; set; }
    }
}
