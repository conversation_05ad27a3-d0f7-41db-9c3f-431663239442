﻿using RevCord.DataAccess;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.ScheduleReportEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;

namespace RevCord.BusinessLogic
{
    public class ScheduleReportManager
    {
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress;

        public ScheduleReportResponse GetAllScheduleReports(ScheduleReportRequest objScheduleReportRequest)
        {
            try
            {
                List<ScheduleReport> objScheduleReportList = null;

                objScheduleReportList = new ScheduleReportDAL(objScheduleReportRequest.tenantId).GetAllScheduleReport(objScheduleReportRequest.UserId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ScheduleReport, "GetAllScheduleReports", objScheduleReportRequest.tenantId, new JavaScriptSerializer().Serialize(objScheduleReportList)));
                return new ScheduleReportResponse
                {
                    Acknowledge = objScheduleReportList == null ? AcknowledgeType.Failure : AcknowledgeType.Success,
                    ScheduleReports = objScheduleReportList
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetAllScheduleReports", objScheduleReportRequest.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetAllScheduleReports", objScheduleReportRequest.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public ScheduleReportResponse GetScheduleReport(ScheduleReportRequest objScheduleReportRequest)
        {
            try
            {
                ScheduleReport objScheduleReport = null;

                objScheduleReport = new ScheduleReportDAL(objScheduleReportRequest.tenantId).GetScheduleReport(objScheduleReportRequest.ScheduleReportId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ScheduleReport, "GetScheduleReport", objScheduleReportRequest.tenantId, new JavaScriptSerializer().Serialize(objScheduleReport)));

                return new ScheduleReportResponse
                {
                    Acknowledge = objScheduleReport != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    ScheduleReport = objScheduleReport
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetScheduleReport", objScheduleReportRequest.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetScheduleReport", objScheduleReportRequest.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public ScheduleReportResponse GetScheduleReportToBeSend(ScheduleReportRequest objScheduleReportRequest)
        {
            try
            {
                List<ScheduleReport> objScheduleReportList = null;

                objScheduleReportList = new ScheduleReportDAL(objScheduleReportRequest.tenantId).GetScheduleReportToBeSend(objScheduleReportRequest.UserId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ScheduleReport, "GetScheduleReportToBeSend", objScheduleReportRequest.tenantId, new JavaScriptSerializer().Serialize(objScheduleReportList)));

                return new ScheduleReportResponse
                {
                    Acknowledge = objScheduleReportList == null ? AcknowledgeType.Failure : AcknowledgeType.Success,
                    ScheduleReports = objScheduleReportList
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetScheduleReportToBeSend", objScheduleReportRequest.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetScheduleReportToBeSend", objScheduleReportRequest.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public ScheduleReportResponse SaveScheduleReport(ScheduleReportRequest objScheduleReportRequest)
        {
            try
            {
                int ScheduleReportId = new ScheduleReportDAL(objScheduleReportRequest.tenantId).SaveScheduleReport(objScheduleReportRequest.ScheduleReport);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ScheduleReport, "SaveScheduleReport", objScheduleReportRequest.tenantId, " ScheduleReportId = " + ScheduleReportId));
                return new ScheduleReportResponse
                {
                    Acknowledge = ScheduleReportId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    ScheduleReportId = ScheduleReportId
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "SaveScheduleReport", objScheduleReportRequest.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "SaveScheduleReport", objScheduleReportRequest.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ScheduleReportResponse UpdateScheduleReport(ScheduleReportRequest objScheduleReportRequest)
        {
            try
            {
                int ScheduleReportId = new ScheduleReportDAL(objScheduleReportRequest.tenantId).UpdateScheduleReport(objScheduleReportRequest.ScheduleReport);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ScheduleReport, "UpdateScheduleReport", objScheduleReportRequest.tenantId, " ScheduleReportId = " + ScheduleReportId));
                return new ScheduleReportResponse
                {
                    Acknowledge = ScheduleReportId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    ScheduleReportId = ScheduleReportId
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "UpdateScheduleReport", objScheduleReportRequest.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "UpdateScheduleReport", objScheduleReportRequest.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ScheduleReportResponse UpdateScheduleReportAfterReportSend(ScheduleReportRequest objScheduleReportRequest)
        {
            try
            {
                int ScheduleReportId = new ScheduleReportDAL(objScheduleReportRequest.tenantId).UpdateScheduleReportAfterReportSend(objScheduleReportRequest.ScheduleReport);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ScheduleReport, "UpdateScheduleReportAfterReportSend", objScheduleReportRequest.tenantId, " ScheduleReportId = " + ScheduleReportId));
                return new ScheduleReportResponse
                {
                    Acknowledge = ScheduleReportId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    ScheduleReportId = ScheduleReportId
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "UpdateScheduleReportAfterReportSend", objScheduleReportRequest.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "UpdateScheduleReportAfterReportSend", objScheduleReportRequest.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ScheduleReportResponse RemoveScheduleReport(ScheduleReportRequest objScheduleReportRequest)
        {
            try
            {
                int ScheduleReportId = new ScheduleReportDAL(objScheduleReportRequest.tenantId).RemoveScheduleReport(objScheduleReportRequest.ScheduleReportId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.ScheduleReport, "RemoveScheduleReport", objScheduleReportRequest.tenantId, " ScheduleReportId = " + ScheduleReportId));

                return new ScheduleReportResponse
                {
                    Acknowledge = ScheduleReportId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    ScheduleReportId = ScheduleReportId
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "RemoveScheduleReport", objScheduleReportRequest.tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "RemoveScheduleReport", objScheduleReportRequest.tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ScheduleReportResponse GetAllReportRecipient(ScheduleReportRequest objScheduleReportRequest)
        {
            try
            {
                List<string> objReportRecipientList = null;

                objReportRecipientList = new ScheduleReportDAL(objScheduleReportRequest.tenantId).GetAllReportRecipient(objScheduleReportRequest.ScheduleReportId);

                return new ScheduleReportResponse
                {
                    Acknowledge = objReportRecipientList != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    ReportRecipients = objReportRecipientList
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}