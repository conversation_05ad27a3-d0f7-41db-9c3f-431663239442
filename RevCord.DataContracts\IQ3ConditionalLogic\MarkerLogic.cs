﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IQ3ConditionalLogic
{
    public class MarkerLogic
    {
        public int Id { get; set; }
        public int MarkerId { get; set; }
        //public int MarkerTypeId { get; set; }
        /// <summary>
        /// Default to 0 for Note type markers. Pass appropriate value for single & multi select types.
        /// </summary>
        public string MatchText { get; set; }
        public int MarkerOptionId { get; set; }
        public MarkerLogicType LogicType { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsDeleted { get; set; }

        //public LogicTrigger LogicTrigger { get; set; }
        public List<LogicTrigger> LogicTriggers { get; set; }
    }
}