﻿//***********************************************************************
// AssemblyName   : 
// Author               :   <PERSON><PERSON>
// Created ON           :   18-April-2012
//
// Last Modified By     :   
// Last Modified On     :   
// Description          :   
//***********************************************************************
using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel;
using System.Reflection;


namespace RevCord.DataContracts.ReportEntities
{
    public partial class RPTReportParameter
    {


        public static List<XAxisData> GetXAxisData()
        {
            return RPTReportParameter.EnumToList<XAxisData>(true, (short) XAxisData.None);
        }

        public static List<GroupData> GetGroupData()
        {
            return RPTReportParameter.EnumToList<GroupData>(true, (short) GroupData.None);
        }

        public static List<YAxisData> GetYAxisData()
        {
            return RPTReportParameter.EnumToList<YAxisData>(true, (short) YAxisData.None);
        }

        private static List<T> EnumToList<T>(bool SkipNone = false, short NoneValue = 0)
        {
            Type enumType = typeof(T);

            if (enumType.BaseType != typeof(Enum))
                throw new ArgumentException("It must be enumeration type.");

            Array enumArray = Enum.GetValues(enumType);
            List<T> enumList = new List<T>(enumArray.Length);

            foreach (short val in enumArray)
            {
                if (SkipNone == true && val == NoneValue)
                    continue;

                enumList.Add((T)Enum.Parse(enumType, val.ToString()));
            }

            return enumList;
        }

        public static string GetEnumDescription(Enum enumVal)
        {
            FieldInfo fi = enumVal.GetType().GetField(enumVal.ToString());
            DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

            if (attributes.Length > 0)
            {
                return attributes[0].Description;
            }
            else
            {
                return enumVal.ToString();
            }
        }


    }
}