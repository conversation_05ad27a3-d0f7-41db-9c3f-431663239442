﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.SqlClient;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.CommonEntities;
using System.Threading.Tasks;
using RevCord.Util;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.DataAccess
{
    public class CallTaggingDAL
    {
        private int _tenantId;
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 300);

        public CallTaggingDAL(int tenantId)
        {
            _tenantId = tenantId;
        }

        public List<CallTag> GetCallTags()
        {
            List<CallTag> callTags = null;
            CallTag Calltag = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CallTags.GETALL_CALLTAGS;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CallTag, "GetCallTags", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callTags = new List<CallTag>();
                        while (dr.Read())
                        {
                            Calltag = new CallTag();
                            Calltag.TagID = Convert.ToInt32(dr["Id"]);
                            Calltag.TagName = Convert.ToString(dr["TagName"]);
                            Calltag.TagColor = Convert.ToString(dr["TagColorID"]);
                            callTags.Add(Calltag);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callTags;

        }

        public List<CallTag> SaveCallTags(List<CallTag> lcalltags)
        {
            List<CallTag> callTags = null;
            CallTag Calltag = null;
            try
            {
                foreach (var calltag in lcalltags)
                {
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.CallTags.UPDATE_CALLTAG;
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@Id", calltag.TagID);
                        cmd.Parameters.AddWithValue("@TagName", calltag.TagName.Trim());
                        cmd.Parameters.AddWithValue("@TagColor", calltag.TagColor.Trim());
                        

                        conn.Open();
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CallTag, "SaveCallTags", _tenantId));

                        cmd.ExecuteReader();
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return lcalltags;
        }

        public int DeleteCallTagging(int tagid)
        {
            try
            {
                int rowsAffected = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CallTags.DELETE_CALLTAG;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@Id", tagid);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.CallTag, "DeleteCallTagging", _tenantId));
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }
    }
}
