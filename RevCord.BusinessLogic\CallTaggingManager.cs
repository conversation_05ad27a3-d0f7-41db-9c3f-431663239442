﻿using System;
using System.Collections.Generic;
using RevCord.DataAccess;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.BusinessLogic
{
    public class CallTaggingManager
    {
        public List<CallTag> GetCallTags(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "GetCallTags", tenantId, "GetCallTags function has been called successfully. "));
                return new CallTaggingDAL(tenantId).GetCallTags();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "GetCallTags", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "GetCallTags", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<CallTag> SaveCallTags(List<CallTag> lcalltags, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "GetCallTags", tenantId, "GetCallTags function has been called successfully. "));
                return new CallTaggingDAL(tenantId).SaveCallTags(lcalltags);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "GetCallTags", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "GetCallTags", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int DeleteCallTagging(int tagid, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Playlist, "DeleteCallTagging", tenantId, "GetCallTags function has been called successfully. "));
                return new CallTaggingDAL(tenantId).DeleteCallTagging(tagid);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Common, "DeleteCallTagging", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Common, "DeleteCallTagging", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

    }
}
