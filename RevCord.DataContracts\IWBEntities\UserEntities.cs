﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.IWBEntities
{
    public class IwbUser
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Phone { get; set; }
        public IwbUserType Type { get; set; }
        public DateTime? DOB { get; set; }
        public string SocialSecurityNumber { get; set; }
        public string WelderStencilNumber { get; set; }
        public int RoleId { get; set; }


        public int CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int? LastModifiedBy { get; set; }
    }

    public class IwbRole
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }

        public List<IwbModule> Modules { get; set; }
    }
    public class IwbModule
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string URL { get; set; }
        public List<IwbPermission> Permissions { get; set; }
    }

    public class IwbPermission
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string CssClassName { get; set; }
        public string Description { get; set; }

        public int ModuleId { get; set; }

        //[JsonConverter(typeof(StringEnumConverter))]
        //public IwbPermissionType Type { get; set; }
    }

    /*public enum IwbPermissionType
    {
        [EnumMember(Value = "Page")]
        [DescriptionAttribute("Page")]
        Page = 1,

        [EnumMember(Value = "Module")]
        [DescriptionAttribute("Module")]
        Module = 2,
    }*/
}