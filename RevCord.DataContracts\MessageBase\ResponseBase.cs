﻿using System;

namespace RevCord.DataContracts.MessageBase
{
    public class ResponseBase
    {
        /// <summary>
        /// Default Constructor for ResponseBase.
        /// </summary>
        //public ResponseBase() { }

        /// <summary>
        /// A flag indicating success or failure of the web service response back to the
        /// client. Default is success.
        /// </summary>
        public AcknowledgeType Acknowledge = AcknowledgeType.Success;

        /// <summary>
        /// Message back to client. Mostly used when a web service failure occurs.
        /// </summary>
        public string Message;

        /// <summary>
        /// Number of rows affected by "Create", "Update", or "Delete" action.
        /// </summary>
        public int RowsAffected;
        public TagRuleInfo tagRule = TagRuleInfo.Yes;
    }

}
