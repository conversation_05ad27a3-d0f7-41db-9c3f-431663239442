﻿using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.VoiceRecEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.DTO
{
    public class DALMediaResponse
    {
        public AcknowledgeType Acknowledge = AcknowledgeType.Success;

        public int TotalPages { get; set; }

        public long TotalRecords { get; set; }

        public string Message;

        public List<MediaInfo> ListOfMedias { get; set; }

        public string ProcessingTime { get; set; }

    }
}
