﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataAccess;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.Messages;
using RevCord.DataAccess.Util;
using System.Data.SqlClient;
using RevCord.DataContracts;
using RevCord.DataContracts.EMREntities;
using RevCord.DataContracts.IQ3InspectionEntities;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.BusinessLogic
{
    public class InquireRxManager
    {
        public InquireRxResponse GetEventDetails(InquireRxRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireRx, "GetEventDetails", request.TenantId, "GetEventDetails function has been called successfully. request.EventId = " + request.EventId));
                var iq3Event = new InquireRxDAL(request.TenantId).GetEventDetailsById(request.EventId);
                return new InquireRxResponse
                {
                    Case = iq3Event,
                    Acknowledge = iq3Event != null ? AcknowledgeType.Success : AcknowledgeType.Failure, //Commented by Arivu
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireRx, "GetEventDetails", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireRx, "GetEventDetails", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public List<InspectionTemplate> GetInspectionTemplateById(int inspectionTemplateId, string userEmail, int TenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireRx, "GetInspectionTemplateById", TenantId, "GetInspectionTemplateById function has been called successfully. request.inspectionTemplateId = " + inspectionTemplateId));
                List<InspectionTemplate> _InspectionTemplate = new InquireRxDAL(TenantId).GetInspectionTemplateById(inspectionTemplateId, userEmail);
                return _InspectionTemplate;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireRx, "GetInspectionTemplateById", TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireRx, "GetInspectionTemplateById", TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public List<InspectionNotesHistory> FetchInspectionNotesHistory(int inspectionTemplateId, string TemplateName, string userEmail, int TenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireRx, "FetchInspectionNotesHistory", TenantId, "FetchInspectionNotesHistory function has been called successfully. request.inspectionTemplateId = " + inspectionTemplateId));
                List<InspectionNotesHistory> _InspectionNotesHistory = new InquireRxDAL(TenantId).FetchInspectionNotesHistory(inspectionTemplateId, TemplateName, userEmail);
                return _InspectionNotesHistory;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireRx, "FetchInspectionNotesHistory", TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireRx, "FetchInspectionNotesHistory", TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        

        public InquireRxResponse GetEventImages(InquireRxRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireRx, "GetEventImages", request.TenantId, "GetEventImages function has been called successfully. request.EventId = " + request.EventId));
                var mdEventImages = new InquireRxDAL(request.TenantId).GetEventImagesById(request.EventId);
                return new InquireRxResponse
                {
                    CaseImages = mdEventImages,
                    Acknowledge = mdEventImages != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireRx, "GetEventImages", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireRx, "GetEventImages", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public InquireRxResponse SaveCustomFieldsData(InquireRxRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireRx, "SaveCustomFieldsData", request.TenantId, "SaveCustomFieldsData function has been called successfully. request.EventId = " + request.EventId));
                var result = new InquireRxDAL(request.TenantId).SaveCustomFieldsData(request.UserNum, request.CustomFields);
                return new InquireRxResponse
                {
                    Acknowledge = result > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireRx, "SaveCustomFieldsData", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireRx, "SaveCustomFieldsData", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
    }
}