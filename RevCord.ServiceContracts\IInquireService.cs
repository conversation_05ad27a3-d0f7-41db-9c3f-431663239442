﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.InquireEntities;
using RevCord.DataContracts;
using RevCord.DataContracts.Messages;

namespace RevCord.ServiceContracts
{
    public interface IInquireService
    {
        InquireResponse GetInvitationsByUser(int userId, int tenantId);
        //IEnumerable<Invitation> GetByStatus(int userId);
        Invitation GetInvitation(int invitationId, int tenantId);
        InquireResponse GetInvitationsByWhereClause(InquireRequest request);
        bool CheckAlreadyInvited(string email, InvitationStatus status, int tenantId);
        InquireResponse CreateInvitation(InquireRequest request);
        InquireResponse ResendInvitation(InquireRequest request);
        bool ChangeInvitationStatus(int invitationId, InvitationStatus status, int? userId, DateTime? modifiedDate, int tenantId);
        bool CancelInvitation(int invitationId, int userId, DateTime modifiedDate, int tenantId);//RevokeInvitation
        InquireResponse AcceptInvitation(InquireRequest request);
        bool RejectInvitation(int invitationId, DateTime modifiedDate, int tenantId);
        //InvitationStatus GetInvitationStatus(int invitationId);
        InquireResponse SaveEventInvitation(InquireRequest request);
        EventInvitationGroup GetEventInvitation(InquireRequest request);

        InquireResponse GetPictureEventsByCallId(int tenantId, string callId);
    }
}
