﻿using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.MGODataEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class MGODataResponse : ResponseBase
    {
        public bool FlagStatus { get; set; }
        public int Id { get; set; }

        public MGOTempData TempData { get; set; }
        public MGOReportData ReportData { get; set; }

        public List<MGOInspectionType> InspectionTypes { get; set; }
        public List<MGOInspectionOption> InspectionOptions { get; set; }
    }
}
