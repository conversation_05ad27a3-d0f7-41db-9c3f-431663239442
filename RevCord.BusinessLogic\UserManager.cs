﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.Messages;
using RevCord.DataAccess;
using RevCord.DataContracts.Criteria;
using RevCord.Util;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.DTO;
using System.Xml.Linq;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using RevCord.DataContracts.IQ3;

namespace RevCord.BusinessLogic
{
    public class UserManager
    {
        private readonly static int _secRecAdminDefaultValue = AppSettingsUtil.GetInt("secRecAdmDefault", 1000);
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress; //"127.0.0.1";

        public UMResponse GetGroupsTree(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGroupsTree", umReq.TenantId, "GetGroupsTree function has been called successfully."));

                var criteria = umReq.TreeCriteria as GroupTreeCriteria;

                List<TreeviewData> dbNodes = null;
                if (umReq.IsRoleBasedAccessEnabled)
                {
                    dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType, criteria.IsRevCellRequired);
                }
                else
                {
                    switch (umReq.UserType)
                    {
                        case UserType.AdminSuper:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType);
                            break;
                        case UserType.AdminNormal:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType);
                            break;
                        case UserType.Simple:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType);
                            break;
                        case UserType.Additional:
                            break;
                        case UserType.Default:
                            break;
                        default:
                            throw new InvalidOperationException("Invalid User Type");
                    }
                }
                if (dbNodes == null) return null;
                var treeNodes = dbNodes.BuildGroupsTree();
                var treeNodes_1 = dbNodes.BuildGroupsTree();
                List<GroupTree> groupsAll = new List<GroupTree>
            {
                new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio"/*"audio_list.png"*/, TreeviewData = treeNodes},
                new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen"/*"screens_list.png"*/, TreeviewData = treeNodes},
                new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire"/*"screens_list.png"*/, TreeviewData = treeNodes},
            };

                if (umReq.IsDemoMode)
                    groupsAll.AddRange(DemoData.GetDemoGroups());//DemoData.GroupTree);

                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    AllGroups = groupsAll.OrderBy(g => g.Id).ToList(),
                    NoOfTreeNode = dbNodes.Count,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetGroupsTree", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupsTree", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetAudioGroupsTree(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetAudioGroupsTree", umReq.TenantId, "GetAudioGroupsTree function has been called successfully."));
                var criteria = umReq.TreeCriteria as GroupTreeCriteria;
                List<TreeviewData> dbNodes = null;

                if (umReq.IsRoleBasedAccessEnabled)
                {
                    dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType, criteria.IsRevCellRequired);
                }
                else
                {
                    switch (umReq.UserType)
                    {
                        case UserType.AdminSuper:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, criteria.IsRevCellRequired);
                            break;
                        case UserType.AdminNormal:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType, criteria.IsRevCellRequired/*,criteria.SelectType*/);
                            break;
                        case UserType.Simple:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType, criteria.IsRevCellRequired/*,criteria.SelectType*/);
                            break;
                        case UserType.Additional:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType, criteria.IsRevCellRequired/*,criteria.SelectType*/);
                            break;
                        case UserType.Default:
                            break;
                        default:
                            throw new InvalidOperationException("Invalid User Type");
                    }
                }
                var treeNodes = dbNodes.BuildGroupsTree();
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    AudioGroup = new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = treeNodes },
                    Inquire_group = new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "Inquire", TreeviewData = treeNodes },
                    NoOfTreeNode = dbNodes.Count,
                    DBExtension = dbNodes,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetAudioGroupsTree", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetAudioGroupsTree", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetAudioGroupsTreeFromRecorder(Recorder recorder, UMRequest umReq)
        {
            var criteria = umReq.TreeCriteria as GroupTreeCriteria;
            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetAudioGroupsTreeFromRecorder", umReq.TenantId, "GetAudioGroupsTreeFromRecorder function has been called successfully."));
            List<TreeviewData> dbNodes = null;
            try
            {
                if (recorder.IsPrimary)
                {
                    switch (umReq.UserType)
                    {
                        case UserType.AdminSuper:
                            dbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTreeFromRecorder(recorder, criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType);
                            break;
                        case UserType.AdminNormal:
                            dbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTreeNonAdminFromRecorder(recorder, criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType);
                            break;
                        case UserType.Simple:
                            if (umReq.TreeCriteria.IsEnterpriseTree)                // Note : This code section is specific to Enterprise User Tree
                            {
                                int adminUserNum = 1000;
                                int adminUserSelectType = 0;
                                string adminUserId = string.Empty;
                                int type = 1;
                                dbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTreeFromRecorder(recorder, adminUserNum, adminUserId, criteria.AuthNum, criteria.AuthType, type, adminUserSelectType);
                                List<string> recNodes = new List<string>();
                                List<TreeviewData> modifiedTreeview = new List<TreeviewData>();
                                char splitter = (umReq.TreeCriteria.AuthNum == 7) ? 'A' : 'E';
                                recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, recorder);
                                modifiedTreeview = dbNodes.FindAll(p => p.IsGroup == true);
                                foreach (var recNode in recNodes)
                                {
                                    if (dbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                        modifiedTreeview.Add(dbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                                }
                                dbNodes = modifiedTreeview;
                            }
                            else
                                dbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTreeNonAdminFromRecorder(recorder, criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType);
                            break;
                        case UserType.Additional:
                            dbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTreeNonAdminFromRecorder(recorder, criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType);
                            break;
                        case UserType.Default:
                            break;
                        default:
                            throw new InvalidOperationException("Invalid User Type");
                    }
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    //entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc");
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                    switch (umReq.UserType)
                    {
                        case UserType.AdminSuper:
                            dbNodes = entClient.GetGroupsTreeFromRecorder(recorder, _secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType).ToList();
                            break;
                        case UserType.AdminNormal:
                            dbNodes = entClient.GetGroupsTreeNonAdminFromRecorder(recorder, _secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType).ToList();
                            break;
                        case UserType.Simple:
                            if (umReq.TreeCriteria.IsEnterpriseTree)                // Note : This code section is specific to Enterprise User Tree
                            {
                                int adminUserNum = 1000;
                                int adminUserSelectType = 0;
                                string adminUserId = string.Empty;
                                int type = 1;
                                dbNodes = entClient.GetGroupsTreeFromRecorder(recorder, adminUserNum, adminUserId, criteria.AuthNum, criteria.AuthType, type, adminUserSelectType).ToList();
                                List<string> recNodes = new List<string>();
                                List<TreeviewData> modifiedTreeview = new List<TreeviewData>();
                                char splitter = (umReq.TreeCriteria.AuthNum == 7) ? 'A' : 'E';
                                recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, recorder);
                                modifiedTreeview = dbNodes.FindAll(p => p.IsGroup == true);
                                foreach (var recNode in recNodes)
                                {
                                    if (dbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                        modifiedTreeview.Add(dbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                                }
                                dbNodes = modifiedTreeview;
                            }
                            else
                                dbNodes = entClient.GetGroupsTreeNonAdminFromRecorder(recorder, _secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType).ToList();
                            break;
                        case UserType.Additional:
                            if (umReq.TreeCriteria.IsEnterpriseTree)                // Note : This code section is specific to Enterprise User Tree
                            {
                                int adminUserNum = 1000;
                                int adminUserSelectType = 0;
                                string adminUserId = string.Empty;
                                int type = 1;
                                dbNodes = entClient.GetGroupsTreeFromRecorder(recorder, adminUserNum, adminUserId, criteria.AuthNum, criteria.AuthType, type, adminUserSelectType).ToList();
                                List<string> recNodes = new List<string>();
                                List<TreeviewData> modifiedTreeview = new List<TreeviewData>();
                                char splitter = (umReq.TreeCriteria.AuthNum == 7) ? 'A' : 'E';
                                recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, recorder);
                                modifiedTreeview = dbNodes.FindAll(p => p.IsGroup == true);
                                foreach (var recNode in recNodes)
                                {
                                    if (dbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                        modifiedTreeview.Add(dbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                                }
                                dbNodes = modifiedTreeview;
                            }
                            else
                                dbNodes = entClient.GetGroupsTreeNonAdminFromRecorder(recorder, _secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType).ToList();
                            break;
                        case UserType.Default:
                            break;
                        default:
                            throw new InvalidOperationException("Invalid User Type");
                    }
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetAudioGroupsTreeFromRecorder", umReq.TenantId, "GetAudioGroupsTreeFromRecorder(). Audio groups tree build-up successfully from recorder. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name));
                TreeviewData treeNodes = new TreeviewData();
                if (dbNodes != null)
                    treeNodes = dbNodes.BuildGroupsTree();
                else
                    dbNodes = new List<TreeviewData>();
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    AudioGroup = new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = treeNodes },
                    Inquire_group = new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "Inquire", TreeviewData = treeNodes },
                    NoOfTreeNode = dbNodes.Count,
                    DBExtension = dbNodes,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetAudioGroupsTreeFromRecorder", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function GetAudioGroupsTreeFromRecorder(). Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetAudioGroupsTreeFromRecorder", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetInquireGroupsTree(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetInquireGroupsTree", umReq.TenantId, "GetInquireGroupsTree function has been called successfully."));
                var criteria = umReq.TreeCriteria as GroupTreeCriteria;
                List<TreeviewData> dbNodes = null;
                if (umReq.IsRoleBasedAccessEnabled)
                {
                    dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType, criteria.IsRevCellRequired);
                }
                else
                {
                    switch (umReq.UserType)
                    {
                        case UserType.AdminSuper:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetInquireGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType);
                            break;
                        case UserType.AdminNormal:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType/*,criteria.SelectType*/);
                            break;
                        case UserType.Simple:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType);
                            break;
                        case UserType.Additional:
                            dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, umReq.Role.Id, umReq.Role.RoleType);
                            break;
                        case UserType.Default:
                            break;
                        default:
                            throw new InvalidOperationException("Invalid User Type");
                    }
                }
                var treeNodes = dbNodes.BuildGroupsTree();
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Inquire_group = new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "Inquire", TreeviewData = treeNodes },
                    NoOfTreeNode = dbNodes.Count,
                    DBExtension = dbNodes,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetInquireGroupsTree", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetInquireGroupsTree", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetInquireGroupsTreeFromRecorder(Recorder recorder, UMRequest umReq)
        {
            var criteria = umReq.TreeCriteria as GroupTreeCriteria;
            List<TreeviewData> dbNodes = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetInquireGroupsTreeFromRecorder", umReq.TenantId, "GetInquireGroupsTreeFromRecorder function has been called successfully."));
                switch (umReq.UserType)
                {
                    case UserType.AdminSuper:
                        dbNodes = new UserManagerDALEC(umReq.TenantId).GetInquireGroupsTreeFromRecorder(recorder, criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType);
                        break;
                    case UserType.AdminNormal:
                        dbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTreeNonAdminFromRecorder(recorder, criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType/*,criteria.SelectType*/);
                        break;
                    case UserType.Simple:
                        if (umReq.TreeCriteria.IsEnterpriseTree)        // Note : This code section is specific to Enterprise User Tree
                        {
                            int adminUserNum = 1000;
                            int adminUserSelectType = 0;
                            string adminUserId = string.Empty;
                            int type = 7;
                            dbNodes = new UserManagerDALEC(umReq.TenantId).GetInquireGroupsTreeFromRecorder(recorder, adminUserNum, adminUserId, criteria.AuthNum, criteria.AuthType, type, adminUserSelectType);
                            List<string> recNodes = new List<string>();
                            List<TreeviewData> modifiedTreeview = new List<TreeviewData>();
                            char splitter = 'E';    //(umReq.TreeCriteria.AuthNum == 5 || umReq.TreeCriteria.AuthNum == 7) ? 'A' : 'E';
                            recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, recorder);
                            modifiedTreeview = dbNodes.FindAll(p => p.IsGroup == true);
                            foreach (var recNode in recNodes)
                            {
                                if (dbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                    modifiedTreeview.Add(dbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                            }
                            dbNodes = modifiedTreeview;
                        }
                        else
                            dbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTreeNonAdminFromRecorder(recorder, criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType/*,criteria.SelectType*/);
                        break;
                    case UserType.Additional:
                        dbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTreeNonAdminFromRecorder(recorder, criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType/*,criteria.SelectType*/);
                        break;
                    case UserType.Default:
                        break;
                    default:
                        throw new InvalidOperationException("Invalid User Type");
                }
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetInquireGroupsTreeFromRecorder", umReq.TenantId, "GetInquireGroupsTreeFromRecorder(). Inquire groups tree build-up successfully from recorder. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name));
                var treeNodes = dbNodes.BuildGroupsTree();
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Inquire_group = new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "Inquire", TreeviewData = treeNodes },
                    NoOfTreeNode = dbNodes.Count,
                    DBExtension = dbNodes,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetInquireGroupsTreeFromRecorder", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function GetInquireGroupsTreeFromRecorder(). Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetInquireGroupsTreeFromRecorder", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        //public UMResponse GetMDGroupsTree(UMRequest umReq)
        //{
        //    try
        //    {
        //        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetMDGroupsTree", umReq.TenantId, "GetMDGroupsTree function has been called successfully."));

        //        var criteria = umReq.TreeCriteria as GroupTreeCriteria;
        //        List<TreeviewData> dbNodes = null;

        //        switch (umReq.UserType)
        //        {
        //            case UserType.AdminSuper:
        //                dbNodes = new UserManagerDAL(umReq.TenantId).GetInquireGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType);
        //                break;
        //            case UserType.AdminNormal:
        //                dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType/*,criteria.SelectType*/);
        //                break;
        //            case UserType.Simple:
        //                dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType/*,criteria.SelectType*/);
        //                break;
        //            case UserType.Additional:
        //                dbNodes = new UserManagerDAL(umReq.TenantId).GetGroupsTreeNonAdmin(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType/*,criteria.SelectType*/);
        //                break;
        //            case UserType.Default:
        //                break;
        //            default:
        //                throw new InvalidOperationException("Invalid User Type");
        //        }
        //        var treeNodes = dbNodes.BuildGroupsTree();

        //        return new UMResponse
        //        {
        //            Acknowledge = AcknowledgeType.Success,
        //            //MD_group = new GroupTree { Id = 11, GroupType = GroupType.MD, CssName = "md", TreeviewData = treeNodes },
        //            NoOfTreeNode = dbNodes.Count,
        //            DBExtension = dbNodes,
        //        };
        //    }
        //    catch (SqlException sqle)
        //    {
        //        Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetMDGroupsTree", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
        //        throw sqle;
        //    }
        //    catch (Exception ex)
        //    {
        //        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetMDGroupsTree", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
        //        throw ex;
        //    }
        //}

        public UMResponse GetAdvanceReportTreeFromRecorderEC(UMRequest umReq, string pageloadedvalue, int RecId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetAdvanceReportTreeFromRecorderEC", umReq.TenantId, "GetAdvanceReportTreeFromRecorderEC function has been called successfully."));

                List<Recorder> recorders = umReq.Recorders;
                var criteria = umReq.TreeCriteria as GroupTreeCriteria;
                var recGroups = new List<RecorderGroupTree>();
                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        //int secRecAdminDefaultValue = AppSettingsUtil.GetInt("secRecAdmDefault", 1000);
                        List<TreeviewData> audioDbNodes = null;
                        List<TreeviewData> agentDbNodes = null;
                        List<TreeviewData> inqDbNodes = null;
                        //List<TreeviewData> mdDbNodes = null;

                        if (rec.IsPrimary)
                        {
                            audioDbNodes = new UserManagerDALEC(umReq.TenantId).GetAdvanceReportTreeFromRecorderEC(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec);
                            inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetAdvanceReportTreeFromRecorderEC(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 12 : 7, criteria.UserType, rec);
                            //mdDbNodes = new UserManagerDALEC(umReq.TenantId).GetAdvanceReportTreeFromRecorderEC(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 16 : 8, criteria.UserType, rec);
                            //mdDbNodes = new UserManagerDALEC(umReq.TenantId).GetAdvanceReportTreeFromRecorderEC(criteria.UserNum, criteria.UserId, 7, criteria.AuthType, 16, criteria.UserType, rec);
                            //cmd.Parameters.AddWithValue("@AuthNum", 7);
                            //cmd.Parameters.AddWithValue("@AuthType", 1);
                            //cmd.Parameters.AddWithValue("@Type", 16);

                            //inqDbNodes = UserManagerDALEC.GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, criteria.UserType, rec);
                        }
                        else
                        {
                            //recDbNodes = UserManagerDALEC.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 1, 0, rec);
                            //inqDbNodes = UserManagerDALEC.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, 0, rec);

                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                            audioDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 1, 0, rec).ToList();
                            inqDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, 0, rec).ToList();
                            //mdDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 8, 0, rec).ToList();

                            if (criteria.IsEnterpriseTree)              // Note : This code section is specific to Enterprise User Tree
                            {
                                List<string> recNodes = new List<string>();
                                List<string> inqNodes = new List<string>();
                                List<TreeviewData> modifiedRecDBNodes = new List<TreeviewData>();
                                List<TreeviewData> modifiedInqDBNodes = new List<TreeviewData>();
                                //List<TreeviewData> modifiedMdDBNodes = new List<TreeviewData>();
                                //char splitter = (criteria.AuthNum == 5 || criteria.AuthNum == 7) ? 'A' : 'E';
                                char splitter = (criteria.AuthNum == 7) ? 'A' : 'E';

                                recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, rec);
                                modifiedRecDBNodes = audioDbNodes.FindAll(p => p.IsGroup == true);
                                foreach (var recNode in recNodes)
                                {
                                    if (audioDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                        modifiedRecDBNodes.Add(audioDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                                }
                                audioDbNodes = modifiedRecDBNodes;

                            }
                        }

                        //TreeviewData _mdTreeView = new TreeviewData
                        //{
                        //    NodeId = "4000",
                        //    NodeCaption = "Root",
                        //    ParentNodeId = 4000,
                        //    Depth = 0,
                        //    MenuType = 1,
                        //    ViewType = 0,
                        //    IsGroup = true,
                        //    Param1 = "4000",
                        //    Param2 = "",
                        //    Param3 = "1",
                        //    Childrens = mdDbNodes
                        //};


                        var recTreeNodes = new TreeviewData();
                        var inqTreeNodes = new TreeviewData();
                        //var mdTreeNodes = new TreeviewData();

                        if (audioDbNodes != null)
                            recTreeNodes = audioDbNodes.BuildGroupsTree();
                        if (inqDbNodes != null)
                            inqTreeNodes = inqDbNodes.BuildGroupsTree();
                        //if (mdDbNodes != null)
                        //    mdTreeNodes = mdDbNodes.BuildGroupsTree();

                        List<GroupTree> groupsAll = new List<GroupTree>
                    {
                        new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes },
                        new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = recTreeNodes },
                        new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes },
                        //new GroupTree{ Id= 11, GroupType = GroupType.MD, CssName = "md", TreeviewData = mdTreeNodes },
                    };
                        recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll.OrderBy(g => g.Id).ToList() });
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetAdvanceReportTreeFromRecorderEC", umReq.TenantId, "GetAdvanceReportTreeFromRecorderEC(). Groups tree fetched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while fetching groups tree from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetAdvanceReportTreeFromRecorderEC", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroups = recGroups,
                    //NoOfTreeNode = dbNodes.Count,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetAdvanceReportTreeFromRecorderEC", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetAdvanceReportTreeFromRecorderEC", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetTreeNodesByRecorder(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetTreeNodesByRecorder", umReq.TenantId, "GetTreeNodesByRecorder function has been called successfully."));

                List<Recorder> recorders = umReq.Recorders;

                var criteria = umReq.TreeCriteria as GroupTreeCriteria;

                if (criteria.IsAudioOnly)
                {
                    return this.GetGroupsTreeAudioEC(umReq);
                }

                RecorderGroupTree recGroup = null;
                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        List<TreeviewData> audioDbNodes = null;
                        List<TreeviewData> teamsDbNodes = null;
                        List<TreeviewData> revcellDbNodes = null;
                        List<TreeviewData> inqDbNodes = null;
                        //List<TreeviewData> mdDbNodes = null;
                        List<TreeviewData> agentsDbNodes = null;
                        List<GroupTree> groupsAll;

                        if (rec.IsPrimary)
                        {
                            if (umReq.IsInquireView)
                            {
                                //inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetInquireGroupsTreeforEvaluationReportFromRecorder(rec, criteria.UserNum, criteria.SelectType);
                                inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 12 : 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                            }
                            else if (umReq.TreeCriteria.IsAgentNodesRequired)
                            {
                                //recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec);
                                audioDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                teamsDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 20, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                revcellDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 22, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                agentsDbNodes = new UserManagerDALEC(umReq.TenantId).GetUsersTreeFromRecorder(rec, umReq.TreeCriteria.UserNum);
                                //inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetInquireGroupsTreeforEvaluationReportFromRecorder(rec, criteria.UserNum, criteria.SelectType, umReq.Role.Id, umReq.Role.RoleType);
                                inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 12 : 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);

                                if (audioDbNodes == null)
                                    continue;

                                var recTreeNodes = audioDbNodes.BuildGroupsTree();
                                var teamsTreeNodes = teamsDbNodes.BuildGroupsTree();
                                var revcellTreeNodes = teamsDbNodes.BuildGroupsTree();
                                groupsAll = new List<GroupTree> { new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "Audio", TreeviewData = recTreeNodes } };
                                groupsAll.Add(new GroupTree { Id = 13, GroupType = GroupType.Teams, CssName = "Teams", TreeviewData = teamsTreeNodes });
                                groupsAll.Add(new GroupTree { Id = 14, GroupType = GroupType.Revcell, CssName = "Revcell", TreeviewData = revcellTreeNodes });

                                var agentTreeView = new TreeviewData
                                {
                                    NodeId = "2000",
                                    NodeCaption = "Evaluated Agents",
                                    ParentNodeId = 2000,
                                    Depth = 0,
                                    MenuType = 1,
                                    ViewType = 0,
                                    IsGroup = true,
                                    Param1 = "2000",
                                    Param2 = "",
                                    Param3 = "1",
                                    Childrens = agentsDbNodes
                                };
                                groupsAll.Add(new GroupTree { Id = 8, GroupType = GroupType.Agents, CssName = "Agents", TreeviewData = agentTreeView });
                                recGroup = new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll };
                                continue;
                            }
                            else
                            {
                                audioDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                teamsDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 20, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                revcellDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 22, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 12 : 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                //mdDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 16 : 8, criteria.UserType, rec);
                            }

                            if (criteria.UserType == 0)
                            {
                                // Ability to grab Revcell extensions from inquire and add under audio category
                                //List<TreeviewData> revcellNodes = new List<TreeviewData>();
                                //if (inqDbNodes != null)
                                //{
                                //    revcellNodes = inqDbNodes.Where(rc => rc.IsRevCell == true).ToList();
                                //    if (audioDbNodes != null)
                                //        audioDbNodes.AddRange(revcellNodes);
                                //}

                                // Ability to grab inquire extensions from Audio and add under IQ3 category
                                List<TreeviewData> iq3Nodes = new List<TreeviewData>();
                                if (audioDbNodes != null)
                                {
                                    iq3Nodes = audioDbNodes.Where(rc => rc.IsIQ3 == true).ToList();
                                    if (inqDbNodes != null)
                                        inqDbNodes.AddRange(iq3Nodes);
                                }
                            }

                            if (umReq.IsInquireView)
                            {
                                var inqTreeNodes = inqDbNodes.BuildGroupsTree();
                                groupsAll = new List<GroupTree>
                                {
                                    new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "Inquire", TreeviewData = inqTreeNodes },
                                };
                            }
                            else
                            {
                                if (audioDbNodes == null)
                                    continue;
                                var recTreeNodes = audioDbNodes.BuildGroupsTree();

                                if (teamsDbNodes == null)
                                    continue;
                                var teamsTreeNodes = teamsDbNodes.BuildGroupsTree();

                                if (revcellDbNodes == null)
                                    continue;
                                var revcellTreeNodes = revcellDbNodes.BuildGroupsTree();

                                bool b_isinquire = false;
                                if (inqDbNodes == null)
                                    continue;
                                foreach (TreeviewData item in inqDbNodes.BuildGroupsTree().Childrens)
                                    if (!item.IsGroup) b_isinquire = true;

                                //bool b_isMD = false;
                                //if (mdDbNodes == null)
                                //    continue;
                                //foreach (TreeviewData item in mdDbNodes.BuildGroupsTree().Childrens)
                                //    if (!item.IsGroup) b_isMD = true;

                                groupsAll = new List<GroupTree>
                            {
                                new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "Audio", TreeviewData = recTreeNodes },
                                new GroupTree{ Id= 13, GroupType = GroupType.Teams, CssName = "Teams", TreeviewData = teamsTreeNodes },
                                new GroupTree{ Id= 14, GroupType = GroupType.Revcell, CssName = "Revcell", TreeviewData = revcellTreeNodes },
                            };

                                if (umReq.TreeCriteria.IsAgentNodesRequired)
                                {
                                    var agentTreeView = new TreeviewData
                                    {
                                        NodeId = "2000",
                                        NodeCaption = "Evaluated Agents",
                                        ParentNodeId = 2000,
                                        Depth = 0,
                                        MenuType = 1,
                                        ViewType = 0,
                                        IsGroup = true,
                                        Param1 = "2000",
                                        Param2 = "",
                                        Param3 = "1",
                                        Childrens = agentsDbNodes
                                    };
                                    groupsAll.Add(new GroupTree { Id = 8, GroupType = GroupType.Agents, CssName = "Agents", TreeviewData = agentTreeView });
                                }

                                if (b_isinquire)
                                {
                                    var inqTreeNodes = inqDbNodes.BuildGroupsTree();
                                    groupsAll.Add(new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "Inquire", TreeviewData = inqTreeNodes });
                                }

                                //if (b_isMD)
                                //{
                                //    var mdTreeNodes = mdDbNodes.BuildGroupsTree();
                                //    groupsAll.Add(new GroupTree { Id = 11, GroupType = GroupType.MD, CssName = "MD", TreeviewData = mdTreeNodes });
                                //}
                            }

                            if (!umReq.IsTeamsEnabled)
                            {
                                groupsAll.RemoveAll(x => x.GroupType == GroupType.Teams);
                            }

                            if (!umReq.IsRevcellEnabled)
                            {
                                groupsAll.RemoveAll(x => x.GroupType == GroupType.Revcell);
                            }
                        }
                        else
                        {
                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                            audioDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, 0, rec).ToList();
                            //inqDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 12, 0, rec).ToList();
                            //mdDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 16, 0, rec).ToList();
                            agentsDbNodes = entClient.GetUsersTreeFromRecorder(rec, criteria.UserNum).ToList();

                            //inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 12 : 7, criteria.UserType, rec);
                            //mdDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 16 : 8, criteria.UserType, rec);

                            if (criteria.IsEnterpriseTree)              // Note : This code section is specific to Enterprise User Tree
                            {
                                List<string> audioNodes = new List<string>();
                                List<string> inqNodes = new List<string>();
                                List<TreeviewData> modifiedRecDBNodes = new List<TreeviewData>();
                                List<TreeviewData> modifiedInqDBNodes = new List<TreeviewData>();
                                //char splitter = (criteria.AuthNum == 5 || criteria.AuthNum == 7) ? 'A' : 'E';
                                char splitter = (criteria.AuthNum == 7) ? 'A' : 'E';

                                audioNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, rec);
                                modifiedRecDBNodes = audioDbNodes.FindAll(p => p.IsGroup == true);
                                foreach (var recNode in audioNodes)
                                {
                                    if (audioDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                        modifiedRecDBNodes.Add(audioDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                                }
                                audioDbNodes = modifiedRecDBNodes;
                            }

                            if (audioDbNodes == null)
                                continue;
                            var audioTreeNodes = audioDbNodes.BuildGroupsTree();

                            groupsAll = new List<GroupTree>
                            {
                                new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = audioTreeNodes },
                                //new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = recTreeNodes },
                            };

                            if (umReq.TreeCriteria.IsAgentNodesRequired)
                            {
                                var agentTreeView = new TreeviewData
                                {
                                    NodeId = "2000",
                                    NodeCaption = "Evaluated Agents",
                                    ParentNodeId = 2000,
                                    Depth = 0,
                                    MenuType = 1,
                                    ViewType = 0,
                                    IsGroup = true,
                                    Param1 = "2000",
                                    Param2 = "",
                                    Param3 = "1",
                                    Childrens = agentsDbNodes
                                };
                                groupsAll.Add(new GroupTree { Id = 8, GroupType = GroupType.Agents, CssName = "agents", TreeviewData = agentTreeView });
                            }
                        }

                        recGroup = new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll };
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetTreeNodesByRecorder", umReq.TenantId, "GetTreeNodesByRecorder(). Groups tree fetched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while fetching groups tree from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetTreeNodesByRecorder", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }

                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroup = recGroup,
                };

            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetTreeNodesByRecorder", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetTreeNodesByRecorder", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetGroupsTreeEC(UMRequest umReq)
        {
            try
            {

                List<Recorder> recorders = umReq.Recorders;

                //bool isDemoMode = AppSettingsUtil.GetBool("isDemo");//bool isDemo = AppSettingsHelper.GetValueAsBool("isDemo", false);
                //bool isEcEnable = AppSettingsUtil.GetBool("isECEnabled");

                var criteria = umReq.TreeCriteria as GroupTreeCriteria;

                if (criteria.IsAudioOnly)
                {
                    return this.GetGroupsTreeAudioEC(umReq);
                }

                var recGroups = new List<RecorderGroupTree>();
                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        //int secRecAdminDefaultValue = AppSettingsUtil.GetInt("secRecAdmDefault", 1000);
                        List<TreeviewData> recDbNodes = null;
                        List<TreeviewData> inqDbNodes = null;
                        //List<TreeviewData> mdDbNodes = null;
                        List<TreeviewData> RadioTalkGroupDbNodes = null;
                        List<GroupTree> groupsAll;

                        if (rec.IsPrimary)
                        {
                            recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                            inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 12 : 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                            //mdDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 16 : 8, criteria.UserType, rec);

                            //var rcNodes = inqDbNodes.Where(rc => rc.IsRevCell == true);
                            //recDbNodes.AddRange(rcNodes);

                            if (criteria.UserType == 0)
                            {
                                // Ability to grab Revcell extensions from inquire and add under audio category
                                List<TreeviewData> revcellNodes = new List<TreeviewData>();
                                if (inqDbNodes != null)
                                {
                                    revcellNodes = inqDbNodes.Where(rc => rc.IsRevCell == true).ToList();
                                    if (recDbNodes != null)
                                        recDbNodes.AddRange(revcellNodes);
                                }

                                // Ability to grab inquire extensions from Audio and add under IQ3 category
                                List<TreeviewData> iq3Nodes = new List<TreeviewData>();
                                if (recDbNodes != null)
                                {
                                    iq3Nodes = recDbNodes.Where(rc => rc.IsIQ3 == true).ToList();
                                    if (inqDbNodes != null)
                                        inqDbNodes.AddRange(iq3Nodes);
                                }
                            }


                            if (umReq.IsInquireView)
                            {
                                var inqTreeNodes = inqDbNodes.BuildGroupsTree();
                                groupsAll = new List<GroupTree>
                                {
                                    new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes },
                                };
                            }
                            else
                            {

                                if (criteria.IsRadioTalkGroup)
                                    RadioTalkGroupDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 15, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);

                                if (recDbNodes == null)
                                    continue;
                                var recTreeNodes = recDbNodes.BuildGroupsTree();

                                bool b_isinquire = false;
                                if (inqDbNodes == null)
                                    continue;
                                foreach (TreeviewData item in inqDbNodes.BuildGroupsTree().Childrens)
                                    if (!item.IsGroup) b_isinquire = true;

                                //bool b_isMD = false;
                                //if (mdDbNodes == null)
                                //    continue;
                                //foreach (TreeviewData item in mdDbNodes.BuildGroupsTree().Childrens)
                                //    if (!item.IsGroup) b_isMD = true;

                                bool b_isTalkgroup = false;
                                if (criteria.IsRadioTalkGroup && RadioTalkGroupDbNodes != null && rec.IsPrimary)
                                {
                                    foreach (TreeviewData item in RadioTalkGroupDbNodes.BuildGroupsTree().Childrens)
                                        if (!item.IsGroup) b_isTalkgroup = true;
                                }
                                

                                if (criteria.HideTextAndScreensTree)
                                {
                                    groupsAll = new List<GroupTree>
                                    {
                                        new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes },
                                    };
                                }
                                else
                                {
                                    groupsAll = new List<GroupTree>
                                    {
                                        new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes },
                                        new GroupTree { Id = 3, GroupType =  GroupType.Text, CssName = "text", TreeviewData = recTreeNodes },
                                        new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = recTreeNodes },
                                    };
                                }
                                if (b_isinquire)
                                {
                                    var inqTreeNodes = inqDbNodes.BuildGroupsTree();
                                    groupsAll.Add(new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes });
                                }

                                //if (b_isMD)
                                //{
                                //    var mdTreeNodes = mdDbNodes.BuildGroupsTree();
                                //    groupsAll.Add(new GroupTree { Id = 11, GroupType = GroupType.MD, CssName = "md", TreeviewData = mdTreeNodes });
                                //}

                                if (b_isTalkgroup)
                                {
                                    var RadioTalkGroupTreeNodes = RadioTalkGroupDbNodes.BuildGroupsTree();
                                    groupsAll.Add(new GroupTree { Id = 8, GroupType = GroupType.Filter, CssName = "FILTER", TreeviewData = RadioTalkGroupTreeNodes });
                                }
                            }
                        }
                        else
                        {
                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                            recDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 1, 0, rec).ToList();

                            if (criteria.IsEnterpriseTree)              // Note : This code section is specific to Enterprise User Tree
                            {
                                List<string> recNodes = new List<string>();
                                List<string> inqNodes = new List<string>();
                                List<TreeviewData> modifiedRecDBNodes = new List<TreeviewData>();
                                List<TreeviewData> modifiedInqDBNodes = new List<TreeviewData>();
                                //char splitter = (criteria.AuthNum == 5 || criteria.AuthNum == 7) ? 'A' : 'E';
                                char splitter = (criteria.AuthNum == 7) ? 'A' : 'E';

                                recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, rec);
                                modifiedRecDBNodes = recDbNodes.FindAll(p => p.IsGroup == true);
                                foreach (var recNode in recNodes)
                                {
                                    if (recDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                        modifiedRecDBNodes.Add(recDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                                }
                                recDbNodes = modifiedRecDBNodes;
                            }

                            if (recDbNodes == null)
                                continue;
                            var recTreeNodes = recDbNodes.BuildGroupsTree();

                            //if (umReq.IsInquireView)
                            //{
                            //    groupsAll = new List<GroupTree>
                            //    {

                            //    };
                            //}
                            //else
                            //{
                                groupsAll = new List<GroupTree>
                                {
                                    new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes },
                                    //new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = recTreeNodes },
                                };
                            //}
                        }
                        recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll.OrderBy(g => g.Id).ToList() });
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGroupsTreeEC", umReq.TenantId, "GetGroupsTreeEC(). Groups tree fetched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while fetching groups tree from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupsTreeEC", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroups = recGroups,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetGroupsTreeEC", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupsTreeEC", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetGroupsTreeEC(UMRequest umReq, string pageloadedvalue, int RecId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGroupsTreeEC", umReq.TenantId, "GetGroupsTreeEC function has been called successfully."));

                List<Recorder> recorders = umReq.Recorders;
                var criteria = umReq.TreeCriteria as GroupTreeCriteria;

                if (criteria.IsAudioOnly)
                {
                    return this.GetGroupsTreeAudioEC(umReq, pageloadedvalue, RecId);
                }

                var recGroups = new List<RecorderGroupTree>();
                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;

                    try
                    {

                        //int secRecAdminDefaultValue = AppSettingsUtil.GetInt("secRecAdmDefault", 1000);
                        List<TreeviewData> recDbNodes = null;
                        List<TreeviewData> inqDbNodes = null;
                        //List<TreeviewData> mdDbNodes = null;
                        //if (umReq.UserType == UserType.AdminSuper)
                        //{
                        //    recDbNodes = UserManagerDALEC.GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec);
                        //}
                        //if (criteria.IsEnterpriseTree == true && criteria.UserNum != 1000)
                        //{
                        //    StringBuilder sbRec = new StringBuilder();
                        //    List<TreeviewData> enterpriseTreeViews = UserManagerDALEC.GetEnterpriseUserData(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec);
                        //    enterpriseTreeViews = enterpriseTreeViews.Where(s => s.RecId == rec.Id && s.IsGroup == false).ToList();

                        //    sbRec.Append("(");
                        //    foreach (var enterpriseTreeView in enterpriseTreeViews)
                        //    {
                        //        sbRec.Append(enterpriseTreeView.NodeId.Split('E')[1] + ",");
                        //    }
                        //    if (sbRec.Length > 1)
                        //        sbRec.Remove(sbRec.Length - 1, 1);
                        //    else
                        //        sbRec.Append("9999");
                        //    sbRec.Append(")");

                        //    StringBuilder sbInq = new StringBuilder();
                        //    List<TreeviewData> enterpriseTreeViewsInq = UserManagerDALEC.GetEnterpriseUserData(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec);
                        //    enterpriseTreeViewsInq = enterpriseTreeViewsInq.Where(s => s.RecId == rec.Id && s.IsGroup == false).ToList();

                        //    sbInq.Append("(");
                        //    foreach (var enterpriseTreeViewInq in enterpriseTreeViewsInq)
                        //    {
                        //        sbInq.Append(enterpriseTreeViewInq.NodeId.Split('E')[1] + ",");
                        //    }
                        //    if (sbInq.Length > 1)
                        //        sbInq.Remove(sbInq.Length - 1, 1);
                        //    else
                        //        sbInq.Append("9999");
                        //    sbInq.Append(")");

                        //    recDbNodes = UserManagerDALEC.GetEnterpriseGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, sbRec.ToString(), rec);
                        //    inqDbNodes = UserManagerDALEC.GetEnterpriseGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, criteria.UserType, sbInq.ToString(), rec);
                        //}
                        //else
                        //{
                        if (umReq.IsInquireView) // Inquire View
                        {
                            inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 12 : 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                        }
                        else if (rec.IsPrimary)
                        {
                            recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                            inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 12 : 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                            //mdDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 16 : 8, criteria.UserType, rec);
                            //inqDbNodes = UserManagerDALEC.GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, criteria.UserType, rec);
                        }
                        else
                        {
                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                            recDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 1, 0, rec).ToList();
                            if (criteria.IsEnterpriseTree)              // Note : This code section is specific to Enterprise User Tree
                            {
                                List<string> recNodes = new List<string>();
                                List<string> inqNodes = new List<string>();
                                List<TreeviewData> modifiedRecDBNodes = new List<TreeviewData>();
                                List<TreeviewData> modifiedInqDBNodes = new List<TreeviewData>();
                                //List<TreeviewData> modifiedMdDBNodes = new List<TreeviewData>();
                                //char splitter = (criteria.AuthNum == 5 || criteria.AuthNum == 7) ? 'A' : 'E';
                                char splitter = (criteria.AuthNum == 7) ? 'A' : 'E';

                                recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, rec);
                                modifiedRecDBNodes = recDbNodes.FindAll(p => p.IsGroup == true);
                                foreach (var recNode in recNodes)
                                {
                                    if (recDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                        modifiedRecDBNodes.Add(recDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                                }
                                recDbNodes = modifiedRecDBNodes;
                            }
                        }

                        var recTreeNodes = new TreeviewData();
                        var inqTreeNodes = new TreeviewData();
                        //var mdTreeNodes = new TreeviewData();

                        if (recDbNodes != null)
                            recTreeNodes = recDbNodes.BuildGroupsTree();
                        if (inqDbNodes != null)
                            inqTreeNodes = inqDbNodes.BuildGroupsTree();
                        //if (mdDbNodes != null)
                        //    mdTreeNodes = mdDbNodes.BuildGroupsTree();

                        List<GroupTree> groupsAll = new List<GroupTree>();
                        if (criteria.HideTextAndScreensTree)
                        {
                            groupsAll = new List<GroupTree>
                                {
                                    new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes },
                                };
                        }
                        else
                        {
                            groupsAll = new List<GroupTree> {
                                new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes },
                                new GroupTree { Id = 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = recTreeNodes },
                                new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes },
                                //new GroupTree{ Id= 11, GroupType = GroupType.MD, CssName = "md", TreeviewData = inqTreeNodes },
                            };
                        }
                        recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll.OrderBy(g => g.Id).ToList() });
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGroupsTreeEC", umReq.TenantId, "GetGroupsTreeEC(). Groups tree fetched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while fetching groups tree from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupsTreeEC", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroups = recGroups,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetGroupsTreeEC", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupsTreeEC", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetIQ3Tree(UMRequest umReq, int RecId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetIQ3Tree", umReq.TenantId, "GetIQ3Tree function has been called successfully."));

                List<Recorder> recorders = umReq.Recorders;
                var criteria = umReq.TreeCriteria as GroupTreeCriteria;

                var recGroups = new List<RecorderGroupTree>();
                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;

                    try
                    {

                        List<TreeviewData> inqDbNodes = null;
                        inqDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, (criteria.AuthNum == 5 && umReq.UserType == UserType.Simple && criteria.IsEnterpriseTree) ? 12 : 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                        var inqTreeNodes = new TreeviewData();

                        if (inqDbNodes != null)
                            inqTreeNodes = inqDbNodes.BuildGroupsTree();
                        List<GroupTree> groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes },
                        };
                        recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll.OrderBy(g => g.Id).ToList() });
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetIQ3Tree", umReq.TenantId, "GetIQ3Tree(). Groups tree fetched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while fetching groups tree from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetIQ3Tree", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroups = recGroups,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetIQ3Tree", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetIQ3Tree", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetRVITree(UMRequest umReq, int RecId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetRVITree", umReq.TenantId, "GetRVITree function has been called successfully."));

                List<Recorder> recorders = umReq.Recorders;
                var criteria = umReq.TreeCriteria as GroupTreeCriteria;

                var recGroups = new List<RecorderGroupTree>();
                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;

                    try
                    {
                        List<TreeviewData> rviNodes = null;
                        rviNodes = new UserManagerDALEC(umReq.TenantId).GetRVITree(criteria.UserNum, rec);
                        var inqTreeNodes = new TreeviewData();

                        if (rviNodes != null)
                            inqTreeNodes = rviNodes.BuildRVIGroupChannelsTree();
                        List<GroupTree> groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes },
                        };
                        recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll.OrderBy(g => g.Id).ToList() });
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetRVITree", umReq.TenantId, "GetRVITree(). RVI ext tree fetched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while fetching RVI ext tree from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetRVITree", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroups = recGroups,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetRVITree", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetIQ3Tree", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetEnterpriseGroupsTree(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetEnterpriseGroupsTree", umReq.TenantId, "GetEnterpriseGroupsTree function has been called successfully."));

                List<Recorder> recorders = umReq.Recorders;
                var criteria = umReq.TreeCriteria as GroupTreeCriteria;
                var recGroups = new List<RecorderGroupTree>();
                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        List<TreeviewData> recDbNodes = null;
                        List<TreeviewData> allRecDbNodes = null;
                        List<TreeviewData> allInqDbNodes = null;
                        if (rec.IsPrimary)
                        {
                            allRecDbNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec);
                            allInqDbNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec);
                        }
                        else
                        {
                            // Alternative approach
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });

                            allRecDbNodes = entClient.GetEnterpriseUserTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec).ToList();
                            allInqDbNodes = entClient.GetEnterpriseUserTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec).ToList();
                        }
                        recDbNodes = allRecDbNodes.Where(n => n.IsGroup == true || (n.IsGroup == false && n.ChannelType == 0)).ToList();
                        var recTreeNodes = recDbNodes.BuildGroupsTree();
                        List<GroupTree> groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes },
                        };
                        recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll.OrderBy(g => g.Id).ToList() });
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetEnterpriseGroupsTree", umReq.TenantId, "GetEnterpriseGroupsTree(). Enterprise Groups tree fetched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while fetching Enterprise Groups tree from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetEnterpriseGroupsTree", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroups = recGroups,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetEnterpriseGroupsTree", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetEnterpriseGroupsTree", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetGroupsTreeAudioEC(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, "GetGroupsTreeAudioEC function has been called successfully."));
                List<Recorder> recorders = umReq.Recorders;
                var criteria = umReq.TreeCriteria as GroupTreeCriteria;
                var recGroups = new List<RecorderGroupTree>();
                foreach (var rec in recorders)
                {
                    bool bExceptionThrown = false;
                    try
                    {
                        List<TreeviewData> recDbNodes = null;
                        List<TreeviewData> inqDBNodes = null;
                        //List<TreeviewData> mdDBNodes = null;
                        if (rec.IsPrimary)
                        {
                            recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                            inqDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                            //mdDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 8, criteria.UserType, rec);
                        }
                        else
                        {
                            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                            recDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 1, 0, rec).ToList();
                            inqDBNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, 0, rec).ToList();
                            //mdDBNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 8, 0, rec).ToList();

                            if (criteria.IsEnterpriseTree)                  // Note : This code section is specific to Enterprise User Tree           
                            {
                                List<string> recNodes = new List<string>();
                                List<string> inqNodes = new List<string>();
                                List<TreeviewData> modifiedRecDBNodes = new List<TreeviewData>();
                                List<TreeviewData> modifiedInqDBNodes = new List<TreeviewData>();
                                //char splitter = (criteria.AuthNum == 5 || criteria.AuthNum == 7) ? 'A' : 'E';
                                char splitter = (criteria.AuthNum == 7) ? 'A' : 'E';

                                recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, rec);
                                modifiedRecDBNodes = recDbNodes.FindAll(p => p.IsGroup == true);
                                foreach (var recNode in recNodes)
                                {
                                    if (recDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                        modifiedRecDBNodes.Add(recDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                                }
                                recDbNodes = modifiedRecDBNodes;

                                inqNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, rec);
                            }
                        }

                        var recTreeNodes = recDbNodes.BuildGroupsTree();

                        List<GroupTree> groupsAll;

                        bool b_isinquire = false;
                        foreach (TreeviewData item in inqDBNodes.BuildGroupsTree().Childrens)
                            if (!item.IsGroup) b_isinquire = true;

                        if (umReq.IsInquireView)
                        {
                            var inqTreeNodes = inqDBNodes.BuildGroupsTree();
                            groupsAll = new List<GroupTree>
                            {
                                new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes },
                            };
                        }
                        else if (rec.IsPrimary && b_isinquire)
                        {
                            var inqTreeNodes = inqDBNodes.BuildGroupsTree();
                            //var mdTreeNodes = mdDBNodes.BuildGroupsTree();
                            groupsAll = new List<GroupTree>
                            {
                                new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes },
                               // new GroupTree{ Id= 6, GroupType = GroupType.Screens, CssName = "screen", TreeviewData = recTreeNodes },
                                new GroupTree{ Id= 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes },
                                //new GroupTree{ Id= 11, GroupType = GroupType.MD, CssName = "md", TreeviewData = mdTreeNodes },
                            };
                        }
                        else
                        {
                            groupsAll = new List<GroupTree>
                        {
                            new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes },
                        };
                        }
                        // recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, AudioGroup = new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes }, DbNodes = recDbNodes });
                        recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll.OrderBy(g => g.Id).ToList() });
                        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, "GetGroupsTreeAudioEC(). Audio groups tree fetched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                    }
                    catch (Exception ex)
                    {
                        bExceptionThrown = true;
                        var errorMsg = "An error has occurred while fetching agent's data from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                    }
                    if (bExceptionThrown)
                        continue;
                }
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroups = recGroups,
                    //NoOfTreeNode = dbNodes.Count,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetGroupsTreeAudioEC(UMRequest umReq, string pageloadedvalue, int RecId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, "GetGroupsTreeAudioEC function has been called successfully."));

                List<Recorder> recorders = umReq.Recorders;
                var criteria = umReq.TreeCriteria as GroupTreeCriteria;
                var recGroups = new List<RecorderGroupTree>();
                if (pageloadedvalue == "/Monitor" || pageloadedvalue == ("/VRec/Monitor"))
                {
                    foreach (var rec in recorders)
                    {
                        bool bExceptionThrown = false;
                        List<GroupTree> groupsAll = new List<GroupTree>();
                        if (rec.Id == RecId)
                        {
                            try
                            {
                                List<TreeviewData> recDbNodes = null;
                                List<TreeviewData> inqDBNodes = null;
                                //List<TreeviewData> mdDBNodes = null;
                                if (rec.IsPrimary)
                                {
                                    if (umReq.IsInquireView)
                                    {
                                        if (pageloadedvalue == "/Monitor" || pageloadedvalue == ("/VRec/Monitor"))
                                            inqDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 13, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                        else
                                            inqDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);

                                        var inqTreeNodes = new TreeviewData();
                                        if (inqDBNodes != null)
                                        {
                                            inqTreeNodes = inqDBNodes.BuildGroupsTree();
                                            groupsAll.Add(new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes });
                                        }
                                    }
                                    else
                                    {
                                        if (criteria.IsRevCellRequired)
                                            recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                        else
                                            recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTreeWithoutRevCell(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, criteria.IsRevCellRequired, rec, umReq.Role.Id, umReq.Role.RoleType);
                                        if (pageloadedvalue == "/Monitor" || pageloadedvalue == ("/VRec/Monitor"))
                                        {
                                            inqDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 13, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                            //mdDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 14, criteria.UserType, rec);
                                        }
                                        else
                                        {
                                            inqDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                            //mdDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 8, criteria.UserType, rec);
                                        }
                                        var recTreeNodes = new TreeviewData();
                                        var inqTreeNodes = new TreeviewData();
                                        var mdTreeNodes = new TreeviewData();
                                        if (recDbNodes != null)
                                        {
                                            recTreeNodes = recDbNodes.BuildGroupsTree();
                                            groupsAll.Add(new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes });
                                        }
                                        if (inqDBNodes != null)
                                        {
                                            inqTreeNodes = inqDBNodes.BuildGroupsTree();
                                            groupsAll.Add(new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes });
                                        }
                                        //if (mdDBNodes != null)
                                        //{
                                        //    mdTreeNodes = mdDBNodes.BuildGroupsTree();
                                        //    groupsAll.Add(new GroupTree { Id = 11, GroupType = GroupType.MD, CssName = "md", TreeviewData = mdTreeNodes });
                                        //}
                                    }
                                }
                                else
                                {
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    recDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 1, 0, rec).ToList();
                                    if (criteria.IsEnterpriseTree)                  // Note : This code section is specific to Enterprise User Tree           
                                    {
                                        List<string> recNodes = new List<string>();
                                        List<TreeviewData> modifiedRecDBNodes = new List<TreeviewData>();

                                        //char splitter = (criteria.AuthNum == 5 || criteria.AuthNum == 7) ? 'A' : 'E';
                                        char splitter = (criteria.AuthNum == 7) ? 'A' : 'E';
                                        recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, rec);
                                        modifiedRecDBNodes = recDbNodes.FindAll(p => p.IsGroup == true);
                                        foreach (var recNode in recNodes)
                                        {
                                            if (recDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                                modifiedRecDBNodes.Add(recDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                                        }
                                        recDbNodes = modifiedRecDBNodes;
                                    }
                                    var recTreeNodes = new TreeviewData();
                                    if (recDbNodes != null)
                                    {
                                        recTreeNodes = recDbNodes.BuildGroupsTree();
                                        groupsAll.Add(new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes });
                                    }
                                }
                                if (groupsAll.Count > 0)
                                    recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll.OrderBy(g => g.Id).ToList() });
                                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, "GetGroupsTreeAudioEC(). Audio groups tree fetched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                            }
                            catch (Exception ex)
                            {
                                bExceptionThrown = true;
                                var errorMsg = "An error has occurred while fetching agent's data from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                            }
                        }
                        if (bExceptionThrown)
                            continue;
                    }
                }
                else
                {
                    foreach (var rec in recorders)
                    {
                        bool bExceptionThrown = false;
                        List<GroupTree> groupsAll = new List<GroupTree>();

                        try
                        {
                            List<TreeviewData> recDbNodes = null;
                            List<TreeviewData> inqDBNodes = null;
                            //List<TreeviewData> mdDBNodes = null;
                            if (rec.IsPrimary)
                            {
                                if (criteria.IsRevCellRequired)
                                    recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                else
                                    recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTreeWithoutRevCell(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, criteria.IsRevCellRequired, rec, umReq.Role.Id, umReq.Role.RoleType);
                                if (pageloadedvalue == "/Monitor" || pageloadedvalue == ("/VRec/Monitor"))
                                {
                                    //  inqDBNodes = UserManagerDALEC.GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 13, criteria.UserType, rec);
                                }
                                else
                                {
                                    //inqDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, criteria.UserType, rec);
                                    inqDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 7, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                                    //mdDBNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, 8, criteria.UserType, rec);
                                }
                                var recTreeNodes = new TreeviewData();
                                var inqTreeNodes = new TreeviewData();
                                //var mdTreeNodes = new TreeviewData();
                                if (recDbNodes != null && !umReq.IsInquireView)
                                {
                                    recTreeNodes = recDbNodes.BuildGroupsTree();
                                    groupsAll.Add(new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes });
                                }
                                if (inqDBNodes != null)
                                {
                                    inqTreeNodes = inqDBNodes.BuildGroupsTree();
                                    groupsAll.Add(new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "inquire", TreeviewData = inqTreeNodes });
                                }
                                //if (mdDBNodes != null && !umReq.IsInquireView)
                                //{
                                //    mdTreeNodes = mdDBNodes.BuildGroupsTree();
                                //    groupsAll.Add(new GroupTree { Id = 11, GroupType = GroupType.MD, CssName = "md", TreeviewData = mdTreeNodes });
                                //}
                            }
                            else
                            {
                                RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                recDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 1, 0, rec).ToList();
                                if (criteria.IsEnterpriseTree)                  // Note : This code section is specific to Enterprise User Tree           
                                {
                                    List<string> recNodes = new List<string>();
                                    List<TreeviewData> modifiedRecDBNodes = new List<TreeviewData>();

                                    //char splitter = (criteria.AuthNum == 5 || criteria.AuthNum == 7) ? 'A' : 'E';
                                    char splitter = (criteria.AuthNum == 7) ? 'A' : 'E';
                                    recNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserNodes(criteria.UserNum, criteria.AuthNum, rec);
                                    modifiedRecDBNodes = recDbNodes.FindAll(p => p.IsGroup == true);
                                    foreach (var recNode in recNodes)
                                    {
                                        if (recDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode) != null)
                                            modifiedRecDBNodes.Add(recDbNodes.Find(p => p.IsGroup == false && p.NodeId.Split(splitter)[1] == recNode));
                                    }
                                    recDbNodes = modifiedRecDBNodes;
                                }
                                var recTreeNodes = new TreeviewData();
                                if (recDbNodes != null)
                                {
                                    recTreeNodes = recDbNodes.BuildGroupsTree();
                                    groupsAll.Add(new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes });
                                }
                            }
                            if (groupsAll.Count > 0)
                                recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, GroupsTree = groupsAll.OrderBy(g => g.Id).ToList() });
                            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, "GetGroupsTreeAudioEC(). Audio groups tree fetched successfully from recorder. Recorder Id = " + rec.Id + " , Recorder Name = " + rec.Name));
                        }
                        catch (Exception ex)
                        {
                            bExceptionThrown = true;
                            var errorMsg = "An error has occurred while fetching agent's data from Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                            Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                        }

                        if (bExceptionThrown)
                            continue;
                    }
                }
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroups = recGroups
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupsTreeAudioEC", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        public UMResponse GetEnterpriseUserRightsData(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetEnterpriseUserRightsData", umReq.TenantId, "GetEnterpriseUserRightsData function has been called successfully."));

                var userRecGrp = new List<Tuple<int, int>>();
                var criteria = umReq.TreeCriteria as GroupTreeCriteria;
                var dbNodes = new UserManagerDALEC(umReq.TenantId).GetEnterpriseUserRightsData(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, out userRecGrp);
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    NoOfTreeNode = dbNodes.Count,
                    EnterpriseNodeDTO = dbNodes,
                    UserRecordersGroups = userRecGrp,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetEnterpriseUserRightsData", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetEnterpriseUserRightsData", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetGMGroupsTreeAudioEC(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGMGroupsTreeAudioEC", umReq.TenantId, "GetGMGroupsTreeAudioEC function has been called successfully."));

                List<Recorder> recorders = umReq.Recorders;

                var criteria = umReq.TreeCriteria as GroupTreeCriteria;

                var recGroups = new List<RecorderGroupTree>();
                foreach (var rec in recorders)
                {
                    List<TreeviewData> recDbNodes = null;
                    if (rec.IsPrimary == true)
                    {
                        recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                    }
                    else
                    {
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        recDbNodes = entClient.GetGroupsTree(_secRecAdminDefaultValue, (string.IsNullOrEmpty(criteria.UserId) ? "" : criteria.UserId), criteria.AuthNum, criteria.AuthType, 1, 0, rec).ToList();
                    }
                    var recTreeNodes = recDbNodes.BuildGroupsTree();
                    List<GroupTree> groupsAll = new List<GroupTree>
                    {
                        new GroupTree{ Id= 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes}
                    };
                    recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, AudioGroup = new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes }, DbNodesAudio = recDbNodes });
                }
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroups = recGroups,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetGMGroupsTreeAudioEC", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGMGroupsTreeAudioEC", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }


        #region --- Users with old objects ---

        public UMResponse GetAppUsersForAllRecorders(UMRequest umReq)
        {
            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetAppUsersForAllRecorders", umReq.TenantId, "GetAppUsersForAllRecorders function has been called successfully."));

            List<Recorder> recorders = umReq.Recorders;

            var recUsers = new List<RecorderUserOld>();
            foreach (var rec in recorders)
            {
                bool bExceptionThrown = false;
                try
                {
                    if (rec.IsPrimary)
                    {
                        UserManagementResponse recResponse = new UserManagerDALEC(umReq.TenantId).GetAppUsers(rec);
                        recUsers.Add(new RecorderUserOld { RecorderId = rec.Id, RecorderName = rec.Name, UserManagementResponse = recResponse });
                    }
                    else
                    {
                        // Alternative approach using service based call.
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        UserManagementResponse recResponse = entClient.GetAppUsers(rec);
                        recUsers.Add(new RecorderUserOld { RecorderId = rec.Id, RecorderName = rec.Name, UserManagementResponse = recResponse });
                    }
                }
                catch (SqlException sqle)
                {
                    bExceptionThrown = true;
                    Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetAppUsersForAllRecorders", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                }
                catch (Exception ex)
                {
                    bExceptionThrown = true;
                    var errorMsg = "An error has occurred while calling the function GetAppUsersForAllRecorders. Recorder Id = " + rec.Id + ", Recorder Name = " + rec.Name;
                    Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetAppUsersForAllRecorders", umReq.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                }
                if (bExceptionThrown)
                    continue;
            }
            return new UMResponse { RecorderUsersOld = recUsers };
        }

        #endregion

        #region --- User Activity Logging ---

        public UMResponse SaveUserActivity(UMRequest umReq)
        {
            try
            {
                int lastSavedId = new UserManagerDAL(umReq.TenantId).SaveUserActivity(umReq.UserActivity.UserId, umReq.UserActivity.ActivityId, umReq.UserActivity.ActivityPerformed, umReq.UserActivity.CreatedDate, umReq.UserActivity.MessageKey, umReq.UserActivity.MessageData, umReq.UserActivity.Comments, umReq.UserActivity.ClientIP);
                return new UMResponse
                {
                    Acknowledge = lastSavedId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    UserActivityId = lastSavedId,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "SaveUserActivity", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "SaveUserActivity", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetUserActivites(int userId, DateTime startDate, DateTime endDate, int pageSize, int pageIndex, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetUserActivites", tenantId, "GetUserActivites function has been called successfully."));

                int totalPages = 0;
                long totalRecords = 0;
                return new UMResponse
                {
                    UserActivities = new UserManagerDAL(tenantId).GetUserActivities(userId, startDate, endDate, pageSize, pageIndex, out totalPages, out totalRecords),
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetUserActivites", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetUserActivites", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UMResponse GetUserActivityCount(int userId, DateTime startDate, DateTime endDate, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetUserActivityCount", tenantId, "GetUserActivityCount function has been called successfully."));

                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    Message = "Successfully fetched the user activity count.",
                    TotalRecords = new UserManagerDAL(tenantId).GetUserActivityCount(userId, startDate, endDate)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetUserActivityCount", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    Message = sqle.Message,
                    TotalRecords = 0
                };
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetUserActivityCount", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Failure,
                    Message = ex.Message,
                    TotalRecords = 0
                };
            }
        }
        #endregion

        public UMResponse GetUsers(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetUsers", umReq.TenantId, "GetUsers function has been called successfully."));
                return new UMResponse { Users = new UserManagerDAL(umReq.TenantId).GetUsers() };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetUsers", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetUsers", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UMResponse GetUserNumByExt(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetUserNumByExt", umReq.TenantId, "GetUsers function has been called successfully."));
                return new UMResponse { UserNum = new UserManagerDAL(umReq.TenantId).GetUserNumByExt(Convert.ToInt32(umReq.User.Ext)) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetUsers", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetUsers", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UMResponse GetInquireGroupsTreeforEvualtionreport(UMRequest umRequest, int SelectType)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetInquireGroupsTreeforEvualtionreport", umRequest.TenantId, "GetInquireGroupsTreeforEvualtionreport function has been called successfully."));

                List<TreeviewData> agentTreeViewChildData = null;
                agentTreeViewChildData = new UserManagerDAL(umRequest.TenantId).GetInquireGroupsTreeforEvualtionreport(umRequest.TreeCriteria.UserNum, SelectType);
                TreeviewData _agentTreeView = new TreeviewData
                {
                    NodeId = "3000",
                    NodeCaption = "Root",
                    ParentNodeId = 3000,
                    Depth = 0,
                    MenuType = 1,
                    ViewType = 0,
                    IsGroup = true,
                    Param1 = "3000",
                    Param2 = "",
                    Param3 = "1",
                    Childrens = agentTreeViewChildData
                };

                return new UMResponse { AllGroups = new List<GroupTree> { new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "Inquire", TreeviewData = _agentTreeView } } };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetInquireGroupsTreeforEvualtionreport", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetInquireGroupsTreeforEvualtionreport", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UMResponse GetInquireGroupsTreeforEvaluationReportFromRecorder(Recorder recorder, UMRequest umRequest, int SelectType)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetInquireGroupsTreeforEvaluationReportFromRecorder", umRequest.TenantId, "GetInquireGroupsTreeforEvaluationReportFromRecorder function has been called successfully."));

                List<TreeviewData> agentTreeViewChildData = null;

                if (recorder.IsPrimary)
                    agentTreeViewChildData = new UserManagerDALEC(umRequest.TenantId).GetInquireGroupsTreeforEvaluationReportFromRecorder(recorder, umRequest.TreeCriteria.UserNum, SelectType);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    agentTreeViewChildData = entClient.GetInquireGroupsTreeforEvaluationReportFromRecorder(recorder, umRequest.TreeCriteria.UserNum, SelectType).ToList();
                }
                TreeviewData _agentTreeView = new TreeviewData
                {
                    NodeId = "3000",
                    NodeCaption = "Root",
                    ParentNodeId = 3000,
                    Depth = 0,
                    MenuType = 1,
                    ViewType = 0,
                    IsGroup = true,
                    Param1 = "3000",
                    Param2 = "",
                    Param3 = "1",
                    Childrens = agentTreeViewChildData
                };
                return new UMResponse { AllGroups = new List<GroupTree> { new GroupTree { Id = 7, GroupType = GroupType.IQ3, CssName = "Inquire", TreeviewData = _agentTreeView } } };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetInquireGroupsTreeforEvaluationReportFromRecorder", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetInquireGroupsTreeforEvaluationReportFromRecorder", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        //public UMResponse GetMDGroupsTreeForEvaluationReport(UMRequest umRequest, int SelectType)
        //{
        //    try
        //    {
        //        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetMDGroupsTreeForEvaluationReport", umRequest.TenantId, "GetMDGroupsTreeForEvaluationReport function has been called successfully."));
        //        List<TreeviewData> agentTreeViewChildData = null;
        //        agentTreeViewChildData = new UserManagerDAL(umRequest.TenantId).GetMDGroupsTreeForEvaluationReport(umRequest.TreeCriteria.UserNum, SelectType);
        //        TreeviewData _agentTreeView = new TreeviewData
        //        {
        //            NodeId = "4000",
        //            NodeCaption = "Root",
        //            ParentNodeId = 4000,
        //            Depth = 0,
        //            MenuType = 1,
        //            ViewType = 0,
        //            IsGroup = true,
        //            Param1 = "4000",
        //            Param2 = "",
        //            Param3 = "1",
        //            Childrens = agentTreeViewChildData
        //        };
        //        //return new UMResponse { AllGroups = new List<GroupTree> { new GroupTree { Id = 11, GroupType = GroupType.MD, CssName = "MD", TreeviewData = _agentTreeView } } };
        //    }
        //    catch (SqlException sqle)
        //    {
        //        Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetMDGroupsTreeForEvaluationReport", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
        //        throw sqle;
        //    }
        //    catch (Exception ex)
        //    {
        //        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetMDGroupsTreeForEvaluationReport", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
        //        throw ex;
        //    }
        //}

        //public UMResponse GetMDGroupsTreeForEvaluationReportFromRecorder(Recorder recorder, UMRequest umRequest, int SelectType)
        //{
        //    try
        //    {
        //        Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetMDGroupsTreeForEvaluationReportFromRecorder", umRequest.TenantId, "GetMDGroupsTreeForEvaluationReportFromRecorder function has been called successfully."));

        //        List<TreeviewData> agentTreeViewChildData = null;
        //        if (recorder.IsPrimary)
        //            agentTreeViewChildData = new UserManagerDALEC(umRequest.TenantId).GetMDGroupsTreeForEvaluationReportFromRecorder(recorder, umRequest.TreeCriteria.UserNum, SelectType);
        //        else
        //        {
        //            // TODO SARFRAZ
        //            // Alternative approach
        //            RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
        //            entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
        //            agentTreeViewChildData = entClient.GetMDGroupsTreeForEvaluationReportFromRecorder(recorder, umRequest.TreeCriteria.UserNum, SelectType).ToList();
        //        }
        //        TreeviewData _agentTreeView = new TreeviewData
        //        {
        //            NodeId = "4000",
        //            NodeCaption = "Root",
        //            ParentNodeId = 4000,
        //            Depth = 0,
        //            MenuType = 1,
        //            ViewType = 0,
        //            IsGroup = true,
        //            Param1 = "4000",
        //            Param2 = "",
        //            Param3 = "1",
        //            Childrens = agentTreeViewChildData
        //        };
        //        //return new UMResponse { AllGroups = new List<GroupTree> { new GroupTree { Id = 11, GroupType = GroupType.MD, CssName = "md", TreeviewData = _agentTreeView } } };
        //        return new UMResponse();
        //    }
        //    catch (SqlException sqle)
        //    {
        //        Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetMDGroupsTreeForEvaluationReportFromRecorder", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
        //        throw sqle;
        //    }
        //    catch (Exception ex)
        //    {
        //        Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetMDGroupsTreeForEvaluationReportFromRecorder", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
        //        throw ex;
        //    }
        //}
        public UMResponse GetUsersGroupsTree(UMRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetUsersGroupsTree", umRequest.TenantId, "GetUsersGroupsTree function has been called successfully."));

                List<TreeviewData> userTreeViewChildData = null;
                userTreeViewChildData = new UserManagerDAL(umRequest.TenantId).GetUsersTree(umRequest.TreeCriteria.UserNum);
                TreeviewData _userTreeView = new TreeviewData
                {
                    NodeId = "2000",
                    NodeCaption = "Evaluated Users",
                    ParentNodeId = 2000,
                    Depth = 0,
                    MenuType = 1,
                    ViewType = 0,
                    IsGroup = true,
                    Param1 = "2000",
                    Param2 = "",
                    Param3 = "1",
                    Childrens = userTreeViewChildData
                };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetUsersGroupsTree", umRequest.TenantId, "GetUsersGroupsTree(). Users tree fetched and build successfully."));
                return new UMResponse { AllGroups = new List<GroupTree> { new GroupTree { Id = 8, GroupType = GroupType.Agents, CssName = "agents", TreeviewData = _userTreeView } } };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetMDGroupsTreeForEvaluationReportFromRecorder", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetMDGroupsTreeForEvaluationReportFromRecorder", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetUsersGroupsTreeFromRecorder(Recorder recorder, UMRequest umRequest)
        {
            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetUsersGroupsTreeFromRecorder", umRequest.TenantId, "GetUsersGroupsTreeFromRecorder function has been called successfully."));

            List<TreeviewData> agentTreeViewChildData = null;
            TreeviewData _agentTreeView = null;
            try
            {
                if (recorder.IsPrimary)
                    agentTreeViewChildData = new UserManagerDALEC(umRequest.TenantId).GetUsersTreeFromRecorder(recorder, umRequest.TreeCriteria.UserNum);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    agentTreeViewChildData = entClient.GetUsersTreeFromRecorder(recorder, umRequest.TreeCriteria.UserNum).ToList();
                }
                _agentTreeView = new TreeviewData
                {
                    NodeId = "2000",
                    NodeCaption = "Evaluated Agents",
                    ParentNodeId = 2000,
                    Depth = 0,
                    MenuType = 1,
                    ViewType = 0,
                    IsGroup = true,
                    Param1 = "2000",
                    Param2 = "",
                    Param3 = "1",
                    Childrens = agentTreeViewChildData
                };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetAgentsGroupsTreeFromRecorder", umRequest.TenantId, "GetAgentsGroupsTreeFromRecorder(). Agents group tree build-up successfully from recorder. Recorder Id = " + recorder.Id + " , Recorder Name = " + recorder.Name));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetUsersGroupsTreeFromRecorder", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                var errorMsg = "An error has occurred while calling the function GetUsersGroupsTreeFromRecorder(). Recorder Id = " + recorder.Id + ", Recorder Name = " + recorder.Name;
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetUsersGroupsTreeFromRecorder", umRequest.TenantId, errorMsg + " - " + ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return new UMResponse { AllGroups = new List<GroupTree> { new GroupTree { Id = 8, GroupType = GroupType.Agents, CssName = "agents", TreeviewData = _agentTreeView } } };
        }

        #region Inquire User Management



        public UMResponse GetInquireGroups(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetInquireGroups", umReq.TenantId, "GetInquireGroups function has been called successfully."));

                List<TreeviewData> inqDbNodes = null;
                inqDbNodes = new UserManagerDAL(umReq.TenantId).GetInquireGroups();
                return null;//new UMResponse { inquireDbNodes = inqDbNodes };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetInquireGroups", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetInquireGroups", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetInquireUnAssignedUsers(UMRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetInquireUnAssignedUsers", umRequest.TenantId, "GetInquireUnAssignedUsers function has been called successfully."));

                int GroupNum = 1000;
                List<InquireUserInfo> lGVData = null;
                //lGVData = UserManagerDAL.GetInquireGroupUsers(GroupNum);
                return null;//new UMResponse { gvData = lGVData };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetInquireUnAssignedUsers", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetInquireUnAssignedUsers", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UMResponse GetInquireCustomMarkers(UMRequest umRequest, int Id)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetInquireCustomMarkers", umRequest.TenantId, "GetInquireCustomMarkers function has been called successfully. Id = " + Id));

                List<CustomMarkersData> lLVData = null;
                lLVData = new UserManagerDAL(umRequest.TenantId).GetInquireCustomMarkers(Id);
                return new UMResponse { lvData = lLVData };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetInquireCustomMarkers", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetInquireCustomMarkers", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public UMResponse GetInquireCustomMarkersByEventId(UMRequest umReq, string eventId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetInquireCustomMarkersByEventId", umReq.TenantId, "GetInquireCustomMarkers function has been called successfully. EventId = " + eventId));

                List<CustomMarkersData> lLVData = null;
                lLVData = new UserManagerDAL(umReq.TenantId).GetInquireCustomMarkersByEventId(eventId);
                return new UMResponse { lvData = lLVData };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetInquireCustomMarkers", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetInquireCustomMarkers", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool addMarker(CustomMarkersData cmData, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "addMarker", tenantId, "addMarker function has been called successfully. "));
                bool bAdded = new UserManagerDAL(tenantId).addMarker(cmData);
                return bAdded;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "addMarker", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "addMarker", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool UpdateMarker(CustomMarkersData cmData, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "UpdateMarker", tenantId, "UpdateMarker function has been called successfully. "));
                bool bUpdated = new UserManagerDAL(tenantId).UpdateMarker(cmData);
                return bUpdated;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "UpdateMarker", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "UpdateMarker", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public bool removeMarker(CustomMarkersData cmData, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "removeMarker", tenantId, "removeMarker function has been called successfully. "));
                bool bRemoved = new UserManagerDAL(tenantId).removeMarker(cmData);
                return bRemoved;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "removeMarker", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "removeMarker", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool UpdateReportName(string ReportName, string eventId, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "UpdateReportName", tenantId, "UpdateReportName function has been called successfully. "));
                bool bUpdated = new UserManagerDAL(tenantId).UpdateReportName(ReportName, eventId);
                return bUpdated;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "UpdateReportName", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "UpdateReportName", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public int AddSubMarker(CustomMarkersData customMarkerData, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "AddSubMarker", tenantId, "AddSubMarker function has been called successfully. "));
                int serverId = new UserManagerDAL(tenantId).AddSubMarker(customMarkerData);
                return serverId;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "AddSubMarker", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "AddSubMarker", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool UpdateSubMarker(CustomMarkersData customMarkersData, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "UpdateSubMarker", tenantId, "UpdateSubMarker function has been called successfully. "));
                bool bUpdated = new UserManagerDAL(tenantId).UpdateSubMarker(customMarkersData);
                return bUpdated;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "UpdateSubMarker", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "UpdateSubMarker", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool RemoveSubMarker(int id, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "RemoveSubMarker", tenantId, "RemoveSubMarker function has been called successfully. "));
                bool bRemoved = new UserManagerDAL(tenantId).RemoveSubMarker(id);
                return bRemoved;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "RemoveSubMarker", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "RemoveSubMarker", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        
        public bool AddEditMarkerNote(int id, string markerNote, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "AddEditMarkerNote", tenantId, "AddEditMarkerNote function has been called successfully. "));
                bool bAdded = new UserManagerDAL(tenantId).AddEditMarkerNote(id, markerNote);
                return bAdded;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "AddEditMarkerNote", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "AddEditMarkerNote", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool RemoveMarkerNote(int id, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "RemoveMarkerNote", tenantId, "RemoveMarkerNote function has been called successfully. "));
                bool bRemoved = new UserManagerDAL(tenantId).RemoveMarkerNote(id);
                return bRemoved;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "RemoveMarkerNote", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "RemoveMarkerNote", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UMResponse GetGroupmanagemtGroups(UMRequest umReq)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetGroupmanagemtGroups", umReq.TenantId, "GetGroupmanagemtGroups function has been called successfully. "));

                List<Recorder> recorders = umReq.Recorders;

                var criteria = umReq.TreeCriteria as GroupTreeCriteria;

                var recGroups = new List<RecorderGroupTree>();
                foreach (var rec in recorders)
                {
                    List<TreeviewData> recDbNodes = null;

                    if (rec.IsPrimary)
                        recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(criteria.UserNum, criteria.UserId, criteria.AuthNum, criteria.AuthType, criteria.Type, criteria.UserType, rec, umReq.Role.Id, umReq.Role.RoleType);
                    else
                        recDbNodes = new UserManagerDALEC(umReq.TenantId).GetGroupsTree(_secRecAdminDefaultValue, criteria.UserId, criteria.AuthNum, criteria.AuthType, 1, 0, rec, umReq.Role.Id, umReq.Role.RoleType);

                    var recTreeNodes = recDbNodes.BuildGroupsTree();

                    recGroups.Add(new RecorderGroupTree { RecorderId = rec.Id, RecorderName = rec.Name, AudioGroup = new GroupTree { Id = 1, GroupType = GroupType.Audio, CssName = "audio", TreeviewData = recTreeNodes }, DbNodes = recDbNodes });
                }
                return new UMResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    RecorderGroups = recGroups,
                    //NoOfTreeNode = dbNodes.Count,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetGroupmanagemtGroups", umReq.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetGroupmanagemtGroups", umReq.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }
        #endregion

        #region Simple User Rights
        public bool EnableDisableSimpleUserRights(int UserNum, int selecttype, int tenantId, int revsyncUserNum)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "EnableDisableSimpleUserRights", tenantId, "EnableDisableSimpleUserRights function has been called successfully. "));
                return new UserManagerDAL(tenantId).EnableDisableSimpleUserRights(UserNum, selecttype, revsyncUserNum);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "EnableDisableSimpleUserRights", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "EnableDisableSimpleUserRights", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UMResponse getAssignedSimpleUserRights(int UserNum, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "getAssignedSimpleUserRights", tenantId, "getAssignedSimpleUserRights function has been called successfully. "));
                return new UMResponse { AssignedRights = new UserManagerDAL(tenantId).getAssignedSimpleUserRights(UserNum) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "getAssignedSimpleUserRights", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "getAssignedSimpleUserRights", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public UMResponse getSimpleUserUnAssignedCghannels(int uId, string accessRight, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "getSimpleUserUnAssignedCghannels", tenantId, "getSimpleUserUnAssignedCghannels function has been called successfully. uId = " + uId + " - accessRight = " + accessRight));

                return new UMResponse { Users = new UserManagerDAL(tenantId).getSimpleUserUnAssignedChannels(uId, accessRight) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "getSimpleUserUnAssignedCghannels", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "getSimpleUserUnAssignedCghannels", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool SaveSimpleUserRightsChannels(UMRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "SaveSimpleUserRightsChannels", umRequest.TenantId, "SaveSimpleUserRightsChannels function has been called successfully. "));
                return new UserManagerDAL(umRequest.TenantId).SaveSimpleUserRightsChannels(umRequest.User);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "SaveSimpleUserRightsChannels", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "SaveSimpleUserRightsChannels", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Enterprise User

        #region Channel/Agent based simple rights. Specific to IR Lite, Monitor, Search, QA Evaluation, QA Evaluation Reports, Adv. Reports, IR Full
        public UMResponse SaveEnterpriseUserRights(UMRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "SaveEnterpriseUserRights", umRequest.TenantId, "SaveEnterpriseUserRights function has been called successfully. "));
                List<EnterpriseNodeDTO> enterpriseNodeDTOs = umRequest.EnterpriseUserNodes;
                if (enterpriseNodeDTOs != null)
                    enterpriseNodeDTOs = enterpriseNodeDTOs.Where(c => c.ModuleBit != ModuleBit.Setup && c.ModuleBit != ModuleBit.Dashboard).OrderBy(g => g.ModuleBit).ThenBy(tb => tb.RecId).ToList();
                XElement permissionXML = CreatePermissionsXML(enterpriseNodeDTOs, umRequest.UserId, umRequest.TenantId);
                string strPermissionXML = permissionXML.ToString();
                int rowsAffected = new UserManagerDALEC(umRequest.TenantId).SaveEnterpriseUserRights(strPermissionXML);
                return new UMResponse { Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "SaveEnterpriseUserRights", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "SaveEnterpriseUserRights", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Group based access rights : Channel/Agent based rights + Tab based rights
        public UMResponse SaveEnterpriseUserGroupBasedRights(UMRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "SaveEnterpriseUserGroupBasedRights", umRequest.TenantId, "SaveEnterpriseUserGroupBasedRights function has been called successfully. "));
                EnterpriseGroupRightDTO enterpriseGroupRight = umRequest.EnterpriseGroupRight;
                List<EnterpriseRecorderGroup> enterpriseRecGroup = enterpriseGroupRight.EnterpriseRecorderGroups;
                List<ModuleBit> moduleBits = getModuleBitsByAccessRightString(umRequest.EnterpriseGroupRight.Permission, umRequest.TenantId);
                List<EnterpriseNodeDTO> newRights = new List<EnterpriseNodeDTO>();

                // Get the existing rights and deleted them by setting DbStatus = false
                List<EnterpriseNodeDTO> existingRights = new UserManagerDALEC(umRequest.TenantId).GetEnterpriseUserRightsData(enterpriseGroupRight.UserId, 1);
                foreach (var existingRight in existingRights)
                    existingRight.DbStatus = false;
                XElement existingUserRightPermissionXML = CreateGroupPermissionsXML(existingRights, enterpriseGroupRight.UserId, umRequest.TenantId);
                string strExistingUserRightPermissionXML = existingUserRightPermissionXML.ToString();
                int existingRowsUpdated = new UserManagerDALEC(umRequest.TenantId).SaveEnterpriseUserRights(strExistingUserRightPermissionXML, false);

                // Get the List of new Extensions and Agents based on GroupId and prepare EnterpriseNodeDTO object
                foreach (var recorder in umRequest.Recorders)
                {
                    int groupId = enterpriseRecGroup.Where(r => r.RecId == recorder.Id).First().GroupId;
                    Tuple<List<int>, List<int>> extensionAgentTuple = new UserManagerDALEC(umRequest.TenantId).GetExtensionsNAgentsByGroupId(groupId, recorder);
                    List<int> extensions = extensionAgentTuple.Item1;
                    List<int> agents = extensionAgentTuple.Item2;
                    foreach (var extension in extensions)
                    {
                        foreach (var moduleBit in moduleBits)
                            if (moduleBit != ModuleBit.Evaluation || moduleBit != ModuleBit.QAEvaluationReports)
                                newRights.Add(new EnterpriseNodeDTO { RecId = recorder.Id, NodeId = (moduleBit == ModuleBit.Setup || moduleBit == ModuleBit.Dashboard) ? 0 : extension, ModuleBit = moduleBit, DbStatus = true });
                    }
                    foreach (var agent in agents)
                    {
                        foreach (var moduleBit in moduleBits)
                            if (moduleBit == ModuleBit.Evaluation || moduleBit == ModuleBit.QAEvaluationReports)
                                newRights.Add(new EnterpriseNodeDTO { RecId = recorder.Id, NodeId = agent, ModuleBit = moduleBit, DbStatus = true });
                    }
                }

                // extension rights
                List<EnterpriseNodeDTO> enterpriseUserRightNodeDTOs = newRights;
                if (enterpriseUserRightNodeDTOs != null)
                    enterpriseUserRightNodeDTOs = enterpriseUserRightNodeDTOs.Where(c => c.ModuleBit != ModuleBit.Setup && c.ModuleBit != ModuleBit.Dashboard).OrderBy(g => g.ModuleBit).ThenBy(tb => tb.RecId).ToList();
                XElement userRightPermissionXML = CreateGroupPermissionsXML(enterpriseUserRightNodeDTOs, enterpriseGroupRight.UserId, umRequest.TenantId);
                string strUserRightPermissionXML = userRightPermissionXML.ToString();

                // tab rights
                List<EnterpriseNodeDTO> enterpriseTabNodeDTOs = newRights;
                if (enterpriseTabNodeDTOs != null)
                    enterpriseTabNodeDTOs = enterpriseTabNodeDTOs.Where(cond => cond.ModuleBit == ModuleBit.Setup || cond.ModuleBit == ModuleBit.Dashboard).OrderBy(g => g.ModuleBit).ThenBy(tb => tb.RecId).ToList();
                if (enterpriseTabNodeDTOs.Count == 0)
                {
                    foreach (var rec in umRequest.Recorders)
                    {
                        enterpriseTabNodeDTOs.Add(new EnterpriseNodeDTO { RecId = rec.Id, NodeId = 0, DbStatus = false, ModuleBit = ModuleBit.Setup });
                        enterpriseTabNodeDTOs.Add(new EnterpriseNodeDTO { RecId = rec.Id, NodeId = 0, DbStatus = false, ModuleBit = ModuleBit.Dashboard });
                    }
                }
                XElement tabPermissionXML = CreateTabBasedPermissionsXML(enterpriseTabNodeDTOs, enterpriseGroupRight.UserId, umRequest.TenantId);
                string strTabPermissionXML = tabPermissionXML.ToString();

                int rowsAffected = new UserManagerDALEC(umRequest.TenantId).SaveEnterpriseGroupRights(strUserRightPermissionXML, strTabPermissionXML);

                // update umEntUserGroup table here
                foreach (var recGroup in enterpriseGroupRight.EnterpriseRecorderGroups)
                {
                    bool isEmptyPermission = enterpriseGroupRight.Permission == "0000000000" ? true : false;
                    int returnVal = new UserManagerDALEC(umRequest.TenantId).SaveEnterpriseUserRecordersGroup(recGroup.RecId, enterpriseGroupRight.UserId, recGroup.GroupId, isEmptyPermission);
                }

                return new UMResponse { Acknowledge = (existingRowsUpdated > 0 || rowsAffected > 0 || enterpriseGroupRight.Permission == "0000000000") ? AcknowledgeType.Success : AcknowledgeType.Failure };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "SaveEnterpriseUserGroupBasedRights", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "SaveEnterpriseUserGroupBasedRights", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Enterprise User : Utility Functions
        private XElement CreatePermissionsXML(List<EnterpriseNodeDTO> entNodes, int userNum, int tenantId = 0)
        {
            try
            {
                XElement permissions = new XElement("Permissions");

                // Permission Bit = 2
                XElement irLitePermissions = new XElement("IRLite");
                XElement recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.IRLite, ModuleAccessRight.IRLite.ToString(), recorders, tenantId);
                irLitePermissions.Add(recorders);

                // Permission Bit = 3
                XElement monitorPermissions = new XElement("Monitor");
                recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.Monitor, ModuleAccessRight.Monitor.ToString(), recorders, tenantId);
                monitorPermissions.Add(recorders);

                // Permission Bit = 4
                XElement searchPermissions = new XElement("Search");
                recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.Search, ModuleAccessRight.Search.ToString(), recorders, tenantId);
                searchPermissions.Add(recorders);

                // Permission Bit = 5
                XElement evaluationPermissions = new XElement("Evaluation");
                recorders = new XElement("Recorders");
                buildAgentBasedRecorderXML(entNodes, userNum, ModuleBit.Evaluation, ModuleAccessRight.Evaluation.ToString(), recorders, tenantId);
                evaluationPermissions.Add(recorders);

                // Permission Bit = 7
                XElement qaEvaluationReportsPermissions = new XElement("QAEvaluationReports");
                recorders = new XElement("Recorders");
                buildAgentBasedRecorderXML(entNodes, userNum, ModuleBit.QAEvaluationReports, ModuleAccessRight.QAEvaluationReports.ToString(), recorders, tenantId);
                qaEvaluationReportsPermissions.Add(recorders);

                // Permission Bit = 8
                XElement advReportsPermissions = new XElement("AdvancedReports");
                recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.AdvancedReports, ModuleAccessRight.AdvancedReports.ToString(), recorders, tenantId);
                advReportsPermissions.Add(recorders);

                // Permission Bit = 9
                XElement irFullPermissions = new XElement("IRFull");
                recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.IRFull, ModuleAccessRight.IRFull.ToString(), recorders, tenantId);
                irFullPermissions.Add(recorders);

                // Permission Bit = 10
                XElement saveNEmailPermissions = new XElement("SaveAndEmail");
                recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.Search, ModuleAccessRight.SaveNEmail.ToString(), recorders, tenantId);
                saveNEmailPermissions.Add(recorders);

                if (irLitePermissions.Value != string.Empty)
                    permissions.Add(irLitePermissions);
                if (monitorPermissions.Value != string.Empty)
                    permissions.Add(monitorPermissions);
                if (searchPermissions.Value != string.Empty)
                    permissions.Add(searchPermissions);
                if (evaluationPermissions.Value != string.Empty)
                    permissions.Add(evaluationPermissions);
                if (qaEvaluationReportsPermissions.Value != string.Empty)
                    permissions.Add(qaEvaluationReportsPermissions);
                if (advReportsPermissions.Value != string.Empty)
                    permissions.Add(advReportsPermissions);
                if (irFullPermissions.Value != string.Empty)
                    permissions.Add(irFullPermissions);
                if (saveNEmailPermissions.Value != string.Empty)
                    permissions.Add(saveNEmailPermissions);

                return permissions;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "CreatePermissionsXML", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "CreatePermissionsXML", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        private XElement CreateGroupPermissionsXML(List<EnterpriseNodeDTO> entNodes, int userNum, int tenantId = 0)
        {
            try
            {
                XElement permissions = new XElement("Permissions");

                // Permission Bit = 2
                XElement irLitePermissions = new XElement("IRLite");
                XElement recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.IRLite, ModuleAccessRight.IRLite.ToString(), recorders, tenantId);
                irLitePermissions.Add(recorders);

                // Permission Bit = 3
                XElement monitorPermissions = new XElement("Monitor");
                recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.Monitor, ModuleAccessRight.Monitor.ToString(), recorders, tenantId);
                monitorPermissions.Add(recorders);

                // Permission Bit = 4
                XElement searchPermissions = new XElement("Search");
                recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.Search, ModuleAccessRight.Search.ToString(), recorders, tenantId);
                searchPermissions.Add(recorders);

                // Permission Bit = 5
                XElement evaluationPermissions = new XElement("Evaluation");
                recorders = new XElement("Recorders");
                buildAgentBasedRecorderXML(entNodes, userNum, ModuleBit.Evaluation, ModuleAccessRight.Evaluation.ToString(), recorders, tenantId);
                evaluationPermissions.Add(recorders);

                // Permission Bit = 7
                XElement qaEvaluationReportsPermissions = new XElement("QAEvaluationReports");
                recorders = new XElement("Recorders");
                buildAgentBasedRecorderXML(entNodes, userNum, ModuleBit.QAEvaluationReports, ModuleAccessRight.QAEvaluationReports.ToString(), recorders, tenantId);
                qaEvaluationReportsPermissions.Add(recorders);

                // Permission Bit = 8
                XElement advReportsPermissions = new XElement("AdvancedReports");
                recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.AdvancedReports, ModuleAccessRight.AdvancedReports.ToString(), recorders, tenantId);
                advReportsPermissions.Add(recorders);

                // Permission Bit = 9
                XElement irFullPermissions = new XElement("IRFull");
                recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.IRFull, ModuleAccessRight.IRFull.ToString(), recorders, tenantId);
                irFullPermissions.Add(recorders);

                // Permission Bit = 10
                XElement saveNEmailPermissions = new XElement("SaveAndEmail");
                recorders = new XElement("Recorders");
                buildExtensionBasedRecorderXML(entNodes, userNum, ModuleBit.SaveNEmail, ModuleAccessRight.SaveNEmail.ToString(), recorders, tenantId);
                saveNEmailPermissions.Add(recorders);

                if (irLitePermissions.Value != string.Empty)
                    permissions.Add(irLitePermissions);
                if (monitorPermissions.Value != string.Empty)
                    permissions.Add(monitorPermissions);
                if (searchPermissions.Value != string.Empty)
                    permissions.Add(searchPermissions);
                if (evaluationPermissions.Value != string.Empty)
                    permissions.Add(evaluationPermissions);
                if (qaEvaluationReportsPermissions.Value != string.Empty)
                    permissions.Add(qaEvaluationReportsPermissions);
                if (advReportsPermissions.Value != string.Empty)
                    permissions.Add(advReportsPermissions);
                if (irFullPermissions.Value != string.Empty)
                    permissions.Add(irFullPermissions);
                if (saveNEmailPermissions.Value != string.Empty)
                    permissions.Add(saveNEmailPermissions);

                return permissions;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "CreateGroupPermissionsXML", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "CreateGroupPermissionsXML", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        private XElement CreateTabBasedPermissionsXML(List<EnterpriseNodeDTO> entNodes, int userNum, int tenantId = 0)
        {
            try
            {
                XElement permissions = new XElement("Permissions");
                XElement tabPermission = new XElement("TabPermission");

                // Permission Bit = 1
                XElement setupPermissions = new XElement("Setup");
                XElement recorders = new XElement("Recorders");
                buildTabBasedRecorderXML(entNodes, userNum, ModuleBit.Setup, ModuleAccessRight.Setup.ToString(), recorders, tenantId);
                setupPermissions.Add(recorders);

                // Permission Bit = 6
                XElement dashboardPermissions = new XElement("Dashboard");
                recorders = new XElement("Recorders");
                buildTabBasedRecorderXML(entNodes, userNum, ModuleBit.Dashboard, ModuleAccessRight.Dashboard.ToString(), recorders, tenantId);
                dashboardPermissions.Add(recorders);

                if (setupPermissions.Value != string.Empty)
                    tabPermission.Add(setupPermissions);
                if (dashboardPermissions.Value != string.Empty)
                    tabPermission.Add(dashboardPermissions);

                if (tabPermission.Value != string.Empty)
                    permissions.Add(tabPermission);

                return permissions;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "CreateTabBasedPermissionsXML", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "CreateTabBasedPermissionsXML", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }
        private static void buildExtensionBasedRecorderXML(List<EnterpriseNodeDTO> entNodes, int userNum, ModuleBit moduleBit, string moduleAccessRights, XElement recorders, int tenantId = 0)
        {
            try
            {
                List<int> recIds = entNodes.Where(w => w.ModuleBit == moduleBit).Select(c => c.RecId).Distinct().ToList();

                foreach (var recId in recIds)
                {
                    XElement rec = new XElement("Recorder" + recId.ToString());

                    var csvExts = string.Join(",", entNodes.Where(w => w.RecId == Convert.ToInt32(recId) && w.ModuleBit == moduleBit).Select(c => c.NodeId));
                    var csvExtNames = string.Join(",", entNodes.Where(w => w.RecId == Convert.ToInt32(recId) && w.ModuleBit == moduleBit).Select(c => "Extension " + c.NodeId));
                    var csvStatus = string.Join(",", entNodes.Where(w => w.RecId == Convert.ToInt32(recId) && w.ModuleBit == moduleBit).Select(c => c.DbStatus == true ? 1 : 0)).ToLower();

                    rec.Add(new XElement("IsExtensionBased", true.ToString().ToLower()));
                    rec.Add(new XElement("RecId", recId.ToString()));
                    rec.Add(new XElement("MainExt", "0"));
                    rec.Add(new XElement("MainUserNum", userNum.ToString()));
                    rec.Add(new XElement("SelectedExts", csvExts.ToString()));
                    rec.Add(new XElement("SelectedExtNames", csvExtNames.ToString()));
                    rec.Add(new XElement("SelectedExtStatus", csvStatus.ToString()));
                    rec.Add(new XElement("SelectedUserNums", string.Empty));
                    rec.Add(new XElement("SelectedExtNames", string.Empty));
                    rec.Add(new XElement("SelectedExtStatus", string.Empty));
                    rec.Add(new XElement("AccessRight", moduleAccessRights.ToString()));

                    recorders.Add(rec);
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "buildExtensionBasedRecorderXML", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "buildExtensionBasedRecorderXML", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }
        private static void buildAgentBasedRecorderXML(List<EnterpriseNodeDTO> entNodes, int userNum, ModuleBit moduleBit, string moduleAccessRights, XElement recorders, int tenantId = 0)
        {
            try
            {
                List<int> recIds = entNodes.Where(w => w.ModuleBit == moduleBit).Select(c => c.RecId).Distinct().ToList();

                foreach (var recId in recIds)
                {
                    XElement rec = new XElement("Recorder" + recId.ToString());

                    var csvUserNums = string.Join(",", entNodes.Where(w => w.RecId == Convert.ToInt32(recId) && w.ModuleBit == moduleBit).Select(c => c.NodeId));
                    var csvUserNames = string.Join(",", entNodes.Where(w => w.RecId == Convert.ToInt32(recId) && w.ModuleBit == moduleBit).Select(c => "Agent " + c.NodeId));
                    var csvStatus = string.Join(",", entNodes.Where(w => w.RecId == Convert.ToInt32(recId) && w.ModuleBit == moduleBit).Select(c => c.DbStatus == true ? 1 : 0)).ToLower();

                    rec.Add(new XElement("IsExtensionBased", false.ToString().ToLower()));
                    rec.Add(new XElement("RecId", recId.ToString()));
                    rec.Add(new XElement("MainExt", "0"));
                    rec.Add(new XElement("MainUserNum", userNum.ToString()));
                    rec.Add(new XElement("SelectedExts", string.Empty));
                    rec.Add(new XElement("SelectedExtNames", string.Empty));
                    rec.Add(new XElement("SelectedExtStatus", string.Empty));
                    rec.Add(new XElement("SelectedUserNums", csvUserNums.ToString()));
                    rec.Add(new XElement("SelectedUserNames", csvUserNames.ToString()));
                    rec.Add(new XElement("SelectedUserStatus", csvStatus.ToString()));
                    rec.Add(new XElement("AccessRight", moduleAccessRights.ToString()));

                    recorders.Add(rec);
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "buildAgentBasedRecorderXML", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "buildAgentBasedRecorderXML", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        private static void buildTabBasedRecorderXML(List<EnterpriseNodeDTO> entNodes, int userNum, ModuleBit moduleBit, string moduleAccessRights, XElement recorders, int tenantId)
        {
            try
            {
                List<int> recIds = entNodes.Where(w => w.ModuleBit == moduleBit).Select(c => c.RecId).Distinct().ToList();
                foreach (var recId in recIds)
                {
                    List<bool> isEnabled = entNodes.Where(w => w.RecId == Convert.ToInt32(recId) && w.ModuleBit == moduleBit).Select(c => c.DbStatus).ToList();

                    XElement rec = new XElement("Recorder" + recId.ToString());
                    rec.Add(new XElement("RecId", recId.ToString()));
                    rec.Add(new XElement("MainUserNum", userNum.ToString()));
                    rec.Add(new XElement("IsEnabled", isEnabled.FirstOrDefault() == true ? 1 : 0));
                    rec.Add(new XElement("AccessRight", moduleAccessRights.ToString()));
                    recorders.Add(rec);
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "buildTabBasedRecorderXML", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "buildTabBasedRecorderXML", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        private static List<ModuleBit> getModuleBitsByAccessRightString(string accessRights, int tenantId = 0)
        {
            try
            {
                List<ModuleBit> moduleBits = new List<ModuleBit>();
                if (accessRights.Substring(0, 1) == "1")
                    moduleBits.Add(ModuleBit.Setup);
                if (accessRights.Substring(1, 1) == "1")
                    moduleBits.Add(ModuleBit.IRLite);
                if (accessRights.Substring(2, 1) == "1")
                    moduleBits.Add(ModuleBit.Monitor);
                if (accessRights.Substring(3, 1) == "1")
                    moduleBits.Add(ModuleBit.Search);
                if (accessRights.Substring(4, 1) == "1")
                    moduleBits.Add(ModuleBit.Evaluation);
                if (accessRights.Substring(5, 1) == "1")
                    moduleBits.Add(ModuleBit.Dashboard);
                if (accessRights.Substring(6, 1) == "1")
                    moduleBits.Add(ModuleBit.QAEvaluationReports);
                if (accessRights.Substring(7, 1) == "1")
                    moduleBits.Add(ModuleBit.AdvancedReports);
                if (accessRights.Substring(8, 1) == "1")
                    moduleBits.Add(ModuleBit.IRFull);
                if (accessRights.Substring(9, 1) == "1")
                    moduleBits.Add(ModuleBit.SaveNEmail);
                return moduleBits;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "getModuleBitsByAccessRightString", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "getModuleBitsByAccessRightString", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #endregion

        #region QB Functionalities
        public string GetSystemSerialTag(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetSystemSerialTag", tenantId, "GetSystemSerialTag function has been called successfully. "));
                return new UserManagerDAL(tenantId).GetSystemSerialTag();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetSystemSerialTag", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetSystemSerialTag", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Channel Count
        public int GetActiveChannelCount(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetActiveChannelCount", tenantId, "GetActiveChannelCount function has been called successfully. "));
                return new UserManagerDAL(tenantId).GetActiveChannelCount();
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetActiveChannelCount", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetActiveChannelCount", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Custom Fields
        public UMResponse GetCustomFields(UMRequest umRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "GetCustomFields", umRequest.TenantId, "GetCustomFields function has been called successfully. UserNum = " + umRequest.UserId));
                List<CustomField> customFields = null;
                customFields = new UserManagerDAL(umRequest.TenantId).GetCustomFields(umRequest.User.UserNum);
                return new UMResponse { CustomFields = customFields };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "GetCustomFields", umRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "GetCustomFields", umRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public bool UpdateCustomField(CustomField customField, int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.UserManager, "UpdateCustomField", tenantId, "UpdateCustomField function has been called successfully. "));
                bool bUpdated = new UserManagerDAL(tenantId).UpdateCustomField(customField);
                return bUpdated;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.UserManager, "UpdateCustomField", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.UserManager, "UpdateCustomField", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion
    }
}