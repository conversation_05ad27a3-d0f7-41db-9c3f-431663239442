﻿using System;
using System.Collections.Generic;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.RoleManagement;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.DataContracts.TenantEntities;

namespace RevCord.DataContracts.Messages
{
    public class VRRequest : RequestBase
    {
        public int UserId { get; set; }
        public int UserType { get; set; }

        public CallCriteria Criteria { get; set; }

        public CustomSearchCriteria CustomSearchCriteria { get; set; }

        public Playlist Playlist { get; set; }

        public int PlaylistId { get; set; }

        public int PlaylistRevSyncServerID { get; set; }


        public int PlaylistDetailId { get; set; }

        /************* Purpose: used for Paging ****************/
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        /*******************************************************/

        public bool IsChainDBsConfigured { get; set; }

        //public bool GetAllChannels { get; set; }

        public ChannelCriteria ChannelCriteria { get; set; }

        public CallInfo CallInfo { get; set; }

        public Bookmark Bookmark { get; set; }

        public List<string> CallIds { get; set; }

        public List<Recorder> Recorders { get; set; }

        public Recorder Recorder { get; set; }

        public int RecorderId { get; set; }

        public CallCustomField CallCustomField { get; set; }

        public bool IsDemoMode { get; set; }

        public bool IsDemoBasedSearch { get; set; }

        /************* Purpose: used for Transcription ****************/
        public string CallId { get; set; }
        public string Transcription { get; set; }
        public float ConfidenceScore { get; set; }
        public int TranscriptionId { get; set; }
        public string STTFileName { get; set; }

        /************* T1-Extension Filtering ************************/
        /************* Author : Arivu ********************************/

        public string TagRuleUserData { get; set; }
        public bool IsTagRuleUser { get; set; }

        public bool IsInquireView { get; set; }
        public bool IsRoleBasedAccessEnabled { get; set; }
        //public int RoleId { get; set; }
        public Role Role { get; set; }
        public string MinT1Ch { get; set; }
        public string MaxT1Ch { get; set; }

        public bool IsEnterpriseRecorder { get; set; }

        public ScheduleEventInfo ScheduleEvent { get; set; }

        public Channel Channel { get; set; }

        public List<int> ChannelIds { get; set; }
        public int ChannelId { get; set; }
        public int NoOfAnalogChannels { get; set; }
        public int NoOfVoIPChannels { get; set; }

        public string ChannelName { get; set; }
        public string TeamsUserId { get; set; }

        public bool IsSearchPageCall { get; set; }
        public CallAudit CallAudit { get; set; }

        public UserSearch UserSearch { get; set; }
        public int UserSearchId { get; set; }

        public MultiCallEvaluationGroup MultiCallEvaluationGroup { get; set; }

        public bool IsOnlyIQ3ModeEnabled { get; set; }
        public bool IsOnlyIWBModeEnabled { get; set; }
        public bool HideTextAndScreensTree { get; set; }
        public bool IsTeamsEnabled { get; set; }
        public bool IsRevcellEnabled { get; set; }
        public bool Is2FAEnabled { get; set; }
        public ColumnReorder ColumnReorder { get; set; }
        public TenantGateway TenantGateway { get; set; }

        public string EventId { get; set; }
        public int TenantId { get; set; }
        public int AssetId { get; set; }

        public bool IsIwbModeEnabled { get; set; }

    }
}