﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.ByteArrayContent">
      <summary>Предоставляет содержимое HTTP на основе массива байтов.</summary>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <param name="content">Содержимое, используемое для инициализации <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="content" /> — null. </exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <param name="content">Содержимое, используемое для инициализации <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <param name="offset">Смещение в байтах в параметре <paramref name="content" />, используемом для инициализации объекта <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <param name="count">Число байтов в параметре <paramref name="content" />, начиная с параметра <paramref name="offset" />, используемых для инициализации объекта <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="content" /> — null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="offset" /> меньше нуля.– или –Значение параметра <paramref name="offset" /> больше длины содержимого, указанного параметром <paramref name="content" />.– или –Значение параметра <paramref name="count " /> меньше нуля.– или –Значение параметра <paramref name="count" /> больше длины содержимого, указанного параметром <paramref name="content" /> без учета параметра <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.CreateContentReadStreamAsync">
      <summary>Создает поток содержимого HTTP как асинхронную операцию для чтения, чье резервное хранилище — память из <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Сериализация и запись указанного в конструкторе массива байтов в поток содержимого HTTP в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task" />. Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="stream">Целевой поток.</param>
      <param name="context">Сведения о транспорте, например, о токене привязки каналов.Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.TryComputeLength(System.Int64@)">
      <summary>Определяет, имеет ли массив байтов допустимую длину в байтах.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если <paramref name="length" /> является допустимой длиной; в противном случае — значение false.</returns>
      <param name="length">Длина (в байтах) массива байтов.</param>
    </member>
    <member name="T:System.Net.Http.ClientCertificateOption">
      <summary>Определяет способ предоставления клиентских сертификатов.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Automatic">
      <summary>Объект <see cref="T:System.Net.Http.HttpClientHandler" /> будет пытаться предоставить все доступные клиентские сертификаты автоматически.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Manual">
      <summary>Приложение вручную предоставляет клиентские сертификаты объектам <see cref="T:System.Net.Http.WebRequestHandler" />.Это значение по умолчанию.</summary>
    </member>
    <member name="T:System.Net.Http.DelegatingHandler">
      <summary>Тип для обработчиков HTTP, которые делегируют обработку ответных сообщений HTTP другому обработчику, который называется внутренним обработчиком.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.DelegatingHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Создает новый экземпляр класса <see cref="T:System.Net.Http.DelegatingHandler" /> с указанным внутренним обработчиком.</summary>
      <param name="innerHandler">Внутренний обработчик, отвечающий за обработку сообщений откликов HTTP.</param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.DelegatingHandler" />, и по возможности — управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов. </param>
    </member>
    <member name="P:System.Net.Http.DelegatingHandler.InnerHandler">
      <summary>Получает или задает внутренний обработчик, который обрабатывает сообщения откликов HTTP.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpMessageHandler" />.Внутренний обработчик для сообщений HTTP-откликов.</returns>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Отправляет HTTP-запрос внутреннему обработчику, отправляемый серверу в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />. Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="request">Сообщения HTTP-запроса, которые необходимо отправить на сервер.</param>
      <param name="cancellationToken">Токен отмены для отмены операции.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="request" /> — null.</exception>
    </member>
    <member name="T:System.Net.Http.FormUrlEncodedContent">
      <summary>Контейнер для кортежей " имя-значение ", закодированных с помощью типа MIME application/x-www-form-urlencoded.</summary>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.FormUrlEncodedContent" /> с конкретной коллекцией пар "имя-значение".</summary>
      <param name="nameValueCollection">Коллекция пар имен и значений.</param>
    </member>
    <member name="T:System.Net.Http.HttpClient">
      <summary>Предоставляет базовый класс для отправки HTTP-запросов и получения HTTP-ответов от ресурса с заданным URI. </summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpClient" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpClient" /> указанным обработчиком.</summary>
      <param name="handler">Стек обработчика HTTP-данных, используемый для отправки запросов. </param>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpClient" /> указанным обработчиком.</summary>
      <param name="handler">Компонент <see cref="T:System.Net.Http.HttpMessageHandler" />, отвечающий за обработку сообщений ответов HTTP.</param>
      <param name="disposeHandler">Значение true, если внутренний обработчик должен быть удален с помощью Dispose(); значение false, если планируется повторно использовать внутренний обработчик.</param>
    </member>
    <member name="P:System.Net.Http.HttpClient.BaseAddress">
      <summary>Возвращает или задает базовый адрес универсального кода ресурса (URI) интернет-ресурса, используемого при отправке запросов.</summary>
      <returns>Возвращает <see cref="T:System.Uri" />.Базовый адрес универсального кода ресурса (URI) интернет-ресурса, используемого при отправке запросов.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.CancelPendingRequests">
      <summary>Отмена всех ожидающих запросов на этом экземпляре.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultRequestHeaders">
      <summary>Возвращает заголовки, которые должны отправляться с каждым запросом.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />.Заголовки, которые должны отправляться с каждым запросом.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String)">
      <summary>Отправка запроса DELETE согласно указанному универсальному коду ресурса (URI) в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Сообщение запроса уже было отправлено экземпляром <see cref="T:System.Net.Http.HttpClient" />.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String,System.Threading.CancellationToken)">
      <summary>Отправка запроса DELETE к указанному URI с токеном отмены в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Сообщение запроса уже было отправлено экземпляром <see cref="T:System.Net.Http.HttpClient" />.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri)">
      <summary>Отправка запроса DELETE согласно указанному универсальному коду ресурса (URI) в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Сообщение запроса уже было отправлено экземпляром <see cref="T:System.Net.Http.HttpClient" />.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>Отправка запроса DELETE к указанному URI с токеном отмены в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Сообщение запроса уже было отправлено экземпляром <see cref="T:System.Net.Http.HttpClient" />.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpClient" />, и опционально удаляет управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов.</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String)">
      <summary>Отправка запроса GET согласно указанному универсальному коду ресурса (URI) в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption)">
      <summary>Отправка запроса GET указанному универсальному коду ресурса (URI) с параметром "выполнение HTTP" в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="completionOption">Значение параметра завершения операции HTTP, указывающее, когда следует считать операцию завершенной.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Отправка запроса GET к указанному универсальному коду ресурса (URI) с параметром "выполнение HTTP" и токеном отмены в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="completionOption">Значение параметра завершения операции HTTP, указывающее, когда следует считать операцию завершенной.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Threading.CancellationToken)">
      <summary>Отправка запроса DELETE указанному универсальному коду ресурса (URI) с токеном отмены в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri)">
      <summary>Отправка запроса GET согласно указанному универсальному коду ресурса (URI) в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption)">
      <summary>Отправка запроса GET указанному универсальному коду ресурса (URI) с параметром "выполнение HTTP" в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="completionOption">Значение параметра завершения операции HTTP, указывающее, когда следует считать операцию завершенной.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Отправка запроса GET к указанному универсальному коду ресурса (URI) с параметром "выполнение HTTP" и токеном отмены в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="completionOption">Значение параметра завершения операции HTTP, указывающее, когда следует считать операцию завершенной.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>Отправка запроса DELETE указанному универсальному коду ресурса (URI) с токеном отмены в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.String)">
      <summary>Отправка запроса GET согласно указанному универсальному коду ресурса (URI) и возврат текста ответа в виде массива байтов в асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.Uri)">
      <summary>Отправка запроса GET согласно указанному универсальному коду ресурса (URI) и возврат текста ответа в виде массива байтов в асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.String)">
      <summary>Отправка запроса GET согласно указанному универсальному коду ресурса (URI) и возврат текста ответа в виде потока в асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.Uri)">
      <summary>Отправка запроса GET согласно указанному универсальному коду ресурса (URI) и возврат текста ответа в виде потока в асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.String)">
      <summary>Отправка запроса GET согласно указанному универсальному коду ресурса (URI) и возврат текста ответа в виде строки в асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.Uri)">
      <summary>Отправка запроса GET согласно указанному универсальному коду ресурса (URI) и возврат текста ответа в виде строки в асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.MaxResponseContentBufferSize">
      <summary>Возвращает или задает максимальное число байтов в буфере при чтении содержимого отклика.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Максимальное число байтов в буфере при чтении содержимого отклика.Значением по умолчанию для этого свойства является 2 гигабайта.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Указанный размер меньше или равен нулю.</exception>
      <exception cref="T:System.InvalidOperationException">Операция для текущего экземпляра уже запущена. </exception>
      <exception cref="T:System.ObjectDisposedException">Текущий экземпляр был удален. </exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Отправка запроса POST по указанному универсальному коду ресурса (URI) в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="content">Содержимое HTTP-запроса, отправляемое на сервер.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Отправка запроса POST с токеном отмены в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="content">Содержимое HTTP-запроса, отправляемое на сервер.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Отправка запроса POST по указанному универсальному коду ресурса (URI) в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="content">Содержимое HTTP-запроса, отправляемое на сервер.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Отправка запроса POST с токеном отмены в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="content">Содержимое HTTP-запроса, отправляемое на сервер.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Отправка запроса PUT по указанному универсальному коду ресурса (URI) в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="content">Содержимое HTTP-запроса, отправляемое на сервер.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Отправка запроса PUT с токеном отмены в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="content">Содержимое HTTP-запроса, отправляемое на сервер.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Отправка запроса PUT по указанному универсальному коду ресурса (URI) в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="content">Содержимое HTTP-запроса, отправляемое на сервер.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Отправка запроса PUT с токеном отмены в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="requestUri">Универсальный код ресурса (URI), по которому отправляется запрос.</param>
      <param name="content">Содержимое HTTP-запроса, отправляемое на сервер.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="requestUri" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage)">
      <summary>Отправка HTTP-запроса в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="request">Сообщение HTTP-запроса для отправки.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="request" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Сообщение запроса уже было отправлено экземпляром <see cref="T:System.Net.Http.HttpClient" />.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption)">
      <summary>Отправка HTTP-запроса в качестве асинхронной операции. </summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="request">Сообщение HTTP-запроса для отправки.</param>
      <param name="completionOption">Когда должна завершиться операция (как только будет доступен отклик или после считывания всего содержимого отклика).</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="request" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Сообщение запроса уже было отправлено экземпляром <see cref="T:System.Net.Http.HttpClient" />.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Отправка HTTP-запроса в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="request">Сообщение HTTP-запроса для отправки.</param>
      <param name="completionOption">Когда должна завершиться операция (как только будет доступен отклик или после считывания всего содержимого отклика).</param>
      <param name="cancellationToken">Токен отмены для отмены операции.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="request" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Сообщение запроса уже было отправлено экземпляром <see cref="T:System.Net.Http.HttpClient" />.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Отправка HTTP-запроса в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="request">Сообщение HTTP-запроса для отправки.</param>
      <param name="cancellationToken">Токен отмены для отмены операции.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="request" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Сообщение запроса уже было отправлено экземпляром <see cref="T:System.Net.Http.HttpClient" />.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.Timeout">
      <summary>Возвращает или задает время ожидания для выполнения запроса.</summary>
      <returns>Возвращает <see cref="T:System.TimeSpan" />.Время ожидания для выполнения запроса.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Указанное время ожидания меньше или равно нулю и не является <see cref="F:System.Threading.Timeout.InfiniteTimeSpan" />.</exception>
      <exception cref="T:System.InvalidOperationException">Операция для текущего экземпляра уже запущена. </exception>
      <exception cref="T:System.ObjectDisposedException">Текущий экземпляр был удален.</exception>
    </member>
    <member name="T:System.Net.Http.HttpClientHandler">
      <summary>Обработчик сообщений по умолчанию, используемый объектом <see cref="T:System.Net.Http.HttpClient" />.  </summary>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.#ctor">
      <summary>Создает экземпляр класса <see cref="T:System.Net.Http.HttpClientHandler" />.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect">
      <summary>Получает или задает значение, которое указывает, должен ли обработчик следовать откликам переадресации.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если обработчик должен следовать откликам перенаправления; в противном случае — значение false.Значение по умолчанию — true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AutomaticDecompression">
      <summary>Получает или задает тип метода распаковки, используемый обработчиком для автоматической распаковки содержимого отклика HTTP.</summary>
      <returns>Возвращает <see cref="T:System.Net.DecompressionMethods" />.Метод автоматической распаковки, используемый обработчиком.Значение по умолчанию — <see cref="F:System.Net.DecompressionMethods.None" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.ClientCertificateOptions">
      <summary>Получает или задает коллекцию сертификатов безопасности, связанных с данным обработчиком.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.ClientCertificateOption" />.Коллекция сертификатов безопасности, связанная с данным обработчиком.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.CookieContainer">
      <summary>Получает или задает контейнер файлов cookie, используемый для хранения файлов cookie сервера обработчиком.</summary>
      <returns>Возвращает <see cref="T:System.Net.CookieContainer" />.Контейнер файлов cookie, используемый для хранения файлов cookie сервера обработчиком.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Credentials">
      <summary>Получает или задает данные для аутентификации, используемые этим обработчиком.</summary>
      <returns>Возвращает <see cref="T:System.Net.ICredentials" />.Учетные данные аутентификации, связанные с заголовком.Значение по умолчанию: null.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpClientHandler" />, и при необходимости удаляет управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов.</param>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections">
      <summary>Получает или задает максимальное количество переадресаций, выполняемых обработчиком.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Максимальное количество откликов переадресации, указаниям которых следует обработчик.Значение по умолчанию - 50.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxRequestContentBufferSize">
      <summary>Получает или задает максимальный размер буфера содержимого запроса, используемый обработчиком.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Максимальный размер буфера содержимого запроса в байтах.По умолчанию используется значение 2 гигабайта.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.PreAuthenticate">
      <summary>Получает или задает значение, указывающее, отправляет ли обработчик заголовок авторизации вместе с запросом.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true для обработчика, с запросом требуется отправить заголовок авторизации HTTP после выполнения аутентификации; в противном случае — значение false.Значение по умолчанию — false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Proxy">
      <summary>Получает или задает сведения о прокси-сервере, используемом обработчиком.</summary>
      <returns>Возвращает <see cref="T:System.Net.IWebProxy" />.Сведения о прокси-сервере, используемом обработчиком.Значение по умолчанию — null.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Создает экземпляр <see cref="T:System.Net.Http.HttpResponseMessage" /> на основе сведений, предоставленных в <see cref="T:System.Net.Http.HttpRequestMessage" /> как операция, которая не блокируется.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="request">Сообщение HTTP-запроса.</param>
      <param name="cancellationToken">Токен отмены для отмены операции.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="request" /> — null.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsAutomaticDecompression">
      <summary>Получает значение, указывающее, поддерживает ли обработчик автоматическую распаковку содержимого ответа.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если обработчик поддерживает автоматическую распаковку содержимого отклика; в противном случае — значение false.Значение по умолчанию — true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsProxy">
      <summary>Получает значение, указывающее, поддерживает ли обработчик параметры прокси.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если обработчик поддерживает параметры прокси-сервера; в противном случае — значение false.Значение по умолчанию — true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsRedirectConfiguration">
      <summary>Получает значение, указывающее, поддерживает ли обработчик параметры конфигурации для свойств <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> и <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если обработчик поддерживает параметры конфигурации для свойств <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> и <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" />; в противном случае — значение false.Значение по умолчанию — true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseCookies">
      <summary>Получает или задает значение, указывающее, использует ли обработчик свойство <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> для хранения файлов cookie сервера, а также использует ли он эти файлы cookie при отправке запросов.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если обработчик использует свойство <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> для хранения файлов cookie сервера и использует эти файлы cookie при отправке запросов; в противном случае — значение false.Значение по умолчанию — true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseDefaultCredentials">
      <summary>Получает или задает значение, которое управляет отправкой обработчиком учетных данных по умолчанию вместе с запросами.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение равно true, если используются учетные данные по умолчанию, в противном случае — false.Значение по умолчанию — false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseProxy">
      <summary>Получает или задает значение, указывающее, использует ли обработчик прокси для запросов. </summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если обработчик должен использовать прокси-сервер для запросов; в противном случае — значение false.Значение по умолчанию — true.</returns>
    </member>
    <member name="T:System.Net.Http.HttpCompletionOption">
      <summary>Указывает, должны ли операции <see cref="T:System.Net.Http.HttpClient" /> считаться завершенными, как только имеется отклик, или после чтения всего сообщения отклика, включая содержимое. </summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseContentRead">
      <summary>Операция должна завершиться после считывания всего отклика, включая содержимое.</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseHeadersRead">
      <summary>Операция должна завершиться сразу после того, как отклик станет доступен и будут считаны заголовки.Содержимое еще не прочитано.</summary>
    </member>
    <member name="T:System.Net.Http.HttpContent">
      <summary>Базовый класс, представляющий заголовки содержимого и тело сущности HTTP.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream)">
      <summary>Выполнит сериализацию содержимого HTTP в поток байтов и копирует его в объект потока, предоставленный в качестве параметра <paramref name="stream" />.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="stream">Целевой поток.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Выполнит сериализацию содержимого HTTP в поток байтов и копирует его в объект потока, предоставленный в качестве параметра <paramref name="stream" />.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="stream">Целевой поток.</param>
      <param name="context">Сведения о транспорте (например, о токене привязки каналов).Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CreateContentReadStreamAsync">
      <summary>Сериализация содержимого HTTP в поток памяти в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose">
      <summary>Освобождает неуправляемые ресурсы и удаляет управляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpContent" />, и при необходимости удаляет управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов.</param>
    </member>
    <member name="P:System.Net.Http.HttpContent.Headers">
      <summary>Получает заголовки содержимого HTTP, определенные в RFC 2616.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpContentHeaders" />.Заголовки содержимого, соответствующие определениям в RFC 2616.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync">
      <summary>Сериализация содержимого HTTP в буфер памяти в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task" />.Объект задачи, представляющий асинхронную операцию.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync(System.Int64)">
      <summary>Сериализация содержимого HTTP в буфер памяти в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="maxBufferSize">Максимальный размер используемого буфера в байтах.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsByteArrayAsync">
      <summary>Сериализация содержимого HTTP в массив байтов в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStreamAsync">
      <summary>Выполнять сериализацию содержимого HTTP и возвращать поток, который представляет содержимое как асинхронную операции. </summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStringAsync">
      <summary>Сериализация содержимого HTTP в строку в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Сериализация содержимого HTTP в поток в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="stream">Целевой поток.</param>
      <param name="context">Сведения о транспорте (например, о токене привязки каналов).Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.TryComputeLength(System.Int64@)">
      <summary>Определяет, имеет ли содержимое HTTP допустимую длину в байтах.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если <paramref name="length" /> является допустимой длиной; в противном случае — значение false.</returns>
      <param name="length">Длина (в байтах) HTTP-содержимого.</param>
    </member>
    <member name="T:System.Net.Http.HttpMessageHandler">
      <summary>Базовый тип обработчиков сообщений HTTP.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose">
      <summary>Освобождает неуправляемые ресурсы и удаляет управляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpMessageHandler" />, и при необходимости удаляет управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Отправляет HTTP-запрос в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="request">Сообщение HTTP-запроса для отправки.</param>
      <param name="cancellationToken">Токен отмены для отмены операции.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="request" /> — null.</exception>
    </member>
    <member name="T:System.Net.Http.HttpMessageInvoker">
      <summary>Специальный Класс, который позволяет приложениям вызывать метод <see cref="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)" /> по цепочке обработчика HTTP. </summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Инициализирует экземпляр класса <see cref="T:System.Net.Http.HttpMessageInvoker" /> с конкретным <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" />,  отвечающий за обработку сообщений откликов HTTP.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>Инициализирует экземпляр класса <see cref="T:System.Net.Http.HttpMessageInvoker" /> с конкретным <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" />,  отвечающий за обработку сообщений откликов HTTP.</param>
      <param name="disposeHandler">true, если внутренний обработчик должен быть удален с помощью Dispose (); false, если планируется повторно использовать внутренний обработчик.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose">
      <summary>Освобождает неуправляемые ресурсы и удаляет управляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpMessageInvoker" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpMessageInvoker" />, и при необходимости удаляет управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Отправляет HTTP-запрос в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="request">Сообщение HTTP-запроса для отправки.</param>
      <param name="cancellationToken">Токен отмены для отмены операции.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="request" /> — null.</exception>
    </member>
    <member name="T:System.Net.Http.HttpMethod">
      <summary>Вспомогательный класс для получения и сравнения стандартных методов HTTP и создания новых методов HTTP.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMethod.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpMethod" /> с конкретным методом HTTP.</summary>
      <param name="method">Метод HTTP.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Delete">
      <summary>Представляет метод протокола HTTP DELETE.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Net.Http.HttpMethod)">
      <summary>Определяет, равен ли указанный объект <see cref="T:System.Net.Http.HttpMethod" /> текущему объекту <see cref="T:System.Object" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.true, если заданный объект равен текущему объекту; в противном случае — false.</returns>
      <param name="other">Метод HTTP для сравнения с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Object)">
      <summary>Определяет, равен ли указанный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Object" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.true, если заданный объект равен текущему объекту; в противном случае — false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Get">
      <summary>Представляет метод протокола HTTP GET.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.GetHashCode">
      <summary>Служит в качестве хэш-функции для данного типа.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта <see cref="T:System.Object" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Head">
      <summary>Представляет метод протокола HTTP HEAD.Метод HEAD идентичен методу GET за исключением того, что сервер возвращает в ответе только заголовки сообщений без основного текста сообщений.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Method">
      <summary>Метод HTTP. </summary>
      <returns>Возвращает <see cref="T:System.String" />.Метод HTTP, представленный в виде <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Equality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>Оператор равенства для сравнения двух объектов <see cref="T:System.Net.Http.HttpMethod" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если указанные параметры <paramref name="left" /> и <paramref name="right" /> равны; в противном случае — значение false.</returns>
      <param name="left">Объект <see cref="T:System.Net.Http.HttpMethod" />, который находится слева от оператора равенства.</param>
      <param name="right">Объект <see cref="T:System.Net.Http.HttpMethod" />, который находится справа от оператора равенства.</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Inequality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>Оператор неравенства для сравнения двух объектов <see cref="T:System.Net.Http.HttpMethod" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если указанные параметры <paramref name="left" /> и <paramref name="right" /> не равны; в противном случае — значение false.</returns>
      <param name="left">Объект <see cref="T:System.Net.Http.HttpMethod" />, который находится слева от оператора неравенства.</param>
      <param name="right">Объект <see cref="T:System.Net.Http.HttpMethod" />, который находится справа от оператора неравенства.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Options">
      <summary>Представляет метод протокола HTTP OPTIONS.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Post">
      <summary>Представляет метод протокола HTTP POST, который используется для публикации новой сущности в качестве дополнения к URI.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Put">
      <summary>Представляет метод протокола HTTP PUT, который используется для замены сущности, указанной с помощью URI.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.ToString">
      <summary>Возвращает строку, представляющую текущий объект.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Trace">
      <summary>Представляет метод протокола HTTP TRACE.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="T:System.Net.Http.HttpRequestException">
      <summary>Базовый класс для исключений, вызванных классами <see cref="T:System.Net.Http.HttpClient" /> и <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpRequestException" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpRequestException" /> с конкретным сообщением, описывающим текущее исключением.</summary>
      <param name="message">Сообщение, описывающее текущее исключение.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpRequestException" /> с конкретным сообщением, описывающим текущее исключение и внутреннее исключение.</summary>
      <param name="message">Сообщение, описывающее текущее исключение.</param>
      <param name="inner">Внутреннее исключение.</param>
    </member>
    <member name="T:System.Net.Http.HttpRequestMessage">
      <summary>Представляет сообщение HTTP-запроса.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpRequestMessage" /> с методом HTTP и <see cref="T:System.Uri" /> запроса.</summary>
      <param name="method">Метод HTTP.</param>
      <param name="requestUri">Строка, представляющая объект <see cref="T:System.Uri" /> запроса.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.Uri)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpRequestMessage" /> с методом HTTP и <see cref="T:System.Uri" /> запроса.</summary>
      <param name="method">Метод HTTP.</param>
      <param name="requestUri">Запрашиваемый объект <see cref="T:System.Uri" />.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Content">
      <summary>Получает или задает содержимое сообщения HTTP. </summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpContent" />.Содержимое сообщения</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose">
      <summary>Освобождает неуправляемые ресурсы и удаляет управляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpRequestMessage" />, и при необходимости удаляет управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Headers">
      <summary>Получает коллекцию заголовков HTTP-запросов.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />.Коллекция заголовков HTTP-запросов.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Method">
      <summary>Получает или задает метод HTTP, используемый сообщением запроса HTTP.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpMethod" />.Метод HTTP, используемый сообщением запроса.Значение по умолчанию — метод GET.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Properties">
      <summary>Получает набор свойств для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.RequestUri">
      <summary>Получает или задает <see cref="T:System.Uri" />, используемый для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Uri" />.<see cref="T:System.Uri" />, используемый для HTTP-запроса.</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.ToString">
      <summary>Возвращает строку, представляющую текущий объект.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строковое представление текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Version">
      <summary>Получает или задает версию сообщения HTTP.</summary>
      <returns>Возвращает <see cref="T:System.Version" />.Версия сообщения HTTP.Значение по умолчанию — 1.1.</returns>
    </member>
    <member name="T:System.Net.Http.HttpResponseMessage">
      <summary>Представляет ответное сообщение HTTP, включая код и данные о состоянии.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor(System.Net.HttpStatusCode)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.HttpResponseMessage" /> с конкретным свойством <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" />.</summary>
      <param name="statusCode">Код состояния HTTP-отклика.</param>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Content">
      <summary>Получает или задает содержимое сообщения отклика HTTP. </summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpContent" />.Содержимое сообщения HTTP-отклика.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose">
      <summary>Освобождает неуправляемые ресурсы и удаляет неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.HttpResponseMessage" />, и при необходимости удаляет управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов.</param>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode">
      <summary>Создает исключение, если свойство <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode" /> для HTTP-отклика имеет значение false.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpResponseMessage" />.Сообщение ответа HTTP, если вызов метода завершился успешно.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Headers">
      <summary>Получает коллекцию заголовков HTTP-откликов. </summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpResponseHeaders" />.Коллекция заголовков HTTP-откликов.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode">
      <summary>Получает значение, указывающее, был ли успешен HTTP-отклик.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение, указывающее, был ли успешен HTTP ответ.Значение true, если значение свойства <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> находится в диапазоне 200-299; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.ReasonPhrase">
      <summary>Получает или задает фразу причины, которая обычно отправляется серверами вместе с кодом состояния. </summary>
      <returns>Возвращает <see cref="T:System.String" />.Фраза причины, отправленная сервером.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.RequestMessage">
      <summary>Получает или задает сообщение запроса, на которое получено это сообщение отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpRequestMessage" />.Сообщение запроса, на которое получено это сообщение отклика.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.StatusCode">
      <summary>Получает или задает код состояния HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.HttpStatusCode" />.Код состояния HTTP-отклика.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.ToString">
      <summary>Возвращает строку, представляющую текущий объект.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строковое представление текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Version">
      <summary>Получает или задает версию сообщения HTTP. </summary>
      <returns>Возвращает <see cref="T:System.Version" />.Версия сообщения HTTP.Значение по умолчанию — 1.1.</returns>
    </member>
    <member name="T:System.Net.Http.MessageProcessingHandler">
      <summary>Базовый тип для обработчиков, которые выполняют определенную небольшую часть обработки запросов и ответных сообщений.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor">
      <summary>Создает экземпляр класса <see cref="T:System.Net.Http.MessageProcessingHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Создает экземпляр класса <see cref="T:System.Net.Http.MessageProcessingHandler" /> с указанным внутренним обработчиком.</summary>
      <param name="innerHandler">Внутренний обработчик, отвечающий за обработку сообщений откликов HTTP.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Выполняет обработку для каждого запроса, отправленного серверу.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpRequestMessage" />.Обработанное сообщение HTTP-запроса.</returns>
      <param name="request">Сообщение HTTP-запроса для обработки.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessResponse(System.Net.Http.HttpResponseMessage,System.Threading.CancellationToken)">
      <summary>Выполнить обработку на каждом ответе сервера.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.HttpResponseMessage" />.Обработанное сообщение HTTP-отклика.</returns>
      <param name="response">Сообщение HTTP-отклика для обработки.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Отправляет HTTP-запрос внутреннему обработчику, отправляемый серверу в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="request">Сообщения HTTP-запроса, которые необходимо отправить на сервер.</param>
      <param name="cancellationToken">Токен отмены, который может использоваться другими объектами или потоками для получения уведомления об отмене.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="request" /> — null.</exception>
    </member>
    <member name="T:System.Net.Http.MultipartContent">
      <summary>Предоставляет коллекцию объектов <see cref="T:System.Net.Http.HttpContent" />, которые сериализуются используя спецификацию типа содержимого multipart/*.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.MultipartContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String)">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.MultipartContent" />.</summary>
      <param name="subtype">Подтип составного содержимого.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="subtype" />имел значение null или содержит только пробелы.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String,System.String)">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.MultipartContent" />.</summary>
      <param name="subtype">Подтип составного содержимого.</param>
      <param name="boundary">Строка, представляющая границу составного содержимого.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="subtype" /> имеет значение null или содержит пустую строку.Параметр <paramref name="boundary" />имел значение null или содержит только пробелы.– или –Параметр <paramref name="boundary" /> заканчивается символом пробела.</exception>
      <exception cref="T:System.OutOfRangeException">Длина параметра <paramref name="boundary" /> больше 70.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Add(System.Net.Http.HttpContent)">
      <summary>Добавить многокомпонентное содержимое HTTP в коллекцию объектов <see cref="T:System.Net.Http.HttpContent" />, которые сериализуются используя спецификацию типа содержимого multipart/*.</summary>
      <param name="content">HTTP-содержимое, добавляемое в коллекцию.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="content" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.MultipartContent" />, и при необходимости удаляет управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.GetEnumerator">
      <summary>Возвращает перечислитель, который перебирает коллекцию объектов <see cref="T:System.Net.Http.HttpContent" />, которые сериализуются, используя спецификацию типа содержимого multipart/*.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.IEnumerator`1" />.Объект, который может использоваться для итерации по коллекции.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Сериализация содержимого HTTP, состоящего из нескольких частей, в поток в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="stream">Целевой поток.</param>
      <param name="context">Сведения о транспорте (например, о токене привязки каналов).Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.System#Collections#IEnumerable#GetEnumerator">
      <summary>Явная реализация метода <see cref="M:System.Net.Http.MultipartContent.GetEnumerator" />.</summary>
      <returns>Возвращает <see cref="T:System.Collections.IEnumerator" />.Объект, который может использоваться для итерации по коллекции.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.TryComputeLength(System.Int64@)">
      <summary>Определяет, имеет ли содержимое HTTP из нескольких частей допустимую длину в байтах.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если <paramref name="length" /> является допустимой длиной; в противном случае — значение false.</returns>
      <param name="length">Длина (в байта) HTTP-содержимого.</param>
    </member>
    <member name="T:System.Net.Http.MultipartFormDataContent">
      <summary>Предоставляет контейнер для закодированного с помощью типа MIME "multipart/form-data" содержимого.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.MultipartFormDataContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor(System.String)">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.MultipartFormDataContent" />.</summary>
      <param name="boundary">Строка, представляющая границу составного содержимого данных формы.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="boundary" />имел значение null или содержит только пробелы.– или –Параметр <paramref name="boundary" /> заканчивается символом пробела.</exception>
      <exception cref="T:System.OutOfRangeException">Длина параметра <paramref name="boundary" /> больше 70.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent)">
      <summary>Добавление содержимого HTTP в коллекцию объектов <see cref="T:System.Net.Http.HttpContent" />, которые сериализуются в тип MIME "multipart/form-data".</summary>
      <param name="content">HTTP-содержимое, добавляемое в коллекцию.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="content" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String)">
      <summary>Добавление содержимого HTTP в коллекцию объектов <see cref="T:System.Net.Http.HttpContent" />, которые сериализуются в тип MIME "multipart/form-data".</summary>
      <param name="content">HTTP-содержимое, добавляемое в коллекцию.</param>
      <param name="name">Имя добавляемого содержимого HTTP.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="name" />имел значение null или содержит только пробелы.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="content" /> — null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String,System.String)">
      <summary>Добавление содержимого HTTP в коллекцию объектов <see cref="T:System.Net.Http.HttpContent" />, которые сериализуются в тип MIME "multipart/form-data".</summary>
      <param name="content">HTTP-содержимое, добавляемое в коллекцию.</param>
      <param name="name">Имя добавляемого содержимого HTTP.</param>
      <param name="fileName">Имя файла для HTTP-содержимого, которое требуется добавить в коллекцию.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="name" />имел значение null или содержит только пробелы.– или –Параметр <paramref name="fileName" />имел значение null или содержит только пробелы.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="content" /> — null.</exception>
    </member>
    <member name="T:System.Net.Http.StreamContent">
      <summary>Предоставляет содержимое HTTP на основе потока.</summary>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream)">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.StreamContent" />.</summary>
      <param name="content">Содержимое, используемое для инициализации <see cref="T:System.Net.Http.StreamContent" />.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream,System.Int32)">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.StreamContent" />.</summary>
      <param name="content">Содержимое, используемое для инициализации <see cref="T:System.Net.Http.StreamContent" />.</param>
      <param name="bufferSize">Размер (в байтах) буфера, доступного для <see cref="T:System.Net.Http.StreamContent" />.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="content" /> — null.</exception>
      <exception cref="T:System.OutOfRangeException">Значение параметра <paramref name="bufferSize" /> было меньше или равно нулю. </exception>
    </member>
    <member name="M:System.Net.Http.StreamContent.CreateContentReadStreamAsync">
      <summary>Запись содержимого потока HTTP в поток в памяти в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task`1" />.Объект задачи, представляющий асинхронную операцию.</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Net.Http.StreamContent" />, и при необходимости удаляет управляемые ресурсы.</summary>
      <param name="disposing">Значение true для освобождения управляемых и неуправляемых ресурсов; значение false для освобождения только неуправляемых ресурсов.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Сериализация содержимого HTTP в поток в качестве асинхронной операции.</summary>
      <returns>Возвращает <see cref="T:System.Threading.Tasks.Task" />.Объект задачи, представляющий асинхронную операцию.</returns>
      <param name="stream">Целевой поток.</param>
      <param name="context">Сведения о транспорте (например, о токене привязки каналов).Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.TryComputeLength(System.Int64@)">
      <summary>Определяет, имеет ли содержимое потока допустимую длину в байтах.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если <paramref name="length" /> является допустимой длиной; в противном случае — значение false.</returns>
      <param name="length">Длина (в байтах) содержимого потока.</param>
    </member>
    <member name="T:System.Net.Http.StringContent">
      <summary>Предоставляет содержимое HTTP на основе строки.</summary>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String)">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.StringContent" />.</summary>
      <param name="content">Содержимое, используемое для инициализации <see cref="T:System.Net.Http.StringContent" />.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding)">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.StringContent" />.</summary>
      <param name="content">Содержимое, используемое для инициализации <see cref="T:System.Net.Http.StringContent" />.</param>
      <param name="encoding">Кодировка, используемая для содержимого.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding,System.String)">
      <summary>Создание нового экземпляра класса <see cref="T:System.Net.Http.StringContent" />.</summary>
      <param name="content">Содержимое, используемое для инициализации <see cref="T:System.Net.Http.StringContent" />.</param>
      <param name="encoding">Кодировка, используемая для содержимого.</param>
      <param name="mediaType">Тип мультимедиа, используемый для содержимого.</param>
    </member>
    <member name="T:System.Net.Http.Headers.AuthenticationHeaderValue">
      <summary>Представляет сведения об аутентификации в значениях заголовка Authorization, ProxyAuthorization, WWW-Authneticate и Proxy-Authenticate.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <param name="scheme">Схема, которую требуется использовать для авторизации.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <param name="scheme">Схема, которую требуется использовать для авторизации.</param>
      <param name="parameter">Учетные данные, содержащие сведения для аутентификации агента пользователя для запрашиваемого ресурса.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом. </param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Parameter">
      <summary>Получает учетные данные, содержащие сведения для аутентификации агента пользователя для запрашиваемого ресурса.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Учетные данные, содержащие сведения об аутентификации.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
      <param name="input">Строка представляющая сведения о значении заголовка аутентификации.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка аутентификации.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Scheme">
      <summary>Получает схему, используемую для авторизации.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Схема, которую требуется использовать для авторизации.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.TryParse(System.String,System.Net.Http.Headers.AuthenticationHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.CacheControlHeaderValue">
      <summary>Представляет значение заголовка Cache-Control.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Extensions">
      <summary>Токены Кэш-расширения, каждый с необязательным присвоенным значения.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.Коллекция токенов кэш-расширений, каждый с необязательным присвоенным значением.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxAge">
      <summary>Максимальная длительность, указанная в секундах, в течение которой клиент HTTP готов принять отклик. </summary>
      <returns>Возвращает <see cref="T:System.TimeSpan" />.Время в секундах. </returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStale">
      <summary>Готов ли клиент HTTP принять отклик, срок действия которого истек.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если клиент HTTP готов принять отклик, срок действия которого истек; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStaleLimit">
      <summary>Максимальное время (в секундах), в течение которого клиент HTTP готов принять отклик, срок действия которого истек.</summary>
      <returns>Возвращает <see cref="T:System.TimeSpan" />.Время в секундах.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MinFresh">
      <summary>Длительность существования, указанная в секундах, в течение которой клиент HTTP готов принять отклик.</summary>
      <returns>Возвращает <see cref="T:System.TimeSpan" />.Время в секундах.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MustRevalidate">
      <summary>Требует ли сервер-источник выполнения повторной проверки записи кэша при любом последующем использовании, когда запись кэша устаревает.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если сервер-источник требует выполнения повторной проверки записи кэша при любом последующем использовании, когда запись кэша устаревает; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCache">
      <summary>Готов ли клиент HTTP принять кэшированный отклик.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если клиент HTTP готов принять кэшированный отклик; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCacheHeaders">
      <summary>Коллекция имен полей в некэшированной директиве в поле заголовка кэш-элемента управления в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.Коллекция имен полей.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoStore">
      <summary>Не должна ли храниться в кэше какая-либо часть сообщения HTTP-запроса или любого отклика.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если в кэше не должна храниться какая-либо часть сообщения HTTP-запроса или любого отклика; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoTransform">
      <summary>Не должен ли кэш или прокси изменять какой-либо аспект тела сущности.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если кэш или прокси не должны изменять какой-либо аспект тела сущности; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.OnlyIfCached">
      <summary>Должен ли кэш отвечать, используя согласованную с другими ограничениями HTTP-запроса кэшированную запись или состояние 504 (время ожидания шлюза).</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если в отклике кэш должен либо использовать кэшированную запись, согласованную с другими ограничениями HTTP-запроса, либо состояние 504 (истекло время ожидания шлюза); в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка элемента управления кэшем.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка управления кэшем.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Private">
      <summary>Предназначено ли все сообщение отклика HTTP или его часть для одного пользователя и не должно ли это сообщение или его часть кэшироваться в общем кэше.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если сообщение HTTP-отклика предназначено для одного пользователя и не должно кэшироваться в общем кэше; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.PrivateHeaders">
      <summary>Имена полей коллекции в закрытой директиве в поле заголовка кэш-элемента управления в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.Коллекция имен полей.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.ProxyRevalidate">
      <summary>Требует ли сервер-источник выполнения повторной проверки записи кэша при любом последующем использовании, когда запись кэша устаревает для общих кэшей агентов пользователей.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если сервер-источник требует выполнения повторной проверки записи кэша при любом последующем использовании, когда запись кэша устаревает для общих кэшей агентов пользователей; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Public">
      <summary>Может ли отклик HTTP кэшироваться любым кэшем, даже если он обычно не кэшируется или кэшируется только в необщем кэше.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если HTTP-отклик может кэшироваться любым кэшем, даже если он обычно не кэшируется или кэшируется только в кэше без общего доступа; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.SharedMaxAge">
      <summary>Общая максимальная длительность (в секундах) в отклике HTTP, которая переопределяет директиву "max-age" в заголовке управления кэшем или в заголовке Expires для общего кэша.</summary>
      <returns>Возвращает <see cref="T:System.TimeSpan" />.Время в секундах.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.TryParse(System.String,System.Net.Http.Headers.CacheControlHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentDispositionHeaderValue">
      <summary>Представляет значение заголовка Content-Disposition.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.Net.Http.Headers.ContentDispositionHeaderValue)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <param name="source">Объект <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />. </param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <param name="dispositionType">Строка, содержащая <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.CreationDate">
      <summary>Дата создания файла.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Дата создания файла.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.DispositionType">
      <summary>Тип расположения для части основного текста содержимого.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Тип расположения. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileName">
      <summary>Предложение для построения имя файла для хранения полезных данные сообщения, используемое, если сущность удалена и хранится в отдельном файле.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Предлагаемое имя файла.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileNameStar">
      <summary>Предложение для построения имен файлов для хранения полезных данные сообщений, используемое, если сущности удалены и хранятся в отдельном файле.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Предложенное имя файла в виде filename*.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ModificationDate">
      <summary>Дата последнего изменения файла. </summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Дата изменения файла.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Name">
      <summary>Имя части тела содержимого.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Имя части тела содержимого.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Parameters">
      <summary>Набор параметров содержал заголовок Content-Disposition.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.Коллекция параметров. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</returns>
      <param name="input">Строка представляющая сведения о значении заголовка расположения содержимого.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка расположения содержимого.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ReadDate">
      <summary>Дата последнего чтения файла.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Дата последнего считывания.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Size">
      <summary>Приблизительный размер файла в байтах. </summary>
      <returns>Возвращает <see cref="T:System.Int64" />.Приблизительный размер в байтах.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentDispositionHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentRangeHeaderValue">
      <summary>Представляет значение заголовка Content-Range.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <param name="length">Начальная или конечная точка диапазона, в байтах.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <param name="from">Позиция начала передачи данных в байтах.</param>
      <param name="to">Позиция окончания передачи данных в байтах.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64,System.Int64)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <param name="from">Позиция начала передачи данных в байтах.</param>
      <param name="to">Позиция окончания передачи данных в байтах.</param>
      <param name="length">Начальная или конечная точка диапазона, в байтах.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект текущему объекту <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.From">
      <summary>Получает положение, с которого начнется отправка данных.</summary>
      <returns>Возвращает <see cref="T:System.Int64" />.Позиция начала передачи данных в байтах.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasLength">
      <summary>Получает значение, указывающее, задана ли длина заголовка Content-Range.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.true, если длина заголовка Content-Range  задана; в противном случае — false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasRange">
      <summary>Получает значение, указывающее, задано ли диапазон для Content-Range. </summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.true, если диапазон заголовка Content-Range задан; в противном случае — false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Length">
      <summary>Получает длину полного тела сущности.</summary>
      <returns>Возвращает <see cref="T:System.Int64" />.Длина полного тела сущности.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка диапазона содержимого.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка диапазона содержимого.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.To">
      <summary>Получает положение остановки передачи данных.</summary>
      <returns>Возвращает <see cref="T:System.Int64" />.Место остановки передачи данных.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentRangeHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Unit">
      <summary>Используемые единицы диапазона.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Объект <see cref="T:System.String" />, содержащий единицы диапазона. </returns>
    </member>
    <member name="T:System.Net.Http.Headers.EntityTagHeaderValue">
      <summary>Представляет значение заголовка тега сущности.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <param name="tag">Строка, содержащая <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <param name="tag">Строка, содержащая <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</param>
      <param name="isWeak">Значение, указывающее, является ли данный заголовок тега сущности слабым проверяющим элементом.Если заголовок тега сущности — слабый проверяющий элемент, <paramref name="isWeak" /> должно быть установлено в значение true.Если заголовок тега сущности — сильный проверяющий элемент, <paramref name="isWeak" /> должно быть установлено в значение false.</param>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Any">
      <summary>Получает значение заголовка тега сущности.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.IsWeak">
      <summary>Получает значение, указывающее предшествует ли тегу сущности индикатор ослабления.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.true, если тегу сущности предшествует индикатор ослабления; в противном случае — false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка тега сущности.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка тега сущности.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Tag">
      <summary>Получает Непрозрачную строку в кавычках. </summary>
      <returns>Возвращает <see cref="T:System.String" />.Непрозрачная строка в кавычках.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.TryParse(System.String,System.Net.Http.Headers.EntityTagHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpContentHeaders">
      <summary>Представляет коллекцию заголовков Content в соответствии с RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Allow">
      <summary>Получает значение заголовка содержимого Allow в HTTP-ответе. </summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.Значение заголовка Allow в HTTP-отклике.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentDisposition">
      <summary>Получает значение заголовка содержимого Content-Disposition в HTTP-ответе.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.Значение заголовка содержимого Content-Disposition в HTTP-отклике.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentEncoding">
      <summary>Получает значение заголовка содержимого Content-Encoding в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.Значение заголовка содержимого Content-Encoding в HTTP-отклике.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLanguage">
      <summary>Получает значение заголовка содержимого Content-Language в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.Значение заголовка содержимого Content-Language в HTTP-отклике.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLength">
      <summary>Получает или задает значение заголовка содержимого Content-Length в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.Int64" />.Значение заголовка содержимого Content-Length в HTTP-отклике.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLocation">
      <summary>Получает или задает значение заголовка содержимого Content-Location в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.Uri" />.Значение заголовка содержимого Content-Location в HTTP-отклике.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentMD5">
      <summary>Получает или задает значение заголовка содержимого Content-MD5 в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.Byte" />.Значение заголовка содержимого Content-MD5 в HTTP-отклике.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentRange">
      <summary>Получает или задает значение заголовка содержимого Content-Range в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.Значение заголовка содержимого Content-Range в HTTP-отклике.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentType">
      <summary>Получает или задает значение заголовка содержимого Content-Type в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.Значение заголовка содержимого Content-Type в HTTP-отклике.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Expires">
      <summary>Получает или задает значение заголовка содержимого Expires в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Значение заголовка содержимого Expires в HTTP-отклике.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.LastModified">
      <summary>Получает или задает значение заголовка содержимого Last-Modified в HTTP-отклике.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Значение заголовка содержимого Last-Modified в HTTP-отклике.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaders">
      <summary>Коллекция заголовков и их значения, как указано в RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Добавляет указанный заголовок и его значения в коллекцию <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <param name="name">Заголовок для добавления в коллекцию.</param>
      <param name="values">Список значений заголовков для добавления в коллекцию.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.String)">
      <summary>Добавляет указанный заголовок и его значение в коллекцию <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <param name="name">Заголовок для добавления в коллекцию.</param>
      <param name="value">Содержимое данного заголовка.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Clear">
      <summary>Удаляет все заголовки из коллекции <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Contains(System.String)">
      <summary>Возвращает, существует ли конкретный заголовок в коллекции <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если указанный заголовок существует в коллекции; в противном случае — значение false.</returns>
      <param name="name">Определенный заголовок.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetEnumerator">
      <summary>Возвращает перечислитель, с помощью которого можно перебирать все элементы экземпляра коллекции <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.IEnumerator`1" />.Перечислитель для объекта <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetValues(System.String)">
      <summary>Возвращает все значения заголовка для указанного заголовка, хранящихся в коллекции <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.IEnumerable`1" />.Массив строк заголовка.</returns>
      <param name="name">Указанный заголовок, для которого требуется вернуть значения.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Remove(System.String)">
      <summary>Удаляет указанный заголовок из коллекции <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.</returns>
      <param name="name">Имя заголовка, который должен быть удален из коллекции. </param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, который может перебирать элементы <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Возвращает <see cref="T:System.Collections.IEnumerator" />.Экземпляр реализации <see cref="T:System.Collections.IEnumerator" />, который может перебирать элементы в коллекции <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Возвращает значение, указывающее, были ли добавлены указанный заголовок и его значения в коллекцию <see cref="T:System.Net.Http.Headers.HttpHeaders" /> без проверки предоставленных сведения.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если указанный заголовок <paramref name="name" /> и <paramref name="values" /> можно добавить в коллекцию; в противном случае — значение false.</returns>
      <param name="name">Заголовок для добавления в коллекцию.</param>
      <param name="values">Значения заголовка.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.String)">
      <summary>Возвращает значение, указывающее, были ли добавлены указанный заголовок и его значение в коллекцию <see cref="T:System.Net.Http.Headers.HttpHeaders" /> без проверки предоставленных сведения.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если указанный заголовок <paramref name="name" /> и <paramref name="value" /> можно добавить в коллекцию; в противном случае — значение false.</returns>
      <param name="name">Заголовок для добавления в коллекцию.</param>
      <param name="value">Содержимое данного заголовка.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
      <summary>Указывает, хранятся ли указанный заголовок и заданные значения в коллекции <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметры <paramref name="name" /> и values указанного заголовка хранятся в коллекции; в противном случае — значение false.</returns>
      <param name="name">Указанное средство чтения.</param>
      <param name="values">Заданные значения заголовка.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaderValueCollection`1">
      <summary>Представляет коллекцию значений заголовка.</summary>
      <typeparam name="T">Тип коллекции заголовков.</typeparam>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Add(`0)">
      <summary>Добавляет запись в коллекцию <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <param name="item">Элемент для добавления в коллекцию заголовков.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Clear">
      <summary>Удаляет все записи из <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Contains(`0)">
      <summary>Определяет, содержит ли <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> элемент.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если запись содержится в экземпляре <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> ; в противном случае — значение false.</returns>
      <param name="item">Элемент для поиска в коллекцию заголовков.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.CopyTo(`0[],System.Int32)">
      <summary>Копирует целый массив <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> в совместимый одномерный массив <see cref="T:System.Array" />, начиная с заданного индекса целевого массива.</summary>
      <param name="array">Одномерный массив <see cref="T:System.Array" />, в который копируются элементы из интерфейса <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Массив <see cref="T:System.Array" /> должен иметь индексацию, начинающуюся с нуля.</param>
      <param name="arrayIndex">Индекс (с нуля) в массиве <paramref name="array" />, с которого начинается копирование.</param>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.Count">
      <summary>Получает количество заголовков в наборе <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Число заголовков в коллекции.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов списка <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.IEnumerator`1" />.Возвращает перечислитель для экземпляра <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.IsReadOnly">
      <summary>Получает значение, указывающее, действительно ли экземпляр <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> доступен только для чтения.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если экземпляр класса <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> доступен только для чтения, в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ParseAdd(System.String)">
      <summary>Анализирует и добавляет запись в <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <param name="input">Добавляемая запись.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Remove(`0)">
      <summary>Удаляет указанный элемент из поля со списком <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если элемент <paramref name="item" /> успешно удален из экземпляра <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />, в противном случае — значение false.</returns>
      <param name="item">Удаляемый элемент.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, осуществляющий перебор элементов списка <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Возвращает <see cref="T:System.Collections.IEnumerator" />.Возвращает перечислитель для экземпляра <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />. объект.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.TryParseAdd(System.String)">
      <summary>Определяет, может ли ввод быть проанализировано и добавлен в <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.значение true, если <paramref name="input" /> может быть проанализировано и добавлен в экземпляр <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />; в противном случае — значение false</returns>
      <param name="input">Проверяемое запись.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpRequestHeaders">
      <summary>Представляет коллекцию заголовков Request в соответствии с RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Accept">
      <summary>Получает значение заголовка Accept для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Accept для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptCharset">
      <summary>Получает значение заголовка Accept-Charset для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Accept-Charset для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptEncoding">
      <summary>Получает значение заголовка Accept-Encoding для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Accept-Encoding для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptLanguage">
      <summary>Получает значение заголовка Accept-Language для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Accept-Language для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Authorization">
      <summary>Получает или задает значение заголовка Authorization для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.Значение заголовка Authorization для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.CacheControl">
      <summary>Получает или задает значение заголовка Cache-Control для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.Значение заголовка Cache-Control для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Connection">
      <summary>Получает значение заголовка Connection для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Connection для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ConnectionClose">
      <summary>Получает или задает значение, указывающее, содержит ли заголовок Connection HTTP-запроса инструкцию Close.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заголовок Connection содержит Close; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Date">
      <summary>Получает или задает значение заголовка Date для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Значение заголовка Date для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Expect">
      <summary>Получает значение заголовка Expect для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Expect для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ExpectContinue">
      <summary>Получает или задает значение, указывающее, содержит ли заголовок Expect HTTP-запроса инструкцию Continue.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заголовок Expect содержит Continue; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.From">
      <summary>Получает или задает значение заголовка From для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Значение заголовка From для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Host">
      <summary>Получает или задает значение заголовка Host для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Значение заголовка Host для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfMatch">
      <summary>Получает значение заголовка If-Match для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка If-Match для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfModifiedSince">
      <summary>Получает или задает значение заголовка If-Modified-Since для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Значение заголовка If-Modified-Since для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfNoneMatch">
      <summary>Получает значение заголовка If-None-Match для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Получает значение заголовка If-None-Match для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfRange">
      <summary>Получает или задает значение заголовка If-Range для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.Значение заголовка If-Range для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfUnmodifiedSince">
      <summary>Получает или задает значение заголовка If-Unmodified-Since для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Значение заголовка If-Unmodified-Since для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.MaxForwards">
      <summary>Получает или задает значение заголовка Max-Forwards для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Значение заголовка Max-Forwards для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Pragma">
      <summary>Получает значение заголовка Pragma для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Pragma для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ProxyAuthorization">
      <summary>Получает или задает значение заголовка Proxy-Authorization для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.Значение заголовка Proxy-Authorization для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Range">
      <summary>Получает или задает значение заголовка Range для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.Значение заголовка Range для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Referrer">
      <summary>Получает или задает значение заголовка Referer для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Uri" />.Значение заголовка Referer для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TE">
      <summary>Получает значение заголовка TE для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка TE для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Trailer">
      <summary>Получает значение заголовка Trailer для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Trailer для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncoding">
      <summary>Получает значение заголовка Transfer-Encoding для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Transfer-Encoding для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncodingChunked">
      <summary>Получает или задает значение, указывающее, содержит ли заголовок Transfer-Encoding HTTP-запроса инструкцию chunked.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заголовок Transfer-Encoding содержит параметр "chunked"; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Upgrade">
      <summary>Получает значение заголовка Upgrade для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Upgrade для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.UserAgent">
      <summary>Получает значение заголовка User-Agent для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка User-Agent для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Via">
      <summary>Получает значение заголовка Via для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Via для HTTP-запроса.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Warning">
      <summary>Получает значение заголовка Warning для HTTP-запроса.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Warning для HTTP-запроса.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpResponseHeaders">
      <summary>Представляет коллекцию заголовков Response в соответствии с RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.AcceptRanges">
      <summary>Получает значение заголовка Accept-Ranges для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Accept-Ranges для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Age">
      <summary>Получает или задает значение заголовка Age для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.TimeSpan" />.Значение заголовка Age для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.CacheControl">
      <summary>Получает или задает значение заголовка Cache-Control для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.Значение заголовка Cache-Control для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Connection">
      <summary>Получает значение заголовка Connection для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Connection для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ConnectionClose">
      <summary>Получает или задает значение, указывающее, содержит ли заголовок Connection HTTP-ответа инструкцию Close.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заголовок Connection содержит Close; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Date">
      <summary>Получает или задает значение заголовка Date для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Значение заголовка Date для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ETag">
      <summary>Получает или задает значение заголовка ETag для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.Значение заголовка ETag для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Location">
      <summary>Получает или задает значение заголовка Location для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Uri" />.Значение заголовка Location для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Pragma">
      <summary>Получает значение заголовка Pragma для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Pragma для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ProxyAuthenticate">
      <summary>Получает значение заголовка Proxy-Authenticate для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Proxy-Authenticate для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.RetryAfter">
      <summary>Получает или задает значение заголовка Retry-After для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.Значение заголовка Retry-After для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Server">
      <summary>Получает значение заголовка Server для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Server для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Trailer">
      <summary>Получает значение заголовка Trailer для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Trailer для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncoding">
      <summary>Получает значение заголовка Transfer-Encoding для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Transfer-Encoding для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncodingChunked">
      <summary>Получает или задает значение, указывающее, содержит ли заголовок Transfer-Encoding HTTP-ответа инструкцию chunked.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заголовок Transfer-Encoding содержит параметр "chunked"; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Upgrade">
      <summary>Получает значение заголовка Upgrade для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Upgrade для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Vary">
      <summary>Получает значение заголовка Vary для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Vary для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Via">
      <summary>Получает значение заголовка Via для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Via для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Warning">
      <summary>Получает значение заголовка Warning для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка Warning для HTTP-отклика.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.WwwAuthenticate">
      <summary>Получает значение заголовка WWW-Authenticate для HTTP-отклика.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Значение заголовка WWW-Authenticate для HTTP-отклика.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeHeaderValue">
      <summary>Представляет тип носителя, используемый в заголовке типа содержимого согласно определению в стандарте RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <param name="source"> Объект <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />, который используется для инициализации нового экземпляра.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <param name="mediaType">Источник, представленный в виде строки для инициализации нового экземпляра. </param>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.CharSet">
      <summary>Получает или задает кодировку.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Набор символов.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.MediaType">
      <summary>Получает или задает значение заголовка типа носителя.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Значение заголовка типа мультимедиа.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.Parameters">
      <summary>Получает или задает параметры значения заголовка типа носителя.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.Параметры значений заголовка типа мультимедиа.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка типа мультимедиа.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка типа мультимедиа.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue">
      <summary>Представляет тип мультимедиа типа с дополнительными коэффициентом качества, используемый в заголовке типа содержимого.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <param name="mediaType">
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />, представленный в виде строки для инициализации нового экземпляра. </param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <param name="mediaType">
        <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />, представленный в виде строки для инициализации нового экземпляра.</param>
      <param name="quality">Качество, связанное с этим значением заголовка.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</returns>
      <param name="input">Строка, представляющая тип мультимедиа со сведениями о значении заголовка качества.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми типом мультимедиа со сведениями о значении заголовка качества.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Quality">
      <summary>Получает или задает значение качества для <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Double" />.Значение качества для объекта <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeWithQualityHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueHeaderValue">
      <summary>Представляет пару " имя-значение ", которое используется в различных заголовках согласно определению в стандарте RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.Net.Http.Headers.NameValueHeaderValue)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <param name="source">Объект <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />, который используется для инициализации нового экземпляра.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <param name="name">Имя заголовка.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <param name="name">Имя заголовка.</param>
      <param name="value">Значение заголовка.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Name">
      <summary>Получает имя заголовка.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Имя заголовка.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка значения имени.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка значения имени.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Value">
      <summary>Получает значение заголовка.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Значение заголовка.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue">
      <summary>Представляет пару " имя-значение " с параметрами, которые используется в различных заголовках согласно определению в стандарте RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.Net.Http.Headers.NameValueWithParametersHeaderValue)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <param name="source">Объект <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />, который используется для инициализации нового экземпляра.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <param name="name">Имя заголовка.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <param name="name">Имя заголовка.</param>
      <param name="value">Значение заголовка.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parameters">
      <summary>Получает параметры объекта <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.Коллекция, содержащая параметров.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</returns>
      <param name="input">Строка, представляющая значение имени со сведениями о значении заголовка параметров.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимым значением имени со сведениями о значении заголовка параметра.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueWithParametersHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ProductHeaderValue">
      <summary>Представляет значение токена продукта в заголовке агента пользователя.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <param name="name">Название продукта.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <param name="name">Значение имени продукта.</param>
      <param name="version">Значение Версии продукта.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Name">
      <summary>Получает имя токена продукта.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Имя токена продукта.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка продукта.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Version">
      <summary>Получает версию токена продукта.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Версия токена продукта. </returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductInfoHeaderValue">
      <summary>Представляет значение, которое может быть либо продуктом либо комментарием в заголовке User-Agent.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.Net.Http.Headers.ProductHeaderValue)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <param name="product">Объект <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />, который используется для инициализации нового экземпляра.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <param name="comment">Значение комментария.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <param name="productName">Значение имени продукта.</param>
      <param name="productVersion">Значение Версии продукта.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Comment">
      <summary>Получает комментарий из объекта <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Значение комментария — этот <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка информации.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка сведений о продукте.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Product">
      <summary>Получает продукт из объекта <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.Значение продукта из данного объекта <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductInfoHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeConditionHeaderValue">
      <summary>Представляет значение заголовка If-Range, которое может быть либо значением даты и времени, либо значением тега сущности.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <param name="date">Значение даты, используемое для инициализации нового экземпляра .</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.Net.Http.Headers.EntityTagHeaderValue)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <param name="entityTag">Объект <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />, который используется для инициализации нового экземпляра.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <param name="entityTag">Тег сущности, представленный в виде строки, используемой для инициализации нового экземпляра.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.Date">
      <summary>Получает Дата из объекта <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Дата из объекта <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.EntityTag">
      <summary>Получает Тег сущности объекта <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.Тег сущности объекта <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка условия диапазона.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка условия.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeConditionHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeHeaderValue">
      <summary>Представляет значение заголовка Range.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> диапазоном байтов.</summary>
      <param name="from">Место начала передачи данных.</param>
      <param name="to">Место остановки передачи данных.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="from" /> больше величины <paramref name="to" />– или – Значение <paramref name="from" /> или <paramref name="to" /> меньше 0. </exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка диапазона.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка диапазона.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Ranges">
      <summary>Получает диапазоны, указанные из объекта <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.Диапазоны из объекта <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Unit">
      <summary>Получает модуль из объекта <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Модуль из объекта <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeItemHeaderValue">
      <summary>Представляет диапазон байтов в значении заголовка Range.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />.</summary>
      <param name="from">Место начала передачи данных.</param>
      <param name="to">Место остановки передачи данных.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="from" /> больше величины <paramref name="to" />– или – Значение <paramref name="from" /> или <paramref name="to" /> меньше 0. </exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.From">
      <summary>Получает положение, с которого начнется отправка данных.</summary>
      <returns>Возвращает <see cref="T:System.Int64" />.Место начала передачи данных.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.To">
      <summary>Получает положение остановки передачи данных. </summary>
      <returns>Возвращает <see cref="T:System.Int64" />.Место остановки передачи данных. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RetryConditionHeaderValue">
      <summary>Представляет значение заголовка Retry-After, которое может быть либо значением даты и времени, либо значением интервала времени.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <param name="date">Смещение даты и времени, используемое для инициализации нового экземпляра класса .</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.TimeSpan)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <param name="delta">Разность в секундах, используемая для инициализации нового экземпляра.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Date">
      <summary>Получает смещение даты и времени от объекта <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Смещение даты и времени от объекта <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Delta">
      <summary>Получает Разность в секундах из объекта <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.TimeSpan" />.Разность в секундах из объекта <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка условия повтора.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка условия повтора.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RetryConditionHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.StringWithQualityHeaderValue">
      <summary>Представляет значение заголовка строки с необязательным качеством.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <param name="value">Строка, используемая для инициализации нового экземпляра.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <param name="value">Строка, используемая для инициализации нового экземпляра.</param>
      <param name="quality">Коэффициент качества, используемый для инициализации нового экземпляра .</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект текущему объекту <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
      <param name="input">Строка представляющая сведения о значении заголовка качества.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимой строкой со сведениями о значении заголовка качества.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Quality">
      <summary>получает коэффициент качества из объекта <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Double" />.Коэффициент качества из объекта <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.StringWithQualityHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Value">
      <summary>Получает значение строки из Объекта <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Значение строки из объекта <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingHeaderValue">
      <summary>Представляет значение заголовка Accept-Encoding.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.Net.Http.Headers.TransferCodingHeaderValue)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <param name="source">Объект <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />, который используется для инициализации нового экземпляра. </param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <param name="value">Строка, используемая для инициализации нового экземпляра.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект текущему объекту <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Parameters">
      <summary>Получает параметры кодирования передачи.</summary>
      <returns>Возвращает <see cref="T:System.Collections.Generic.ICollection`1" />.параметры кодирования передачи.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка кодирования передачи.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка кодирования передачи.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Value">
      <summary>Получает значение кодирования передачи.</summary>
      <returns>Возвращает <see cref="T:System.String" />.значение кодирования передачи.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue">
      <summary>Представляет значение заголовка Accept-Encoding с необязательным коэффициентом качества.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <param name="value">Строка, используемая для инициализации нового экземпляра.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <param name="value">Строка, используемая для инициализации нового экземпляра.</param>
      <param name="quality">Значение для коэффициента качества.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении кодирования передачи.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимой кодировкой переноса со сведениями о значении заголовка качества.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Quality">
      <summary>Получает Коэффициент качества из объекта <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Double" />.Коэффициент качества из объекта <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingWithQualityHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ViaHeaderValue">
      <summary>Представляет значение заголовка Via.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <param name="protocolVersion">Версия полученного протокола.</param>
      <param name="receivedBy">Узел и порт, которыми был получен запрос или отклик.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <param name="protocolVersion">Версия полученного протокола.</param>
      <param name="receivedBy">Узел и порт, которыми был получен запрос или отклик.</param>
      <param name="protocolName">Имя полученного протокола.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <param name="protocolVersion">Версия полученного протокола.</param>
      <param name="receivedBy">Узел и порт, которыми был получен запрос или отклик.</param>
      <param name="protocolName">Имя полученного протокола.</param>
      <param name="comment">Поле комментария, используемое для идентификации программного обеспечения принимающего прокси или шлюза.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.Comment">
      <summary>Получает поле комментария, используемое для идентификации программного обеспечения принимающего прокси или шлюза.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Поле комментария, используемое для идентификации программного обеспечения принимающего прокси или шлюза.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Возвращает хэш-код текущего объекта.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.Экземпляр <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</returns>
      <param name="input">Строка, представляющая сведения о значении заголовка Via.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка Via.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolName">
      <summary>Получает имя полученного протокола.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Имя протокола.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolVersion">
      <summary>Получает версию полученного протокола.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Версия протокола.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ReceivedBy">
      <summary>Получает Узел и порт, которыми был получен запрос или ответ.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Узел и порт, которыми был получен запрос или отклик.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.TryParse(System.String,System.Net.Http.Headers.ViaHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</param>
    </member>
    <member name="T:System.Net.Http.Headers.WarningHeaderValue">
      <summary>Представляет значение предупреждения, используемое заголовком предупреждения.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <param name="code">Конкретный код предупреждения.</param>
      <param name="agent">Основное приложение, которое присоединило предупреждение.</param>
      <param name="text">Строка в кавычках, содержащая текст предупреждения.</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String,System.DateTimeOffset)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <param name="code">Конкретный код предупреждения.</param>
      <param name="agent">Основное приложение, которое присоединило предупреждение.</param>
      <param name="text">Строка в кавычках, содержащая текст предупреждения.</param>
      <param name="date">Отметка даты и времени предупреждения.</param>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Agent">
      <summary>Получает узел, который присоединил предупреждение.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Основное приложение, которое присоединило предупреждение.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Code">
      <summary>Получает конкретный код предупреждения.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Конкретный код предупреждения.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Date">
      <summary>Получает отметку даты и времени предупреждения.</summary>
      <returns>Возвращает <see cref="T:System.DateTimeOffset" />.Отметка даты и времени предупреждения.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Equals(System.Object)">
      <summary>Определяет, равен ли заданный объект <see cref="T:System.Object" /> текущему объекту <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если заданный объект <see cref="T:System.Object" /> равен текущему объекту; в противном случае — значение false.</returns>
      <param name="obj">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.GetHashCode">
      <summary>Служит в качестве хэш-функции для объекта <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Int32" />.Хэш-код для текущего объекта.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Parse(System.String)">
      <summary>Преобразует строку в экземпляр <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <returns>Возвращает экземпляр объекта <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</returns>
      <param name="input">Строка представляющая сведения о значении заголовка аутентификации.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="input" /> имеет ссылку null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> не является допустимыми сведениями о значении заголовка аутентификации.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Text">
      <summary>Получает строку в кавычках, содержащую текст предупреждения.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка в кавычках, содержащая текст предупреждения.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.ToString">
      <summary>Возвращает строку, представляющую текущий объект <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.String" />.Строка, представляющая текущий объект.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.TryParse(System.String,System.Net.Http.Headers.WarningHeaderValue@)">
      <summary>Определяет, является ли строка допустимой информацией <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <returns>Возвращает <see cref="T:System.Boolean" />.Значение true, если параметр <paramref name="input" /> является допустимой информацией <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />; в противном случае — значение false.</returns>
      <param name="input">Строка, которую следует проверить.</param>
      <param name="parsedValue">Возвращает версию строки <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</param>
    </member>
  </members>
</doc>