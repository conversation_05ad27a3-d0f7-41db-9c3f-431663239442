﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Http</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.ByteArrayContent">
      <summary>Proporciona contenido HTTP basado en una matriz de bytes.</summary>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <param name="content">Contenido usado para inicializar <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="content" /> es null. </exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <param name="content">Contenido usado para inicializar <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <param name="offset">El desplazamiento, en bytes, del parámetro de <paramref name="content" /> usado para inicializar <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <param name="count">El número de bytes de <paramref name="content" /> a partir del parámetro de <paramref name="offset" /> usado para inicializar <see cref="T:System.Net.Http.ByteArrayContent" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="content" /> es null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El parámetro <paramref name="offset" /> es menor que cero.O bienEl parámetro <paramref name="offset" /> es mayor que la longitud del contenido que especifica el parámetro <paramref name="content" />.O bienEl parámetro <paramref name="count " /> es menor que cero.O bienEl valor del parámetro <paramref name="count" /> es mayor que la longitud del contenido que especifica <paramref name="content" /> menos el parámetro <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.CreateContentReadStreamAsync">
      <summary>Crea una secuencia de contenido HTTP como una operación asincrónica para lectura cuya memoria auxiliar es memoria procedente del objeto <see cref="T:System.Net.Http.ByteArrayContent" />.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialice y escriba la matriz de bytes proporcionada en el constructor en una secuencia de contenido HTTP como operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task" />. Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="stream">Secuencia de destino.</param>
      <param name="context">Información sobre el transporte, como el token de enlace de canal.Este parámetro puede ser null.</param>
    </member>
    <member name="M:System.Net.Http.ByteArrayContent.TryComputeLength(System.Int64@)">
      <summary>Determina si una matriz de bytes tiene una longitud válida en bytes.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="length" /> es un longitud válida; de lo contrario, false.</returns>
      <param name="length">Longitud en bytes de la matriz.</param>
    </member>
    <member name="T:System.Net.Http.ClientCertificateOption">
      <summary>Especifica cómo se proporcionan los certificados de cliente.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Automatic">
      <summary>
        <see cref="T:System.Net.Http.HttpClientHandler" /> intentará proporcionar automáticamente todos los certificados de cliente disponibles.</summary>
    </member>
    <member name="F:System.Net.Http.ClientCertificateOption.Manual">
      <summary>La aplicación proporciona manualmente los certificados de cliente a <see cref="T:System.Net.Http.WebRequestHandler" />.Este valor es el predeterminado.</summary>
    </member>
    <member name="T:System.Net.Http.DelegatingHandler">
      <summary>Un tipo para los controladores HTTP que delegan el procesamiento de los mensajes de respuesta HTTP a otro controlador, denominado controlador interno.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.DelegatingHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.DelegatingHandler" /> con un controlador interno concreto.</summary>
      <param name="innerHandler">Controlador interno que es responsable de procesar los mensajes de respuesta HTTP.</param>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que utiliza el objeto <see cref="T:System.Net.Http.DelegatingHandler" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar los recursos administrados y no administrados; es false para liberar sólo los recursos no administrados. </param>
    </member>
    <member name="P:System.Net.Http.DelegatingHandler.InnerHandler">
      <summary>Obtiene o establece el controlador interno que procesa los mensajes de respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.HttpMessageHandler" />.Controlador interno para los mensajes de respuesta HTTP.</returns>
    </member>
    <member name="M:System.Net.Http.DelegatingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Envía una solicitud HTTP al controlador interno para enviar al servidor como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />. Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="request">Mensaje de solicitud HTTP para enviar al servidor.</param>
      <param name="cancellationToken">Token de cancelación para cancelar la operación.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="request" /> era null.</exception>
    </member>
    <member name="T:System.Net.Http.FormUrlEncodedContent">
      <summary>Contenedor para las tuplas de nombre-valor codificadas mediante el tipo MIME de application/x-www-form-urlencoded.</summary>
    </member>
    <member name="M:System.Net.Http.FormUrlEncodedContent.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.FormUrlEncodedContent" /> con una colección específica de pares de nombre y valor.</summary>
      <param name="nameValueCollection">Colección de pares de nombre-valor.</param>
    </member>
    <member name="T:System.Net.Http.HttpClient">
      <summary>Proporciona una clase base para enviar solicitudes HTTP y recibir respuestas HTTP de un recurso identificado por un URI. </summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpClient" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpClient" /> con un controlador específico.</summary>
      <param name="handler">Pila de controlador HTTP que se va a usar para enviar solicitudes. </param>
    </member>
    <member name="M:System.Net.Http.HttpClient.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpClient" /> con un controlador específico.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" /> responsable de procesar los mensajes de respuesta HTTP.</param>
      <param name="disposeHandler">Es true si Dispose() debe desechar el controlador interno; es false si piensa reutilizar el controlador interno.</param>
    </member>
    <member name="P:System.Net.Http.HttpClient.BaseAddress">
      <summary>Obtiene o establece la dirección base de Identificador uniforme de recursos (URI) del recurso de Internet utilizado cuando se envían solicitudes.</summary>
      <returns>Devuelve <see cref="T:System.Uri" />.La dirección base de Identificador uniforme de recursos (URI) del recurso de Internet utilizado cuando se envían solicitudes.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.CancelPendingRequests">
      <summary>Cancela todas las solicitudes pendientes en esta instancia.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClient.DefaultRequestHeaders">
      <summary>Obtiene los encabezados que se deben enviar con cada solicitud.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />.Encabezados que se deben enviar con cada solicitud.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String)">
      <summary>Envía una solicitud DELETE al URI especificado como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
      <exception cref="T:System.InvalidOperationException">Ya se ha enviado el mensaje de solicitud a la <see cref="T:System.Net.Http.HttpClient" /> instancia.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.String,System.Threading.CancellationToken)">
      <summary>Envía una solicitud DELETE al URI especificado con un token de cancelación como operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
      <exception cref="T:System.InvalidOperationException">Ya se ha enviado el mensaje de solicitud a la <see cref="T:System.Net.Http.HttpClient" /> instancia.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri)">
      <summary>Envía una solicitud DELETE al URI especificado como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
      <exception cref="T:System.InvalidOperationException">Ya se ha enviado el mensaje de solicitud a la <see cref="T:System.Net.Http.HttpClient" /> instancia.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.DeleteAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>Envía una solicitud DELETE al URI especificado con un token de cancelación como operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
      <exception cref="T:System.InvalidOperationException">Ya se ha enviado el mensaje de solicitud a la <see cref="T:System.Net.Http.HttpClient" /> instancia.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.Http.HttpClient" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar tanto recursos administrados como no administrados; es false para liberar únicamente recursos no administrados.</param>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String)">
      <summary>Envía una solicitud GET al URI especificado como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption)">
      <summary>Envía una solicitud GET al URI especificado con una opción de finalización de HTTP como operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="completionOption">Valor de opción de finalización de HTTP que indica cuándo se debe considerar completada la operación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Envía una solicitud GET al URI especificado con una opción de finalización de HTTP y un token de cancelación como operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="completionOption">Valor de opción de finalización de HTTP que indica cuándo se debe considerar completada la operación.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.String,System.Threading.CancellationToken)">
      <summary>Envía una solicitud GET al URI especificado con un token de cancelación como operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri)">
      <summary>Envía una solicitud GET al URI especificado como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption)">
      <summary>Envía una solicitud GET al URI especificado con una opción de finalización de HTTP como operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="completionOption">Valor de opción de finalización de HTTP que indica cuándo se debe considerar completada la operación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Envía una solicitud GET al URI especificado con una opción de finalización de HTTP y un token de cancelación como operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="completionOption">Valor de opción de finalización de HTTP que indica cuándo se debe considerar completada la operación.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetAsync(System.Uri,System.Threading.CancellationToken)">
      <summary>Envía una solicitud GET al URI especificado con un token de cancelación como operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.String)">
      <summary>Envía una solicitud GET al URI especificado y devuelve el cuerpo de la respuesta como una matriz de bytes en una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetByteArrayAsync(System.Uri)">
      <summary>Envía una solicitud GET al URI especificado y devuelve el cuerpo de la respuesta como una matriz de bytes en una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.String)">
      <summary>Envía una solicitud GET al URI especificado y devuelve el cuerpo de la respuesta como una secuencia en una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStreamAsync(System.Uri)">
      <summary>Envía una solicitud GET al URI especificado y devuelve el cuerpo de la respuesta como una secuencia en una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.String)">
      <summary>Envía una solicitud GET al URI especificado y devuelve el cuerpo de la respuesta como una cadena en una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.GetStringAsync(System.Uri)">
      <summary>Envía una solicitud GET al URI especificado y devuelve el cuerpo de la respuesta como una cadena en una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.MaxResponseContentBufferSize">
      <summary>Obtiene o establece el número máximo de bytes que se van a almacenar en búfer al leer el contenido de la respuesta.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Número máximo de bytes que se van a almacenar en búfer al leer el contenido de la respuesta.El valor predeterminado de esta propiedad es 2 gigabytes.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">El tamaño especificado es menor o igual que cero.</exception>
      <exception cref="T:System.InvalidOperationException">Una operación se ha iniciado en la instancia actual. </exception>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual. </exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Envía una solicitud POST al URI especificado como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="content">Contenido de la solicitud HTTP que se envía al servidor.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Envía una solicitud POST con un token de cancelación como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="content">Contenido de la solicitud HTTP que se envía al servidor.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Envía una solicitud POST al URI especificado como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="content">Contenido de la solicitud HTTP que se envía al servidor.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PostAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Envía una solicitud POST con un token de cancelación como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="content">Contenido de la solicitud HTTP que se envía al servidor.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent)">
      <summary>Envía una solicitud PUT al URI especificado como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="content">Contenido de la solicitud HTTP que se envía al servidor.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.String,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Envía una solicitud PUT con un token de cancelación como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="content">Contenido de la solicitud HTTP que se envía al servidor.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent)">
      <summary>Envía una solicitud PUT al URI especificado como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="content">Contenido de la solicitud HTTP que se envía al servidor.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.PutAsync(System.Uri,System.Net.Http.HttpContent,System.Threading.CancellationToken)">
      <summary>Envía una solicitud PUT con un token de cancelación como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="requestUri">URI al que se envía la solicitud.</param>
      <param name="content">Contenido de la solicitud HTTP que se envía al servidor.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="requestUri" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage)">
      <summary>Envía una solicitud HTTP como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="request">Mensaje de la solicitud HTTP que se va a enviar.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="request" /> era null.</exception>
      <exception cref="T:System.InvalidOperationException">Ya se ha enviado el mensaje de solicitud a la <see cref="T:System.Net.Http.HttpClient" /> instancia.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption)">
      <summary>Envía una solicitud HTTP como una operación asincrónica. </summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="request">Mensaje de la solicitud HTTP que se va a enviar.</param>
      <param name="completionOption">Cuándo se debe completar la operación (tan pronto como haya una respuesta disponible o después de leer todo el contenido de la respuesta).</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="request" /> era null.</exception>
      <exception cref="T:System.InvalidOperationException">Ya se ha enviado el mensaje de solicitud a la <see cref="T:System.Net.Http.HttpClient" /> instancia.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Net.Http.HttpCompletionOption,System.Threading.CancellationToken)">
      <summary>Envía una solicitud HTTP como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="request">Mensaje de la solicitud HTTP que se va a enviar.</param>
      <param name="completionOption">Cuándo se debe completar la operación (tan pronto como haya una respuesta disponible o después de leer todo el contenido de la respuesta).</param>
      <param name="cancellationToken">Token de cancelación para cancelar la operación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="request" /> era null.</exception>
      <exception cref="T:System.InvalidOperationException">Ya se ha enviado el mensaje de solicitud a la <see cref="T:System.Net.Http.HttpClient" /> instancia.</exception>
    </member>
    <member name="M:System.Net.Http.HttpClient.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Envía una solicitud HTTP como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="request">Mensaje de la solicitud HTTP que se va a enviar.</param>
      <param name="cancellationToken">Token de cancelación para cancelar la operación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="request" /> era null.</exception>
      <exception cref="T:System.InvalidOperationException">Ya se ha enviado el mensaje de solicitud a la <see cref="T:System.Net.Http.HttpClient" /> instancia.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClient.Timeout">
      <summary>Obtiene o establece el tiempo de espera hasta que se agota el tiempo de espera de la solicitud.</summary>
      <returns>Devuelve <see cref="T:System.TimeSpan" />.El tiempo de espera hasta que se agota el tiempo de espera de la solicitud.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">El tiempo de espera especificado es menor o igual que cero y no es <see cref="F:System.Threading.Timeout.InfiniteTimeSpan" />.</exception>
      <exception cref="T:System.InvalidOperationException">Una operación se ha iniciado en la instancia actual. </exception>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual.</exception>
    </member>
    <member name="T:System.Net.Http.HttpClientHandler">
      <summary>Controlador de mensajes predeterminado usado por <see cref="T:System.Net.Http.HttpClient" />.  </summary>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.#ctor">
      <summary>Cree una instancia de una clase <see cref="T:System.Net.Http.HttpClientHandler" />.</summary>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect">
      <summary>Obtiene o establece un valor que indica si el controlador debe seguir las respuestas de redirección.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.true si el controlador debe seguir las respuestas de redirección; si no false.El valor predeterminado es true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.AutomaticDecompression">
      <summary>Obtiene o establece el tipo de método de descompresión utilizado por el controlador para la descompresión automática de la respuesta de contenido HTTP.</summary>
      <returns>Devuelva <see cref="T:System.Net.DecompressionMethods" />.El método de descompresión automática utilizado por el controlador.El valor predeterminado es <see cref="F:System.Net.DecompressionMethods.None" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.ClientCertificateOptions">
      <summary>Obtiene o establece la colección de certificados de seguridad asociados a este controlador.</summary>
      <returns>Devuelva <see cref="T:System.Net.Http.ClientCertificateOption" />.Colección de certificados de seguridad asociados a este controlador.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.CookieContainer">
      <summary>Obtiene o establece el contenedor de cookies que el controlador usa para almacenar cookies del servidor.</summary>
      <returns>Devuelva <see cref="T:System.Net.CookieContainer" />.Contenedor de cookies que el controlador usa para almacenar cookies del servidor.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Credentials">
      <summary>Obtiene o establece la información de autenticación utilizada por este controlador.</summary>
      <returns>Devuelva <see cref="T:System.Net.ICredentials" />.Credenciales de autenticación asociadas con el controlador.El valor predeterminado es null.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.Http.HttpClientHandler" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar los recursos administrados y no administrados; es false para liberar sólo los recursos no administrados.</param>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections">
      <summary>Obtiene o establece el número máximo de redirecciones que seguirá el controlador.</summary>
      <returns>Devuelva <see cref="T:System.Int32" />.El número máximo de respuestas de redirección que seguirá el controlador.El valor predeterminado es 50.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.MaxRequestContentBufferSize">
      <summary>Obtiene o establece el tamaño máximo del búfer de contenido de solicitud usado por el controlador.</summary>
      <returns>Devuelva <see cref="T:System.Int32" />.Tamaño máximo de búfer de contenido de la solicitud en bytes.El valor predeterminado es 2 gigabytes.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.PreAuthenticate">
      <summary>Obtiene o establece un valor que indica si el controlador envía un encabezado de autorización con la solicitud.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.Es true para que el controlador envíe un encabezado de autorización HTTP con las solicitudes después de que tenga lugar la autenticación; de lo contrario, es false.El valor predeterminado es false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.Proxy">
      <summary>Obtiene o establece la información de proxy que usa el controlador.</summary>
      <returns>Devuelva <see cref="T:System.Net.IWebProxy" />.La información de proxy que usará el controlador.El valor predeterminado es null.</returns>
    </member>
    <member name="M:System.Net.Http.HttpClientHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Crea una instancia de <see cref="T:System.Net.Http.HttpResponseMessage" /> basándose en la información proporcionada en el objeto <see cref="T:System.Net.Http.HttpRequestMessage" /> como una operación que no se bloqueará.</summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="request">Mensaje de la solicitud HTTP.</param>
      <param name="cancellationToken">Token de cancelación para cancelar la operación.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="request" /> era null.</exception>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsAutomaticDecompression">
      <summary>Obtiene un valor que indica si el controlador admite la descompresión automática del contenido de la respuesta.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.true si el controlador admite la descompresión automática del contenido de la respuesta; si no false.El valor predeterminado es true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsProxy">
      <summary>Obtiene un valor que indica si el controlador admite valores de proxy.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.true si el controlador admite valores de proxy; si no false.El valor predeterminado es true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.SupportsRedirectConfiguration">
      <summary>Obtiene un valor que indica si el controlador admite opciones de configuración para las propiedades <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> y <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" />.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.true si el controlador admite las opciones de configuración para las propiedades <see cref="P:System.Net.Http.HttpClientHandler.AllowAutoRedirect" /> y <see cref="P:System.Net.Http.HttpClientHandler.MaxAutomaticRedirections" />; si no false.El valor predeterminado es true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseCookies">
      <summary>Obtiene o establece un valor que indica si el controlador utiliza la propiedad <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> para almacenar las cookies de servidor y utiliza estas cookies al enviar solicitudes.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.true si el controlador admite el uso de la propiedad <see cref="P:System.Net.Http.HttpClientHandler.CookieContainer" /> para almacenar las cookies de servidor y usa estas cookies al enviar solicitudes; si no false.El valor predeterminado es true.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseDefaultCredentials">
      <summary>Obtiene o establece un valor que controla si se envían las credenciales predeterminadas con las solicitudes del controlador.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.Es true si se utilizan las credenciales predeterminadas; en cualquier otro caso, es false.El valor predeterminado es false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpClientHandler.UseProxy">
      <summary>Obtiene o establece un valor que indica si el controlador usa un proxy para las solicitudes. </summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.true si el controlador debe usar un servidor proxy para las solicitudes; si no false.El valor predeterminado es true.</returns>
    </member>
    <member name="T:System.Net.Http.HttpCompletionOption">
      <summary>Indica si las operaciones de <see cref="T:System.Net.Http.HttpClient" /> se consideran completadas cualquiera tan pronto como una respuesta esté disponible o después de leer el mensaje de respuesta completo, incluido el contenido. </summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseContentRead">
      <summary>La operación debe completarse después de leer toda la respuesta incluido el contenido.</summary>
    </member>
    <member name="F:System.Net.Http.HttpCompletionOption.ResponseHeadersRead">
      <summary>La operación debe completarse tan pronto como haya una respuesta disponible y se lean los encabezados.El contenido no se ha leído aún.</summary>
    </member>
    <member name="T:System.Net.Http.HttpContent">
      <summary>Una clase base que representa un cuerpo de entidad y encabezados de contenido HTTP.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream)">
      <summary>Serialice el contenido HTTP en una secuencia de bytes y de copias en el objeto de secuencia proporcionado como el parámetro de <paramref name="stream" />.</summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="stream">Secuencia de destino.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CopyToAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialice el contenido HTTP en una secuencia de bytes y de copias en el objeto de secuencia proporcionado como el parámetro de <paramref name="stream" />.</summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="stream">Secuencia de destino.</param>
      <param name="context">Información sobre el transporte (token de enlace de canal, por ejemplo).Este parámetro puede ser null.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.CreateContentReadStreamAsync">
      <summary>Serializa el contenido HTTP en una secuencia de memoria como una operación asincrónica.</summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose">
      <summary>Libera los recursos no administrados y desecha los recursos administrados que usa <see cref="T:System.Net.Http.HttpContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpContent.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.Http.HttpContent" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar los recursos administrados y no administrados; es false para liberar sólo los recursos no administrados.</param>
    </member>
    <member name="P:System.Net.Http.HttpContent.Headers">
      <summary>Obtiene los encabezados de contenido HTTP tal como se define en RFC 2616.</summary>
      <returns>Devuelva <see cref="T:System.Net.Http.Headers.HttpContentHeaders" />.Encabezados de contenido como se define en RFC 2616.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync">
      <summary>Serializa el contenido HTTP en un búfer de memoria como una operación asincrónica.</summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task" />.Objeto de tarea que representa la operación asincrónica.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.LoadIntoBufferAsync(System.Int64)">
      <summary>Serializa el contenido HTTP en un búfer de memoria como una operación asincrónica.</summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="maxBufferSize">El tamaño máximo, en bytes, del búfer que se va a utilizar.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsByteArrayAsync">
      <summary>Serializa el contenido HTTP en una matriz de bytes como una operación asincrónica.</summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStreamAsync">
      <summary>Serializar el contenido HTTP y devolver una secuencia que representa el contenido como una operación asincrónica. </summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.ReadAsStringAsync">
      <summary>Serialice el contenido HTTP en una cadena como una operación asincrónica.</summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
    </member>
    <member name="M:System.Net.Http.HttpContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialice el contenido HTTP en una secuencia como una operación asincrónica.</summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="stream">Secuencia de destino.</param>
      <param name="context">Información sobre el transporte (token de enlace de canal, por ejemplo).Este parámetro puede ser null.</param>
    </member>
    <member name="M:System.Net.Http.HttpContent.TryComputeLength(System.Int64@)">
      <summary>Determina si el contenido HTTP tiene una longitud válida en bytes.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.true si <paramref name="length" /> es un longitud válida; de lo contrario, false.</returns>
      <param name="length">Longitud en bites del contenido HTTP.</param>
    </member>
    <member name="T:System.Net.Http.HttpMessageHandler">
      <summary>Tipo base para los controladores de mensajes HTTP.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose">
      <summary>Libera los recursos no administrados y desecha los recursos administrados que usa <see cref="T:System.Net.Http.HttpMessageHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.Http.HttpMessageHandler" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar los recursos administrados y no administrados; es false para liberar sólo los recursos no administrados.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Envía una solicitud HTTP como una operación asincrónica.</summary>
      <returns>Devuelva <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="request">Mensaje de la solicitud HTTP que se va a enviar.</param>
      <param name="cancellationToken">Token de cancelación para cancelar la operación.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="request" /> era null.</exception>
    </member>
    <member name="T:System.Net.Http.HttpMessageInvoker">
      <summary>Una clase especializada que permite que las aplicaciones llamen al método <see cref="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)" /> en una cadena de controlador HTTP. </summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Inicializa una instancia de una clase <see cref="T:System.Net.Http.HttpMessageInvoker" /> con un <see cref="T:System.Net.Http.HttpMessageHandler" /> específico.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" /> responsable de procesar los mensajes de respuesta HTTP.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean)">
      <summary>Inicializa una instancia de una clase <see cref="T:System.Net.Http.HttpMessageInvoker" /> con un <see cref="T:System.Net.Http.HttpMessageHandler" /> específico.</summary>
      <param name="handler">
        <see cref="T:System.Net.Http.HttpMessageHandler" /> responsable de procesar los mensajes de respuesta HTTP.</param>
      <param name="disposeHandler">true si Dispose() debe desechar el controlador interno,false si piensa reutilizar el controlador interno.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose">
      <summary>Libera los recursos no administrados y desecha los recursos administrados que usa <see cref="T:System.Net.Http.HttpMessageInvoker" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.Http.HttpMessageInvoker" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar los recursos administrados y no administrados; es false para liberar sólo los recursos no administrados.</param>
    </member>
    <member name="M:System.Net.Http.HttpMessageInvoker.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Envía una solicitud HTTP como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="request">Mensaje de la solicitud HTTP que se va a enviar.</param>
      <param name="cancellationToken">Token de cancelación para cancelar la operación.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="request" /> era null.</exception>
    </member>
    <member name="T:System.Net.Http.HttpMethod">
      <summary>Una clase auxiliar para recuperar y comparar métodos HTTP estándar y para crear nuevos métodos HTTP.</summary>
    </member>
    <member name="M:System.Net.Http.HttpMethod.#ctor(System.String)">
      <summary>Inicializa una instancia nueva de la clase <see cref="T:System.Net.Http.HttpMethod" /> con un método HTTP específico.</summary>
      <param name="method">Método HTTP.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Delete">
      <summary>Representa un método de protocolo HTTP DELETE.</summary>
      <returns>Devuelva <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Net.Http.HttpMethod)">
      <summary>Determina si la estructura <see cref="T:System.Net.Http.HttpMethod" /> especificada es igual que la estructura <see cref="T:System.Object" /> actual.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.true si el objeto especificado es igual al objeto actual; de lo contrario, false.</returns>
      <param name="other">Método HTTP que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.Equals(System.Object)">
      <summary>Determina si la estructura <see cref="T:System.Object" /> especificada es igual que la estructura <see cref="T:System.Object" /> actual.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.true si el objeto especificado es igual al objeto actual; de lo contrario, false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Get">
      <summary>Representa un método de protocolo HTTP GET.</summary>
      <returns>Devuelva <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.GetHashCode">
      <summary>Actúa como función hash para este tipo.</summary>
      <returns>Devuelva <see cref="T:System.Int32" />.Código hash para el objeto <see cref="T:System.Object" /> actual.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Head">
      <summary>Representa un método de protocolo HTTP HEAD.El método HEAD es idéntico a GET, excepto que el servidor sólo devuelve los encabezados de mensaje en la respuesta, sin el cuerpo del mensaje.</summary>
      <returns>Devuelva <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Method">
      <summary>Un método HTTP. </summary>
      <returns>Devuelva <see cref="T:System.String" />.Un método HTTP representado como <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Equality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>Operador de igualdad para comparar dos objetos <see cref="T:System.Net.Http.HttpMethod" />.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.Es true si los parámetros <paramref name="left" /> y <paramref name="right" /> especificados son iguales; en caso contrario, es false.</returns>
      <param name="left">
        <see cref="T:System.Net.Http.HttpMethod" /> izquierdo para un operador de igualdad.</param>
      <param name="right">
        <see cref="T:System.Net.Http.HttpMethod" /> derecho para un operador de igualdad.</param>
    </member>
    <member name="M:System.Net.Http.HttpMethod.op_Inequality(System.Net.Http.HttpMethod,System.Net.Http.HttpMethod)">
      <summary>Operador de desigualdad para comparar dos objetos <see cref="T:System.Net.Http.HttpMethod" />.</summary>
      <returns>Devuelva <see cref="T:System.Boolean" />.Es true si los parámetros <paramref name="left" /> y <paramref name="right" /> especificados no son iguales; en caso contrario, es false.</returns>
      <param name="left">
        <see cref="T:System.Net.Http.HttpMethod" /> izquierdo para un operador de desigualdad.</param>
      <param name="right">
        <see cref="T:System.Net.Http.HttpMethod" /> derecho para un operador de desigualdad.</param>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Options">
      <summary>Representa un método de protocolo HTTP OPTIONS.</summary>
      <returns>Devuelva <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Post">
      <summary>Representa un método de protocolo POST HTTP, utilizado para enviar una entidad nueva como adición a un identificador URI.</summary>
      <returns>Devuelva <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Put">
      <summary>Representa un método de protocolo HTTP PUT, utilizado para reemplazar una entidad identificada por un identificador URI.</summary>
      <returns>Devuelva <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="M:System.Net.Http.HttpMethod.ToString">
      <summary>Devuelve una cadena que representa el objeto actual.</summary>
      <returns>Devuelva <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.HttpMethod.Trace">
      <summary>Representa un método de protocolo HTTP TRACE.</summary>
      <returns>Devuelva <see cref="T:System.Net.Http.HttpMethod" />.</returns>
    </member>
    <member name="T:System.Net.Http.HttpRequestException">
      <summary>Una clase base para las excepciones que producen las clases <see cref="T:System.Net.Http.HttpClient" /> y <see cref="T:System.Net.Http.HttpMessageHandler" /> .</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpRequestException" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpRequestException" /> con un mensaje específico que describe la excepción actual.</summary>
      <param name="message">Mensaje que describe la excepción actual.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpRequestException" /> con un mensaje específico que describe la excepción actual y una excepción interna.</summary>
      <param name="message">Mensaje que describe la excepción actual.</param>
      <param name="inner">Excepción interna.</param>
    </member>
    <member name="T:System.Net.Http.HttpRequestMessage">
      <summary>Representa un mensaje de solicitud HTTP.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpRequestMessage" /> con un método HTTP y una solicitud <see cref="T:System.Uri" />.</summary>
      <param name="method">Método HTTP.</param>
      <param name="requestUri">Cadena que representa el objeto <see cref="T:System.Uri" /> de solicitud.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.#ctor(System.Net.Http.HttpMethod,System.Uri)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpRequestMessage" /> con un método HTTP y una solicitud <see cref="T:System.Uri" />.</summary>
      <param name="method">Método HTTP.</param>
      <param name="requestUri">Objeto <see cref="T:System.Uri" /> que se va a solicitar.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Content">
      <summary>Obtiene o establece el contenido del mensaje HTTP. </summary>
      <returns>Devuelve <see cref="T:System.Net.Http.HttpContent" />.Contenido de un mensaje.</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose">
      <summary>Libera los recursos no administrados y desecha los recursos administrados que usa <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.Http.HttpRequestMessage" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar los recursos administrados y no administrados; es false para liberar sólo los recursos no administrados.</param>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Headers">
      <summary>Obtiene la colección de encabezados de la solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpRequestHeaders" />.Colección de encabezados de la solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Method">
      <summary>Obtiene o establece el método HTTP usado por el mensaje de solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.HttpMethod" />.Método HTTP usado por el mensaje de solicitud.El valor predeterminado es el método GET.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Properties">
      <summary>Obtiene un conjunto de propiedades de la solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.RequestUri">
      <summary>Obtiene o establece el <see cref="T:System.Uri" /> usado para la solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Uri" />.<see cref="T:System.Uri" /> que se usa para la solicitud HTTP.</returns>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessage.ToString">
      <summary>Devuelve una cadena que representa el objeto actual.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Representación en forma de cadena del objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.HttpRequestMessage.Version">
      <summary>Obtiene o establece la versión de mensaje HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Version" />.Versión de mensaje HTTP.El valor predeterminado es 1.1.</returns>
    </member>
    <member name="T:System.Net.Http.HttpResponseMessage">
      <summary>Representa un mensaje de respuesta HTTP incluido el código de estado y los datos.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.#ctor(System.Net.HttpStatusCode)">
      <summary>Inicializa una instancia nueva de la clase <see cref="T:System.Net.Http.HttpResponseMessage" /> con un <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> específico.</summary>
      <param name="statusCode">Código de estado de la respuesta HTTP.</param>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Content">
      <summary>Obtiene o establece el contenido de texto de un mensaje de respuesta HTTP. </summary>
      <returns>Devuelve <see cref="T:System.Net.Http.HttpContent" />.Contenido del mensaje de respuesta HTTP.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose">
      <summary>Libera los recursos no administrados y desecha los recursos no administrados que usa <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.Http.HttpResponseMessage" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar los recursos administrados y no administrados; es false para liberar sólo los recursos no administrados.</param>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.EnsureSuccessStatusCode">
      <summary>Se produce una excepción si la propiedad <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode" /> para la respuesta HTTP es false.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.HttpResponseMessage" />.Mensaje de respuesta HTTP si la llamada es correcta.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Headers">
      <summary>Obtiene la colección de encabezados de respuesta HTTP. </summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpResponseHeaders" />.Colección de encabezados de respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode">
      <summary>Obtiene un valor que indica si la respuesta HTTP se realizó correctamente.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.Un valor que indica si la respuesta HTTP se realizó correctamente.true si <see cref="P:System.Net.Http.HttpResponseMessage.StatusCode" /> estaba en el intervalo 200-299; si no false.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.ReasonPhrase">
      <summary>Obtiene o establece la frase de razón que envían normalmente los servidores junto con el código de estado. </summary>
      <returns>Devuelve <see cref="T:System.String" />.La frase de la razón enviada por el servidor.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.RequestMessage">
      <summary>Obtiene o establece el mensaje de solicitud que condujo a este mensaje de respuesta.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.HttpRequestMessage" />.El mensaje de solicitud que condujo a este mensaje de respuesta.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.StatusCode">
      <summary>Obtiene o establece el código de estado de la respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.HttpStatusCode" />.Código de estado de la respuesta HTTP.</returns>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessage.ToString">
      <summary>Devuelve una cadena que representa el objeto actual.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Representación en forma de cadena del objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.HttpResponseMessage.Version">
      <summary>Obtiene o establece la versión de mensaje HTTP. </summary>
      <returns>Devuelve <see cref="T:System.Version" />.Versión de mensaje HTTP.El valor predeterminado es 1.1.</returns>
    </member>
    <member name="T:System.Net.Http.MessageProcessingHandler">
      <summary>Un tipo base para los controladores que solo realizan un pequeño procesamiento de mensajes de solicitud y/o de respuesta.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor">
      <summary>Cree una instancia de una clase <see cref="T:System.Net.Http.MessageProcessingHandler" />.</summary>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary>Crea una instancia de una clase <see cref="T:System.Net.Http.MessageProcessingHandler" /> con un controlador interno concreto.</summary>
      <param name="innerHandler">Controlador interno que es responsable de procesar los mensajes de respuesta HTTP.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessRequest(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Realiza el procesamiento en cada solicitud enviada al servidor.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.HttpRequestMessage" />.Mensaje de solicitud HTTP que se procesó.</returns>
      <param name="request">Mensaje de la solicitud HTTP que se va a procesar.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.ProcessResponse(System.Net.Http.HttpResponseMessage,System.Threading.CancellationToken)">
      <summary>Realiza el procesamiento en cada respuesta del servidor.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.HttpResponseMessage" />.Mensaje de respuesta HTTP que se procesó.</returns>
      <param name="response">Mensaje de respuesta HTTP que se va a procesar.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
    </member>
    <member name="M:System.Net.Http.MessageProcessingHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Envía una solicitud HTTP al controlador interno para enviar al servidor como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="request">Mensaje de solicitud HTTP para enviar al servidor.</param>
      <param name="cancellationToken">Token de cancelación que pueden usar otros objetos o subprocesos para recibir el aviso de cancelación.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="request" /> era null.</exception>
    </member>
    <member name="T:System.Net.Http.MultipartContent">
      <summary>Proporciona una colección de objetos <see cref="T:System.Net.Http.HttpContent" /> que se serializan mediante la especificación de tipo de contenido multipart/*.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.MultipartContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.MultipartContent" />.</summary>
      <param name="subtype">Subtipo del contenido con varias partes.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="subtype" /> era null o solo contiene caracteres de espacios en blanco.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.#ctor(System.String,System.String)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.MultipartContent" />.</summary>
      <param name="subtype">Subtipo del contenido con varias partes.</param>
      <param name="boundary">La cadena delimitadora para el contenido con varias partes.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="subtype" /> era null o una cadena vacía.<paramref name="boundary" /> era null o solo contiene caracteres de espacios en blanco.O bien<paramref name="boundary" /> termina con un carácter de espacio.</exception>
      <exception cref="T:System.OutOfRangeException">La longitud de <paramref name="boundary" /> fue mayor que 70.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Add(System.Net.Http.HttpContent)">
      <summary>Agregue contenido con varias partes HTTP a una colección de objetos <see cref="T:System.Net.Http.HttpContent" /> que se serializan mediante la especificación de tipo de contenido multipart/*.</summary>
      <param name="content">Contenido HTTP que se agregará a la colección.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="content" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartContent.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.Http.MultipartContent" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar los recursos administrados y no administrados; es false para liberar sólo los recursos no administrados.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección de objetos <see cref="T:System.Net.Http.HttpContent" /> que se serializan mediante la especificación de tipo de contenido multipart/*.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.IEnumerator`1" />.Objeto que puede usarse para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialice el contenido HTTP con varias partes en una secuencia como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="stream">Secuencia de destino.</param>
      <param name="context">Información sobre el transporte (token de enlace de canal, por ejemplo).Este parámetro puede ser null.</param>
    </member>
    <member name="M:System.Net.Http.MultipartContent.System#Collections#IEnumerable#GetEnumerator">
      <summary>Implementación explícita del método <see cref="M:System.Net.Http.MultipartContent.GetEnumerator" />.</summary>
      <returns>Devuelve <see cref="T:System.Collections.IEnumerator" />.Objeto que puede usarse para recorrer en iteración la colección.</returns>
    </member>
    <member name="M:System.Net.Http.MultipartContent.TryComputeLength(System.Int64@)">
      <summary>Determina si el contenido HTTP con varias partes tiene una longitud válida en bytes.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="length" /> es un longitud válida; de lo contrario, false.</returns>
      <param name="length">Longitud en bites del contenido HTTP.</param>
    </member>
    <member name="T:System.Net.Http.MultipartFormDataContent">
      <summary>Proporciona un contenedor para contenido codificado mediante el tipo MIME multipart/form-data.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.MultipartFormDataContent" />.</summary>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.#ctor(System.String)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.MultipartFormDataContent" />.</summary>
      <param name="boundary">La cadena delimitadora para el contenido de los datos de formulario con varias partes.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="boundary" /> era null o solo contiene caracteres de espacios en blanco.O bien<paramref name="boundary" /> termina con un carácter de espacio.</exception>
      <exception cref="T:System.OutOfRangeException">La longitud de <paramref name="boundary" /> fue mayor que 70.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent)">
      <summary>Agrega contenido HTTP a una colección de objetos <see cref="T:System.Net.Http.HttpContent" /> que se serializan al tipo MIME multipart/form-data.</summary>
      <param name="content">Contenido HTTP que se agregará a la colección.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="content" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String)">
      <summary>Agrega contenido HTTP a una colección de objetos <see cref="T:System.Net.Http.HttpContent" /> que se serializan al tipo MIME multipart/form-data.</summary>
      <param name="content">Contenido HTTP que se agregará a la colección.</param>
      <param name="name">Nombre para el contenido HTTP que se va a agregar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> era null o solo contiene caracteres de espacios en blanco.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="content" /> era null.</exception>
    </member>
    <member name="M:System.Net.Http.MultipartFormDataContent.Add(System.Net.Http.HttpContent,System.String,System.String)">
      <summary>Agrega contenido HTTP a una colección de objetos <see cref="T:System.Net.Http.HttpContent" /> que se serializan al tipo MIME multipart/form-data.</summary>
      <param name="content">Contenido HTTP que se agregará a la colección.</param>
      <param name="name">Nombre para el contenido HTTP que se va a agregar.</param>
      <param name="fileName">Nombre del archivo para el contenido HTTP se va a agregar a la colección.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> era null o solo contiene caracteres de espacios en blanco.O bien<paramref name="fileName" /> era null o solo contiene caracteres de espacios en blanco.</exception>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="content" /> era null.</exception>
    </member>
    <member name="T:System.Net.Http.StreamContent">
      <summary>Proporciona contenido HTTP basado en una secuencia.</summary>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.StreamContent" />.</summary>
      <param name="content">Contenido usado para inicializar <see cref="T:System.Net.Http.StreamContent" />.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.#ctor(System.IO.Stream,System.Int32)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.StreamContent" />.</summary>
      <param name="content">Contenido usado para inicializar <see cref="T:System.Net.Http.StreamContent" />.</param>
      <param name="bufferSize">Tamaño del búfer, en bytes, para <see cref="T:System.Net.Http.StreamContent" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="content" /> era null.</exception>
      <exception cref="T:System.OutOfRangeException">
        <paramref name="bufferSize" /> era menor o igual que cero. </exception>
    </member>
    <member name="M:System.Net.Http.StreamContent.CreateContentReadStreamAsync">
      <summary>Escribe el contenido de una secuencia HTTP en una secuencia de memoria como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
    </member>
    <member name="M:System.Net.Http.StreamContent.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.Http.StreamContent" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar los recursos administrados y no administrados; es false para liberar sólo los recursos no administrados.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.SerializeToStreamAsync(System.IO.Stream,System.Net.TransportContext)">
      <summary>Serialice el contenido HTTP en una secuencia como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task" />.Objeto de tarea que representa la operación asincrónica.</returns>
      <param name="stream">Secuencia de destino.</param>
      <param name="context">Información sobre el transporte (token de enlace de canal, por ejemplo).Este parámetro puede ser null.</param>
    </member>
    <member name="M:System.Net.Http.StreamContent.TryComputeLength(System.Int64@)">
      <summary>Determina si el contenido de la secuencia tiene una longitud válida en bytes.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="length" /> es un longitud válida; de lo contrario, false.</returns>
      <param name="length">Longitud en bytes del contenido de la secuencia.</param>
    </member>
    <member name="T:System.Net.Http.StringContent">
      <summary>Proporciona contenido HTTP basado en una cadena.</summary>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.StringContent" />.</summary>
      <param name="content">Contenido usado para inicializar <see cref="T:System.Net.Http.StringContent" />.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.StringContent" />.</summary>
      <param name="content">Contenido usado para inicializar <see cref="T:System.Net.Http.StringContent" />.</param>
      <param name="encoding">Codificación que se va a usar para el contenido.</param>
    </member>
    <member name="M:System.Net.Http.StringContent.#ctor(System.String,System.Text.Encoding,System.String)">
      <summary>Crea una nueva instancia de la clase <see cref="T:System.Net.Http.StringContent" />.</summary>
      <param name="content">Contenido usado para inicializar <see cref="T:System.Net.Http.StringContent" />.</param>
      <param name="encoding">Codificación que se va a usar para el contenido.</param>
      <param name="mediaType">Tipo de medio que se va a utilizar para el contenido.</param>
    </member>
    <member name="T:System.Net.Http.Headers.AuthenticationHeaderValue">
      <summary>Representa la información de autenticación en los valores de los encabezados Authorization, ProxyAuthorization, WWW-Authenticate y Proxy-Authenticate.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <param name="scheme">El esquema que se va a usar para la autorización.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.#ctor(System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <param name="scheme">El esquema que se va a usar para la autorización.</param>
      <param name="parameter">Credenciales que contienen la información de autenticación del agente de usuario para el recurso que se está solicitando.</param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual. </param>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Parameter">
      <summary>Obtiene las credenciales que contienen la información de autenticación del agente de usuario para el recurso que se está solicitando.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Credenciales que contienen la información de autenticación.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado de autenticación.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado de autenticación.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.AuthenticationHeaderValue.Scheme">
      <summary>Obtiene el esquema que se va a usar para la autorización.</summary>
      <returns>Devuelve <see cref="T:System.String" />.El esquema que se va a usar para la autorización.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.AuthenticationHeaderValue.TryParse(System.String,System.Net.Http.Headers.AuthenticationHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.CacheControlHeaderValue">
      <summary>Representa el valor del encabezado Cache-Control.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Extensions">
      <summary>Tokens de extensión de caché, cada uno con un valor asignado opcional.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Colección de tokens de la extensión de memoria caché, cada uno con un valor asignado opcional.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxAge">
      <summary>Edad máxima, especificada en segundos, que el cliente HTTP está dispuesto a aceptar una respuesta. </summary>
      <returns>Devuelve <see cref="T:System.TimeSpan" />.Tiempo en segundos. </returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStale">
      <summary>Si un cliente HTTP está dispuesto a aceptar una respuesta que ha superado el período de expiración.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el cliente HTTP está dispuesto a aceptar una respuesta que ha superado la fecha de expiración; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MaxStaleLimit">
      <summary>Tiempo máximo, en segundos, que un cliente HTTP está dispuesto a aceptar una respuesta que ha superado el período de expiración.</summary>
      <returns>Devuelve <see cref="T:System.TimeSpan" />.Tiempo en segundos.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MinFresh">
      <summary>Duración de la actualización, en segundos, que un cliente HTTP está dispuesto a aceptar una respuesta.</summary>
      <returns>Devuelve <see cref="T:System.TimeSpan" />.Tiempo en segundos.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.MustRevalidate">
      <summary>Si el servidor de origen necesita volver a validar una entrada de caché en cualquier uso posterior cuando la entrada de caché se vuelva obsoleta.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el servidor de origen necesita volver a validar una entrada de caché en cualquier uso posterior cuando la entrada se vuelva obsoleta; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCache">
      <summary>Si un cliente HTTP está dispuesto a aceptar una respuesta almacenada en memoria caché.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el cliente HTTP está dispuesto a aceptar una respuesta almacenada en memoria caché; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoCacheHeaders">
      <summary>Nombres de campo de una colección en la directiva “no-cache” de un campo de encabezado de la caché de controles en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Colección de fieldnames.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoStore">
      <summary>Si una memoria caché no debe almacenar ninguna parte del mensaje de solicitud HTTP o ninguna respuesta.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si una memoria caché no debe almacenar ninguna parte del mensaje de solicitud HTTP o ninguna respuesta; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.NoTransform">
      <summary>Si una memoria caché o un proxy no debe cambiar ningún aspecto del cuerpo de entidad.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si una memoria caché o un proxy no debe cambiar ningún aspecto del cuerpo de entidad; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.OnlyIfCached">
      <summary>Si una memoria caché debe responder con una entrada almacenada en memoria caché que sea coherente con las demás restricciones de la solicitud HTTP o responder con un estado 504 (tiempo de espera de la puerta de enlace).</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si una memoria caché debe responder con una entrada almacenada en memoria caché que sea coherente con las demás restricciones de la solicitud HTTP o responder con un estado 504 (tiempo de espera de la puerta de enlace); si no, false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información no válida del valor de encabezado cache-control.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado de control de caché.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Private">
      <summary>Si todo o parte del mensaje de respuesta HTTP está diseñado para un único usuario y no se debe almacenar en una memoria caché compartida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el mensaje de respuesta HTTP está pensado para un único usuario y una memoria caché compartida no lo debe almacenar en memoria caché; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.PrivateHeaders">
      <summary>Nombres de campo de una colección en la directiva “privada” de un campo de encabezado de la caché de controles en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Colección de fieldnames.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.ProxyRevalidate">
      <summary>Si el servidor de origen necesita volver a validar una entrada de caché en cualquier uso posterior cuando la entrada de caché se vuelva obsoleta para memorias caché compartidas de agente de usuario.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el servidor de origen necesita volver a validar una entrada de caché en cualquier uso posterior cuando la entrada se vuelva obsoleta para memorias caché compartidas de agente de usuario; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.Public">
      <summary>Si cualquier memoria caché puede almacenar una respuesta HTTP en memoria caché, aunque sería normalmente no almacenable en caché o almacenable solo dentro de la caché no compartida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si cualquier memoria caché puede almacenar la respuesta HTTP en memoria caché, aunque sería normalmente no almacenable en caché o almacenable solo dentro de la caché no compartida; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.CacheControlHeaderValue.SharedMaxAge">
      <summary>La edad máxima compartida, especificada en segundos, en una respuesta HTTP que reemplaza la directiva de "max-age" en un encabezado cache-control o un encabezado Expires para una memoria caché compartida.</summary>
      <returns>Devuelve <see cref="T:System.TimeSpan" />.Tiempo en segundos.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.CacheControlHeaderValue.TryParse(System.String,System.Net.Http.Headers.CacheControlHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentDispositionHeaderValue">
      <summary>Representa el valor del encabezado Content-Disposition.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.Net.Http.Headers.ContentDispositionHeaderValue)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <param name="source">
        <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />. </param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <param name="dispositionType">Cadena que contiene <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.CreationDate">
      <summary>Fecha y hora de creación del archivo.</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Fecha de creación del archivo.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.DispositionType">
      <summary>Tipo de disposición para una parte del cuerpo de contenido.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Tipo de disposición. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileName">
      <summary>Sugerencia sobre cómo crear un nombre de archivo para almacenar la carga del mensaje que se usará si la entidad se desasocia y se almacena un en archivo independiente.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Nombre de archivo sugerido.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.FileNameStar">
      <summary>Sugerencia sobre cómo crear un nombres de archivo para almacenar cargas de mensajes que se usará si las entidades se desasocian y se almacenan en un archivo independiente.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Nombre de archivo sugerido de filename* del formulario.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ModificationDate">
      <summary>Fecha y hora de la última modificación del archivo. </summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Fecha de modificación del archivo.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Name">
      <summary>Nombre de una parte del cuerpo de contenido.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Nombre de la parte del cuerpo de contenido.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Parameters">
      <summary>Conjunto de parámetros incluidos el encabezado de Content-Disposition.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Colección de parámetros. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado de disposición del contenido.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado de disponibilidad de contenido.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.ReadDate">
      <summary>Fecha en que se leyó el archivo por última vez.</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Última fecha de lectura.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentDispositionHeaderValue.Size">
      <summary>Tamaño aproximado del archivo en bytes. </summary>
      <returns>Devuelve <see cref="T:System.Int64" />.Tamaño aproximado en bytes.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentDispositionHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentDispositionHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ContentRangeHeaderValue">
      <summary>Representa el valor del encabezado Content-Range.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <param name="length">Punto inicial o final del intervalo, en bytes.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <param name="from">Posición, en bytes, en la que se va a empezar a enviar datos.</param>
      <param name="to">Posición, en bytes, en la que se va a terminar de enviar datos.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.#ctor(System.Int64,System.Int64,System.Int64)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <param name="from">Posición, en bytes, en la que se va a empezar a enviar datos.</param>
      <param name="to">Posición, en bytes, en la que se va a terminar de enviar datos.</param>
      <param name="length">Punto inicial o final del intervalo, en bytes.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Equals(System.Object)">
      <summary>Determina si el objeto especificado es igual al objeto <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.From">
      <summary>Obtiene la posición en la que se va a empezar a enviar datos.</summary>
      <returns>Devuelve <see cref="T:System.Int64" />.Posición, en bytes, en la que se va a empezar a enviar datos.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasLength">
      <summary>Obtiene si el encabezado de Content-Range tiene una longitud especificada.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si Content-Range tiene una longitud especificada; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.HasRange">
      <summary>Obtiene si el Content-Range tiene un intervalo especificado. </summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si Content-Range tiene un intervalo especificado; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Length">
      <summary>Obtiene la longitud del cuerpo de entidad completo.</summary>
      <returns>Devuelve <see cref="T:System.Int64" />.La longitud del cuerpo de entidad completo.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado de intervalo del contenido.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado de intervalo del contenido.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.To">
      <summary>Obtiene la posición en la que se va a terminar de enviar datos.</summary>
      <returns>Devuelve <see cref="T:System.Int64" />.Posición en la que se va a terminar de enviar datos.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ContentRangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.ContentRangeHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" /> de la cadena.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ContentRangeHeaderValue.Unit">
      <summary>Las unidades de intervalo usadas.</summary>
      <returns>Devuelve <see cref="T:System.String" />.<see cref="T:System.String" /> que contiene unidades de intervalo. </returns>
    </member>
    <member name="T:System.Net.Http.Headers.EntityTagHeaderValue">
      <summary>Representa un valor de encabezado de etiqueta de entidad.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <param name="tag">Cadena que contiene <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.#ctor(System.String,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <param name="tag">Cadena que contiene <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</param>
      <param name="isWeak">Un valor que indica si este encabezado de la etiqueta de entidad es un validador débil.Si el encabezado de la etiqueta de entidad es un validador débil, <paramref name="isWeak" /> debe establecerse en true.Si el encabezado de la etiqueta de entidad es un validador seguro, <paramref name="isWeak" /> debe establecerse en false.</param>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Any">
      <summary>Obtiene el valor del encabezado de la etiqueta de entidad.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.IsWeak">
      <summary>Obtiene si la etiqueta de entidad es precedida por un indicador de punto débil.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si la etiqueta de entidad va precedida por un indicador de punto débil; si no, false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado de la etiqueta de entidad.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado de la etiqueta de entidad.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.EntityTagHeaderValue.Tag">
      <summary>Obtiene la cadena entre comillas opaca. </summary>
      <returns>Devuelve <see cref="T:System.String" />.Una cadena entrecomillada opaca.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.EntityTagHeaderValue.TryParse(System.String,System.Net.Http.Headers.EntityTagHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpContentHeaders">
      <summary>Representa la colección de encabezados de contenido tal y como se define en RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Allow">
      <summary>Obtiene el valor del encabezado de contenido Allow en una respuesta HTTP. </summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Valor del encabezado Allow en una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentDisposition">
      <summary>Obtiene el valor del encabezado de contenido Content-Disposition en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.ContentDispositionHeaderValue" />.Valor del encabezado de contenido Content-Disposition en una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentEncoding">
      <summary>Obtiene el valor del encabezado de contenido Content-Encoding en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Valor del encabezado de contenido Content-Encoding en una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLanguage">
      <summary>Obtiene el valor del encabezado de contenido Content-Language en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Valor del encabezado de contenido Content-Language en una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLength">
      <summary>Obtiene o establece el valor del encabezado de contenido Content-Length en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Int64" />.Valor del encabezado de contenido Content-Length en una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentLocation">
      <summary>Obtiene o establece el valor del encabezado de contenido Content-Location en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Uri" />.Valor del encabezado de contenido Content-Location en una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentMD5">
      <summary>Obtiene o establece el valor del encabezado de contenido Content-MD5 en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Byte" />.Valor del encabezado de contenido Content-MD5 en una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentRange">
      <summary>Obtiene o establece el valor del encabezado de contenido Content-Range en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.ContentRangeHeaderValue" />.Valor del encabezado de contenido Content-Range en una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.ContentType">
      <summary>Obtiene o establece el valor del encabezado de contenido Content-Type en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.Valor del encabezado de contenido Content-Type en una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.Expires">
      <summary>Obtiene o establece el valor del encabezado de contenido Expires en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Valor del encabezado de contenido Expires en una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpContentHeaders.LastModified">
      <summary>Obtiene o establece el valor del encabezado de contenido Last-Modified en una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Valor del encabezado de contenido Last-Modified en una respuesta HTTP.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaders">
      <summary>Colección de encabezados y sus valores como se define en RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Agrega el encabezado especificado y sus valores en la colección <see cref="T:System.Net.Http.Headers.HttpHeaders" /> .</summary>
      <param name="name">Encabezado que se agrega a la colección.</param>
      <param name="values">Lista de valores de encabezado que se agregan a la colección.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Add(System.String,System.String)">
      <summary>Agrega el encabezado especificado y su valor en la colección <see cref="T:System.Net.Http.Headers.HttpHeaders" /> .</summary>
      <param name="name">Encabezado que se agrega a la colección.</param>
      <param name="value">Contenido del encabezado.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Clear">
      <summary>Quita todos los encabezados de la colección de <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Contains(System.String)">
      <summary>Devuelve si un encabezado concreto existe en la colección de <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el encabezado especificado existe en la colección; en caso contrario, false.</returns>
      <param name="name">El encabezado específico.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetEnumerator">
      <summary>Devuelve un enumerador que puede recorrer en iteración la instancia de <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.IEnumerator`1" />.Enumerador para <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.GetValues(System.String)">
      <summary>Devuelve todos los valores de un encabezado especificado almacenado en la colección de <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.IEnumerable`1" />.Matriz de cadenas de encabezado.</returns>
      <param name="name">El encabezado especificado para el que se van a devolver valores.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.Remove(System.String)">
      <summary>Quita el encabezado especificado de la colección de <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.</returns>
      <param name="name">Nombre del encabezado que se quitará de la colección. </param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.System#Collections#IEnumerable#GetEnumerator">
      <summary>Obtiene un enumerador que puede recorrer en iteración un objeto <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Devuelve <see cref="T:System.Collections.IEnumerator" />.Una instancia de una implementación de <see cref="T:System.Collections.IEnumerator" /> que puede recorrer en iteración un objeto <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Devuelve un valor que indica si el encabezado especificado y sus valores se agregaron a la colección <see cref="T:System.Net.Http.Headers.HttpHeaders" /> sin validar la información proporcionada.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si se pudo agregar el encabezado especificado <paramref name="name" /> y <paramref name="values" /> a la colección; de lo contrario, es false.</returns>
      <param name="name">Encabezado que se agrega a la colección.</param>
      <param name="values">Valores del encabezado.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryAddWithoutValidation(System.String,System.String)">
      <summary>Devuelve un valor que indica si el encabezado especificado y su valor se agregaron a la colección <see cref="T:System.Net.Http.Headers.HttpHeaders" /> sin validar la información proporcionada.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si se pudo agregar el encabezado especificado <paramref name="name" /> y <paramref name="value" /> a la colección; de lo contrario, es false.</returns>
      <param name="name">Encabezado que se agrega a la colección.</param>
      <param name="value">Contenido del encabezado.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaders.TryGetValues(System.String,System.Collections.Generic.IEnumerable{System.String}@)">
      <summary>Se devuelve si un encabezado y determinados valores especificados se almacenan en la colección <see cref="T:System.Net.Http.Headers.HttpHeaders" />.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true es el <paramref name="name" /> de encabezado especificado y values se almacenan en la colección; si no false.</returns>
      <param name="name">Encabezado especificado.</param>
      <param name="values">Valores de encabezado especificados.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpHeaderValueCollection`1">
      <summary>Representa una colección de valores de encabezado.</summary>
      <typeparam name="T">Tipo de colección de encabezado.</typeparam>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Add(`0)">
      <summary>Agrega una entrada al objeto <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <param name="item">Elemento que se agrega al encabezado especificada.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Clear">
      <summary>Quita todas las entradas de <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Contains(`0)">
      <summary>Determina si <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> contiene un elemento.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si la entrada está contenida en la instancia <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />; en caso contrario, false</returns>
      <param name="item">Elemento para encontrar el encabezado especificada.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.CopyTo(`0[],System.Int32)">
      <summary>Copia la totalidad de <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> en una matriz <see cref="T:System.Array" /> unidimensional compatible, comenzando en el índice especificado de la matriz de destino.</summary>
      <param name="array">
        <see cref="T:System.Array" /> unidimensional que constituye el destino de los elementos copiados de <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.<see cref="T:System.Array" /> debe tener una indización de base cero.</param>
      <param name="arrayIndex">Índice de base cero de <paramref name="array" /> en el que empieza la operación de copia.</param>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.Count">
      <summary>Obtiene el número de encabezados de la colección <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Número de encabezados de una colección.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.IEnumerator`1" />.Enumerador para la instancia <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpHeaderValueCollection`1.IsReadOnly">
      <summary>Obtiene un valor que indica si la instancia de <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> es de solo lectura.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si la instancia de <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" /> es de sólo lectura; en caso contrario, false.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ParseAdd(System.String)">
      <summary>Analiza y agrega una entrada a <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <param name="input">Entrada que se va a agregar.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.Remove(`0)">
      <summary>Quita el elemento especificado de <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="item" /> se ha quitado de la instancia <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />; en caso contrario, false.</returns>
      <param name="item">Elemento que se va a quitar.</param>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que recorre en iteración la colección <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Devuelve <see cref="T:System.Collections.IEnumerator" />.Enumerador para la instancia <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.ToString">
      <summary>Devuelve una cadena que representa el objeto actual <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.HttpHeaderValueCollection`1.TryParseAdd(System.String)">
      <summary>Determina si la entrada se puede analizar y agregar a <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> se puede analizar y agregar a la instancia <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />; de lo contrario, false</returns>
      <param name="input">Entrada que se va a validar.</param>
    </member>
    <member name="T:System.Net.Http.Headers.HttpRequestHeaders">
      <summary>Representa la colección de encabezados de solicitud tal y como se define en RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Accept">
      <summary>Obtiene el valor del encabezado Accept para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Accept para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptCharset">
      <summary>Obtiene el valor del encabezado Accept-Charset para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Accept-Charset para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptEncoding">
      <summary>Obtiene el valor del encabezado Accept-Encoding para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Accept-Encoding para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.AcceptLanguage">
      <summary>Obtiene el valor del encabezado Accept-Language para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Accept-Language para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Authorization">
      <summary>Obtiene o establece el valor del encabezado Authorization para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.Valor del encabezado Authorization para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.CacheControl">
      <summary>Obtiene o establece el valor del encabezado Cache-Control para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.Valor del encabezado Cache-Control para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Connection">
      <summary>Obtiene el valor del encabezado Connection para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Connection para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ConnectionClose">
      <summary>Obtiene o establece un valor que indica si el encabezado de Connection para una solicitud HTTP contiene Cerrar.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el encabezado Connection contiene Close; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Date">
      <summary>Obtiene o establece el valor del encabezado Date para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Valor del encabezado Date para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Expect">
      <summary>Obtiene el valor del encabezado Expect para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Expect para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ExpectContinue">
      <summary>Obtiene o establece un valor que indica si el encabezado de Expect para una solicitud HTTP contiene Continuar.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el encabezado Expect contiene Continue, si no false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.From">
      <summary>Obtiene o establece el valor del encabezado From para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Valor del encabezado From para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Host">
      <summary>Obtiene o establece el valor del encabezado Host para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Valor del encabezado Host para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfMatch">
      <summary>Obtiene el valor del encabezado If-Match para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado If-Match para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfModifiedSince">
      <summary>Obtiene o establece el valor del encabezado If-Modified-Since para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Valor del encabezado If-Modified-Since para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfNoneMatch">
      <summary>Obtiene el valor del encabezado If-None-Match para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Obtiene el valor del encabezado If-None-Match para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfRange">
      <summary>Obtiene o establece el valor del encabezado If-Range para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.Valor del encabezado If-Range para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.IfUnmodifiedSince">
      <summary>Obtiene o establece el valor del encabezado If-Unmodified-Since para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Valor del encabezado If-Unmodified-Since para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.MaxForwards">
      <summary>Obtiene o establece el valor del encabezado Max-Forwards para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Valor del encabezado Max-Forwards para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Pragma">
      <summary>Obtiene el valor del encabezado Pragma para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Pragma para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.ProxyAuthorization">
      <summary>Obtiene o establece el valor del encabezado Proxy-Authorization para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.Valor del encabezado Proxy-Authorization para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Range">
      <summary>Obtiene o establece el valor del encabezado Range para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.Valor del encabezado Range para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Referrer">
      <summary>Obtiene o establece el valor del encabezado Referer para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Uri" />.Valor del encabezado Referer para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TE">
      <summary>Obtiene el valor del encabezado TE para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado TE para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Trailer">
      <summary>Obtiene el valor del encabezado Trailer para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Trailer para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncoding">
      <summary>Obtiene el valor del encabezado Transfer-Encoding para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Transfer-Encoding para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.TransferEncodingChunked">
      <summary>Obtiene o establece un valor que indica si el encabezado de Transfer-Encoding para una solicitud HTTP contiene Fragmentar.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el encabezado Transfer-Encoding contiene fragmentos, si no false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Upgrade">
      <summary>Obtiene el valor del encabezado Upgrade para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Upgrade para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.UserAgent">
      <summary>Obtiene el valor del encabezado User-Agent para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado User-Agent para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Via">
      <summary>Obtiene el valor del encabezado Via para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Via para una solicitud HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpRequestHeaders.Warning">
      <summary>Obtiene el valor del encabezado Warning para una solicitud HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Warning para una solicitud HTTP.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.HttpResponseHeaders">
      <summary>Representa la colección de encabezados de respuesta tal y como se define en RFC 2616.</summary>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.AcceptRanges">
      <summary>Obtiene el valor del encabezado Accept-Ranges para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Accept-Ranges para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Age">
      <summary>Obtiene o establece el valor del encabezado Age para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.TimeSpan" />.Valor del encabezado Age para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.CacheControl">
      <summary>Obtiene o establece el valor del encabezado Cache-Control para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.CacheControlHeaderValue" />.Valor del encabezado Cache-Control para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Connection">
      <summary>Obtiene el valor del encabezado Connection para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Connection para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ConnectionClose">
      <summary>Obtiene o establece un valor que indica si el encabezado de Connection para una respuesta HTTP contiene Cerrar.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el encabezado Connection contiene Close; si no, false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Date">
      <summary>Obtiene o establece el valor del encabezado Date para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Valor del encabezado Date para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ETag">
      <summary>Obtiene o establece el valor del encabezado ETag para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.Valor del encabezado ETag para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Location">
      <summary>Obtiene o establece el valor del encabezado Location para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Uri" />.Valor del encabezado Location para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Pragma">
      <summary>Obtiene el valor del encabezado Pragma para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Pragma para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.ProxyAuthenticate">
      <summary>Obtiene el valor del encabezado Proxy-Authenticate para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Proxy-Authenticate para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.RetryAfter">
      <summary>Obtiene o establece el valor del encabezado Retry-After para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.Valor del encabezado Retry-After para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Server">
      <summary>Obtiene el valor del encabezado Server para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Server para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Trailer">
      <summary>Obtiene el valor del encabezado Trailer para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Trailer para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncoding">
      <summary>Obtiene el valor del encabezado Transfer-Encoding para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Transfer-Encoding para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.TransferEncodingChunked">
      <summary>Obtiene o establece un valor que indica si el encabezado de Transfer-Encoding para una respuesta HTTP contiene Fragmentar.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el encabezado Transfer-Encoding contiene fragmentos, si no false.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Upgrade">
      <summary>Obtiene el valor del encabezado Upgrade para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Upgrade para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Vary">
      <summary>Obtiene el valor del encabezado Vary para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Vary para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Via">
      <summary>Obtiene el valor del encabezado Via para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Via para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.Warning">
      <summary>Obtiene el valor del encabezado Warning para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado Warning para una respuesta HTTP.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.HttpResponseHeaders.WwwAuthenticate">
      <summary>Obtiene el valor del encabezado WWW-Authenticate para una respuesta HTTP.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.HttpHeaderValueCollection`1" />.Valor del encabezado WWW-Authenticate para una respuesta HTTP.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeHeaderValue">
      <summary>Representa un tipo de medio utilizado en un encabezado Content-Type como se define en RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <param name="source"> Objeto <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> utilizado para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <param name="mediaType">El origen representado como una cadena para inicializar la nueva instancia. </param>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.CharSet">
      <summary>Obtiene o define el juego de caracteres.</summary>
      <returns>Devuelve <see cref="T:System.String" />.El juego de caracteres.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.MediaType">
      <summary>Obtiene o establece el valor de encabezado del tipo de medio.</summary>
      <returns>Devuelve <see cref="T:System.String" />.El valor del encabezado media-type.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeHeaderValue.Parameters">
      <summary>Obtiene o establece los parámetros del valor de encabezado del tipo de medio.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Los parámetros de valores de encabezado media-type.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado del tipo de medio.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado del tipo de medio.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue">
      <summary>Representa un tipo de medio con un factor de calidad adicional utilizado en un encabezado Content-Type.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <param name="mediaType">Un <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> representado como una cadena para inicializar la nueva instancia. </param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <param name="mediaType">Un <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> representado como una cadena para inicializar la nueva instancia.</param>
      <param name="quality">La calidad asociada a este valor de encabezado.</param>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</returns>
      <param name="input">Cadena que representa el tipo de medios con la información de valor de encabezado de calidad.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> es el tipo de medio no válido con información de valor de encabezado de calidad.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.Quality">
      <summary>Obtiene o establece el valor de calidad de <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Double" />.Valor de calidad del objeto <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.MediaTypeWithQualityHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.MediaTypeWithQualityHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueHeaderValue">
      <summary>Representa un par de nombre y valor utilizado en diferentes encabezados como se define en RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.Net.Http.Headers.NameValueHeaderValue)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <param name="source">Objeto <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> utilizado para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <param name="name">Nombre del encabezado.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.#ctor(System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <param name="name">Nombre del encabezado.</param>
      <param name="value">Valor del encabezado.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Name">
      <summary>Obtiene el nombre de encabezado.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Nombre del encabezado.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado del valor de nombre.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado del valor de nombre.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.NameValueHeaderValue" /> de la cadena.</param>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueHeaderValue.Value">
      <summary>Obtiene el valor de encabezado.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Valor del encabezado.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue">
      <summary>Representa un par de nombre y valor con parámetros utilizado en diferentes encabezados como se define en RFC 2616.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.Net.Http.Headers.NameValueWithParametersHeaderValue)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <param name="source">Objeto <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> utilizado para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <param name="name">Nombre del encabezado.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.#ctor(System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <param name="name">Nombre del encabezado.</param>
      <param name="value">Valor del encabezado.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parameters">
      <summary>Obtiene los parámetros del objeto <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> .</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Una colección que contiene los parámetros.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</returns>
      <param name="input">Cadena que representa el valor de nombre con la información de valor de encabezado del parámetro.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> es un valor de nombre no válido con información de valor de encabezado de parámetro.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.NameValueWithParametersHeaderValue.TryParse(System.String,System.Net.Http.Headers.NameValueWithParametersHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.NameValueWithParametersHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ProductHeaderValue">
      <summary>Representa un valor de token de producto en un encabezado User-Agent.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <param name="name">Nombre del producto.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.#ctor(System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <param name="name">El valor del nombre del producto.</param>
      <param name="version">El valor de la versión del producto.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Name">
      <summary>Obtiene el nombre del token de producto.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Nombre del token de producto.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado del producto.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.ProductHeaderValue" /> de la cadena.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductHeaderValue.Version">
      <summary>Obtiene la versión del token de producto.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Versión del token de producto. </returns>
    </member>
    <member name="T:System.Net.Http.Headers.ProductInfoHeaderValue">
      <summary>Representa un valor que puede ser un producto o un comentario en un encabezado User-Agent.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.Net.Http.Headers.ProductHeaderValue)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <param name="product">Objeto <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> utilizado para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <param name="comment">Un valor de comentario.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.#ctor(System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <param name="productName">El valor del nombre del producto.</param>
      <param name="productVersion">El valor de la versión del producto.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Comment">
      <summary>Obtiene el comentario del objeto <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.El valor de comentario <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado de la información del producto.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado de la información del producto.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ProductInfoHeaderValue.Product">
      <summary>Obtiene el producto del objeto <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.ProductHeaderValue" />.El valor de producto de este <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ProductInfoHeaderValue.TryParse(System.String,System.Net.Http.Headers.ProductInfoHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.ProductInfoHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeConditionHeaderValue">
      <summary>Representa un valor de encabezado If-Range que puede ser de fecha y hora o de etiqueta de entidad.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <param name="date">Un valor de datos utilizado para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.Net.Http.Headers.EntityTagHeaderValue)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <param name="entityTag">Objeto <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" /> utilizado para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <param name="entityTag">Una etiqueta de entidad representada como cadena utilizada para inicializar la nueva instancia.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.Date">
      <summary>Obtiene la fecha del objeto <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> .</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.La fecha del objeto <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> .</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeConditionHeaderValue.EntityTag">
      <summary>Obtiene la etiqueta de entidad del objeto <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> .</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.EntityTagHeaderValue" />.La etiqueta de entidad del objeto <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> .</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado de la condición de intervalo.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información del valor del encabezado de la condición de intervalo.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeConditionHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.RangeConditionHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.RangeHeaderValue">
      <summary>Representa un valor de encabezado Range.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> con un intervalo de bytes.</summary>
      <param name="from">Posición en la que se va a empezar a enviar datos.</param>
      <param name="to">Posición en la que se va a terminar de enviar datos.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> es mayor que <paramref name="to" />O bien <paramref name="from" /> o <paramref name="to" /> es menor que 0. </exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado del intervalo.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado de intervalo.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Ranges">
      <summary>Obtiene los intervalos especificados en el objeto <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Los intervalos del objeto <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> .</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeHeaderValue.TryParse(System.String,System.Net.Http.Headers.RangeHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> de la cadena.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeHeaderValue.Unit">
      <summary>Obtiene la unidad del objeto <see cref="T:System.Net.Http.Headers.RangeHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.La unidad del objeto <see cref="T:System.Net.Http.Headers.RangeHeaderValue" /> .</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RangeItemHeaderValue">
      <summary>Representa un intervalo de bytes en un valor de encabezado Range.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />.</summary>
      <param name="from">Posición en la que se va a empezar a enviar datos.</param>
      <param name="to">Posición en la que se va a terminar de enviar datos.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="from" /> es mayor que <paramref name="to" />O bien <paramref name="from" /> o <paramref name="to" /> es menor que 0. </exception>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.From">
      <summary>Obtiene la posición en la que se va a empezar a enviar datos.</summary>
      <returns>Devuelve <see cref="T:System.Int64" />.Posición en la que se va a empezar a enviar datos.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RangeItemHeaderValue.To">
      <summary>Obtiene la posición en la que se va a terminar de enviar datos. </summary>
      <returns>Devuelve <see cref="T:System.Int64" />.Posición en la que se va a terminar de enviar datos. </returns>
    </member>
    <member name="M:System.Net.Http.Headers.RangeItemHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.RangeItemHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.RetryConditionHeaderValue">
      <summary>Representa un valor de encabezado Retry-After que puede ser de fecha y hora o de duración.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.DateTimeOffset)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <param name="date">Desplazamiento de fecha y hora utilizado para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.#ctor(System.TimeSpan)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <param name="delta">El delta, en segundos, utilizado para inicializar la nueva instancia.</param>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Date">
      <summary>Obtiene el desplazamiento de fecha y hora desde el objeto <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Desplazamiento de fecha y hora desde el objeto <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.RetryConditionHeaderValue.Delta">
      <summary>Obtiene el delta en segundos del objeto <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> .</summary>
      <returns>Devuelve <see cref="T:System.TimeSpan" />.El delta en segundos del objeto <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> .</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado de la condición de reintento.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado de la condición de reintento.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.RetryConditionHeaderValue.TryParse(System.String,System.Net.Http.Headers.RetryConditionHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.RetryConditionHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.StringWithQualityHeaderValue">
      <summary>Representa un valor de encabezado de cadena con una calidad opcional.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <param name="value">La cadena utilizada para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <param name="value">Una cadena utilizada para inicializar la nueva instancia.</param>
      <param name="quality">Un factor de calidad utilizado para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Equals(System.Object)">
      <summary>Determina si el objeto especificado es igual al objeto <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado de calidad.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> es una cadena no válida con información de encabezado de valor de encabezado de calidad.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Quality">
      <summary>Obtiene el factor de calidad del objeto <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> .</summary>
      <returns>Devuelve <see cref="T:System.Double" />.EL factor de calidad del objeto <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> .</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.StringWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.StringWithQualityHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" /> de la cadena.</param>
    </member>
    <member name="P:System.Net.Http.Headers.StringWithQualityHeaderValue.Value">
      <summary>Obtiene el valor de cadena del objeto <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.El valor de cadena del objeto <see cref="T:System.Net.Http.Headers.StringWithQualityHeaderValue" />.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingHeaderValue">
      <summary>Representa un valor de encabezado Accept-Encoding.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.Net.Http.Headers.TransferCodingHeaderValue)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <param name="source">Objeto <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> utilizado para inicializar la nueva instancia. </param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <param name="value">Una cadena utilizada para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Equals(System.Object)">
      <summary>Determina si el objeto especificado es igual al objeto <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Parameters">
      <summary>Obtiene los parámetros de codificación de transferencia.</summary>
      <returns>Devuelve <see cref="T:System.Collections.Generic.ICollection`1" />.Los parámetros de codificación de transferencia.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.AuthenticationHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado de codificación de transferencia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado de codificación de transferencia.</exception>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.TransferCodingHeaderValue" /> de la cadena.</param>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingHeaderValue.Value">
      <summary>Obtiene el valor de codificación de transferencia.</summary>
      <returns>Devuelve <see cref="T:System.String" />.El valor de codificación de transferencia.</returns>
    </member>
    <member name="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue">
      <summary>Representa un valor del encabezado Accept-Encoding con factor de calidad opcional.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <param name="value">Una cadena utilizada para inicializar la nueva instancia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.#ctor(System.String,System.Double)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <param name="value">Una cadena utilizada para inicializar la nueva instancia.</param>
      <param name="quality">Un valor para el factor de calidad.</param>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor de codificación de transferencia.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> es una codificación de transferencia no válida con información de valor de encabezado de calidad.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.Quality">
      <summary>Obtiene el factor de calidad de <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> .</summary>
      <returns>Devuelve <see cref="T:System.Double" />.El factor de calidad de <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> .</returns>
    </member>
    <member name="M:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue.TryParse(System.String,System.Net.Http.Headers.TransferCodingWithQualityHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.TransferCodingWithQualityHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.ViaHeaderValue">
      <summary>Representa el valor de un encabezado Via.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <param name="protocolVersion">La versión de protocolo del protocolo recibido.</param>
      <param name="receivedBy">El host y el puerto donde se recibió la solicitud o la respuesta.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <param name="protocolVersion">La versión de protocolo del protocolo recibido.</param>
      <param name="receivedBy">El host y el puerto donde se recibió la solicitud o la respuesta.</param>
      <param name="protocolName">El nombre de protocolo del protocolo recibido.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.#ctor(System.String,System.String,System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <param name="protocolVersion">La versión de protocolo del protocolo recibido.</param>
      <param name="receivedBy">El host y el puerto donde se recibió la solicitud o la respuesta.</param>
      <param name="protocolName">El nombre de protocolo del protocolo recibido.</param>
      <param name="comment">El campo de comentario usado para identificar el software de proxy o la puerta de enlace del destinatario.</param>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.Comment">
      <summary>Obtiene el campo de comentario usado para identificar el software de proxy o la puerta de enlace del destinatario.</summary>
      <returns>Devuelve <see cref="T:System.String" />.El campo de comentario usado para identificar el software de proxy o la puerta de enlace del destinatario.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Devuelve un código hash para el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.Instancia de <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</returns>
      <param name="input">Cadena que representa a través de la información del valor del encabezado.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida a través de la información del valor del encabezado.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolName">
      <summary>Obtiene el nombre de protocolo del protocolo recibido.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Nombre del protocolo.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ProtocolVersion">
      <summary>Obtiene la versión de protocolo del protocolo recibido.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Versión de protocolo.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.ViaHeaderValue.ReceivedBy">
      <summary>Obtiene el host y el puerto donde se recibió la solicitud o la respuesta.</summary>
      <returns>Devuelve <see cref="T:System.String" />.El host y el puerto donde se recibió la solicitud o la respuesta.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.ViaHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.ViaHeaderValue.TryParse(System.String,System.Net.Http.Headers.ViaHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.ViaHeaderValue" /> de la cadena.</param>
    </member>
    <member name="T:System.Net.Http.Headers.WarningHeaderValue">
      <summary>Representa un valor de advertencia utilizado por el encabezado Warning.</summary>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <param name="code">El código de la advertencia específico.</param>
      <param name="agent">El host que adjuntó la advertencia.</param>
      <param name="text">Una cadena entre comillas que contiene el texto de advertencia.</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.#ctor(System.Int32,System.String,System.String,System.DateTimeOffset)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <param name="code">El código de la advertencia específico.</param>
      <param name="agent">El host que adjuntó la advertencia.</param>
      <param name="text">Una cadena entre comillas que contiene el texto de advertencia.</param>
      <param name="date">Marca de tiempo de la advertencia.</param>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Agent">
      <summary>Obtiene el host que adjuntó la advertencia.</summary>
      <returns>Devuelve <see cref="T:System.String" />.El host que adjuntó la advertencia.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Code">
      <summary>Obtiene el código de la advertencia específico.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.El código de la advertencia específico.</returns>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Date">
      <summary>Obtiene la marca de tiempo de la advertencia.</summary>
      <returns>Devuelve <see cref="T:System.DateTimeOffset" />.Marca de tiempo de la advertencia.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Equals(System.Object)">
      <summary>Determina si el <see cref="T:System.Object" /> especificado es igual al objeto <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> actual.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si el <see cref="T:System.Object" /> especificado es igual al objeto actual; en caso contrario, es false.</returns>
      <param name="obj">Objeto que se va a comparar con el objeto actual.</param>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.Int32" />.Código hash para el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.Parse(System.String)">
      <summary>Convierte una cadena en una instancia de <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <returns>Devuelve una instancia <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</returns>
      <param name="input">Cadena que representa la información del valor del encabezado de autenticación.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> es una referencia null.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="input" /> no es información válida del valor del encabezado de autenticación.</exception>
    </member>
    <member name="P:System.Net.Http.Headers.WarningHeaderValue.Text">
      <summary>Obtiene una cadena entre comillas que contiene el texto de advertencia.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Una cadena entre comillas que contiene el texto de advertencia.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.ToString">
      <summary>Devuelve una cadena que representa el actual objeto <see cref="T:System.Net.Http.Headers.WarningHeaderValue" />.</summary>
      <returns>Devuelve <see cref="T:System.String" />.Cadena que representa el objeto actual.</returns>
    </member>
    <member name="M:System.Net.Http.Headers.WarningHeaderValue.TryParse(System.String,System.Net.Http.Headers.WarningHeaderValue@)">
      <summary>Determina si una cadena es una información de <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> válida.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.true si <paramref name="input" /> es información de <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> válida; de lo contrario, es false.</returns>
      <param name="input">Cadena que se va a validar.</param>
      <param name="parsedValue">Versión <see cref="T:System.Net.Http.Headers.WarningHeaderValue" /> de la cadena.</param>
    </member>
  </members>
</doc>