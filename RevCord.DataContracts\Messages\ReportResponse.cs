﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.ReportEntities;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.IQ3;
using RevCord.DataContracts.VoiceRecEntities;

namespace RevCord.DataContracts.Response
{
    public class ReportResponse : ResponseBase
    {
        //public List<CallInfo> Calls { get; set; }

        public List<RPTCallInfo> CallInfos { get; set; }

        public List<RPTCallInfoDetail> CallInfoDetails { get; set; }

        public List<RPTEvaluation> RPTEvaluations { get; set; }
        public List<RPTEvaluationMain> RPTEvaluationsMain { get; set; }

        public List<Survey> Surveys { get; set; }

        public List<CallAuditReport> CallAuditReports { get; set; }
        public int TotalRecords { get; set; }
        public int TotalPages { get; set; }
        public SavedReport SavedReport { get; set; }
        public List<SavedReport> SavedReports { get; set; }

        public SharedReportInfo SharedReportInfo { get; set; }

        public List<UserActivity> UserActivities { get; set; }
        public List<TenantIQ3Usage> TenantIQ3UsageList { get; set; }
        public List<MediaInfo> MediaInfos { get; set; }
    }
}
