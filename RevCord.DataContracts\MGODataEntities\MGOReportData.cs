﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.MGODataEntities
{
    public class MGOReportData
    {
        public int Id { get; set; }
        public string EventId { get; set; }
        public string ContractorName { get; set; }
        public string ContractorEmail { get; set; }
        public string ContractorPhoneNumber { get; set; }
        public string WorkOrderID { get; set; }
        public string PermitID { get; set; }
        public string Address { get; set; }
        public string Latitude { get; set; }
        public string Longitude { get; set; }

        public int ProjectTypeID { get; set; }
        public int InspectionTypeID { get; set; }
        public string InspectionType { get; set; }
        public int CategoryID { get; set; }
        public string Category { get; set; }

        public int InspectionOptionID { get; set; }
        public string InspectionOption { get; set; }
        public string InspectionOptionNotes { get; set; }
    }
}
