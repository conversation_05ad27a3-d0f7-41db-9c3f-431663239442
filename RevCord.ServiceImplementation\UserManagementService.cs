﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.ServiceContracts;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.UserManagement;
using RevCord.BusinessLogic;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts;

namespace RevCord.ServiceImplementation
{
    public class UserManagementService : IUserManagementService
    {
        #region Global Groups

        public UserManagementResponse CreateGroup(UserManagementRequest umRequest)
        {
            return new UserManagementManager().CreateGroup(umRequest);
        }
        public UserManagementResponse InqCreateGroup(UserManagementRequest umRequest)
        {
            return new UserManagementManager().InqCreateGroup(umRequest);
        }
        public UserManagementResponse DeleteGlobalGroup(UserManagementRequest umRequest) // For Inquire Group Management : Arivu
        {
            return new UserManagementManager().DeleteGlobalGroup(umRequest);
        }
        public UserManagementResponse UpdateGroup(UserManagementRequest umRequest)
        {
            return new UserManagementManager().UpdateGroup(umRequest);
        }

        public UserManagementResponse DeleteGroup(UserManagementRequest umRequest)
        {
            return new UserManagementManager().DeleteteGroup(umRequest);
        }

        public UserManagementResponse InquireDeleteGroup(UserManagementRequest umRequest) // For Inquire Group Management : Arivu
        {
            return new UserManagementManager().InquireDeleteGroup(umRequest);
        }
        public UserManagementResponse InqDeleteUserGroup(UserManagementRequest umRequest) // For Inquire Group Management : Arivu
        {
            return new UserManagementManager().InqDeleteUserGroup(umRequest);
        }

        public List<GlobalGroup> GetGroups(int tenantId)
        {
            return new UserManagementManager().GetGroups(tenantId);
        }

        #endregion

        #region Application User

        public UserManagementResponse DeleteAppUser(UserManagementRequest umRequest)
        {
            return new UserManagementManager().DeleteAppUser(umRequest);
        }

        public UserManagementResponse GetAppUsers(int tenantId)
        {
            return new UserManagementManager().GetAppUsers(tenantId);
        }

        public UserManagementResponse GetAppUsers_User_Manager_Simple_User_Rights(int UserID, int tenantId)
        {
            return new UserManagementManager().GetAppUsers_User_Manager_Simple_User_Rights(UserID, tenantId);
        }

        public UserManagementResponse GetEnterpriseUsers(int tenantId)
        {
            return new UserManagementManager().GetEnterpriseUsers(tenantId);
        }

        public UMResponse GetAppUsersForAllRecorders(UMRequest umRequest)
        {
            return new UserManager().GetAppUsersForAllRecorders(umRequest);
        }

        public UserManagementResponse GetUsersWithoutExtension(int tenantId)
        {
            return new UserManagementManager().GetUsersWithoutExtension(tenantId);
        }

        public UserManagementResponse GetAllUsersAsAgents(int tenantId)
        {
            return new UserManagementManager().GetAllUsersAsAgents(tenantId);
        }

        public UserManagementResponse GetAllUsersWithPermission(int tenantId)
        {
            return new UserManagementManager().GetAllUsersWithPermission(tenantId);
        }

        public UserManagementResponse GetAllAppUsers(int tenantId, int userTypeId)
        {
            return new UserManagementManager().GetAllAppUsers(tenantId, userTypeId);
        }

        public UserManagementResponse GetAgentsFromRecorder(Recorder recorder, int tenantId)
        {
            return new UserManagementManager().GetAgentsFromRecorder(recorder, tenantId);
        }

        public UserManagementResponse GetAgentsFromAllRecorders(List<Recorder> recorders, int tenantId)
        {
            return new UserManagementManager().GetAgentsFromAllRecorders(recorders, tenantId);
        }
        public UserManagementResponse GetUserData(int tenantId)
        {
            return new UserManagementManager().GetUserData(tenantId);
        }
        public UserManagementResponse GetUserDataFromAllRecorders(List<Recorder> recorders, int tenantId)
        {
            return new UserManagementManager().GetUserDataFromAllRecorders(recorders, tenantId);
        }


        #region User Account

        public UserManagementResponse CreateAppUserAccount(UserManagementRequest umRequest)
        {
            return new UserManagementManager().CreateAppUserAccount(umRequest);
        }

        public UserManagementResponse UpdateAppUserAccount(UserManagementRequest umRequest)
        {
            return new UserManagementManager().UpdateAppUserAccount(umRequest);
        }
        public UserManagementResponse UpdateAppUserAccount(Recorder recorder, UserManagementRequest umRequest, bool updateUserPwd)
        {
            return new UserManagementManager().UpdateAppUserAccount(recorder, umRequest, updateUserPwd);
        }

        public UserManagementResponse DeleteAppUserAccount(UserManagementRequest umRequest)
        {
            return new UserManagementManager().DeleteAppUserAccount(umRequest);
        }
        public UserManagementResponse DeleteAppUserAccount(Recorder recorder, UserManagementRequest umRequest)
        {
            return new UserManagementManager().DeleteAppUserAccount(recorder, umRequest);
        }
        public UserManagementResponse RecoverEnterpriseUserAccount(UserManagementRequest umRequest)
        {
            return new UserManagementManager().RecoverEnterpriseUserAccount(umRequest);
        }
        public int PermanentRemoveAppUserAccount(int userNum, int tenantId)
        {
            return new UserManagementManager().PermanentRemoveAppUserAccount(userNum, tenantId);
        }
        public int UpdateQBUserInfo(int userNum, int qbUserNum, bool existsOnQB, int tenantId)
        {
            return new UserManagementManager().UpdateQBUserInfo(userNum, qbUserNum, existsOnQB, tenantId);
        }
        public bool CheckUserAlreadyExistsAndActive(string email, int tenantId)
        {
            return new UserManagementManager().CheckUserAlreadyExistsAndActive(email, tenantId);
        }
        public bool IsInviteeActiveAndLiveMonitorPermitted(string email, int tenantId)
        {
            return new UserManagementManager().IsInviteeActiveAndLiveMonitorPermitted(email, tenantId);
        }
        public UserManagementResponse RecoverAppUserAccount(UserManagementRequest umRequest)
        {
            return new UserManagementManager().RecoverAppUserAccount(umRequest);
        }
        public UserManagementResponse RecoverAppUserAccount(List<Recorder> recorders, UserManagementRequest umRequest)
        {
            return new UserManagementManager().RecoverAppUserAccount(recorders, umRequest);
        }

        public UserManagementResponse GetAppUserAccount(long uId, int tenantId)
        {
            return new UserManagementManager().GetAppUserAccount(uId, tenantId);
        }
        public UserManagementResponse GetAppUserAccount(Recorder recorder, long uId, int tenantId)
        {
            return new UserManagementManager().GetAppUserAccount(recorder, uId, tenantId);
        }

        #endregion

        #region User Profile

        public UserManagementResponse CreateAppUserProfile(UserManagementRequest umRequest)
        {
            return new UserManagementManager().CreateAppUserProfile(umRequest);
        }

        public UserManagementResponse UpdateAppUserProfile(UserManagementRequest umRequest)
        {
            return new UserManagementManager().UpdateAppUserProfile(umRequest);
        }

        public UserManagementResponse GetAppUserProfile(long uId, int tenantId)
        {
            return new UserManagementManager().GetAppUserProfile(uId, tenantId);
        }

        #endregion

        public UserManagementResponse SaveUserGroup(UserManagementRequest umRequest)
        {
            return new UserManagementManager().SaveUserGroup(umRequest);
        }

        public UserManagementResponse SaveUserGroup(Recorder recorder, UserManagementRequest umRequest)
        {
            return new UserManagementManager().SaveUserGroup(recorder, umRequest);
        }

        public UserManagementResponse EnableDisableInquireUser(UserManagementRequest umRequest)
        {
            return new UserManagementManager().EnableDisableInquireUser(umRequest);
        }

        public UserManagementResponse EnableDisableAvrisView(UserManagementRequest umRequest)
        {
            return new UserManagementManager().EnableDisableAvrisView(umRequest);
        }

        public UserManagementResponse EnableDisableIQ3View(UserManagementRequest umRequest)
        {
            return new UserManagementManager().EnableDisableIQ3View(umRequest);
        }

        #region Inquire Group Management

        public UserManagementResponse inqGetUsers(UserManagementRequest umRequest)
        {
            return new UserManagementManager().InqGetUsers(umRequest);
        }
        public UserManagementResponse InqSaveGroupUser(UserManagementRequest umRequest)
        {
            return new UserManagementManager().InqSaveGroupUser(umRequest);
        }
        #endregion
        public UserManagementResponse DeleteUserGroup(UserManagementRequest umRequest)
        {
            return new UserManagementManager().DeleteUserGroup(umRequest);
        }

        public UserManagementResponse DeleteUserGroup(Recorder recorder, UserManagementRequest umRequest)
        {
            return new UserManagementManager().DeleteUserGroup(recorder, umRequest);
        }

        public UserManagementResponse AssignUnassignGroup(UserManagementRequest umRequest)
        {
            return new UserManagementManager().AssignUnassignGroup(umRequest);
        }
        public UserManagementResponse AssignUnassignGroup(Recorder recorder, UserManagementRequest umRequest)
        {
            return new UserManagementManager().AssignUnassignGroup(recorder, umRequest);
        }


        public UserManagementResponse UpdateAppUserPassword(UserManagementRequest userManagementRequest)
        {
            throw new NotImplementedException();
        }

        #endregion

        #region Treeview

        public UserManagementResponse GetTreeView(UserManagementRequest umRequest)
        {
            return new UserManagementManager().GetTreeView(umRequest);
        }

        public UserManagementResponse GetTreeViewFromRecorder(Recorder recorder, UserManagementRequest umRequest)
        {
            return new UserManagementManager().GetTreeViewFromRecorder(recorder, umRequest);
        }

        #endregion

        public User GetRevcordSupportCredentials(UserManagementRequest umRequest)
        {
            return new UserManagementManager().GetRevcordSupportCredentials(umRequest);
        }

        public UserManagementResponse GetMyInformation(UserManagementRequest umRequest)
        {
            return new UserManagementManager().GetMyInformation(umRequest);
        }

        public UserManagementResponse GetRecorderInfo(int tenantId)
        {
            return new UserManagementManager().GetRecorderInfo(tenantId);
        }

        public UserManagementResponse SaveUserImage(UserManagementRequest umRequest)
        {
            return new UserManagementManager().SaveUserImage(umRequest);
        }
        public UserManagementResponse UpdateUserImage(UserManagementRequest umRequest)
        {
            return new UserManagementManager().UpdateUserImage(umRequest);
        }


        public bool UpdateRootNode(int nodeId, string nodeText, int tenantId)
        {
            return new UserManagementManager().UpdateRootNode(nodeId, nodeText, tenantId);
        }

        #region Inquire Custom Marker

        public UserManagementResponse AddCustomMarker(UserManagementRequest umRequest)
        {
            return new UserManagementManager().AddCustomMarker(umRequest);
        }
        #endregion
        #region Invitaion
        public Invitation GetInvitation(int invitationId, int tenantId)
        {
            return new UserManagementManager().GetInvitationById(invitationId, tenantId);
        }

        public UserManagementResponse GetInvitationsByWhereClause(UserManagementRequest request)
        {
            return new UserManagementManager().GetInvitationsByWhereClause(request);
        }

        public bool CheckAlreadyInvited(string email, InvitationStatus status, int tenantId)
        {
            return new UserManagementManager().CheckAlreadyInvited(email, status, tenantId);
        }

        public UserManagementResponse CreateInvitation(UserManagementRequest request)
        {
            return new UserManagementManager().CreateInvitation(request);
        }

        public UserManagementResponse InsertEventDispatchInfo(UserManagementRequest request)
        {
            return new UserManagementManager().InsertEventDispatchInfo(request);
        }

        public bool ChangeInvitationStatus(int invitationId, InvitationStatus status, int? userId, DateTime? modifiedDate, int tenantId)
        {
            return new UserManagementManager().ChangeInvitationStatus(invitationId, status, userId, modifiedDate, tenantId);
        }
        //

        public bool UpdateInvitationURL(int invitationId, string invitationURL, int tenantId)
        {
            return new UserManagementManager().UpdateInvitationURL(invitationId, invitationURL, tenantId);
        }

        public bool CancelInvitation(int invitationId, int userId, DateTime modifiedDate, int tenantId)
        {
            return new UserManagementManager().CancelInvitation(invitationId, userId, modifiedDate, tenantId);
        }

        public UserManagementResponse AcceptInvitation(UserManagementRequest request)
        {
            return new UserManagementManager().AcceptInvitation(request);
        }

        public bool RejectInvitation(int invitationId, DateTime modifiedDate, int tenantId)
        {
            return new UserManagementManager().RejectInvitation(invitationId, modifiedDate, tenantId);
        }

        public bool CheckUniqueId(string email, int tenantId)
        {
            return new UserManagementManager().CheckUniqueId(email, tenantId);
        }

        public UserManagementResponse GetInviteeUser(UserManagementRequest userManagementRequest)
        {
            return new UserManagementManager().GetInviteeUser(userManagementRequest);
        }
        public UserManagementResponse UpdateEmailPassword(UserManagementRequest userManagementRequest, string userid)
        {
            return new UserManagementManager().UpdateEmailPassword(userManagementRequest, userid);
        }

        #endregion
        #region MonitorPlayer
        public UserManagementResponse GetLPSettingsInfo(int UserNum, int tenantId)
        {
            return new UserManagementManager().GetLPSettingsInfo(UserNum, tenantId);
        }
        public bool saveMonitorSettings(int UId, bool multiChannel, bool continuousPlay, int tenantId)
        {
            bool saved = false;
            return saved = new UserManagementManager().saveMonitorSettings(UId, multiChannel, continuousPlay, tenantId);
        }
        #endregion

        public UserManagementResponse GetAllUsers(UserManagementRequest userManagementRequest)
        {
            return new UserManagementManager().GetAllUsers(userManagementRequest);
        }

        public UserManagementResponse GetAllUsersOthers(UserManagementRequest userManagementRequest)
        {
            return new UserManagementManager().GetAllUsersOthers(userManagementRequest);
        }

        public UserManagementResponse CreateEventSpecificUserAccount(UserManagementRequest umRequest)
        {
            return new UserManagementManager().CreateEventSpecificUserAccount(umRequest);
        }
        public UserManagementResponse CreateEventSpecificSimpleUserAccount(UserManagementRequest umRequest)
        {
            return new UserManagementManager().CreateEventSpecificSimpleUserAccount(umRequest);
        }
        public UserManagementResponse UpdateEventSpecificSimpleRights(UserManagementRequest umRequest)
        {
            return new UserManagementManager().UpdateEventSpecificSimpleRights(umRequest);
        }
        public UserManagementResponse AddEventInvitation(UserManagementRequest umRequest)
        {
            return new UserManagementManager().AddEventInvitation(umRequest);
        }
        public UserManagementResponse GetInvitedActiveEventIdsByUserNum(UserManagementRequest umRequest)
        {
            return new UserManagementManager().GetInvitedActiveEventIdsByUserNum(umRequest);
        }
        public UserManagementResponse GetUserByEmailId(UserManagementRequest umRequest)
        {
            return new UserManagementManager().GetUserByEmailId(umRequest);
        }

        public bool CheckValidEventSpecificUser(string email, int tenantId)
        {
            return new UserManagementManager().CheckValidEventSpecificUser(email, tenantId);
        }
        public bool GrantLiveMonitorPermission(int userNum, string userPermissions, int extension, int tenantId)
        {
            return new UserManagementManager().GrantLiveMonitorPermission(userNum, userPermissions, extension, tenantId);
        }
        public bool EnableUserAccount(int userNum, int tenantId)
        {
            return new UserManagementManager().EnableUserAccount(userNum, tenantId);
        }

        public UserManagementResponse EnableDisableInvitationPermission(int userNum, bool bInvitationEnabled, int tenantId)
        {
            return new UserManagementResponse { User = new UserManagementManager().EnableDisableInvitationPermission(userNum, bInvitationEnabled, tenantId) };
        }

        public bool EnableDisableUsersetting(int userNum, bool bAutoUploadEnabled, bool bCustomAssetIdEnabled, int tenantId)
        {
            return new UserManagementManager().EnableDisableUsersetting(userNum, bAutoUploadEnabled, bCustomAssetIdEnabled, tenantId);
        }

        public bool ValidateUserPassword(int userNum, string currentPassword, int tenantId)
        {
            return new UserManagementManager().ValidateUserPassword(userNum, currentPassword, tenantId);
        }
        public bool ValidateUserPasswordByEmail(string email, string currentPassword, int tenantId)
        {
            return new UserManagementManager().ValidateUserPasswordByEmail(email, currentPassword, tenantId);
        }
        public bool ValidateTempUserPassword(int userNum, string currentPassword, int tenantId)
        {
            return new UserManagementManager().ValidateTempUserPassword(userNum, currentPassword, tenantId);
        }

        public bool UpdateUserPassword(int userNum, string newPassword, int tenantId)
        {
            return new UserManagementManager().UpdateUserPassword(userNum, newPassword, tenantId);
        }
        public bool UpdateUserPasswordByEmail(string email, string newPassword, int tenantId)
        {
            return new UserManagementManager().UpdateUserPasswordByEmail(email, newPassword, tenantId);
        }
        public List<string> GetPasswordHistoryByUser(int userId, int tenantId)
        {
            return new UserManagementManager().GetPasswordHistoryByUser(userId, tenantId);
        }
        public List<string> GetPasswordHistoryByEmail(string email, int tenantId)
        {
            return new UserManagementManager().GetPasswordHistoryByEmail(email, tenantId);
        }
        public bool LockUnlockUserAccount(string email, bool lockAccount, int tenantId)
        {
            return new UserManagementManager().LockUnlockUserAccount(email, lockAccount, tenantId);
        }


        public bool UpdateUserTempPassword(int userNum, string newPassword, int tenantId)
        {
            return new UserManagementManager().UpdateUserTempPassword(userNum, newPassword, tenantId);
        }
        public bool ResetUserPasswordToTemp(string userID, string tempPassword, int tenantId)
        {
            return new UserManagementManager().ResetUserPasswordToTemp(userID, tempPassword, tenantId);
        }

        public UserManagementResponse EnableDisableRevCell(UserManagementRequest umRequest)
        {
            return new UserManagementManager().EnableDisableRevCell(umRequest);
        }

        public UserManagementResponse ManageRevcellPermission(UserManagementRequest umRequest)
        {
            return new UserManagementManager().ManageRevcellPermission(umRequest);
        }

        public bool HasRevcellForCurrentServer(UserManagementRequest umRequest)
        {
            return new UserManagementManager().HasRevcellForCurrentServer(umRequest);
        }

        public bool HasActiveRevcellLicense(UserManagementRequest umRequest)
        {
            return new UserManagementManager().HasActiveRevcellLicense(umRequest);
        }

        public bool HasActiveIQ3License(UserManagementRequest umRequest)
        {
            return new UserManagementManager().HasActiveIQ3License(umRequest);
        }

        public UserManagementResponse ManageIQ3Permission(UserManagementRequest umRequest)
        {
            return new UserManagementManager().ManageIQ3Permission(umRequest);
        }

        public bool IsDeviceUser(UserManagementRequest umRequest)
        {
            return new UserManagementManager().IsDeviceUser(umRequest);
        }

        public UserManagementResponse GetUserExtensionInfos(UserManagementRequest umRequest)
        {
            return new UserManagementManager().GetUserExtensionInfos(umRequest);
        }

        public UserManagementResponse GetUsersAboutToExpirePassword(UserManagementRequest request)
        {
            /*return new UserManagementResponse
            {
                Users = new List<User>
                {
                    new User { UserEmail = "<EMAIL>", UserName = "Sher Muhammad", UserNum = 1007, UserID = "<EMAIL>" }
                }
            };*/

            return new UserManagementManager().GetUsersAboutToExpirePassword(request.TenantId);

        }

        public bool IsRevcellEnable(string email, int tenantId) {
            try
            {
                return new UserManagementManager().IsRevcellEnable(email, tenantId);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public UserManagementResponse GetAppUserAccess(string userEmail, int tenantId) {
            try
            {
                return new UserManagementManager().GetAppUserAccess(userEmail, tenantId);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public bool CopyMarkers(int copyTo, int copyOf, int tenantId)
        {
            try
            {
                return new UserManagementManager().CopyMarkers(copyTo, copyOf, tenantId);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public UserManagementResponse FetchAllActiveUsersFromRecorder(Recorder recorder, int tenantId)
        {
            try
            {
                return new UserManagementManager().FetchAllActiveUsersFromRecorder(recorder, tenantId);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public int UpdateCompactViewStatus(int userNum, bool isCompactView, int tenantId)
        {
            return new UserManagementManager().UpdateCompactViewStatus(userNum, isCompactView, tenantId);
        }

        public bool CopyTenantUserMarkers(int copyTo, int copyOf, int sourceTenantId, int destinationTenantId)
        {
            try
            {
                return new UserManagementManager().CopyTenantUserMarkers(copyTo, copyOf, sourceTenantId, destinationTenantId);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public UserManagementResponse AcceptLicenseAgreement(UserManagementRequest umRequest)
        {
            try
            {
                return new UserManagementManager().AcceptLicenseAgreement(umRequest);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public bool AuthenticateUser(string email, string password, int tenantId)
        {
            try
            {
                return new UserManagementManager().AuthenticateUser(email, password, tenantId);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public UserManagementResponse GetUserInformation(UserManagementRequest umRequest)
        {
            return new UserManagementManager().GetUserInformation(umRequest);
        }

        #region IWB

        public UserManagementResponse IwbAddExtension(UserManagementRequest umRequest)
        {
            return new UserManagementManager().IwbAddExtension(umRequest);
        }

        #endregion

        public UserManagementResponse GetAvailableAutoReportRecipients(int tenantId)
        {
            return new UserManagementManager().GetAvailableAutoReportRecipients(tenantId);
        }
    }
}

