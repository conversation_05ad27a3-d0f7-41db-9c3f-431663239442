﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using Newtonsoft.Json;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.IQ3;
using RevCord.Util;
using RevCord.DataContracts;

namespace RevCord.DataAccess
{
    public class UserManagerDAL
    {
        private int _tenantId;
        public UserManagerDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);

        //public static List<Employee> GetAllUsers()
        //{
        //    string connString = "Data Source=|DataDirectory|TreeDemo.sdf;";
        //    var userList = new List<Employee>();
        //    using (var con = new SqlCeConnection(connString))
        //    {
        //        string qry = "SELECT ID,Name,ManagerID FROM Users";
        //        using (var cmd = new SqlCeCommand(qry, con))
        //        {
        //            con.Open();
        //            SqlCeResultSet reader = cmd.ExecuteResultSet(ResultSetOptions.Scrollable);
        //            if (reader.HasRows)
        //            {
        //                while (reader.Read())
        //                {
        //                    var user = new Employee();
        //                    user.ID = reader.GetInt32(reader.GetOrdinal("ID"));
        //                    user.Name = reader.GetString(reader.GetOrdinal("Name"));
        //                    if (!reader.IsDBNull(reader.GetOrdinal("ManagerID")))
        //                        user.ManagerID = reader.GetInt32(reader.GetOrdinal("ManagerID"));

        //                    userList.Add(user);
        //                }
        //            }
        //        }
        //    }
        //    return userList;
        //}
        #region Fields
        public Dictionary<int,int> dUsers = new Dictionary<int,int>();
        #endregion
        public List<TreeviewData> GetGroupsTree(int userNum, string userId, int authNum, string authType, int type, int userType, bool isRevCellRequired = false)
        {
            //EXEC sp_Init_Tree @UserNum = 1000,  @AuthNum = 4,  @AuthType = '1', @Type = 4
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_ADMIN;

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);
                    cmd.Parameters.AddWithValue("@IsRevCellRequired", isRevCellRequired);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetGroupsTree", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.IsRevAgentAssociated = Convert.ToBoolean(dr["IsRevAgentAssociated"]);
                                treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                treeNode.IsIQ3 = Convert.ToBoolean(dr["IsIQ3"]);
                                treeNode.IsText911 = DBRecordExtensions.HasColumn(dr, "IsText911") ? Convert.ToBoolean(dr["IsText911"]) : false;

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }

        public List<TreeviewData> GetGroupsTreeNonAdmin(int userNum, string userId, int authNum, string authType, int type, int userType, int roleId, int roleType, bool isRevCellRequired = false)
        {
            //EXEC sp_MyExt_Tree @UserNum = 1061, @AuthNum = 4, @AuthType = 1, @Type  = 1
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    if (roleId > 0)
                    {
                        cmd.CommandText = roleType == 1 ? DBConstants.UserManagement.TREEVIEW_GET_GROUPBASED : DBConstants.UserManagement.TREEVIEW_GET_CHANNELBASED;
                        cmd.Parameters.AddWithValue("@RoleId", roleId);
                    }
                    else
                        cmd.CommandText = userType == 0 ? DBConstants.UserManagement.TREEVIEW_GET_ADDITIONAL : DBConstants.UserManagement.TREEVIEW_GET_SIMPLE;

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);
                    cmd.Parameters.AddWithValue("@IsRevCellRequired", false);
                    
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetGroupsTreeNonAdmin", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.IsRevAgentAssociated = Convert.ToBoolean(dr["IsRevAgentAssociated"]);
                                treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                treeNode.IsIQ3 = Convert.ToBoolean(dr["IsIQ3"]);
                                treeNode.IsText911 = DBRecordExtensions.HasColumn(dr, "IsText911") ? Convert.ToBoolean(dr["IsText911"]) : false;

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }

        public List<TreeviewData> GetGroupmanagemtGroups(int userNum, string userId, int authNum, string authType, int type, int userType/*,int selectType*/)
        {
            //EXEC sp_MyExt_Tree @UserNum = 1061, @AuthNum = 4, @AuthType = 1, @Type  = 1
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_SIMPLE;

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetGroupmanagemtGroups", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }
		
		public List<TreeviewData> GetInquireGroupsTreeforEvualtionreport(int userNum,int selectType)
            //, string userId, int authNum, string authType, int type, int userType/*,int selectType*/)
        {
            int userType = 0;

            //EXEC sp_MyExt_Tree @UserNum = 1061, @AuthNum = 4, @AuthType = 1, @Type  = 1
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = selectType == 0 ? DBConstants.UserManagement.TREEVIEW_GET : DBConstants.UserManagement.TREEVIEW_SIMPLE_USER_RIGHTS_GET;
                    //cmd.CommandText = userType == 0 ? DBConstants.UserManagement.TREEVIEW_GET_ADDITIONAL : DBConstants.UserManagement.TREEVIEW_GET_SIMPLE;

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", "");
                    if (selectType == 1)
                        cmd.Parameters.AddWithValue("@AuthNum", 7);
                    else
                        cmd.Parameters.AddWithValue("@AuthNum", 4);
                    cmd.Parameters.AddWithValue("@AuthType", 1);
                    cmd.Parameters.AddWithValue("@Type", 11);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetInquireGroupsTreeforEvualtionreport", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                if(!treeNode.IsGroup)
                                    treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }

        public List<TreeviewData> GetMDGroupsTreeForEvaluationReport(int userNum, int selectType)
        {
            int userType = 0;

            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = selectType == 0 ? DBConstants.UserManagement.TREEVIEW_GET : DBConstants.UserManagement.TREEVIEW_SIMPLE_USER_RIGHTS_GET;

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", "");
                    //if (selectType == 1)
                    cmd.Parameters.AddWithValue("@AuthNum", 7);
                    //else
                    //    cmd.Parameters.AddWithValue("@AuthNum", 4);
                    cmd.Parameters.AddWithValue("@AuthType", 1);
                    cmd.Parameters.AddWithValue("@Type", 16);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetMDGroupsTreeForEvaluationReport", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                if (!treeNode.IsGroup)
                                    treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }

        public List<TreeviewData> GetUsersTree(int userNum)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "sp_Init_User_Tree";

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetUsersTree", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.Childrens = new List<TreeviewData>();
                                
                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return treeNodes;
        }

        #region ------- Users -------

        public List<User> GetUsers()
        {
            List<User> users = null;
            User user = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = "SELECT  UserNum, UserType, UserID, UserPW, UserName, Status FROM t_Account WHERE Ext=0";// AND Status = 1
                    cmd.CommandText = "SELECT  UserNum, UserType, UserID, UserPW, UserName, Status FROM t_Account WHERE SUBSTRING(UserName,1,5)<>'Agent' ";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetUsers", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            users = new List<User>();
                            while (dr.Read())
                            {
                                user = new User();
                                user.UserNum = (int)dr["UserNum"];
                                user.UserType = (int)dr["UserType"];
                                user.UserID = Convert.ToString(dr["UserID"]);
                                user.UserPW = Convert.ToString(dr["UserPW"]);
                                user.UserName = Convert.ToString(dr["UserName"]);
                                user.Status = Convert.ToInt32(dr["Status"]);

                                users.Add(user);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return users;
        }

        public int GetUserNumByExt(int ext)
        {
            int userNum = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = "SELECT  UserNum, UserType, UserID, UserPW, UserName, Status FROM t_Account WHERE Ext=0";// AND Status = 1
                    cmd.CommandText = "SELECT  UserNum FROM t_Account WHERE Ext=@Ext";
                    cmd.Parameters.AddWithValue("@Ext", ext);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetUserNumByExt", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                userNum = (int)dr["UserNum"];
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return userNum;
        }

        #endregion
        #region Inquire 
        public List<TreeviewData> GetInquireGroupsTree(int userNum, string userId, int authNum, string authType, int type, int userType)
        {
            //EXEC sp_Init_Tree @UserNum = 1000,  @AuthNum = 4,  @AuthType = '1', @Type = 4
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_ADMIN;

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetInquireGroupsTree", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                treeNode.IsIQ3 = Convert.ToBoolean(dr["IsIQ3"]);
                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }
        #endregion
        #region ------- User Activity -------

        public int SaveUserActivity(int userId, int activityId, string activityLog, DateTime createdDate, string messageKey, string csvMessageData, string comments, string ipaddress)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_ACTIVITY_INSERT;

                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@activityId", activityId);
                    cmd.Parameters.AddWithValue("@activityPerformed", activityLog);
                    cmd.Parameters.AddWithValue("@createdDate", createdDate);
                    cmd.Parameters.AddWithValue("@messageKey", messageKey);
                    cmd.Parameters.AddWithValue("@messageData", csvMessageData);

                    cmd.Parameters.AddWithValue("@comments", (comments == null ? string.Empty : comments));
                    cmd.Parameters.AddWithValue("@clientIP", ipaddress);

                    //return 1;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "SaveUserActivity", _tenantId));

                    int lastId = Convert.ToInt32(cmd.ExecuteScalar());
                    return lastId;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<UserActivity> GetUserActivities(int userId, DateTime startDate, DateTime endDate, int pageSize, int pageIndex, out int totalPages, out long totalRecords)
        {
            List<UserActivity> userActivities = null;
            UserActivity userActivity = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_ACTIVITY_SEARCH;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@userId", userId);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetUserActivities", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            userActivities = new List<UserActivity>();
                            while (dr.Read())
                            {
                                userActivity = new UserActivity();
                                userActivity.Id = Convert.ToInt64(dr["Id"]);
                                userActivity.UserId = (int)dr["UserId"];
                                userActivity.ActivityId = Convert.ToInt32(dr["ActivityId"]);
                                userActivity.ActivityPerformed = Convert.ToString(dr["ActivityPerformed"]);
                                userActivity.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                userActivity.MessageKey = Convert.ToString(dr["MessageKey"]);
                                userActivity.MessageData = Convert.ToString(dr["MessageData"]);
                                userActivity.Comments = Convert.ToString(dr["Comments"]);
                                userActivity.ClientIP = Convert.ToString(dr["ClientIPAddress"]);

                                userActivities.Add(userActivity);
                            }
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return userActivities;
        }
        public int GetUserActivityCount(int userId, DateTime startDate, DateTime endDate)
        {
            int activitiesCount = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_ACTIVITY_COUNT;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@userId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetUserActivityCount", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                            while (dr.Read())
                                activitiesCount = Convert.ToInt32(dr["UserActivitiesCount"]);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return activitiesCount;
        }

        #endregion


        #region Inquire Group Management
        public List<TreeviewData> GetInquireGroups()
        {
            List<TreeviewData> inqDbNodes = null;
            TreeviewData treeNode = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_GROUP_GETALL;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetInquireGroups", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            inqDbNodes = new List<TreeviewData>();

                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentGroup"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.GroupName = Convert.ToString(dr["GroupName"]);
                                treeNode.GroupNumber = Convert.ToInt32(dr["GroupNum"]);
                                inqDbNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            { }
            return inqDbNodes;
        }

        // public static List<GridViewData> GetInquireUsers()
        //public static List<GridViewData> GetInquireUsers()
        //{
        //    List<GridViewData> lUsers = null;
        //    GridViewData gvData = null;
        //    DataTable dt = new DataTable();

        //    try
        //    {
        //        using (var conn = DALHelper.GetConnection())
        //        using (var cmd = conn.CreateCommand())
        //        {
        //            cmd.CommandType = CommandType.StoredProcedure;
        //            //cmd.CommandText = DBConstants.UserManagement.INQUIRE_USERS_GETALL;
        //            // cmd.Parameters.AddWithValue("@UserId", 1000);

        //            conn.Open();

        //            using (SqlDataReader dr = cmd.ExecuteReader())
        //            {
        //                if (dr.HasRows)
        //                {
        //                    lUsers = new List<GridViewData>();
        //                    int i = 1;
        //                    while (dr.Read())
        //                    {
        //                        gvData = new GridViewData();
        //                        // gvData.SNo = i;
        //                        gvData.Ext = Convert.ToInt32(dr["Ext"]);
        //                        //gvData.ChannelNumber = Convert.ToInt32(dr["Ext"]);
        //                        // if(gvData.ChannelNumber!=0)
        //                        // {
        //                        if (gvData.Ext != 0)
        //                        {
        //                            lUsers.Add(gvData);
        //                        }

        //                        //  i++;
        //                        //  }

        //                    }
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //    }

        //    return lUsers;
        //}

        public List<GridViewData> GetInquireGroupUsers(int groupNum)
        {
            List<GridViewData> lUsers = null;
            GridViewData gvData = null;

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_GetUSERS_BY_GROUPID;
                    cmd.Parameters.AddWithValue("@GROUPNUM", groupNum);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetInquireGroupUsers", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            lUsers = new List<GridViewData>();

                            while (dr.Read())
                            {
                                gvData = new GridViewData();
                                gvData.UserName = Convert.ToString(dr["UserName"]);
                                gvData.Ext = Convert.ToInt32(dr["UserNum"]);
                                if (gvData.Ext != 1000)
                                {
                                    lUsers.Add(gvData);
                                }

                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return lUsers;
        }

        public List<CustomMarkersData> GetInquireCustomMarkers(int userNum)
        {
            List<CustomMarkersData> lLVMarkers = null;
            CustomMarkersData lvData = null;
            //List<CustomMarkerDetail> customMarkerDetails = new List<CustomMarkerDetail>();
            //CustomMarkerDetail customMarkerDetail = null;
            int cmId = 0;
            string customMarker = string.Empty;
            string existingMarker = string.Empty;
            string queryType = "select";
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_CUSTOM_MARKER;

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@CustMarkerId", cmId);
                    cmd.Parameters.AddWithValue("@CustomMarker", customMarker);
                    cmd.Parameters.AddWithValue("@queryType", queryType);
                    cmd.Parameters.AddWithValue("@ExistingMarker", existingMarker);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetInquireCustomMarkers", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            lLVMarkers = new List<CustomMarkersData>();
                            int i = 1;
                            while (dr.Read())
                            {
                                lvData = new CustomMarkersData();
                                lvData.SNo = i;
                                lvData.Id = Convert.ToInt32(dr["id"]);
                                lvData.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                lvData.ParentId = Convert.ToInt32(dr["ParentId"]);
                                lvData.Level = Convert.ToInt32(dr["Level"]);
                                //lvData.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                lvData.Ext = Convert.ToInt32(dr["Ext"]);
                                lvData.Markers = Convert.ToString(dr["CustomMarker"]);
                                lvData.MarkerNote =  Convert.ToBoolean(dr["isnotedeleted"])  ? "" : Convert.ToString(dr["NoteTitle"]);
                                lvData.customMarkerId = Convert.ToInt32(dr["CustMarkerId"]);
                                //lvData.CustomMarkerDetails = new List<CustomMarkerDetail>();
                                lLVMarkers.Add(lvData);
                                i++;
                            }
                        }
                        //dr.NextResult();
                        //if (dr.HasRows)
                        //{
                        //    while (dr.Read())
                        //    {
                        //        customMarkerDetail = new CustomMarkerDetail();
                        //        customMarkerDetail.Id = Convert.ToInt32(dr["Id"]);
                        //        customMarkerDetail.CustomMarkerId = Convert.ToInt32(dr["CustomMarkerId"]);
                        //        customMarkerDetail.UserNum = Convert.ToInt32(dr["UserNum"]);
                        //        customMarkerDetail.MarkerText = Convert.ToString(dr["MarkerText"]);
                        //        customMarkerDetail.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                        //        customMarkerDetail.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                        //        customMarkerDetails.Add(customMarkerDetail);
                        //    }
                        //}

                        //if (customMarkerDetails != null)
                        //{
                        //    foreach (var cmDetail in customMarkerDetails)
                        //    {
                        //        if (lLVMarkers.Exists(c => c.Id == cmDetail.CustomMarkerId))
                        //            lLVMarkers.Where(c => c.Id == cmDetail.CustomMarkerId).FirstOrDefault().CustomMarkerDetails.Add(cmDetail);
                        //    }
                        //}
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return lLVMarkers;
        }

        public List<CustomMarkersData> GetInquireCustomMarkersByEventId(string eventId)
        {
            List<CustomMarkersData> lLVMarkers = null;
            CustomMarkersData lvData = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_GET_NONINSPECTED_MARKERS;

                    cmd.Parameters.AddWithValue("@CallId", eventId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetInquireCustomMarkersByEventId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            lLVMarkers = new List<CustomMarkersData>();
                            while (dr.Read())
                            {
                                lvData = new CustomMarkersData();
                                lvData.Id = Convert.ToInt32(dr["id"]);
                                lvData.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                lvData.ParentId = Convert.ToInt32(dr["ParentId"]);
                                lvData.Level = Convert.ToInt32(dr["Level"]);
                                lvData.Ext = Convert.ToInt32(dr["Ext"]);
                                lvData.Markers = Convert.ToString(dr["CustomMarker"]);
                                lvData.customMarkerId = Convert.ToInt32(dr["CustMarkerId"]);
                                lLVMarkers.Add(lvData);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return lLVMarkers;
        }

        public bool addMarker(CustomMarkersData cmData)
        {

            string queryType = "insert";
            string existingMarker = string.Empty;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_CUSTOM_MARKER;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@UserNum", cmData.Ext);
                    cmd.Parameters.AddWithValue("@CustMarkerId", cmData.SNo);
                    cmd.Parameters.AddWithValue("@CustomMarker", cmData.Markers);
                    cmd.Parameters.AddWithValue("@queryType", queryType);
                    cmd.Parameters.AddWithValue("@ExistingMarker", existingMarker);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "addMarker", _tenantId));

                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool breturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagerDAL", "addMarker", JsonConvert.SerializeObject(cmData)));
                        if (breturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagerDAL", "addMarker", JsonConvert.SerializeObject(cmData));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public bool UpdateMarker(CustomMarkersData cmData)
        {
            string queryType = "update";
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_CUSTOM_MARKER;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    cmd.Parameters.AddWithValue("@UserNum", cmData.Ext);
                    cmd.Parameters.AddWithValue("@CustMarkerId", cmData.SNo);
                    cmd.Parameters.AddWithValue("@CustomMarker", cmData.Markers);
                    cmd.Parameters.AddWithValue("@queryType", queryType);
                    cmd.Parameters.AddWithValue("ExistingMarker", cmData.ExistingMarker);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "UpdateMarker", _tenantId));


                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool breturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagerDAL", "UpdateMarker", JsonConvert.SerializeObject(cmData)));
                        if (breturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagerDAL", "UpdateMarker", JsonConvert.SerializeObject(cmData));

                        if (isSent)
                            tran.Commit();
                        else
                            tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

        }
        public bool removeMarker(CustomMarkersData cmData)
        {

            string existingMarker = string.Empty;
            string queryType = "delete";
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_CUSTOM_MARKER;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@UserNum", cmData.Ext);
                    cmd.Parameters.AddWithValue("@CustMarkerId", cmData.SNo);
                    cmd.Parameters.AddWithValue("@CustomMarker", cmData.Markers);
                    cmd.Parameters.AddWithValue("@queryType", queryType);
                    cmd.Parameters.AddWithValue("ExistingMarker", existingMarker);

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "removeMarker", _tenantId));

                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool breturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagerDAL", "RemoveMarker", JsonConvert.SerializeObject(cmData)));
                        if (breturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagerDAL", "RemoveMarker", JsonConvert.SerializeObject(cmData));

                        if (isSent)
                            tran.Commit();
                        else
                            tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        public bool UpdateReportName(string ReportName, string eventId)
        {

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    //cmd.CommandText = "UPDATE iq3CustomFields Set Title = @Title where Id = @Id";
                    cmd.CommandText = "update [t_RevRec_Interviews]  set [ReportName] = @ReportName where interviewid = @interviewid";
                    cmd.Parameters.AddWithValue("@interviewid", eventId);
                    cmd.Parameters.AddWithValue("@ReportName", ReportName);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "UpdateReportName", _tenantId));
                    return Convert.ToBoolean(cmd.ExecuteNonQuery());
                }
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        public int AddSubMarker(CustomMarkersData customMarkerData)
        {
            try
            {
                var serverId = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomMarker.INQ_CUSTOM_MARKER_ADD;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@UserNum", customMarkerData.UserNum);
                    cmd.Parameters.AddWithValue("@Marker", customMarkerData.Markers);
                    cmd.Parameters.AddWithValue("@ParentId", customMarkerData.ParentId);
                    cmd.Parameters.AddWithValue("@Level", customMarkerData.Level);
                    cmd.Parameters.AddWithValue("@IsGroup", customMarkerData.IsGroup);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "AddSubMarker", _tenantId));

                    serverId = Convert.ToInt32(cmd.ExecuteScalar());
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool breturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagerDAL", "AddSubMarker", JsonConvert.SerializeObject(customMarkerData)));
                        if (breturn) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
                return serverId;
                //return true;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public bool UpdateSubMarker(CustomMarkersData customMarkersData)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomMarker.INQ_CUSTOM_MARKER_UPDATE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;

                    cmd.Parameters.AddWithValue("@Id", customMarkersData.Id);
                    cmd.Parameters.AddWithValue("@MarkerText", customMarkersData.Markers);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "UpdateSubMarker", _tenantId));

                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool breturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagerDAL", "UpdateSubMarker", JsonConvert.SerializeObject(customMarkersData)));
                        if (breturn) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public bool RemoveSubMarker(int id)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomMarker.INQ_CUSTOM_MARKER_REMOVE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Id", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "RemoveSubMarker", _tenantId));
                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool breturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagerDAL", "RemoveSubMarker", id.ToString()));
                        if (breturn) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public bool AddEditMarkerNote(int id, string markerNote)
        {
            try
            {
                var serverId = 0;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomMarker.INQ_CUSTOM_MARKER_Note_ADD_UPDATE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@MarkerId", id);
                    cmd.Parameters.AddWithValue("@NoteTitle", markerNote);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "AddEditMarkerNote", _tenantId));

                    serverId = Convert.ToInt32(cmd.ExecuteScalar());
                    if (SiteConfig.RevSyncEnabled)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("id", Convert.ToString(id));
                        _dic.Add("markerNote", Convert.ToString(markerNote));

                        bool breturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagerDAL", "AddEditMarkerNote", JsonConvert.SerializeObject(_dic)));
                        if (breturn) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
                return true;
                //return true;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public bool RemoveMarkerNote(int id)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CustomMarker.INQ_CUSTOM_MARKER_Note_DELETE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@MarkerId", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "RemoveMarkerNote", _tenantId));
                    cmd.ExecuteNonQuery();
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool breturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagerDAL", "RemoveMarkerNote", id.ToString()));
                        if (breturn) tran.Commit(); else tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion

        #region Simple User Rights
        //

        public bool EnableDisableSimpleUserRights(int usernum , int selecttype, int revsyncUserNum )
        {
            try
            {
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.UserManagement.SIMPLE_USER_RIGHTS_ENABLE_DISABLE;//"SELECT  UserNum, UserType, UserID, UserPW, UserName, Status FROM t_Account WHERE Ext=0";// AND Status = 1
                        conn.Open();
                        SqlTransaction tran = conn.BeginTransaction();
                        cmd.Transaction = tran;
                        cmd.Parameters.AddWithValue("@UserNum", usernum);
                        cmd.Parameters.AddWithValue("@selecttype", selecttype);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "EnableDisableSimpleUserRights", _tenantId));

                    cmd.ExecuteNonQuery();
                    if(SiteConfig.RevSyncEnabled)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("UserNum", Convert.ToString(usernum));
                        _dic.Add("SelectType", Convert.ToString(selecttype));

                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagerDAL", "EnableDisableSimpleUserRights", JsonConvert.SerializeObject(_dic)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        Dictionary<string, string> _dic = new Dictionary<string, string>();
                        _dic.Add("UserNum", Convert.ToString(usernum));
                        _dic.Add("SelectType", Convert.ToString(selecttype));

                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagerDAL", "EnableDisableSimpleUserRights", JsonConvert.SerializeObject(_dic));

                        if (isSent)
                            tran.Commit();
                        else
                            tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                    }
                }
            }
            catch (Exception ex)
            {
                return false;
            }
            return true;
        }

        public List<AssignedSimpleUserRights> getAssignedSimpleUserRights(int UserNum)
        {
            string s = null;
            List<AssignedSimpleUserRights> assignedRights = null;
            AssignedSimpleUserRights rights = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.SIMPLE_USER_RIGHTS_GET_ASSIGNED_RIGHTS;//"SELECT  UserNum, UserType, UserID, UserPW, UserName, Status FROM t_Account WHERE Ext=0";// AND Status = 1
                    cmd.Parameters.AddWithValue("@MAINUSERNUM", UserNum);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "getAssignedSimpleUserRights", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            assignedRights = new List<AssignedSimpleUserRights>();
                            while (dr.Read())
                            {
                                rights = new AssignedSimpleUserRights();
                                                               
                                string value = Convert.ToString(dr["AccessRight"]);

                                if (value != "**********")
                                {
                                    for (int i = 0; i < value.Length; i++)
                                    {
                                        if (value.Substring(i, 1) == "1")
                                        {
                                            switch (i)
                                            {
                                                case 0:
                                                    rights.IsAllAssignedForUser = true;
                                                    break;
                                                case 1:
                                                    rights.IsIRLiteAssignedForUser = true;
                                                    break;
                                                case 2:
                                                    rights.IsMonitorAssignedForUser = true;
                                                    break;
                                                case 3:
                                                    rights.IsSearchAssignedForUser = true;
                                                    break;
                                                case 4:
                                                    rights.IsEvaluationAssignedForUser = true;
                                                    break;
                                                case 5:
                                                    break;
                                                case 6:
                                                    rights.IsEvaluationReportsAssignedForUser = true;
                                                    break;
                                                case 7:
                                                    rights.IsAdvanceReportsAssignedForUser = true;
                                                    break;
                                                case 8:
                                                    rights.IsIRFullAssignedForUser = true;
                                                    break;
                                                case 9:
                                                    rights.IsSaveAndEmail = true;
                                                    break;
                                                case 10:
                                                    rights.IsSetUpAssignedForUser = true;
                                                    break;

                                            }
                                        }
                                        if (rights.IsAllAssignedForUser)
                                        {
                                            break;
                                        }

                                    }
                                }
                                assignedRights.Add(rights);

                            }

                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return assignedRights;
        }
        public List<User> getSimpleUserUnAssignedChannels(int uId, string accessRight)
        {
            List<User> users = null;
            User user = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GET_SIMPLE_USER_RIGHTS_CHANNELS;//"SELECT  UserNum, UserType, UserID, UserPW, UserName, Status FROM t_Account WHERE Ext=0";// AND Status = 1
                    cmd.Parameters.AddWithValue("@UserNum", uId);
                    //cmd.Parameters.AddWithValue("@Searchtype", accessRight);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "getSimpleUserUnAssignedChannels", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            users = new List<User>();
                            while (dr.Read())
                            {
                                user = new User();
                                user.Ext = Convert.ToString(dr["Ext"]);
                                user.ExtName = Convert.ToString(dr["ExtName"]);
                                user.IsAssignedForChannel = Convert.ToString(dr["ISAssignedUser"]);
                                user.ChannelType = Convert.ToInt32( dr["Channeltype"]);
                                user.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                                user.UserName = Convert.ToString(dr["UserName"]);
                                if (user.IsAssignedForChannel == "" || user.IsAssignedForChannel == null || user.IsAssignedForChannel.Length < 5)
                                {
                                    user.IsAssignedForChannel = "**********";
                                }
                                if(accessRight== "IRLIGHT"|| accessRight== "IRFULL") {
                                    if(user.ChannelType != 1)
                                        users.Add(user);
                                }
                                else
                                    users.Add(user);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return users;
        }


        public bool SaveSimpleUserRightsChannels(User user)
        {
            try
            {

                for (int i = 0; i < user.simpleuserrightsforuser.Count; i++)
                {
                    using (var conn = DALHelper.GetConnection(_tenantId))
                    using (var cmd = conn.CreateCommand())
                    {
                        conn.Open();
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.UserManagement.SIMPLE_USER_RIGHTS_INSERT;//"SELECT  UserNum, UserType, UserID, UserPW, UserName, Status FROM t_Account WHERE Ext=0";// AND Status = 1
                        SqlTransaction tran = conn.BeginTransaction();
                        cmd.Transaction = tran;
                        cmd.Parameters.AddWithValue("@UserNum", user.UserNum);
                        cmd.Parameters.AddWithValue("@ext", user.simpleuserrightsforuser[i].Ext);
                        cmd.Parameters.AddWithValue("@Searchtype", user.AccessRight);
                        cmd.Parameters.AddWithValue("@VALUE", 1);
                        cmd.Parameters.AddWithValue("@Accessrightsforuser", user.simpleuserrightsforuser[i].AccessRight);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "SaveSimpleUserRightsChannels", _tenantId));

                        cmd.ExecuteNonQuery();
                        if(SiteConfig.RevSyncEnabled)
                        {
                            bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "UserManagerDAL", "SaveSimpleUserRightsChannels", JsonConvert.SerializeObject(user)));
                            if (bReturn) tran.Commit(); else tran.Rollback();
                        }
                        else if (SiteConfig.IsMTEnable)
                        {
                            bool isSent = DALHelper.SendMessageToHub(_tenantId, "UserManagerDAL", "SaveSimpleUserRightsChannels", JsonConvert.SerializeObject(user));

                            if (isSent)
                                tran.Commit();
                            else
                                tran.Rollback();
                        }
                        else
                        {
                            tran.Commit();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return false;
            }
            return true;
        }
        #endregion

        #region  #region QB Functionalities
        public string GetSystemSerialTag()
        {
            var systemSerialTage = "";
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.GET_SERIAL_KEY;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetSystemSerialTag", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                systemSerialTage = Convert.ToString(dr["SerialKey"]);
                            }
                        }
                    }
                }
                return systemSerialTage;
            }
            catch (Exception Ex)
            {
                return "";
            }

        }
        #endregion

        #region Channel Count
        public int GetActiveChannelCount()
        {
            int activeChannelCount = 0;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT COUNT(Ext) AS ActiveChannelCount FROM t_ExtInfo WHERE Status = 1";
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetActiveChannelCount", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                activeChannelCount = Convert.ToInt32(dr["ActiveChannelCount"]);
                            }
                        }
                    }
                }
                return activeChannelCount;
            }
            catch (Exception Ex)
            {
                return 0;
            }
        }
        #endregion

        #region Custom Fields
        public List<CustomField> GetCustomFields(int userNum)
        {
            List<CustomField> customFields = null;
            CustomField customField = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.IQ3.CUSTOMFIELDS_GET_BY_USER;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);

                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "GetCustomFields", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            customFields = new List<CustomField>();
                            while (dr.Read())
                            {
                                customField = new CustomField();
                                customField.Id = Convert.ToInt32(dr["Id"]);
                                customField.Title = Convert.ToString(dr["Title"]);
                                customField.Order = Convert.ToInt32(dr["Order"]);
                                customField.UserNum= Convert.ToInt32(dr["UserNum"]);
                                customField.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                customField.IsDeleted = Convert.ToBoolean(dr["IsDeleted"]);
                                customFields.Add(customField);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return customFields;
        }

        public bool UpdateCustomField(CustomField customField)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "UPDATE iq3CustomFields Set Title = @Title where Id = @Id";
                    cmd.Parameters.AddWithValue("@Id", customField.Id);
                    cmd.Parameters.AddWithValue("@Title", customField.Title);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManager, "UpdateCustomField", _tenantId));
                    return Convert.ToBoolean(cmd.ExecuteNonQuery());
                }
            }
            catch (Exception ex)
            {
                return false;
            }

        }
        #endregion

    }
    public static class StringExtensions
    {
        public static bool ContainsAny(this string str, params string[] values)
        {
            if (!string.IsNullOrEmpty(str) || values.Length > 0)
            {
                foreach (string value in values)
                {
                    if (str.Contains(value))
                        return true;
                }
            }

            return false;
        }
    }
}
