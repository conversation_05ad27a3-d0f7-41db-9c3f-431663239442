﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace RevCord.Util
{
    public static class UriHelper
    {
        public static Uri AttachQueryStringParameter(this Uri url, string parameterName, string parameterValue)
        {
            UriBuilder builder = new UriBuilder(url);
            string query = builder.Query;
            if (query.Length > 1)
                query = query.Substring(1);// remove the '?' character in front of the query string

            string parameterPrefix = parameterName + "=";

            string encodedParameterValue = Uri.EscapeDataString(parameterValue);

            string newQuery = Regex.Replace(query, parameterPrefix + "[^\\&]*", parameterPrefix + encodedParameterValue);
            if (newQuery == query)
            {
                if (newQuery.Length > 0)
                    newQuery += "&";
                newQuery = newQuery + parameterPrefix + encodedParameterValue;
            }
            builder.Query = newQuery;

            return builder.Uri;
        }

        
    }
}
