﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.SqlClient;
using System.Data;
using RevCord.DataContracts.SurveyEntities;
using System.Xml.Linq;
using RevCord.DataContracts;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.Util;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;
using Newtonsoft.Json;

namespace RevCord.DataAccess
{
    public class SurveyDAL
    {
        private int _tenantId;
        public SurveyDAL(int tenantId)
        {
            this._tenantId = tenantId;
        }

        #region Survey

        /// <summary>
        /// Returns Surveys only for Dropdown List for Reports
        /// </summary>
        /// <returns></returns>

        private EvaluationRequest _evalRequest = new EvaluationRequest();
        private EvaluationResponse _evalResponse = null;

        public List<Survey> GetSurveysOnly()
        {
            List<Survey> surveys = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETLIST;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetSurveysOnly", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys.FindAll(s => s.IsPublished == true);
        
        }

        /// <summary>
        /// Returns Survey only without Question and Sections
        /// </summary>
        /// <returns></returns>
        public List<Survey> GetSurveys()
        {
            List<Survey> surveys = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETLIST;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetSurveys", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys;
        }

        public void CreateSurvey(Survey survey)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_INSERT;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Title", survey.Name);                                                       
                    cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);  //TODO                    		
                    cmd.Parameters.AddWithValue("@StartDate", survey.StartDate);                                         
                    cmd.Parameters.AddWithValue("@EndDate", survey.EndDate);                                               
                    cmd.Parameters.AddWithValue("@Description", survey.Description);                                   
                    cmd.Parameters.AddWithValue("@CreatedDate", survey.CreatedDate);                                   
                    cmd.Parameters.AddWithValue("@ActiveAfterEndDate", survey.ActiveAfterEndDate);              
                    cmd.Parameters.AddWithValue("@IsPublished", survey.IsPublished);                                   
                    cmd.Parameters.AddWithValue("@CreatedBy", survey.CreatedBy);                                         
                    cmd.Parameters.AddWithValue("@HasSections", survey.HasSections);                         		
                    cmd.Parameters.AddWithValue("@IsDeleted", survey.IsDeleted);
                    cmd.Parameters.Add("@SectionId", SqlDbType.Int).Direction = ParameterDirection.Output;

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "CreateSurvey", _tenantId));

                    survey.Id = Convert.ToInt32(cmd.ExecuteScalar());
                    survey.Sections = new List<SurveySection>();
                    int RevSyncServerSurveyId = 0;
                    int RevSyncServerSurveySectionId = 0;
                    if (SiteConfig.RevSyncEnabled)
                    {
                        string sReturn = DALHelper.RevsyncAPICall(_tenantId, "SurveyDAL", "CreateSurvey", JsonConvert.SerializeObject(survey));
                        RevSyncServerSurveyId = Convert.ToInt32(sReturn.Split('&')[0]);
                        RevSyncServerSurveySectionId = Convert.ToInt32(sReturn.Split('&')[1]);

                        survey.RevSyncSurveyId = RevSyncServerSurveyId;
                        SurveySection eQSection = new SurveySection { Id = (int)cmd.Parameters["@SectionId"].Value, RevSyncId = RevSyncServerSurveySectionId };
                        survey.Sections.Add(eQSection);
                        if (RevSyncServerSurveyId > 0)
                        {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE emSurvey SET [RevSyncServerID] = " + RevSyncServerSurveyId + " Where Id = " + survey.Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.Survey, "CreateSurvey", _tenantId));

                                Updatecmd.ExecuteNonQuery();
                            }

                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE [emSurveySection] SET [RevSyncServerID] = "+ RevSyncServerSurveySectionId  + " Where [SurveyId] = " + survey.Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.Survey, "CreateSurvey", _tenantId));
                                Updatecmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            tran.Rollback();
                        }
                    }
					else if (SiteConfig.IsMTEnable)
                    {

                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "SurveyDAL", "CreateSurvey", JsonConvert.SerializeObject(survey));

                        if (isSent)
                        {
                            SurveySection eQSection = new SurveySection { Id = (int)cmd.Parameters["@SectionId"].Value };
                            survey.Sections.Add(eQSection);
                            tran.Commit();
                        }
                        else
                            tran.Rollback();
                    }
                    else
                    {
                    SurveySection eQSection = new SurveySection { Id = (int)cmd.Parameters["@SectionId"].Value };
                    survey.Sections.Add(eQSection);
                        tran.Commit();
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public void UpdateSurvey(Survey survey)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_UPDATE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Id", survey.Id);
                    cmd.Parameters.AddWithValue("@Title", survey.Name);
                    cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@StartDate", survey.StartDate);
                    cmd.Parameters.AddWithValue("@EndDate", survey.EndDate);
                    cmd.Parameters.AddWithValue("@Description", survey.Description);
                    cmd.Parameters.AddWithValue("@CreatedDate", survey.CreatedDate);
                    cmd.Parameters.AddWithValue("@ActiveAfterEndDate", survey.ActiveAfterEndDate);
                    cmd.Parameters.AddWithValue("@IsPublished", survey.IsPublished);
                    cmd.Parameters.AddWithValue("@CreatedBy", survey.CreatedBy);
                    cmd.Parameters.AddWithValue("@HasSections", survey.HasSections);
                    cmd.Parameters.AddWithValue("@IsDeleted", survey.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateSurvey", _tenantId));
                    survey.Id = Convert.ToInt32(cmd.ExecuteScalar());

                    if (SiteConfig.RevSyncEnabled) {
                        int RevSyncServerSurveyId = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantId, "SurveyDAL", "UpdateSurvey", JsonConvert.SerializeObject(survey)));    
                        if (RevSyncServerSurveyId > 0) {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE emSurvey SET [RevSyncServerID] = " + RevSyncServerSurveyId + " Where Id = " + survey.Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.Survey, "UpdateSurvey", _tenantId));
                                Updatecmd.ExecuteNonQuery();
                            }
                        } else tran.Rollback();
                    } else if (SiteConfig.IsMTEnable) {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "SurveyDAL", "UpdateSurvey", JsonConvert.SerializeObject(survey));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    } else tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }
        
        public List<Survey> DeleteAndGetSurveys(int surveyId,int revsyncSurveyId)
        {
            List<Survey> surveys = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_DELETE_N_GET;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@SurveyId ", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "DeleteAndGetSurveys", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader()) surveys = ORMapper.MapSurveys(dr);
                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "SurveyDAL", "DeleteAndGetSurveys", JsonConvert.SerializeObject(revsyncSurveyId)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    } else if (SiteConfig.IsMTEnable) {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "SurveyDAL", "DeleteAndGetSurveys", JsonConvert.SerializeObject(surveyId));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys;
        }
        
        public List<Survey> PublishAndGetSurveys(int surveyId, int revsyncSurveyId)
        {
            List<Survey> surveys = null;
            // List<Survey> surveyCalls = null;
            List<CallEvaluation> callEvaluations = new List<CallEvaluation>();
       
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_PUBLISH_N_GET;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@SurveyId ", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "PublishAndGetSurveys", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader()) surveys = ORMapper.MapSurveys(dr);

                    if (SiteConfig.RevSyncEnabled)
                    {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "SurveyDAL", "PublishAndGetSurveys", JsonConvert.SerializeObject(revsyncSurveyId)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
					else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "SurveyDAL", "PublishAndGetSurveys", JsonConvert.SerializeObject(surveyId));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();
                    EvaluatedCallsDetailBySurveyId(surveyId); // For QA-Published Forms Edit/ Delete ARIVU
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys;
        }
        #region Published Forms EDIT
        //Arivu
        public void EvaluatedCallsDetailBySurveyId(int surveyId)
        {
            List<CallEvaluation> callEvaluations = new List<CallEvaluation>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_GETALL_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "EvaluatedCallsDetailBySurveyId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluations = ORMapper.MapCallEvaluationsForPublishedForm(dr);

                        foreach(var callEval in callEvaluations)
                        {
                            if (callEval.StatusId == 3 || callEval.StatusId == 4)
                            {
                                UpdateStatusIdInCallEvaluation(surveyId);
                            }
                        }
                    }
                }
            }
            catch(Exception ex)
            { throw ex; }
            
        }

        public void UpdateStatusIdInCallEvaluation(int surveyId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.STATUSID_UPDATE_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateStatusIdInCallEvaluation", _tenantId));

                    cmd.ExecuteNonQuery();
                   
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public void UpdateIsPublished(int surveyId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_UPDATE_ISPUBLISH;
                    cmd.Parameters.AddWithValue("@Id", surveyId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateIsPublished", _tenantId));
                    cmd.ExecuteNonQuery();
                }
            }
            catch(Exception ex)
            { throw ex; }
        }
        public List<Option> GetQuestionOptionsByQID(long QID)
        {
            List<Option> options = new List<Option>();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_OPTIONS_GETBYQID;
                    cmd.Parameters.AddWithValue("@QuestionId", QID);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetQuestionOptionsByQID", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        options = ORMapper.MapOptions(dr);
                    }
                }
            }
            catch(Exception ex) { throw ex; }
            return options;
        }

       
        #endregion
        public Survey GetSurveyWithDetails(int surveyId)
        {
            Survey survey = null;
            List<Survey> surveys = null;
            List<SurveySection> sections = new List<SurveySection>();
            List<Question> questions = new List<Question>();
            List<Option> options = new List<Option>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETDETAIL;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetSurveyWithDetails", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr);
                        survey = surveys == null || surveys.Count == 0 ? new Survey() : surveys[0];
                        dr.NextResult();
                        sections = ORMapper.MapSections(dr);//Section Table
                        dr.NextResult();
                        questions = ORMapper.MapQuestions(dr); //Question Table
                        dr.NextResult();
                        options = ORMapper.MapOptions(dr); //Question Option Table

                        survey.Sections = sections;
                        survey.Questions = questions;
                        if (survey.Questions != null)
                            survey.Questions.ForEach(q => q.Options = options.FindAll(o => o.QuestionId == q.Id));
                    }

                }
            }
            catch (Exception ex) { throw ex; }
            return survey;

        }

        public bool IsSurveyExist(string surveyTitle)
        {
            try
            {
                bool IsSurveyExists = false;
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = string.Format("select * from emSurvey where Title = '{0}' and IsDeleted = 0",surveyTitle);
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "IsSurveyExist", _tenantId));

                    IsSurveyExists = Convert.ToInt32(cmd.ExecuteScalar()) > 0 ? true : false;
                    if(SiteConfig.RevSyncEnabled)
                    {
                        bool bReturn = Convert.ToBoolean(DALHelper.RevsyncAPICall(_tenantId, "SurveyDAL", "IsSurveyExist", JsonConvert.SerializeObject(surveyTitle)));
                        if (bReturn) tran.Commit(); else tran.Rollback();
                    }
                    else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "SurveyDAL", "IsSurveyExist", JsonConvert.SerializeObject(surveyTitle));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();
                    return IsSurveyExists;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion


        #region Section

        public List<SurveySection> GetSectionsBySurveyId(int surveyId)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_GETBYSURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetSectionsBySurveyId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        public List<SurveySection> CreateAndGetSections(SurveySection surveyGroup)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_INSERT_N_GET;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@Title", surveyGroup.Title);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyGroup.SurveyId);
                    cmd.Parameters.AddWithValue("@CreatedDate", surveyGroup.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", surveyGroup.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "CreateAndGetSections", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                    int RevSyncServerSectionId = 0;
                    if (SiteConfig.RevSyncEnabled)
                    {
                        RevSyncServerSectionId = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantId, "SurveyDAL", "CreateAndGetSections", JsonConvert.SerializeObject(surveyGroup)));
                        
                        if (RevSyncServerSectionId > 0)
                        {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE emSurveySection SET [RevSyncServerID] = " + RevSyncServerSectionId + " Where Id = " + sections[0].Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.Survey, "CreateAndGetSections", _tenantId));

                                Updatecmd.ExecuteNonQuery();
                            }
                        }
                        else tran.Rollback();
                    }
					else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "SurveyDAL", "CreateAndGetSections", JsonConvert.SerializeObject(surveyGroup));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        public List<SurveySection> UpateAndGetSections(SurveySection surveyGroup)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_UPDATE_N_GET;
                    cmd.Parameters.AddWithValue("@Id", surveyGroup.Id);
                    cmd.Parameters.AddWithValue("@Title", surveyGroup.Title);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyGroup.SurveyId);
                    cmd.Parameters.AddWithValue("@CreatedDate", surveyGroup.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", surveyGroup.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpateAndGetSections", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        public List<SurveySection> DeleteAndGetSectionsBySurveyId(int sectionId, int surveyId)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_DELETE_N_GET;
                    cmd.Parameters.AddWithValue("@Id ", sectionId);
                    cmd.Parameters.AddWithValue("@SurveyId ", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "DeleteAndGetSectionsBySurveyId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }

        public List<SurveySection> AssignUnAssignQuestionsAndGetSections(int surveyId, int sectionId, string assignedQuestions, string unassignedQuestions)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_UPDATESECTIONS;
                    cmd.Parameters.AddWithValue("SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("SectionId", sectionId);
                    cmd.Parameters.AddWithValue("Selected", assignedQuestions);
                    cmd.Parameters.AddWithValue("DeSelected", unassignedQuestions);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "AssignUnAssignQuestionsAndGetSections", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }


                    return sections;
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }
        
        #endregion


        #region Question

        public List<Question> GetQuestionsBySurveyId(int surveyId)
        {
            List<Question> questions = null;
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_GETBYSURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "GetQuestionsBySurveyId", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        questions = ORMapper.MapQuestions(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return questions;
        }

        public long CreateQuestion(Question sQuestion)
        {
            try
            {
                XElement xOptions = this.CreateOptionsXML(sQuestion.Options);
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_INSERT;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@SurveyId", sQuestion.SurveyId);
                    cmd.Parameters.AddWithValue("@SectionId", sQuestion.SectionId);
                    cmd.Parameters.AddWithValue("@Title", sQuestion.Statement);
                    cmd.Parameters.AddWithValue("@QuestionTypeId", sQuestion.TypeId);
                    cmd.Parameters.AddWithValue("@Ordering", sQuestion.Ordering);
                    cmd.Parameters.AddWithValue("@Options", xOptions.ToString());
                    cmd.Parameters.AddWithValue("@CreatedDate", sQuestion.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", sQuestion.IsDeleted);
                    cmd.Parameters.AddWithValue("@QuestionId", 0);
                    cmd.Parameters["@QuestionId"].Direction = ParameterDirection.Output;
                    //cmd.Parameters.Add("@QuestionId", SqlDbType.BigInt).Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "CreateQuestion", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (sQuestion.Options != null && sQuestion.Options.Count != 0)
                        {
                            while (dr.Read())
                            {
                                foreach (var item in sQuestion.Options)
                                {
                                    if (item.Ordering == (int)dr["Ordering"])
                                        item.Id = (long)dr["Id"];
                                }
                            }
                        }
                    }
                    sQuestion.Id = Convert.ToInt64(cmd.Parameters["@QuestionId"].Value.ToString());

                    int RevSyncServerQuestionId = 0;
                    if (SiteConfig.RevSyncEnabled)
                    {
                        RevSyncServerQuestionId = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantId, "SurveyDAL", "CreateQuestion", JsonConvert.SerializeObject(sQuestion)));
                        sQuestion.RevSyncQuestionId = RevSyncServerQuestionId;
                        if (RevSyncServerQuestionId > 0)
                        {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE emQuestion SET [RevSyncServerID] = " + RevSyncServerQuestionId + " Where Id = " + sQuestion.Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.Survey, "CreateQuestion", _tenantId));
                                Updatecmd.ExecuteNonQuery();
                            }
                        }
                        else tran.Rollback();
                    }
					else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "SurveyDAL", "CreateQuestion", JsonConvert.SerializeObject(sQuestion));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();
                    return sQuestion.Id;
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public long UpdateQuestion(Question eQuestion)
        {
            try
            {
                XElement xOptions = this.CreateOptionsXML(eQuestion.Options);
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_UPDATE;
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    cmd.Parameters.AddWithValue("@QuestionId", eQuestion.Id);
                    cmd.Parameters.AddWithValue("@SurveyId", eQuestion.SurveyId);
                    cmd.Parameters.AddWithValue("@SectionId", eQuestion.SectionId);
                    cmd.Parameters.AddWithValue("@Title", eQuestion.Statement);
                    cmd.Parameters.AddWithValue("@QuestionTypeId", eQuestion.TypeId);
                    cmd.Parameters.AddWithValue("@Ordering", eQuestion.Ordering);
                    cmd.Parameters.AddWithValue("@Options", xOptions.ToString());
                    cmd.Parameters.AddWithValue("@CreatedDate", eQuestion.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", eQuestion.IsDeleted);
                    //cmd.Parameters.Add("@QuestionId", SqlDbType.BigInt).Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateQuestion", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //eQuestion.Id = (long)cmd.Parameters["@QuestionId"].Value;
                        if (eQuestion.Options != null && eQuestion.Options.Count != 0)
                        {
                            while (dr.Read())
                            {
                                foreach (var item in eQuestion.Options)
                                {
                                    if (item.Ordering == (int)dr["Ordering"])
                                        item.Id = (long)dr["Id"];
                                }
                            }
                        }
                    }
                    int RevSyncServerQuestionId = 0;
                    if (SiteConfig.RevSyncEnabled)
                    {
                        RevSyncServerQuestionId = Convert.ToInt32(DALHelper.RevsyncAPICall(_tenantId, "SurveyDAL", "UpdateQuestion", JsonConvert.SerializeObject(eQuestion)));
                        if (RevSyncServerQuestionId > 0)
                        {
                            tran.Commit();
                            using (var Updatecmd = conn.CreateCommand())
                            {
                                Updatecmd.CommandType = CommandType.Text;
                                Updatecmd.CommandText = "UPDATE emQuestion SET [RevSyncServerID] = " + RevSyncServerQuestionId + " Where Id = " + eQuestion.Id;
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(Updatecmd), Originator.Survey, "UpdateQuestion", _tenantId));
                                Updatecmd.ExecuteNonQuery();
                            }
                        }
                        else tran.Rollback();
                    }
					else if (SiteConfig.IsMTEnable)
                    {
                        bool isSent = DALHelper.SendMessageToHub(_tenantId, "SurveyDAL", "UpdateQuestion", JsonConvert.SerializeObject(eQuestion));
                        if (isSent) tran.Commit(); else tran.Rollback();
                    }
                    else tran.Commit();

                    return eQuestion.Id;
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public long UpdateQuestionsOrder(List<Question> eRequest)
        {
            try
            {
                XElement xQuestions = this.CreateQuestionsXML(eRequest);
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_UPDATEORDER;
                    cmd.Parameters.AddWithValue("Questions", xQuestions.ToString());
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateQuestionsOrder", _tenantId));
                    return Convert.ToInt64(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public void DeleteQuestion(long id)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_DELETE;
                    //cmd.Parameters.AddWithValue("@SurveyId ", eQuestion.SurveyId);
                    cmd.Parameters.AddWithValue("@QuestionId", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "DeleteQuestion", _tenantId));
                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public long AssignUnAssignQuestionsToSection(int surveyId, int sectionId, string assignedQuestions, string unassignedQuestions)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_UPDATESECTIONS;
                    cmd.Parameters.AddWithValue("SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("SectionId", sectionId);
                    cmd.Parameters.AddWithValue("Selected", assignedQuestions);
                    cmd.Parameters.AddWithValue("DeSelected", unassignedQuestions);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "AssignUnAssignQuestionsToSection", _tenantId));
                    return Convert.ToInt64(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }

        public bool UpdateQuestionSection(int questionId, int sectionId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = string.Format("update emQuestion Set SectionId = {0} where Id = {1}", sectionId, questionId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Survey, "UpdateQuestionSection", _tenantId));
                    return Convert.ToInt32(cmd.ExecuteScalar()) > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Utility Methods

        private XElement CreateOptionsXML(List<Option> aOptions)
        {
            try
            {
                XElement options = new XElement("Options");
                foreach (var option in aOptions)
                {
                    options.Add(new XElement("Option",
                                    new XAttribute("Id", option.Id),
                                    new XAttribute("QuestionId", option.QuestionId),
                                    new XAttribute("Title", option.Title),
                                    new XAttribute("Ordering", option.Ordering),
                                    new XAttribute("CreatedDate", DateTime.Now),
                                    new XAttribute("IsDeleted", option.IsDeleted),
                                    new XAttribute("Score", option.Score)
                                ));
                }
                return options;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private XElement CreateQuestionsXML(List<Question> eQuestions)
        {
            try
            {
                XElement questions = new XElement("Questions");
                foreach (var qItem in eQuestions)
                {
                    questions.Add(new XElement("Question",
                                    new XAttribute("Id", qItem.Id),
                                    new XAttribute("SurveyId", qItem.SurveyId),
                                    new XAttribute("Ordering", qItem.Ordering),
                                    new XAttribute("IsDeleted", qItem.IsDeleted)
                                ));
                }
                return questions;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
                
        #endregion


    }
}
