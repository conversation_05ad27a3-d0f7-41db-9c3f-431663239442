﻿using System;

namespace RevCord.DataContracts.IWBEntities
{
    public class UserWorkHistory
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int OrganizationId { get; set; }
        public string OrganizationName { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int Rating { get; set; }
        public string FileName { get; set; }

        public int? JobId { get; set; }

        public int CreatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public int? LastModifiedBy { get; set; }

        public string Title { get; set; }
        public string StartDate { get; set; }
        public string EndDate { get; set; }
        public string Location { get; set; }
    }

    public class JobWelder
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    public class UserInfoData
    {
        public int Id { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
        public string SSN { get; set; }
        public string StencilNumber { get; set; }
        public string State { get; set; }
        public string Role { get; set; }
        public string City { get; set; }
        public string Phone { get; set; }
        public DateTime DOB { get; set; }
    }

}