﻿using RevCord.DataContracts.IQ3InspectionEntities;
using RevCord.DataContracts.MessageBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class InspectionResponse: RequestBase
    {
        public List<Inspection> Inspections { get; set; }
        public InspectionTemplate InspectionTemplate { get; set; }
        public List<InspectionTemplate> InspectionTemplates { get; set; }
        public List<PreInspection> PreInspections { get; set; }
        public List<Section> Sections { get; set; }
        public Marker Marker { get; set; }
        public List<Marker> Markers { get; set; }
        public bool FlagStatus { get; set; }
        public int MarkerId { get; set; }
        public int SectionId { get; set; }

        public Inspection Inspection { get; set; }

        public AcknowledgeType Acknowledge { get; set; }

        public InspectionTitle InspectionTitle { get; set; }

        public List<MarkerSection> MarkerSections { get; set; }

        public int RowsAffected;

        public GraphicMarkerDetail GraphicMarker { get; set; }

        public List<IQ3InspectionParameter> IQ3InspectionParameters { get; set; }

        public RVIMessage RVIMessage { get; set; }
        public List<AutoReportRecipient> AutoReportRecipients { get; set; }

        public List<PreInspection> CustomFields { get; set; }
        public List<PreInspectionGroup> PreInspectionGroups { get; set; }

    }
}
