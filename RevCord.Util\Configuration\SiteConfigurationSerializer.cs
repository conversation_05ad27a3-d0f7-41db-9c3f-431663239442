﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace RevCord.Util.Configuration
{
    public class SiteConfigurationSerializer
    {
        public static T Deserialize<T>(string xmlFilePath) where T : class
        {
            XmlSerializer objXmlSerializer = new XmlSerializer(typeof(T));

            using (StreamReader objReader = new StreamReader(xmlFilePath))
            {
                return (T)objXmlSerializer.Deserialize(objReader);
            }
        }

        public static void Serialize<T>(T objSerialize, string xmlFilePath)
        {
            XmlSerializer xmlSerializer = new XmlSerializer(objSerialize.GetType());

            using (StreamWriter objWriter = new StreamWriter(xmlFilePath))
            {
                xmlSerializer.Serialize(objWriter, objSerialize);
            }
        }
    }
}