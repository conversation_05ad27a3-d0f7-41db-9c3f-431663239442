﻿using RevCord.DataContracts.RoleManagement;
using RevCord.DataContracts.UserManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class RoleManagementResponse
    {
        public bool DBResponse { get; set; }

        public List<Role> Roles { get; set; }

        public Role Role { get; set; }

        public List<MMSPermission> Permissions { get; set; }

        public List<RolePermission> RolePermissions { get; set; }
        public List<RoleChannelPermission> RoleChannelPermissions { get; set; }

        public List<User> Users { get; set; }

    }
}
