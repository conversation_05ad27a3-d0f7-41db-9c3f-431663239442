﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.Messages;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataAccess;
using RevCord.DataContracts.UserManagement;
using System.Data.SqlClient;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;

namespace RevCord.BusinessLogic
{
    public class InquireGroupManager
    {
        public InquireGMResponse GetGroups(InquireGMRequest gmRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireGroup, "GetGroups", gmRequest.TenantId, "GetGroups function has been called successfully."));
                List<UserGroup> lGroups = null;
                UserGroup groups = null;
                lGroups = new InquireGroupManagerDAL(gmRequest.TenantId).GetGroups();
                return new InquireGMResponse { Group = lGroups };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireGroup, "GetGroups", gmRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireGroup, "GetGroups", gmRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public InquireGMResponse GetInquireGroupUsers(InquireGMRequest gmRequest, int Id)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireGroup, "GetInquireGroupUsers", gmRequest.TenantId, "GetInquireGroupUsers function has been called successfully."));
                int GroupNum = Id;
                List<InquireUserInfo> lGVData = null;
                lGVData = new InquireGroupManagerDAL(gmRequest.TenantId).GetInqGroupUsers(GroupNum);
                if (gmRequest.IsOnlyIQ3ModeEnabled) {
                    foreach (var data in lGVData)
                    {
                        data.UserName = data.UserName.Replace("Agent", "User");
                    }
                }
                return new InquireGMResponse { userinfoData = lGVData };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireGroup, "GetInquireGroupUsers", gmRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireGroup, "GetInquireGroupUsers", gmRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public InquireGMResponse InqCreateGroup(InquireGMRequest gmRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireGroup, "InqCreateGroup", gmRequest.TenantId, "InqCreateGroup function has been called successfully."));

                var dal = new InquireGroupManagerDAL(gmRequest.TenantId);
                InquireGMResponse sResponse = new InquireGMResponse();
                sResponse.GlobalGroups = dal.InqCreateGroup(gmRequest.GlobalGroup);
                sResponse.FlagStatus = (gmRequest.GlobalGroup.Id != -1);
                return sResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireGroup, "InqCreateGroup", gmRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireGroup, "InqCreateGroup", gmRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public InquireGMResponse GetGroupDepth(InquireGMRequest gmRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireGroup, "GetGroupDepth", gmRequest.TenantId, "GetGroupDepth function has been called successfully."));
                var dal = new InquireGroupManagerDAL(gmRequest.TenantId);
                InquireGMResponse sResponse = new InquireGMResponse();
                sResponse.GlobalGroups = dal.GetGroupDepth(gmRequest.GlobalGroup);
                sResponse.FlagStatus = (gmRequest.GlobalGroup.Id != -1);
                return sResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireGroup, "GetGroupDepth", gmRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireGroup, "GetGroupDepth", gmRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public InquireGMResponse InquireDeleteGroup(InquireGMRequest gmRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireGroup, "InquireDeleteGroup", gmRequest.TenantId, "InquireDeleteGroup function has been called successfully."));
                var dal = new InquireGroupManagerDAL(gmRequest.TenantId);
                InquireGMResponse sResponse = new InquireGMResponse();
                sResponse.GlobalGroups = dal.InqDeleteGroup(gmRequest.GlobalGroup.Id);
                sResponse.FlagStatus = (gmRequest.GlobalGroup.Id != -1);
                return sResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireGroup, "InquireDeleteGroup", gmRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireGroup, "InquireDeleteGroup", gmRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public InquireGMResponse InqSaveGroupUser(InquireGMRequest gmRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireGroup, "InqSaveGroupUser", gmRequest.TenantId, "InqSaveGroupUser function has been called successfully."));
                return new InquireGMResponse { FlagStatus = new InquireGroupManagerDAL(gmRequest.TenantId).InqSaveGroupUser(gmRequest.User.UserGroup) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireGroup, "InqSaveGroupUser", gmRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireGroup, "InqSaveGroupUser", gmRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public InquireGMResponse InqDeleteUserGroup(InquireGMRequest gmRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.InquireGroup, "InqDeleteUserGroup", gmRequest.TenantId, "InqDeleteUserGroup function has been called successfully."));
                return new InquireGMResponse { FlagStatus = new InquireGroupManagerDAL(gmRequest.TenantId).InqDeleteGroupUser(gmRequest.User.UserGroup) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.InquireGroup, "InqDeleteUserGroup", gmRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.InquireGroup, "InqDeleteUserGroup", gmRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
    }
}