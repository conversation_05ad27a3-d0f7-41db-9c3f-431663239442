﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RevCord.DataContracts.DTO
{
    public class CallInfoSearchCriteriaDTO
    {
        /************* Purpose: used for search Calls ****************/

        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public TimeSpan StartDuration { get; set; }
        public TimeSpan EndDuration { get; set; }

        /* Shift Restriction */
        public bool HasShiftRest { get; set; }
        public DateTime LoginTime { get; set; }
        public string StartTimeString { get; set; }
        /* Shift Restriction */

        public string DurationStr { get; set; }
        public int SearchRest { get; set; }
        public string SearchType { get; set; }//Custom,Random
        public bool IsRandom { get; set; }
        public int NoOfRandomCalls { get; set; }
        public bool IsPercentage { get; set; }

        //public string UseGroup { get; set; }
        public string Criteria { get; set; }

        public string AdvanceSearchData { get; set; }

        /*************************************************************************/
    }
    
    public class SimpleUserSearchCriteria
    {
        public int UserNum { get; set; }
        public int AuthNum { get; set; }
        public int Type { get; set; }
        public string AuthType { get; set; }


        public object UserId { get; set; }
    }

}
