﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.ViewModelEntities;
namespace RevCord.DataContracts.Messages
{
    public class InquireGMResponse
    {
        #region properties
        ///<summary>
        /// Determines whether the transaction is sucessfull or not..
        /// </summary>
         
        public bool FlagStatus { get; set; }

        public List<User> User { get; set; }
        public List<UserGroup> Group { get; set; }

        public List<TreeviewData> inquireDbNodes { get; set; }

        public List<InquireUserInfo> userinfoData { get; set; }

        public List<GlobalGroup> GlobalGroups { get; set; }
        #endregion
    }
}
