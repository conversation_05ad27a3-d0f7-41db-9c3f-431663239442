﻿using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.ReportEntities;
using RevCord.DataContracts.Request;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Script.Serialization;
using System.Xml.Linq;

namespace RevCord.DataAccess
{
    public class EnterpriseDAL
    {
        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("commandTimeout", 60);

        #region User Manager

        public static UserManagementResponse GetAppUsers(Recorder recorder, int uId = 1000)
        {
            List<User> users = null;
            List<UserGroup> uGroups = null;
            List<Permission> permissions = null;
            List<GlobalGroup> groups = null;
            List<TreeViewDataDTO> treeViewDataDTOs = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GETLIST;
                    cmd.Parameters.AddWithValue("@UserId", uId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetAppUsers", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsers(dr, recorder.Id, recorder.Name);
                        dr.NextResult();
                        uGroups = ORMapper.MapUserGroups(dr, recorder.Id);//User Permission Table
                        //dr.NextResult();
                        //userPermissions = ORMapper.MapPermissions(dr);//Create User Permissions
                        dr.NextResult();
                        groups = ORMapper.MapGlobalGroups(dr); //Group Table
                        dr.NextResult();
                        permissions = ORMapper.MapPermissions(dr); //Permissions Table
                        dr.NextResult();
                        treeViewDataDTOs = ORMapper.MapTreeViewDataDTOs(dr); //treeViewData Table
                        uGroups.ForEach(g =>
                        {

                            List<TreeViewDataDTO> tvdDTOs = treeViewDataDTOs.FindAll(tvd => tvd.Param1 == Convert.ToString(g.GroupNum) && tvd.IsGroup == false);
                            if (tvdDTOs != null && tvdDTOs.Count != 0)
                                g.TreeViewDataDTOs = tvdDTOs.Select(i => (TreeViewDataDTO)i.Clone()).ToList();
                        });
                        if (users != null)
                        {
                            users.ForEach(u => u.UserGroup = uGroups.FirstOrDefault(ug => ug.UserNum == u.UserNum));
                            users.ForEach(u =>
                            {
                                u.Permissions = GetPermissions(u.UserNum, u.UserGroup, permissions);
                                u.EnterprisePermissions = GetEnterprisePermissions(u.UserNum, u.UserGroup, permissions);
                            });
                            users.ForEach(u =>
                            {
                                GlobalGroup gGroup = groups.FirstOrDefault(g => g.Id == u.GroupNum);
                                if (gGroup != null) u.GlobalGroup = (GlobalGroup)gGroup.Clone();
                            });
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new UserManagementResponse { Users = users, GlobalGroups = groups, Permissions = permissions, TreeViewDataDTOs = treeViewDataDTOs };
        }

        public static UserManagementResponse GetAppUserAccount(Recorder recorder, long appUserId, long uId = 1000)
        {
            List<User> users = null;
            List<UserGroup> uGroup = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GETBYID;
                    cmd.Parameters.AddWithValue("@UserNum", appUserId);
                    cmd.Parameters.AddWithValue("@UserId", uId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetAppUserAccount", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsers(dr, recorder.Id, recorder.Name);
                        dr.NextResult();
                        uGroup = ORMapper.MapUserGroups(dr); //Permissions Table

                    }
                    if (users != null)
                    {
                        users.ForEach(u => u.UserGroup = uGroup.FirstOrDefault(ug => ug.UserNum == u.UserNum));
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new UserManagementResponse { Users = users, };
        }


        public int RecoverAppUserAccount(Recorder recorder, string userIds)
        {
            try
            {
                userIds = userIds.Replace("\"", "").Replace("'", "");
                string[] s_useridslist = userIds.Split(',');
                int i_recovered_users = 0;
                for (int i = 0; i < s_useridslist.Count(); i++)
                {
                    using (var conn = DALHelper.GetLocalDBConnection())
                    using (var cmd = conn.CreateCommand())
                    {
                        conn.Open();
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.UserManagement.APPUSER_RECOVER;
                        cmd.Parameters.AddWithValue("@userNum_STR", s_useridslist[i]);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "RecoverAppUserAccount", 0));
                        cmd.ExecuteNonQuery();
                        i_recovered_users++;
                    }
                }
                return i_recovered_users;
            }
            catch (Exception ex) { throw ex; }
        }

        public List<User> UpdateAppUserAccount(Recorder recorder, User appUser)
        {
            List<User> appUsers = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_UPDATE;
                    cmd.Parameters.AddWithValue("@UserNum", appUser.UserNum);
                    cmd.Parameters.AddWithValue("@UserType", appUser.UserType);
                    cmd.Parameters.AddWithValue("@UserID", appUser.UserID);
                    cmd.Parameters.AddWithValue("@UserName", appUser.UserName);
                    cmd.Parameters.AddWithValue("@UserPW", appUser.UserPW);
                    cmd.Parameters.AddWithValue("@Ext", appUser.Ext);
                    cmd.Parameters.AddWithValue("@SearchRest", appUser.SearchRest);
                    cmd.Parameters.AddWithValue("@UserEmail", appUser.UserID);
                    cmd.Parameters.AddWithValue("@IdentityNumber", appUser.IdentityNumber);
                    cmd.Parameters.AddWithValue("@UserPhone", appUser.UserPhone);
                    cmd.Parameters.AddWithValue("@UserFax", appUser.UserFax);
                    cmd.Parameters.AddWithValue("@JoinBeginDate", appUser.JoinBeginDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@JoinEndDate", appUser.JoinEndDate.ToString("yyyyMMdd"));
                    cmd.Parameters.AddWithValue("@Descr", appUser.Descr);
                    cmd.Parameters.AddWithValue("@POD", appUser.POD);
                    cmd.Parameters.AddWithValue("@EOD", appUser.EOD);
                    cmd.Parameters.AddWithValue("@Pause", appUser.Pause);
                    cmd.Parameters.AddWithValue("@IsEnterpriseUser", appUser.IsEnterpriseUser);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateAppUserAccount", 0));
                    cmd.ExecuteScalar();
                    if (appUsers == null) appUsers = new List<User>();
                    appUsers.Add(appUser);
                }

                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.INQ_PASSWORD_CHANGE;
                    cmd.Parameters.AddWithValue("@UserID", appUser.UserID);
                    cmd.Parameters.AddWithValue("@UserPW", appUser.UserPW);

                    cmd.ExecuteScalar();
                }

            }
            catch (Exception ex) { throw ex; }
            return appUsers;
        }

        public UserManagementResponse DeleteAppUserAccount(Recorder recorder, int id)
        {
            UserManagementResponse aUsers = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_DELETE;
                    cmd.Parameters.AddWithValue("@userNum_STR", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "DeleteAppUserAccount", 0));

                    cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
            return aUsers;
        }

        public bool SaveAssignedGroup(Recorder recorder, long uId, int gId)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_GROUP_ASSIGN;
                    cmd.Parameters.AddWithValue("@UserNum", uId);
                    cmd.Parameters.AddWithValue("@GroupNum", gId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "SaveAssignedGroup", 0));

                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public bool DeleteUserGroup(Recorder recorder, UserGroup uGroup)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.APPUSER_USERGROUP_DELETE;
                    cmd.Parameters.AddWithValue("@GroupNum", uGroup.GroupNum);
                    cmd.Parameters.AddWithValue("@UserNum", uGroup.UserNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "DeleteUserGroup", 0));
                    cmd.ExecuteNonQuery();
                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public List<UserInfoLite> GetUserData(Recorder recorder)
        {
            List<UserInfoLite> userInfos = new List<UserInfoLite>();
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_Account WHERE Status = 1 AND Ext='0';";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetUserData", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        UserInfoLite liteUser = null;
                        while (dr.Read())
                        {
                            liteUser = new UserInfoLite();
                            liteUser.Id = Convert.ToInt32(dr["UserNum"]);
                            liteUser.FullName = Convert.ToString(dr["UserName"]);
                            liteUser.RecId = recorder.Id;
                            userInfos.Add(liteUser);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return userInfos;
        }

        #endregion

        #region Tree View
        public List<TreeViewDataDTO> GetTreeViewFromRecorder(Recorder recorder, int userNum, string userId, int authNum, string authType, int type, bool getOnlyAudioChannels, int selectType)
        {
            List<TreeViewDataDTO> treeViewDataDTOs = null;
            TreeViewDataDTO TreeViewDataDTO = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    if (!getOnlyAudioChannels)
                    {
                        cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_WRAPPER;
                        cmd.Parameters.AddWithValue("@SelectType", selectType);
                    }
                    else
                    {
                        cmd.CommandText = selectType == 0 ? DBConstants.UserManagement.TREEVIEW_GET : DBConstants.UserManagement.TREEVIEW_SIMPLE_USER_RIGHTS_GET;

                    }

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetTreeViewFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (treeViewDataDTOs == null) treeViewDataDTOs = new List<TreeViewDataDTO>();
                        while (dr.Read())
                        {
                            TreeViewDataDTO = new TreeViewDataDTO();

                            TreeViewDataDTO.NodeId = Convert.ToString(dr["NodeId"]);
                            TreeViewDataDTO.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                            TreeViewDataDTO.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                            //TreeViewDataDTO.Depth = Convert.ToInt32(dr["Depth"]);
                            TreeViewDataDTO.Depth = dr["Depth"] == DBNull.Value ? 0 : Convert.ToInt32(dr["Depth"]);
                            TreeViewDataDTO.MenuType = Convert.ToInt32(dr["MenuType"]);
                            TreeViewDataDTO.ViewType = Convert.ToInt32(dr["ViewType"]);
                            TreeViewDataDTO.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                            TreeViewDataDTO.Param1 = Convert.ToString(dr["Param1"]);
                            TreeViewDataDTO.Param2 = Convert.ToString(dr["Param2"]);
                            TreeViewDataDTO.Param3 = Convert.ToString(dr["Param3"]);

                            treeViewDataDTOs.Add(TreeViewDataDTO);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeViewDataDTOs;
        }
        public static List<TreeviewData> GetGroupsTree(int userNum, string userId, int authNum, string authType, int type, int userType, Recorder recorder)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                {
                    System.Diagnostics.Debug.WriteLine(string.Format("{0}{1}", recorder.Id, recorder.Name));
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = userType == 0 ? DBConstants.UserManagement.TREEVIEW_GET_ADDITIONAL : DBConstants.UserManagement.TREEVIEW_GET_SIMPLE; //DBConstants.UserManagement.TREEVIEW_GET_SIMPLE;

                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        cmd.Parameters.AddWithValue("@UserID", userId);
                        cmd.Parameters.AddWithValue("@AuthNum", authNum);
                        cmd.Parameters.AddWithValue("@AuthType", authType);
                        cmd.Parameters.AddWithValue("@Type", type);

                        conn.Open();
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetGroupsTree", 0));

                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            if (dr.HasRows)
                            {
                                treeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                    treeNodes.Add(treeNode);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;//.Take(9).Where(n => n.NodeId.ContainsAny("1000", "1002","1003")).ToList();
        }

        public static List<TreeviewData> GetGroupsTreeFromRecorder(Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.TREEVIEW_GET_ADMIN;

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetGroupsTreeFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return treeNodes;
        }

        public static List<TreeviewData> GetGroupsTreeNonAdminFromRecorder(Recorder recorder, int userNum, string userId, int authNum, string authType, int type, int userType)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = userType == 0 ? DBConstants.UserManagement.TREEVIEW_GET_ADDITIONAL : DBConstants.UserManagement.TREEVIEW_GET_SIMPLE;

                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", userId);
                    cmd.Parameters.AddWithValue("@AuthNum", authNum);
                    cmd.Parameters.AddWithValue("@AuthType", authType);
                    cmd.Parameters.AddWithValue("@Type", type);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetGroupsTreeNonAdminFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return treeNodes;
        }

        public static List<TreeviewData> GetUsersTreeFromRecorder(Recorder recorder, int userNum)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "sp_Init_User_Tree";

                    cmd.Parameters.AddWithValue("@UserNum", userNum);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetUsersTreeFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                treeNode.Childrens = new List<TreeviewData>();

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return treeNodes;
        }

        public static List<TreeviewData> GetInquireGroupsTreeforEvaluationReportFromRecorder(Recorder recorder, int userNum, int selectType)
        {
            int userType = 0;
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = selectType == 0 ? DBConstants.UserManagement.TREEVIEW_GET : DBConstants.UserManagement.TREEVIEW_SIMPLE_USER_RIGHTS_GET;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", "");
                    if (selectType == 1)
                        cmd.Parameters.AddWithValue("@AuthNum", 7);
                    else
                        cmd.Parameters.AddWithValue("@AuthNum", 4);
                    cmd.Parameters.AddWithValue("@AuthType", 1);
                    cmd.Parameters.AddWithValue("@Type", 11);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetInquireGroupsTreeforEvaluationReportFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;
        }

        public static List<TreeviewData> GetMDGroupsTreeForEvaluationReportFromRecorder(Recorder recorder, int userNum, int selectType)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = selectType == 0 ? DBConstants.UserManagement.TREEVIEW_GET : DBConstants.UserManagement.TREEVIEW_SIMPLE_USER_RIGHTS_GET;
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@UserID", "");
                    cmd.Parameters.AddWithValue("@AuthNum", 7);
                    cmd.Parameters.AddWithValue("@AuthType", 1);
                    cmd.Parameters.AddWithValue("@Type", 16);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetMDGroupsTreeForEvaluationReportFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            treeNodes = new List<TreeviewData>();
                            while (dr.Read())
                            {
                                treeNode = new TreeviewData();

                                treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                treeNode.Param3 = Convert.ToString(dr["Param3"]);

                                treeNodes.Add(treeNode);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;
        }
        #endregion

        #region Enterprise User
        public static List<EnterpriseNodeDTO> GetEnterpriseUserRightsData(int userNum, string userId, int authNum, string authType, int type, int userType, out List<Tuple<int, int>> userRecodersGroups)
        {
            List<EnterpriseNodeDTO> entTreeNodes = null;
            EnterpriseNodeDTO entTreeNode = null;
            try
            {

                using (var conn = DALHelper.GetLocalDBConnection())
                {
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = "um_EnterpriseUser_GetNodeData_Wrapper"; //"um_EnterpriseUserTree_Wrapper";

                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        cmd.Parameters.AddWithValue("@AuthNum", authNum);

                        conn.Open();
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetEnterpriseUserRightsData", 0));

                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            entTreeNodes = new List<EnterpriseNodeDTO>();
                            //1. IRLite
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.IRLite;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                //entTreeNode.NodeCaption = Convert.ToString(dr["SelectedExtName"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //2. Monitor
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.Monitor;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //3. Search
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.Search;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //4. QA Evaluation
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.Evaluation;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedUserNum"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //5. QA Evaluation Reports
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.QAEvaluationReports;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedUserNum"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //6. Advanced Reports
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.AdvancedReports;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //7. IRFull
                            dr.NextResult();
                            while (dr.Read())
                            {
                                #region Mapping
                                entTreeNode = new EnterpriseNodeDTO();
                                entTreeNode.RecId = Convert.ToInt32(dr["RecId"]);
                                entTreeNode.ModuleBit = ModuleBit.IRFull;
                                entTreeNode.NodeId = Convert.ToInt32(dr["SelectedExt"]);
                                entTreeNode.DbStatus = true;
                                entTreeNodes.Add(entTreeNode);
                                #endregion
                            }
                            //8. Recorder Groups
                            dr.NextResult();
                            var userRecGrp = new List<Tuple<int, int>>();
                            while (dr.Read())
                            {
                                if (Convert.ToInt32(dr["GroupNum"]) != 0)
                                    userRecGrp.Add(new Tuple<int, int>(Convert.ToInt32(dr["RecId"]), Convert.ToInt32(dr["GroupNum"])));
                            }
                            userRecodersGroups = userRecGrp;
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return entTreeNodes;
        }

        public static List<TreeviewData> GetEnterpriseUserTree(int userNum, string userId, int authNum, string authType, int type, int userType, Recorder recorder)
        {
            List<TreeviewData> treeNodes = null;
            TreeviewData treeNode = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                {
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = "um_EnterpriseUserTree";

                        cmd.Parameters.AddWithValue("@UserNum", userNum);
                        cmd.Parameters.AddWithValue("@UserID", userId);
                        cmd.Parameters.AddWithValue("@AuthNum", authNum);
                        cmd.Parameters.AddWithValue("@AuthType", authType);
                        cmd.Parameters.AddWithValue("@Type", type);

                        conn.Open();
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetEnterpriseUserTree", 0));

                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            if (dr.HasRows)
                            {
                                treeNodes = new List<TreeviewData>();
                                while (dr.Read())
                                {
                                    treeNode = new TreeviewData();

                                    treeNode.NodeId = Convert.ToString(dr["NodeId"]);
                                    treeNode.NodeCaption = Convert.ToString(dr["NodeCaption"]);
                                    treeNode.ParentNodeId = Convert.ToInt32(dr["ParentNodeId"]);
                                    treeNode.Depth = Convert.ToInt32(dr["Depth"]);
                                    treeNode.MenuType = Convert.ToInt32(dr["MenuType"]);
                                    treeNode.ViewType = Convert.ToInt32(dr["ViewType"]);
                                    treeNode.IsGroup = Convert.ToBoolean(dr["IsGroup"]);
                                    treeNode.Param1 = Convert.ToString(dr["Param1"]);
                                    treeNode.Param2 = Convert.ToString(dr["Param2"]);
                                    treeNode.Param3 = Convert.ToString(dr["Param3"]);
                                    treeNode.ChannelType = Convert.ToInt32(dr["ChannelType"]);

                                    treeNodes.Add(treeNode);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return treeNodes;
        }
        #endregion

        #region Monitor
        public static List<MonitorChannel> GetChannelsToMonitorFromRecorder(Recorder recorder, string whereClause)
        {
            List<MonitorChannel> channels = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CHANNELS_MONITOR_GETBY_WHERECLAUSE;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetChannelsToMonitorFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (channels == null) channels = new List<MonitorChannel>();
                        while (dr.Read())
                        {
                            var channel = new MonitorChannel
                            {
                                UserNum = Convert.ToInt32(dr["UserNum"]),
                                UserName = Convert.ToString(dr["UserName"]),
                                GroupNum = Convert.ToInt32(dr["GroupNum"]),
                                GroupName = Convert.ToString(dr["GroupName"]),
                                Ext = Convert.ToString(dr["Ext"]),
                                AgentIP = Convert.ToString(dr["AgentIP"]),
                                UserPW = Convert.ToString(dr["UserPW"]),
                                ROD = Convert.ToString(dr["ROD"]),
                                POD = Convert.ToString(dr["POD"]),
                                EOD = Convert.ToString(dr["EOD"]),
                                SOD = Convert.ToString(dr["SOD"]),
                                ExtName = Convert.ToString(dr["ExtName"]),
                                UserEmail = Convert.ToString(dr["UserEmail"]),
                                RecId = Convert.ToInt32(dr["RecId"]),
                                RecIP = Convert.ToString(dr["RecIP"]),
                                RecPort = Convert.ToInt16(dr["RecPort"]),
                                Status = "Inactive"
                            };

                            channels.Add(channel);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return channels;
        }

        #endregion

        #region Survey
        public List<Survey> GetSurveysFromRecorder(Recorder recorder)
        {
            List<Survey> surveys = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETLIST;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetSurveysFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr, recorder);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys;
        }
        public List<Survey> GetPublishedSurveysFromRecorder(Recorder recorder)
        {
            List<Survey> surveys = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETLIST;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetPublishedSurveysFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys.Where(s => s.IsPublished == true).ToList();
        }
        public List<Survey> DeleteAndGetSurveysFromRecorder(int surveyId, Recorder recorder)
        {
            List<Survey> surveys = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_DELETE_N_GET;
                    cmd.Parameters.AddWithValue("@SurveyId ", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "DeleteAndGetSurveysFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr, recorder);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys;
        }
        public List<Survey> PublishAndGetSurveysFromRecorders(int surveyId, Recorder recorder)
        {
            List<Survey> surveys = null;
            List<CallEvaluation> callEvaluations = new List<CallEvaluation>();

            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_PUBLISH_N_GET;
                    cmd.Parameters.AddWithValue("@SurveyId ", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "PublishAndGetSurveysFromRecorders", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr, recorder);
                    }

                    EvaluatedCallsDetailBySurveyId(surveyId, recorder); // For QA-Published Forms Edit/ Delete ARIVU
                }
            }
            catch (Exception ex) { throw ex; }
            return surveys;
        }
        public Survey GetSurveyDetailsFromRecorder(int surveyId, Recorder recorder)
        {
            Survey survey = null;
            List<Survey> surveys = null;
            List<SurveySection> sections = new List<SurveySection>();
            List<Question> questions = new List<Question>();
            List<Option> options = new List<Option>();

            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETDETAIL;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetSurveyDetailsFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr);
                        survey = surveys == null || surveys.Count == 0 ? new Survey() : surveys[0];
                        dr.NextResult();
                        sections = ORMapper.MapSections(dr);//Section Table
                        dr.NextResult();
                        questions = ORMapper.MapQuestions(dr); //Question Table
                        dr.NextResult();
                        options = ORMapper.MapOptions(dr); //Question Option Table

                        survey.Sections = sections;
                        survey.Questions = questions;
                        if (survey.Questions != null)
                            survey.Questions.ForEach(q => q.Options = options.FindAll(o => o.QuestionId == q.Id));
                    }

                }
            }
            catch (Exception ex) { throw ex; }
            return survey;

        }
        public bool IsSurveyExistOnRecorder(string surveyTitle, Recorder recorder)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = string.Format("select * from emSurvey where Title = '{0}' and IsDeleted = 0", surveyTitle);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "IsSurveyExistOnRecorder", 0));
                    return Convert.ToInt32(cmd.ExecuteScalar()) > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public Survey CreateSurveyOnRecorder(Survey survey, Recorder recorder)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_INSERT;
                    cmd.Parameters.AddWithValue("@Title", survey.Name);
                    cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@StartDate", survey.StartDate);
                    cmd.Parameters.AddWithValue("@EndDate", survey.EndDate);
                    cmd.Parameters.AddWithValue("@Description", survey.Description);
                    cmd.Parameters.AddWithValue("@CreatedDate", survey.CreatedDate);
                    cmd.Parameters.AddWithValue("@ActiveAfterEndDate", survey.ActiveAfterEndDate);
                    cmd.Parameters.AddWithValue("@IsPublished", survey.IsPublished);
                    cmd.Parameters.AddWithValue("@CreatedBy", survey.CreatedBy);
                    cmd.Parameters.AddWithValue("@HasSections", survey.HasSections);
                    cmd.Parameters.AddWithValue("@IsDeleted", survey.IsDeleted);
                    cmd.Parameters.Add("@SectionId", SqlDbType.Int).Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "CreateSurveyOnRecorder", 0));
                    survey.Id = Convert.ToInt32(cmd.ExecuteScalar());
                    survey.Sections = new List<SurveySection>();
                    SurveySection eQSection = new SurveySection { Id = (int)cmd.Parameters["@SectionId"].Value };
                    survey.Questions = new List<Question>();
                    survey.Sections.Add(eQSection);
                }
                return survey;
            }
            catch (Exception ex) { throw ex; }
            //finally{}
        }
        public long CreateQuestionOnRecorder(Question sQuestion, Recorder recorder)
        {
            try
            {
                XElement xOptions = this.CreateOptionsXML(sQuestion.Options);
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_INSERT;
                    cmd.Parameters.AddWithValue("@SurveyId", sQuestion.SurveyId);
                    cmd.Parameters.AddWithValue("@SectionId", sQuestion.SectionId);
                    cmd.Parameters.AddWithValue("@Title", sQuestion.Statement);
                    cmd.Parameters.AddWithValue("@QuestionTypeId", sQuestion.TypeId);
                    cmd.Parameters.AddWithValue("@Ordering", sQuestion.Ordering);
                    cmd.Parameters.AddWithValue("@Options", xOptions.ToString());
                    cmd.Parameters.AddWithValue("@CreatedDate", sQuestion.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", sQuestion.IsDeleted);
                    cmd.Parameters.AddWithValue("@QuestionId", 0);
                    cmd.Parameters["@QuestionId"].Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "CreateQuestionOnRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (sQuestion.Options != null && sQuestion.Options.Count != 0)
                        {
                            while (dr.Read())
                            {
                                foreach (var item in sQuestion.Options)
                                {
                                    if (item.Ordering == (int)dr["Ordering"])
                                        item.Id = (long)dr["Id"];
                                }
                            }
                        }
                    }
                    sQuestion.Id = Convert.ToInt64(cmd.Parameters["@QuestionId"].Value.ToString());

                    return sQuestion.Id;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public List<SurveySection> CreateAndGetSectionsOnRecorder(SurveySection surveyGroup, Recorder recorder)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_INSERT_N_GET;
                    cmd.Parameters.AddWithValue("@Title", surveyGroup.Title);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyGroup.SurveyId);
                    cmd.Parameters.AddWithValue("@CreatedDate", surveyGroup.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", surveyGroup.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "CreateAndGetSectionsOnRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }
        public List<Question> GetQuestionsBySurveyIdFromRecorder(int surveyId, Recorder recorder)
        {
            List<Question> questions = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_GETBYSURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetQuestionsBySurveyIdFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        questions = ORMapper.MapQuestions(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return questions;
        }
        public List<SurveySection> GetSectionsBySurveyIdFromRecorder(int surveyId, Recorder recorder)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_GETBYSURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetSectionsBySurveyIdFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }
        public bool UpdateQuestionSectionOnRecorder(int questionId, int sectionId, Recorder recorder)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = string.Format("update emQuestion Set SectionId = {0} where Id = {1}", sectionId, questionId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateQuestionSectionOnRecorder", 0));
                    return Convert.ToInt32(cmd.ExecuteScalar()) > 0 ? true : false;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public List<SurveySection> UpdateAndGetSectionsFromRecorder(SurveySection surveyGroup, Recorder recorder)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_UPDATE_N_GET;
                    cmd.Parameters.AddWithValue("@Id", surveyGroup.Id);
                    cmd.Parameters.AddWithValue("@Title", surveyGroup.Title);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyGroup.SurveyId);
                    cmd.Parameters.AddWithValue("@CreatedDate", surveyGroup.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", surveyGroup.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateAndGetSectionsFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }
        public List<SurveySection> DeleteAndGetSectionsFromRecorder(int sectionId, int surveyId, Recorder recorder)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SECTION_DELETE_N_GET;
                    cmd.Parameters.AddWithValue("@Id ", sectionId);
                    cmd.Parameters.AddWithValue("@SurveyId ", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "DeleteAndGetSectionsFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return sections;
        }
        public List<SurveySection> AssignUnAssignQuestionsAndGetSectionsFromRecorder(int surveyId, int sectionId, string assignedQuestions, string unassignedQuestions, Recorder recorder)
        {
            List<SurveySection> sections = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_UPDATESECTIONS;
                    cmd.Parameters.AddWithValue("SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("SectionId", sectionId);
                    cmd.Parameters.AddWithValue("Selected", assignedQuestions);
                    cmd.Parameters.AddWithValue("DeSelected", unassignedQuestions);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "AssignUnAssignQuestionsAndGetSectionsFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        sections = ORMapper.MapSections(dr);
                    }
                    return sections;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public Survey GetSurveyFromRecorder(int surveyId, Recorder recorder)
        {
            Survey survey = null;
            List<Survey> surveys = null;
            List<SurveySection> sections = new List<SurveySection>();
            List<Question> questions = new List<Question>();
            List<Option> options = new List<Option>();

            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_GETDETAIL;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetSurveyFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        surveys = ORMapper.MapSurveys(dr);
                        survey = surveys == null || surveys.Count == 0 ? new Survey() : surveys[0];
                        dr.NextResult();
                        sections = ORMapper.MapSections(dr);//Section Table
                        dr.NextResult();
                        questions = ORMapper.MapQuestions(dr); //Question Table
                        dr.NextResult();
                        options = ORMapper.MapOptions(dr); //Question Option Table

                        survey.Sections = sections;
                        survey.Questions = questions;
                        if (survey.Questions != null)
                            survey.Questions.ForEach(q => q.Options = options.FindAll(o => o.QuestionId == q.Id));
                    }

                }
            }
            catch (Exception ex) { throw ex; }
            return survey;

        }
        public void UpdateSurveyOnRecorder(Survey survey, Recorder recorder)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_UPDATE;
                    cmd.Parameters.AddWithValue("@Id", survey.Id);
                    cmd.Parameters.AddWithValue("@Title", survey.Name);
                    cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    cmd.Parameters.AddWithValue("@StartDate", survey.StartDate);
                    cmd.Parameters.AddWithValue("@EndDate", survey.EndDate);
                    cmd.Parameters.AddWithValue("@Description", survey.Description);
                    cmd.Parameters.AddWithValue("@CreatedDate", survey.CreatedDate);
                    cmd.Parameters.AddWithValue("@ActiveAfterEndDate", survey.ActiveAfterEndDate);
                    cmd.Parameters.AddWithValue("@IsPublished", survey.IsPublished);
                    cmd.Parameters.AddWithValue("@CreatedBy", survey.CreatedBy);
                    cmd.Parameters.AddWithValue("@HasSections", survey.HasSections);
                    cmd.Parameters.AddWithValue("@IsDeleted", survey.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateSurveyOnRecorder", 0));
                    survey.Id = Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public long UpdateQuestionOnRecorder(Question eQuestion, Recorder recorder)
        {
            try
            {
                XElement xOptions = this.CreateOptionsXML(eQuestion.Options);
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_UPDATE;
                    cmd.Parameters.AddWithValue("@QuestionId", eQuestion.Id);
                    cmd.Parameters.AddWithValue("@SurveyId", eQuestion.SurveyId);
                    cmd.Parameters.AddWithValue("@SectionId", eQuestion.SectionId);
                    cmd.Parameters.AddWithValue("@Title", eQuestion.Statement);
                    cmd.Parameters.AddWithValue("@QuestionTypeId", eQuestion.TypeId);
                    cmd.Parameters.AddWithValue("@Ordering", eQuestion.Ordering);
                    cmd.Parameters.AddWithValue("@Options", xOptions.ToString());
                    cmd.Parameters.AddWithValue("@CreatedDate", eQuestion.CreatedDate);
                    cmd.Parameters.AddWithValue("@IsDeleted", eQuestion.IsDeleted);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateQuestionOnRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (eQuestion.Options != null && eQuestion.Options.Count != 0)
                        {
                            while (dr.Read())
                            {
                                foreach (var item in eQuestion.Options)
                                {
                                    if (item.Ordering == (int)dr["Ordering"])
                                        item.Id = (long)dr["Id"];
                                }
                            }
                        }
                    }
                    return eQuestion.Id;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public void DeleteQuestionFromRecorder(long id, Recorder recorder)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_DELETE;
                    //cmd.Parameters.AddWithValue("@SurveyId ", eQuestion.SurveyId);
                    cmd.Parameters.AddWithValue("@QuestionId", id);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "DeleteQuestionFromRecorder", 0));
                    cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public void UpdateIsPublishedOnRecorder(int surveyId, Recorder recorder)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.SURVEY_UPDATE_ISPUBLISH;
                    cmd.Parameters.AddWithValue("@Id", surveyId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateIsPublishedOnRecorder", 0));
                    cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            { throw ex; }
        }
        public long UpdateQuestionsOrderOnRecorder(List<Question> eRequest, Recorder recorder)
        {
            try
            {
                XElement xQuestions = this.CreateQuestionsXML(eRequest);
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Survey.QUESTION_UPDATEORDER;
                    cmd.Parameters.AddWithValue("Questions", xQuestions.ToString());
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateQuestionsOrderOnRecorder", 0));
                    return Convert.ToInt64(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Search
        public static List<CallInfo> SearchCallsPrimaryDB(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, out int totalPages, out long totalRecords, string duration, string criteria, Recorder recorder)
        {
            CallInfo callInfo = null;
            List<CallInfo> calls = new List<CallInfo>();
            try
            {
                System.Diagnostics.Debug.WriteLine(string.Format("{0}-{1}-{2}", recorder.Id, recorder.Name, recorder.ConnectionString));
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@StartTime", startTime);
                    cmd.Parameters.AddWithValue("@EndTime", endTime);
                    cmd.Parameters.AddWithValue("@IsGlobalSearch", isGlobalSearch);
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "SearchCallsPrimaryDB", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //if (dr.HasRows)
                        //    calls = new List<CallInfo>();
                        while (dr.Read())
                        {
                            callInfo = new CallInfo();

                            #region --- Old Mapping ---

                            //////callInfo.RecorderId = recorder.Id;
                            //////callInfo.RecorderName = recorder.Name;

                            //////callInfo.RowNo = (long)dr["RowNo"];
                            //////callInfo.CallId = Convert.ToString(dr["CallID"]);
                            //////callInfo.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                            //////callInfo.RecorderIP = recorder.IP;
                            //////callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                            //////callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                            //////callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                            //////callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                            //////callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                            //////callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                            //////callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            //////callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                            //////callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                            //////callInfo.FileName = Convert.ToString(dr["FileName"]);
                            //////callInfo.CustName = Convert.ToString(dr["CustName"]);
                            //////callInfo.ANI = Convert.ToString(dr["ANI"]);
                            //////callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                            //////callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                            //////callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                            //////callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                            //////callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                            //////callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                            //////callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                            //////callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                            //////callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                            //////callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                            //////callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                            //////callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                            //////callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                            //////callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                            //////callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                            //////callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                            //////callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                            //////callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                            //////callInfo.Tag16 = Convert.ToString(dr["Tag16"]);
                            //////callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                            //////callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                            //////callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                            //////callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                            //////callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);// dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();
                            //////callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                            //////callInfo.interview_DateTime = Convert.ToString(dr["DateTime"]);
                            //////callInfo.interview_Interviewee = Convert.ToString(dr["Interviewer"]);
                            //////callInfo.interview_MdInterviewee = dr.IsDBNull(dr.GetOrdinal("MDInterviewee")) ? null : Convert.ToString(dr["MDInterviewee"]); //Convert.ToString(dr["MdInterviewee"]);
                            //////callInfo.interview_Interviewer = Convert.ToString(dr["Interviewee"]);
                            //////callInfo.interview_InterviewId = Convert.ToString(dr["InterviewId"]);
                            //////callInfo.interview_GPS = Convert.ToString(dr["GPS"]);
                            //////callInfo.interview_Notes = Convert.ToString(dr["Notes"]);

                            //////if (callInfo.CallType != 7)
                            //////{
                            //////    callInfo.CallType_inq = callInfo.CallType;
                            //////}
                            //////else
                            //////{
                            //////    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                            //////    {
                            //////        callInfo.CallType_inq = 8;
                            //////    }
                            //////    else
                            //////    {
                            //////        callInfo.CallType_inq = 7;
                            //////    }
                            //////}
                            //////callInfo.interview_Interviewee = callInfo.CallType == 7 ? callInfo.interview_Interviewee : callInfo.interview_MdInterviewee;

                            //////callInfo.BookmarkCSV = "";
                            //////if (dr.FieldExists("BookMarkXML"))
                            //////{
                            //////    callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                            //////    if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                            //////    {
                            //////        callInfo.BookmarkCSV = callInfo.BookmarkXML.ConvertXmlStringToCsvString();
                            //////    }
                            //////}
                            #endregion

                            #region --- Mapping ---

                            callInfo.RecorderId = recorder.Id;
                            callInfo.RecorderName = recorder.Name;

                            callInfo.RowNo = (long)dr["RowNo"];

                            callInfo.CallId = Convert.ToString(dr["CallID"]);
                            callInfo.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                            callInfo.RecorderId = Convert.ToInt32(dr["RecID"]);
                            callInfo.RecorderIP = recorder.IP;
                            //if (!dr.IsDBNull(dr.GetOrdinal("RecName")))
                            //    callInfo.RecorderName = dr.GetString(dr.GetOrdinal("RecName"));
                            //callInfo.RecorderName = Convert.ToString(dr["RecName"]);
                            //callInfo.RecorderName = recorder.Name;
                            callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                            callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                            callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                            callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                            callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                            callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                            //callInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
                            callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                            callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                            callInfo.FileName = Convert.ToString(dr["FileName"]);

                            if (callInfo.CallType != 7)
                            {
                                callInfo.CallType_inq = callInfo.CallType;

                            }
                            else
                            {
                                if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                                {
                                    callInfo.CallType_inq = 8;
                                }
                                else if (Regex.IsMatch(callInfo.FileName, "zip", RegexOptions.IgnoreCase))
                                {
                                    callInfo.CallType_inq = 12;
                                }
                                else
                                {
                                    callInfo.CallType_inq = 7;
                                }
                            }

                            callInfo.CustName = Convert.ToString(dr["CustName"]);
                            callInfo.ANI = Convert.ToString(dr["ANI"]);
                            callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                            callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                            callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                            callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                            callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                            callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                            callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                            callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                            callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                            callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                            callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                            callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                            callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                            callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                            callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                            callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                            callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                            callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                            callInfo.Tag16 = Convert.ToString(dr["Tag16"]);

                            callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                            callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                            callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                            callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                            callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                            //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                            callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);//dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();

                            callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);

                            callInfo.Transcription = Convert.ToString(dr["Transcription"]);
                            callInfo.TranscriptionId = dr["TranscriptionId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["TranscriptionId"]);
                            callInfo.Confidence = dr["Confidence"] == DBNull.Value ? 0 : Convert.ToDecimal(dr["Confidence"]);

                            callInfo.IsSyncedFromClient = dr.IsDBNull(dr.GetOrdinal("IsSyncedFromClient")) ? 0 : Convert.ToInt32(dr["IsSyncedFromClient"]);

                            callInfo.interview_DateTime = Convert.ToString(dr["DateTime"]);
                            callInfo.interview_Interviewee = Convert.ToString(dr["Interviewee"]);
                            callInfo.interview_MdInterviewee = dr.IsDBNull(dr.GetOrdinal("MDInterviewee")) ? null : Convert.ToString(dr["MDInterviewee"]); //Convert.ToString(dr["MdInterviewee"]);

                            callInfo.interview_Interviewee = callInfo.CallType == 7 ? callInfo.interview_Interviewee : callInfo.interview_MdInterviewee;

                            callInfo.interview_Interviewer = Convert.ToString(dr["Interviewer"]);
                            callInfo.interview_InterviewId = Convert.ToString(dr["InterviewId"]);
                            callInfo.interview_GPS = Convert.ToString(dr["GPS"]);
                            callInfo.interview_Notes = Convert.ToString(dr["Notes"]);

                            callInfo.ErrorInMuxProcess = 0;
                            if (dr["ErrorinMuxProcess"] != DBNull.Value)
                                callInfo.ErrorInMuxProcess = Convert.ToInt32(dr["ErrorinMuxProcess"]);

                            callInfo.BookmarkCSV = string.Empty;
                            if (dr.FieldExists("BookMarkXML"))
                            {
                                callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                                if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                                {
                                    callInfo.BookmarkCSV = callInfo.BookmarkXML.Inquire_ConvertXmlStringToCsvString(callInfo.CallType);
                                }
                            }
                            callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                            callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                            callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                            callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                            callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                            callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                            callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                            callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                            callInfo.VesselId = Convert.ToString(dr["VesselId"]);
                            callInfo.AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty;
                            #endregion

                            calls.Add(callInfo);
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }
        public static List<CallInfo> SearchCallsPrimaryDB(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, bool isRandom, bool isPercentage, out int totalPages, out long totalRecords, string duration, string criteria, int noOfCalls, Recorder recorder)
        {
            CallInfo callInfo = null;
            List<CallInfo> calls = new List<CallInfo>();
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_RANDOM_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@StartTime", startTime);
                    cmd.Parameters.AddWithValue("@EndTime", endTime);

                    cmd.Parameters.AddWithValue("@IsRandom", isRandom);
                    cmd.Parameters.AddWithValue("@IsPercentage", isPercentage);
                    cmd.Parameters.AddWithValue("@NoOfCalls", noOfCalls);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", isGlobalSearch);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "SearchCallsPrimaryDB(QA)", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //calls = new List<CallInfo>();
                        while (dr.Read())
                        {
                            callInfo = new CallInfo();

                            #region --- Old Mapping ---

                            //////callInfo.RecorderId = recorder.Id;
                            //////callInfo.RecorderName = recorder.Name;

                            //////callInfo.RowNo = (long)dr["RowNo"];
                            //////callInfo.CallId = Convert.ToString(dr["CallID"]);
                            //////callInfo.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                            //////callInfo.RecorderIP = recorder.IP;
                            //////callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                            //////callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                            //////callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                            //////callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                            //////callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                            //////callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                            //////callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            //////callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                            //////callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                            //////callInfo.FileName = Convert.ToString(dr["FileName"]);
                            //////callInfo.CustName = Convert.ToString(dr["CustName"]);
                            //////callInfo.ANI = Convert.ToString(dr["ANI"]);
                            //////callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                            //////callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                            //////callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                            //////callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                            //////callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                            //////callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                            //////callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                            //////callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                            //////callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                            //////callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                            //////callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                            //////callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                            //////callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                            //////callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                            //////callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                            //////callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                            //////callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                            //////callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                            //////callInfo.Tag16 = Convert.ToString(dr["Tag16"]);
                            //////callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                            //////callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                            //////callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                            //////callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                            //////callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);
                            //////callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                            //////callInfo.interview_DateTime = Convert.ToString(dr["DateTime"]);
                            //////callInfo.interview_Interviewee = Convert.ToString(dr["Interviewer"]);
                            //////callInfo.interview_MdInterviewee = dr.IsDBNull(dr.GetOrdinal("MDInterviewee")) ? null : Convert.ToString(dr["MDInterviewee"]); //Convert.ToString(dr["MdInterviewee"]);
                            //////callInfo.interview_Interviewer = Convert.ToString(dr["Interviewee"]);
                            //////callInfo.interview_InterviewId = Convert.ToString(dr["InterviewId"]);
                            //////callInfo.interview_GPS = Convert.ToString(dr["GPS"]);

                            //////if (callInfo.CallType != 7)
                            //////{
                            //////    callInfo.CallType_inq = callInfo.CallType;
                            //////}
                            //////else
                            //////{
                            //////    if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                            //////    {
                            //////        callInfo.CallType_inq = 8;
                            //////    }
                            //////    else
                            //////    {
                            //////        callInfo.CallType_inq = 7;
                            //////    }
                            //////}

                            //////callInfo.BookmarkCSV = "";
                            //////if (dr.FieldExists("BookMarkXML"))
                            //////{
                            //////    callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                            //////    if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                            //////    {
                            //////        callInfo.BookmarkCSV = callInfo.BookmarkXML.ConvertXmlStringToCsvString();
                            //////    }
                            //////}
                            #endregion

                            #region --- Mapping ---

                            callInfo.RecorderId = recorder.Id;
                            callInfo.RecorderName = recorder.Name;
                            callInfo.RecorderIP = recorder.IP;

                            callInfo.RowNo = (long)dr["RowNo"];

                            callInfo.CallId = Convert.ToString(dr["CallID"]);
                            callInfo.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                            callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                            callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                            callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                            callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                            callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                            callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                            //callInfo.StartTime = Convert.ToDateTime(dr["StartTime"]);
                            callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                            callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                            callInfo.FileName = Convert.ToString(dr["FileName"]);

                            if (callInfo.CallType != 7)
                            {
                                callInfo.CallType_inq = callInfo.CallType;

                            }
                            else
                            {
                                if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                                {
                                    callInfo.CallType_inq = 8;
                                }
                                else
                                {
                                    callInfo.CallType_inq = 7;
                                }
                            }

                            callInfo.CustName = Convert.ToString(dr["CustName"]);
                            callInfo.ANI = Convert.ToString(dr["ANI"]);
                            callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                            callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                            callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                            callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                            callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                            callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                            callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                            callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                            callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                            callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                            callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                            callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                            callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                            callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                            callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                            callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                            callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                            callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                            callInfo.Tag16 = Convert.ToString(dr["Tag16"]);

                            callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                            callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                            callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                            callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                            callInfo.MessageBody = Convert.ToString(dr["Tag2"]);
                            //callInfo.ScreenRecFile = Convert.ToString(dr["Screen_Rec_File"]);
                            callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);//dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();

                            callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);

                            callInfo.Transcription = Convert.ToString(dr["Transcription"]);
                            callInfo.TranscriptionId = dr["TranscriptionId"] == DBNull.Value ? 0 : Convert.ToInt32(dr["TranscriptionId"]);
                            callInfo.Confidence = dr["Confidence"] == DBNull.Value ? 0 : Convert.ToDecimal(dr["Confidence"]);

                            callInfo.IsSyncedFromClient = dr.IsDBNull(dr.GetOrdinal("IsSyncedFromClient")) ? 0 : Convert.ToInt32(dr["IsSyncedFromClient"]);

                            callInfo.interview_DateTime = Convert.ToString(dr["DateTime"]);
                            callInfo.interview_Interviewee = Convert.ToString(dr["Interviewee"]);
                            callInfo.interview_MdInterviewee = dr.IsDBNull(dr.GetOrdinal("MDInterviewee")) ? null : Convert.ToString(dr["MDInterviewee"]); //Convert.ToString(dr["MdInterviewee"]);

                            callInfo.interview_Interviewee = callInfo.CallType == 7 ? callInfo.interview_Interviewee : callInfo.interview_MdInterviewee;

                            callInfo.interview_Interviewer = Convert.ToString(dr["Interviewer"]);
                            callInfo.interview_InterviewId = Convert.ToString(dr["InterviewId"]);
                            callInfo.interview_GPS = Convert.ToString(dr["GPS"]);
                            callInfo.interview_Notes = Convert.ToString(dr["Notes"]);

                            callInfo.ErrorInMuxProcess = 0;
                            if (dr["ErrorinMuxProcess"] != DBNull.Value)
                                callInfo.ErrorInMuxProcess = Convert.ToInt32(dr["ErrorinMuxProcess"]);

                            callInfo.BookmarkCSV = string.Empty;
                            if (dr.FieldExists("BookMarkXML"))
                            {
                                callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                                if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                                {
                                    callInfo.BookmarkCSV = callInfo.BookmarkXML.Inquire_ConvertXmlStringToCsvString(callInfo.CallType);
                                }
                            }
                            callInfo.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                            callInfo.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                            callInfo.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                            callInfo.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                            callInfo.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                            callInfo.RevViewPhoneNumber = DBRecordExtensions.HasColumn(dr, "RevViewPhoneNumber") ? Convert.ToString(dr["RevViewPhoneNumber"]) : string.Empty;
                            callInfo.RevViewAgentName = DBRecordExtensions.HasColumn(dr, "RevViewAgentName") ? Convert.ToString(dr["RevViewAgentName"]) : string.Empty;
                            callInfo.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                            callInfo.VesselId = Convert.ToString(dr["VesselId"]);
                            callInfo.AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty;
                            #endregion


                            calls.Add(callInfo);
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }
        public static List<CallInfo> SearchCallsChainedDBs(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, out int totalPages, out long totalRecords, string duration, string criteria, Recorder recorder)
        {
            System.Diagnostics.Debug.WriteLine(string.Format("SearchCallsChainedDBs : {0}-{1}", recorder.Id, recorder.Name));
            List<CallInfo> calls = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_CHAIN_DB;

                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@StartTime", startTime);
                    cmd.Parameters.AddWithValue("@EndTime", endTime);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", isGlobalSearch);

                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria.Replace("OR (  )  OR", " "));

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "SearchCallsChainedDBs", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        calls = ORMapper.MapCallInfos(dr);

                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());

                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());

                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public static List<CallInfo> GetCallsByIds(Recorder recorder, string callIds)
        {
            List<CallInfo> calls = new List<CallInfo>();
            try
            {
                var tCallIds = callIds.Split(',');
                foreach (var cId in tCallIds)
                {
                    using (var conn = DALHelper.GetLocalDBConnection())
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.VoiceRec.CALLS_GET_BY_COMMA_SEPERATED_IDS;
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", cId);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetCallsByIds", 0));
                        conn.Open();
                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            var call = ORMapper.MapCallInfo(dr, recorder.Id, recorder.Name);
                            calls.Add(call);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return calls;
        }

        public static List<CallInfoExportResult> GetCallInfoExportResults(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, string duration, string criteria, Recorder recorder)
        {
            CallInfoExportResult callExportResult = null;
            List<CallInfoExportResult> callExportResults = new List<CallInfoExportResult>();
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_EXPORT_RESULT;

                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@StartTime", startTime);
                    cmd.Parameters.AddWithValue("@EndTime", endTime);
                    cmd.Parameters.AddWithValue("@IsGlobalSearch", isGlobalSearch);
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetCallInfoExportResults", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            callExportResult = new CallInfoExportResult();

                            #region --- Mapping ---



                            callExportResult.RowNo = (long)dr["RowNo"];
                            callExportResult.GroupName = Convert.ToString(dr["GroupName"]);
                            callExportResult.AgentId = Convert.ToInt32(dr["UserNum"]);
                            //callExportResult.ChannelName = Convert.ToString(dr["ChannelName"]);
                            callExportResult.ChannelName = Convert.ToString(dr["ExtName"]);
                            callExportResult.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            callExportResult.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);

                            callExportResult.BookmarkCSV = Convert.ToString(dr["BookmarkCSV"]);
                            callExportResult.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                            callExportResult.CalledID = Convert.ToString(dr["CalledID"]);

                            callExportResult.Tag4 = Convert.ToString(dr["Tag4"]);

                            callExportResult.ANIName = Convert.ToString(dr["ANI_NAME"]);
                            callExportResult.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                            callExportResult.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);

                            callExportResult.Tag5 = Convert.ToString(dr["Tag5"]);
                            callExportResult.Tag6 = Convert.ToString(dr["Tag6"]);
                            callExportResult.Tag7 = Convert.ToString(dr["Tag7"]);
                            callExportResult.Tag8 = Convert.ToString(dr["Tag8"]);
                            callExportResult.Tag9 = Convert.ToString(dr["Tag9"]);
                            callExportResult.Tag10 = Convert.ToString(dr["Tag10"]);
                            callExportResult.Tag11 = Convert.ToString(dr["Tag11"]);
                            callExportResult.Tag12 = Convert.ToString(dr["Tag12"]);
                            callExportResult.Tag13 = Convert.ToString(dr["Tag13"]);
                            callExportResult.Tag14 = Convert.ToString(dr["Tag14"]);
                            callExportResult.Tag15 = Convert.ToString(dr["Tag15"]);
                            callExportResult.Tag16 = Convert.ToString(dr["Tag16"]);
                            callExportResult.RecorderName = recorder.Name;
                            #endregion
                            callExportResults.Add(callExportResult);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return callExportResults;
        }
        public static List<CallInfoExportResult> GetCallInfoExportResultsByIds(Recorder recorder, string callIds, DateTime startDate, DateTime endDate)
        {
            List<CallInfoExportResult> callInfoExportResults = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandText = DBConstants.VoiceRec.EXPORT_RESULTS_GET_BY_COMMA_SEPERATED_IDS;
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));
                    cmd.Parameters.AddWithValue("@StartDateTime", startDate.ToString("yyyyMMddHHmmss"));
                    cmd.Parameters.AddWithValue("@EndDateTime", endDate.ToString("yyyyMMddHHmmss"));

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetCallInfoExportResultsByIds", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callInfoExportResults = ORMapper.MapCallInfoExportResults(recorder.Name, dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callInfoExportResults;
        }

        

        #endregion

        #region ------- Custom Fields -------

        public static int UpdateCallsCustomFields(Recorder recorder, string callIds, int fieldType, string fieldText, int userId)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_UPDATE_CUSTOM_FIELDS_CHAIN_DB;
                    cmd.Parameters.AddWithValue("@Comment", fieldText);
                    //cmd.Parameters.AddWithValue("@CallID", callIds.Split(',')[0]);
                    //cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", callIds);
                    cmd.Parameters.AddWithValue("@CommaSeparatedCallIds", new System.Data.SqlTypes.SqlChars(new System.Data.SqlTypes.SqlString(callIds)));
                    //cmd.Parameters.AddWithValue("@Ext", callInfo.CalledID);
                    cmd.Parameters.AddWithValue("@Type", fieldType);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateCallsCustomFields", 0));
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public static int UpdateCallCustomFields(Recorder recorder, string callId, string tag1, string tag2, string tag3, string tag4, string custName, string callComments)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_CUSTOM_FIELDS_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", callId);
                    cmd.Parameters.AddWithValue("@Tag1", tag1);
                    cmd.Parameters.AddWithValue("@Tag2", tag2);
                    cmd.Parameters.AddWithValue("@Tag3", tag3);
                    cmd.Parameters.AddWithValue("@Tag4", tag4);
                    cmd.Parameters.AddWithValue("@CustName", custName);
                    cmd.Parameters.AddWithValue("@CallComments", callComments);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateCallCustomFields", 0));

                    //return Convert.ToBoolean(cmd.ExecuteScalar());
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public static int UpdateCallRetention(Recorder recorder, string callId, bool retainValue)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_RETAIN_VALUE_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", callId);
                    cmd.Parameters.AddWithValue("@RetainValue", retainValue);
                    //cmd.Parameters.AddWithValue("@Ext", callInfo.CalledID);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateCallRetention", 0));

                    //string s = Convert.ToString( cmd.ExecuteScalar());
                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        public static int Inquiremarkerupdate(Recorder recorder, string callId, int markerid, string markertext, string markernotes)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_INQUIRE_MARKER_UPDATE;
                    cmd.Parameters.AddWithValue("@CallID", callId);
                    cmd.Parameters.AddWithValue("@markerid", markerid);
                    cmd.Parameters.AddWithValue("@markertext", markertext);
                    cmd.Parameters.AddWithValue("@markernotes", markernotes);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "Inquiremarkerupdate", 0));

                    return cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region ------- Bookmark -------

        public static List<string> InsertBookmarkAndGetByCallId(Recorder recorder, Bookmark bookmark, out int rowsAffected)
        {
            List<string> bookmarks = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_INSERT_N_GET;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "InsertBookmarkAndGetByCallId", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        rowsAffected = dr.RecordsAffected;
                        if (dr.HasRows)
                        {
                            dr.Read();
                            bookmarks = new List<string>();
                            //string BookmarkXML = Convert.ToString(dr[0]);
                            string BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                            string BookmarkCSV = "";
                            if (!String.IsNullOrEmpty(BookmarkXML))
                            {
                                BookmarkCSV = BookmarkXML.ConvertXmlStringToCsvString();
                            }
                            bookmarks.Add(BookmarkXML);
                            bookmarks.Add(BookmarkCSV);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return bookmarks;
        }

        public static List<string> UpdateBookmarkAndGetByCallId(Recorder recorder, Bookmark bookmark, out int rowsAffected)
        {
            List<string> bookmarks = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.BOOKMARK_UPDATE_N_GET;
                    cmd.Parameters.AddWithValue("@CallID", bookmark.CallId);
                    cmd.Parameters.AddWithValue("@BookMarkPos", bookmark.Position);
                    cmd.Parameters.AddWithValue("@BookMarkText", bookmark.Text);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateBookmarkAndGetByCallId", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        rowsAffected = dr.RecordsAffected;
                        if (dr.HasRows)
                        {
                            dr.Read();
                            bookmarks = new List<string>();
                            //string BookmarkXML = Convert.ToString(dr[0]);
                            string BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                            string BookmarkCSV = "";
                            if (!String.IsNullOrEmpty(BookmarkXML))
                            {
                                BookmarkCSV = BookmarkXML.ConvertXmlStringToCsvString();
                            }
                            bookmarks.Add(BookmarkXML);
                            bookmarks.Add(BookmarkCSV);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return bookmarks;
        }

        #endregion

        #region QA Evaluation
        public static RecorderEvaluation GetAllDrilldownChartsFromRecorder(Recorder recorder)
        {
            List<Tuple<int, string, int, string, string>> evalsByStatus = null;
            List<Tuple<int, string, int, string, string>> evalsByCampaign = null;
            List<Tuple<int, string, int, string, string>> evalsByEvaluator = null;
            List<Tuple<int, string, int, string, string>> evalsByTopScorer = null;
            List<Tuple<int, string, int, string, string>> multiCallEvalsByStatus = null;

            Tuple<int, string, int, string, string> eval = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.DASHBOARD_GETALL;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetAllDrilldownChartsFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (evalsByStatus == null) evalsByStatus = new List<Tuple<int, string, int, string, string>>();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                eval = new Tuple<int, string, int, string, string>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Description"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));
                                evalsByStatus.Add(eval);
                            }
                        }
                        dr.NextResult();

                        if (evalsByCampaign == null) evalsByCampaign = new List<Tuple<int, string, int, string, string>>();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                eval = new Tuple<int, string, int, string, string>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Title"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));
                                evalsByCampaign.Add(eval);
                            }
                        }
                        dr.NextResult();

                        if (evalsByEvaluator == null) evalsByEvaluator = new List<Tuple<int, string, int, string, string>>();
                        int num = 0;
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                eval = new Tuple<int, string, int, string, string>(++num, Convert.ToString(dr["Evaluator"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));
                                evalsByEvaluator.Add(eval);
                            }
                        }
                        dr.NextResult();

                        num = 0;
                        if (evalsByTopScorer == null) evalsByTopScorer = new List<Tuple<int, string, int, string, string>>();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                eval = new Tuple<int, string, int, string, string>(++num, Convert.ToString(dr["TopScorer"]), Convert.ToInt32(dr["TotalScore"]), dr["EvalIds"] == DBNull.Value ? string.Empty : Convert.ToString(dr["EvalIds"]), dr["EvalScores"] == DBNull.Value ? string.Empty : Convert.ToString(dr["EvalScores"]));
                                evalsByTopScorer.Add(eval);
                            }
                        }

                        dr.NextResult();

                        if (multiCallEvalsByStatus == null) multiCallEvalsByStatus = new List<Tuple<int, string, int, string, string>>();
                        while (dr.Read())
                        {
                            if (dr["Id"] != null)
                            {
                                eval = new Tuple<int, string, int, string, string>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Description"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));
                                multiCallEvalsByStatus.Add(eval);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new RecorderEvaluation { EvalsByStatus = evalsByStatus, EvalsByCampaigns = evalsByCampaign, EvalsByEvaluators = evalsByEvaluator, EvalsByTopScorers = evalsByTopScorer, MultiCallEvalsByStatus = multiCallEvalsByStatus, RecorderId = recorder.Id, RecorderName = recorder.Name };
        }
        public bool InsertCallsForEvaluation(string calls, int userId, int surveyId)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = "em_CallEvaluation_Insert";
                    cmd.Parameters.AddWithValue("@CallInfos", calls.ToString());
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "InsertCallsForEvaluation", 0));

                    cmd.ExecuteNonQuery();

                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
            return false;
        }
        public bool InsertEnterpriseEvaluations(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.ENTERPRISE_EVALUATION_INSERT;
                    cmd.Parameters.AddWithValue("@CallInfos", callIds.ToString());
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("@EvaluatorId", evaluatorId);
                    cmd.Parameters.AddWithValue("@EvaluatorName", evaluatorName);
                    cmd.Parameters.AddWithValue("@EvaluatorEmail", evaluatorEmail);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "InsertEnterpriseEvaluations", 0));
                    cmd.ExecuteNonQuery();

                    return true;
                }
            }
            catch (Exception ex) { throw ex; }
            return false;
        }
        public short AddEnterpriseEvaluations(string callIds, int userId, int surveyId, int evaluatorId, string evaluatorName, string evaluatorEmail)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.ENTERPRISE_EVALUATION_INSERT;
                    cmd.Parameters.AddWithValue("@CallInfos", callIds.ToString());
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("@EvaluatorId", evaluatorId);
                    cmd.Parameters.AddWithValue("@EvaluatorName", evaluatorName);
                    cmd.Parameters.AddWithValue("@EvaluatorEmail", evaluatorEmail);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "AddEnterpriseEvaluations", 0));
                    cmd.ExecuteNonQuery();
                    return Convert.ToInt16(1);
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public List<CallEvaluationDTO> GetEvaluations(Recorder recorder, string whereClause, int userId, int pageIndex, int pageSize, out int totalPages, out int totalRecords, bool SharedRequired, bool IsEvaluatorSearch)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_GETALL_BY_WHERE_CLAUSE_EC;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@SharedRequired", SharedRequired == true ? 1 : 0);
                    cmd.Parameters.AddWithValue("@IsEvaluatorSearch", IsEvaluatorSearch == true ? 1 : 0);
                    //////cmd.Parameters.AddWithValue("@SharedRequired", (SharedRequired == true ? 1 : 0));
                    //////cmd.Parameters.AddWithValue("@IsEvaluatorSearch", (IsEvaluatorSearch == true ? 1 : 0));
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetEvaluations", 0));
                    conn.Open();
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr, recorder);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }
        public List<CallEvaluationDTO> GetEnterpriseAssociatedEvaluations(Recorder recorder, string whereClause, int userId, int pageIndex, int pageSize, out int totalPages, out int totalRecords, bool SharedRequired, bool IsEvaluatorSearch)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_GETALL_BY_WHERE_CLAUSE_EC;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    //cmd.Parameters.AddWithValue("@SharedRequired", SharedRequired);
                    cmd.Parameters.AddWithValue("@SharedRequired", SharedRequired == true ? 1 : 0);
                    cmd.Parameters.AddWithValue("@IsEvaluatorSearch", IsEvaluatorSearch == true ? 1 : 0);
                    cmd.Parameters.AddWithValue("@IsEnterpriseSearch", 1);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetEnterpriseAssociatedEvaluations", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr, recorder);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }
        public List<User> GetUsers(Recorder recorder)
        {
            List<User> users = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text; ;
                    cmd.CommandText = "SELECT ACC.*, AG.* FROM t_ACCOUNT ACC LEFT JOIN t_AccountGroup AG ON AG.UserNum = ACC.UserNum WHERE ACC.STATUS = 1 AND (ACC.EXT = 0)";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetUsers", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapUsers(dr, recorder.Id, recorder.Name);
                        //dr.NextResult();
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return users;
        }
        public List<CallEvaluationDTO> PerformActionAndGetEnterpriseEvaluationDTO(Recorder recorder, int actionToPerformOnRecorder, string callSurveyIds, string whereClause, bool SharedRequired, bool IsEvaluatorSearch, int action, int actionValue, int userId, DateTime? ActionDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_DO_ACTION_EC_N_GETALL_BY_WHERE_CLAUSE;
                    cmd.Parameters.AddWithValue("@CallEvaluationIds", callSurveyIds);
                    cmd.Parameters.AddWithValue("@Action", action);
                    cmd.Parameters.AddWithValue("@Value", actionValue);
                    cmd.Parameters.AddWithValue("@ActionDate", ActionDate);
                    cmd.Parameters.AddWithValue("@bActionRequired", actionToPerformOnRecorder == recorder.Id ? true : false);

                    cmd.Parameters.AddWithValue("@SharedRequired", SharedRequired == true ? 1 : 0);
                    cmd.Parameters.AddWithValue("@IsEvaluatorSearch", IsEvaluatorSearch == true ? 1 : 0);
                    cmd.Parameters.AddWithValue("@IsEnterpriseSearch", 1);

                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "PerformActionAndGetEnterpriseEvaluationDTO", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr, recorder);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }
        public List<CallEvaluationDTO> ShareUnshareAndGetEvaluationDTO(Recorder recorder, int actionToPerformOnRecorder, string callSurveyIds, string whereClause, int action, int actionValue, int userId, string shareWith, bool isSharedEvaluatorRetains, DateTime? ActionDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_DO_ACTION_EC_N_GETALL_BY_WHERE_CLAUSE;
                    cmd.Parameters.AddWithValue("@CallEvaluationIds", callSurveyIds);
                    cmd.Parameters.AddWithValue("@Action", action);
                    cmd.Parameters.AddWithValue("@Value", actionValue);
                    cmd.Parameters.AddWithValue("@ActionDate", ActionDate);
                    cmd.Parameters.AddWithValue("@bActionRequired", actionToPerformOnRecorder == recorder.Id ? true : false);
                    cmd.Parameters.AddWithValue("@IsEvaluatorSearch", recorder.Id == 1 ? false : true);

                    cmd.Parameters.AddWithValue("@WhereClause", whereClause.Replace("OR (  )  OR", " "));

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@ShareWith", shareWith);
                    cmd.Parameters.AddWithValue("@IsSharedEvaluatorRetains", isSharedEvaluatorRetains);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "ShareUnshareAndGetEvaluationDTO", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr, recorder);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }
        public CallEvaluation GetCallEvaluationDetailsById(int callEvaluationId, int userId)
        {
            CallEvaluation callEvaluation = new CallEvaluation();
            Survey survey = null;
            List<Answer> answers = null;
            List<Option> options = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_GET_DETAILS_BY_ID;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", callEvaluationId);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetCallEvaluationDetailsById", 0));

                    //Total 8 Tables return.
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Survey ResultSet
                        survey = ORMapper.MapSurvey(dr);//SurveyTable
                        if (survey == null) return callEvaluation;
                        dr.NextResult();
                        //2. Sections
                        survey.Sections = ORMapper.MapSections(dr);//Section Table
                        dr.NextResult();
                        //3. Questions
                        survey.Questions = ORMapper.MapQuestions(dr); //Question Table
                        dr.NextResult();
                        //4. Question Options
                        options = ORMapper.MapOptions(dr); //Option Table
                        if (survey.Questions != null)
                        {
                            survey.Questions.ForEach(q => q.Options = options.FindAll(o => o.QuestionId == q.Id));
                        }
                        dr.NextResult();
                        //5. CallEvaluation Resultset
                        callEvaluation = ORMapper.MapCallEvaluation(dr);
                        dr.NextResult();
                        //6. Answer ResultSet
                        answers = ORMapper.MapCallEvauationAnswerMaster(dr);
                        callEvaluation.Answers = answers;
                        dr.NextResult();
                        //7. Answer Options ResultSet
                        options = ORMapper.MapCallEvauationAnswerChild(dr);
                        callEvaluation.Answers.ForEach(a => a.Options = options.FindAll(o => o.QuestionId == a.QuestionId));
                        dr.NextResult();
                        //8. CallInfo ResultSet
                        callEvaluation.CallInfo = ORMapper.MapCallInfo(dr);
                        callEvaluation.Survey = survey;
                        dr.NextResult();
                        //9. Agent ResultSet
                        callEvaluation.Agent = ORMapper.MapUserWithoutChannel(dr);
                        return callEvaluation;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public CallEvaluation UpdateCallEvaluation(CallEvaluation callEvaluation)
        {
            try
            {
                XElement xEvalCallSurveyDetails = this.CreateCallSurveyDetailsXML(callEvaluation);

                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_UPDATE;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", callEvaluation.Id);
                    cmd.Parameters.AddWithValue("@StatusId", callEvaluation.StatusId);
                    cmd.Parameters.AddWithValue("@ModifiedDate", callEvaluation.ModifiedDate);
                    cmd.Parameters.AddWithValue("@CallEvaluationAnswers", xEvalCallSurveyDetails.ToString());
                    cmd.Parameters.AddWithValue("@ScorerComments", callEvaluation.SupervisorComments);
                    cmd.Parameters.AddWithValue("@EvaluatedScore", callEvaluation.EvaluatedScore);
                    cmd.Parameters["@EvaluatedScore"].Direction = ParameterDirection.Output;
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateCallEvaluation", 0));
                    cmd.ExecuteNonQuery();

                    callEvaluation.EvaluatedScore = Convert.ToSingle(cmd.Parameters["@EvaluatedScore"].Value.ToString());
                    return callEvaluation;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public User UpdateAndGetAssociatedUser(int evalId, int userNum)
        {
            try
            {
                User user = null;
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_UPDATE_ASSOCIATED_AGENT;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", evalId);
                    cmd.Parameters.AddWithValue("@AgentId", userNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateAndGetAssociatedUser", 0));

                    int count = (int)cmd.ExecuteNonQuery();

                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT Acc.*, AG.* FROM t_Account Acc LEFT JOIN t_AccountGroup AG ON AG.UserNum = Acc.UserNum WHERE Acc.UserNum = @userNum";

                    cmd.Parameters.AddWithValue("@userNum", userNum);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateAndGetAssociatedUser", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        user = ORMapper.MapUser(dr);
                    }
                    return user;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public List<UserEvaluation> GetEvaluationId(int surveyId, string callId, int userId)
        {
            List<UserEvaluation> userEvals = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.GET_EVALUATION_ID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("@CallId", callId);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetEvaluationId", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        userEvals = ORMapper.MapUserEvaluationsForSearchPage(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return userEvals;
        }
        public bool PerformActionOnRecorder(Recorder recorder, string callSurveyIds, int action, int actionValue, int userId, DateTime? ActionDate)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_DO_ACTION_EC;
                    cmd.Parameters.AddWithValue("@CallEvaluationIds", callSurveyIds);
                    cmd.Parameters.AddWithValue("@Action", action);
                    cmd.Parameters.AddWithValue("@Value", actionValue);
                    cmd.Parameters.AddWithValue("@ActionDate", ActionDate);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "PerformActionOnRecorder", 0));
                    conn.Open();
                    return Convert.ToBoolean(cmd.ExecuteNonQuery());
                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion

        #region IR
        public static List<CallInfo> SearchRecordedCalls(int pageSize, int pageIndex, string startDate, string endDate, string startTime, string endTime, bool isGlobalSearch, out int totalPages, out long totalRecords, string duration, string criteria, Recorder recorder)
        {
            CallInfo callInfo = null;
            List<CallInfo> calls = new List<CallInfo>();
            try
            {
                System.Diagnostics.Debug.WriteLine(string.Format("{0}-{1}-{2}", recorder.Id, recorder.Name, recorder.ConnectionString));
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_PRIMARY_DB;

                    //cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    //cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    //cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    //cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    //cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);
                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@StartTime", startTime);
                    cmd.Parameters.AddWithValue("@EndTime", endTime);
                    cmd.Parameters.AddWithValue("@IsGlobalSearch", isGlobalSearch);
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "SearchRecordedCalls", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //if (dr.HasRows) calls = new List<CallInfo>();
                        while (dr.Read())
                        {
                            callInfo = new CallInfo();

                            #region --- Mapping ---

                            callInfo.RecorderId = recorder.Id;
                            callInfo.RecorderName = recorder.Name;
                            callInfo.RecorderIP = recorder.IP;
                            callInfo.RowNo = (long)dr["RowNo"];
                            callInfo.CallId = Convert.ToString(dr["CallID"]);
                            callInfo.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                            callInfo.GroupId = Convert.ToInt32(dr["GroupNum"]);
                            callInfo.GroupName = Convert.ToString(dr["GroupName"]);
                            callInfo.AgentId = Convert.ToInt32(dr["UserNum"]);
                            callInfo.ChannelId = Convert.ToInt32(dr["Ext"]);
                            callInfo.ChannelName = Convert.ToString(dr["ExtName"]);
                            callInfo.CallType = Convert.ToInt32(dr["CallType"]);
                            callInfo.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            callInfo.StartTimeString = Convert.ToString(dr["StartTime"]);
                            callInfo.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                            callInfo.FileName = Convert.ToString(dr["FileName"]);
                            callInfo.CustName = Convert.ToString(dr["CustName"]);
                            callInfo.ANI = Convert.ToString(dr["ANI"]);
                            callInfo.CallerID = Convert.ToString(dr["CallerID"]);
                            callInfo.CalledID = Convert.ToString(dr["CalledID"]);
                            callInfo.SGroupID = dr.IsDBNull(dr.GetOrdinal("SGroupID")) ? 0 : Convert.ToInt32(dr["SGroupID"]);
                            callInfo.Tag1 = Convert.ToString(dr["Tag1"]);
                            callInfo.Tag2 = Convert.ToString(dr["Tag2"]);
                            callInfo.Tag3 = Convert.ToString(dr["Tag3"]);
                            callInfo.Tag4 = Convert.ToString(dr["Tag4"]);
                            callInfo.Tag5 = Convert.ToString(dr["Tag5"]);
                            callInfo.Tag6 = Convert.ToString(dr["Tag6"]);
                            callInfo.Tag7 = Convert.ToString(dr["Tag7"]);
                            callInfo.Tag8 = Convert.ToString(dr["Tag8"]);
                            callInfo.Tag9 = Convert.ToString(dr["Tag9"]);
                            callInfo.Tag10 = Convert.ToString(dr["Tag10"]);
                            callInfo.Tag11 = Convert.ToString(dr["Tag11"]);
                            callInfo.Tag12 = Convert.ToString(dr["Tag12"]);
                            callInfo.Tag13 = Convert.ToString(dr["Tag13"]);
                            callInfo.Tag14 = Convert.ToString(dr["Tag14"]);
                            callInfo.Tag15 = Convert.ToString(dr["Tag15"]);
                            callInfo.Tag16 = Convert.ToString(dr["Tag16"]);
                            callInfo.ANIPhone = Convert.ToString(dr["ANI_PH"]);
                            callInfo.ANIName = Convert.ToString(dr["ANI_NAME"]);
                            callInfo.ANIDetails = Convert.ToString(dr["ANI_DETAILS"]);
                            callInfo.CallComments = Convert.ToString(dr["CALL_COMMENT"]);
                            callInfo.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);// dr.IsDBNull(dr.GetOrdinal("ScreenFileNames")) ? null : Convert.ToString(dr["ScreenFileNames"]).Split(',').ToList();
                            callInfo.RetainValue = Convert.ToInt32(dr["RetainValue"]);
                            callInfo.interview_DateTime = Convert.ToString(dr["DateTime"]);
                            callInfo.interview_Interviewee = Convert.ToString(dr["Interviewer"]);
                            callInfo.interview_MdInterviewee = dr.IsDBNull(dr.GetOrdinal("MDInterviewee")) ? null : Convert.ToString(dr["MDInterviewee"]); //Convert.ToString(dr["MdInterviewee"]);

                            callInfo.interview_Interviewer = Convert.ToString(dr["Interviewee"]);
                            callInfo.interview_InterviewId = Convert.ToString(dr["InterviewId"]);
                            callInfo.interview_GPS = Convert.ToString(dr["GPS"]);
                            callInfo.interview_Notes = Convert.ToString(dr["Notes"]);

                            if (callInfo.CallType != 7)
                            {
                                callInfo.CallType_inq = callInfo.CallType;
                            }
                            else
                            {
                                if (Regex.IsMatch(callInfo.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(callInfo.FileName, "m4v", RegexOptions.IgnoreCase))
                                {
                                    callInfo.CallType_inq = 8;
                                }
                                else
                                {
                                    callInfo.CallType_inq = 7;
                                }
                            }
                            callInfo.interview_Interviewee = callInfo.CallType == 7 ? callInfo.interview_Interviewee : callInfo.interview_MdInterviewee;

                            callInfo.BookmarkCSV = "";
                            if (dr.FieldExists("BookMarkXML"))
                            {
                                callInfo.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                                if (!String.IsNullOrEmpty(callInfo.BookmarkXML))
                                {
                                    callInfo.BookmarkCSV = callInfo.BookmarkXML.ConvertXmlStringToCsvString();
                                }
                            }
                            callInfo.VesselId = Convert.ToString(dr["VesselId"]);
                            callInfo.AssetId = DBRecordExtensions.HasColumn(dr, "AssetId") ? Convert.ToString(dr["AssetId"]) : string.Empty;
                            #endregion


                            calls.Add(callInfo);
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }
        public static CallInfoLite GetCallDetailsFromRecorder(Recorder recorder, string callId)
        {
            CallInfoLite call = null;
            try
            {
                using (var conn = DALHelper.GetConnection(0))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALL_GET_BY_ID_PRIMARY_DB;
                    cmd.Parameters.AddWithValue("@CallId", callId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetCallDetailsFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.Read())
                        {
                            call = new CallInfoLite
                            {
                                RowNo = 1,
                                CallId = Convert.ToString(dr["CallID"]),
                                AgentId = Convert.ToInt32(dr["UserNum"]),
                                ChannelId = Convert.ToInt32(dr["Ext"]),
                                //ChannelName = Convert.ToString(dr["ExtName"]),
                                StartTimeString = Convert.ToString(dr["StartTime"]),
                                DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]),
                                FileName = Convert.ToString(dr["FileName"]),
                                CallComments = Convert.ToString(dr["CALL_COMMENT"]),
                                TagRule = Convert.ToString(dr["TagRule"]),
                                RecorderId = recorder.Id,
                                RecorderIP = recorder.IP
                            };
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return call;
        }
        #endregion

        #region Advance Reports

        public List<RPTCallInfo> GetSearchResults(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, Recorder recorder)
        {
            SqlConnection con = DALHelper.GetLocalDBConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", selectKey);
            cmd.Parameters.AddWithValue("@selectYear", selectYear);
            cmd.Parameters.AddWithValue("@selectMonth", selectMonth);
            cmd.Parameters.AddWithValue("@selectDay", selectDay);
            cmd.Parameters.AddWithValue("@FromDate", fromDate);
            cmd.Parameters.AddWithValue("@ToDate", toDate);
            cmd.Parameters.AddWithValue("@TimeRange", timeRange);
            cmd.Parameters.AddWithValue("@Duration", duration);
            cmd.Parameters.AddWithValue("@groupKey", groupKey);
            cmd.Parameters.AddWithValue("@groupYear", groupYear);
            cmd.Parameters.AddWithValue("@groupMonth", groupMonth);
            cmd.Parameters.AddWithValue("@groupDay", groupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", optionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);

            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;

            try
            {
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetSearchResults", 0));
                con.Open();
                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"]),
                                     recorder.Id,
                                     recorder.Name)
                                 { }).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }

        public List<RPTCallInfoDetail> GetDetailSearchResults(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, Recorder recorder)
        {
            SqlConnection con = DALHelper.GetLocalDBConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_CALLDETAILS_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", selectKey);
            //cmd.Parameters.AddWithValue("@selectYear", selectYear);
            //cmd.Parameters.AddWithValue("@selectMonth", selectMonth);
            cmd.Parameters.AddWithValue("@selectDay", selectDay);
            cmd.Parameters.AddWithValue("@FromDate", fromDate);
            cmd.Parameters.AddWithValue("@ToDate", toDate);
            cmd.Parameters.AddWithValue("@TimeRange", timeRange);
            cmd.Parameters.AddWithValue("@Duration", duration);
            //cmd.Parameters.AddWithValue("@groupKey", groupKey);
            //cmd.Parameters.AddWithValue("@groupYear", groupYear);
            //cmd.Parameters.AddWithValue("@groupMonth", groupMonth);
            //cmd.Parameters.AddWithValue("@groupDay", groupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", optionString);


            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);

            DataTable dtCallInfoDetails = new DataTable();
            List<RPTCallInfoDetail> detailResults = null;

            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetDetailSearchResults", 0));
                dataAdapter.Fill(dtCallInfoDetails);
                detailResults = (from DataRow row in dtCallInfoDetails.Rows
                                 select new RPTCallInfoDetail(
                                     row["Key"].ToString(),
                                     row["CallID"].ToString(),
                                     row["UserName"].ToString(),
                                     row["GroupName"].ToString(),
                                     row["ChannelName"].ToString(),
                                     row["Ext"].ToString(),
                                     row["StartTime"].ToString(),
                                     Convert.ToInt64(row["Duration"]),
                                     //(DayOfWeek)Enum.Parse(typeof(DayOfWeek), (Convert.ToInt32(row["Day"]) - 1).ToString()),
                                     recorder.Id, recorder.Name
                                     )
                                 { }
                                     ).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            detailResults.TrimExcess();
            return detailResults;
        }

        /*public ReportResponse GetCallSearchResults(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, bool getDetailReportData, Recorder recorder)
        {
            SqlConnection con = DALHelper.GetLocalDBConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", selectKey);
            cmd.Parameters.AddWithValue("@selectYear", selectYear);
            cmd.Parameters.AddWithValue("@selectMonth", selectMonth);
            cmd.Parameters.AddWithValue("@selectDay", selectDay);
            cmd.Parameters.AddWithValue("@FromDate", fromDate);
            cmd.Parameters.AddWithValue("@ToDate", toDate);
            cmd.Parameters.AddWithValue("@TimeRange", timeRange);
            cmd.Parameters.AddWithValue("@Duration", duration);
            cmd.Parameters.AddWithValue("@groupKey", groupKey);
            cmd.Parameters.AddWithValue("@groupYear", groupYear);
            cmd.Parameters.AddWithValue("@groupMonth", groupMonth);
            cmd.Parameters.AddWithValue("@groupDay", groupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", optionString);
            cmd.Parameters.AddWithValue("@getCallDetails", getDetailReportData);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataSet dsCalls = new DataSet();
            List<RPTCallInfo> searchResults = null;
            List<RPTCallInfoDetail> detailResults = null;
            try
            {
                con.Open();
                dataAdapter.Fill(dsCalls);
                searchResults = (from DataRow row in dsCalls.Tables[0].Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"]),
                                     recorder.Id,
                                     recorder.Name
                                     )
                                 { }
                                     ).ToList();
                //).OrderBy(c => c.CallDate).ToList();
                if (getDetailReportData)
                {
                    detailResults = (from DataRow row in dsCalls.Tables[1].Rows
                                     select new RPTCallInfoDetail(
                                         row["Key"].ToString(),
                                         row["CallID"].ToString(),
                                         row["UserName"].ToString(),
                                         row["GroupName"].ToString(),
                                         row["ChannelName"].ToString(),
                                         row["Ext"].ToString(),
                                         row["StartTime"].ToString(),
                                         Convert.ToInt64(row["Duration"]))
                                     { }
                                         ).ToList();
                }
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess(); detailResults.TrimExcess();
            return new ReportResponse { CallInfos = searchResults, CallInfoDetails = detailResults };
        }*/

        public List<RPTCallInfo> GetSearchResultsMonthDayOfWeek(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, Recorder recorder)
        {
            SqlConnection con = DALHelper.GetLocalDBConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", selectKey);
            cmd.Parameters.AddWithValue("@selectYear", selectYear);
            cmd.Parameters.AddWithValue("@selectMonth", selectMonth);
            cmd.Parameters.AddWithValue("@selectDay", selectDay);
            cmd.Parameters.AddWithValue("@FromDate", fromDate);
            cmd.Parameters.AddWithValue("@ToDate", toDate);
            cmd.Parameters.AddWithValue("@TimeRange", timeRange);
            cmd.Parameters.AddWithValue("@Duration", duration);
            cmd.Parameters.AddWithValue("@groupKey", groupKey);
            cmd.Parameters.AddWithValue("@groupYear", groupYear);
            cmd.Parameters.AddWithValue("@groupMonth", groupMonth);
            cmd.Parameters.AddWithValue("@groupDay", groupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", optionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> monthWeekDays = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetSearchResultsMonthDayOfWeek", 0));
                dataAdapter.Fill(dt);
                monthWeekDays = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     (DayOfWeek)Enum.Parse(typeof(DayOfWeek), (Convert.ToInt32(row["Day"]) - 1).ToString()),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"]),
                                     recorder.Id,
                                     recorder.Name
                                     )
                                 { }
                                     ).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            monthWeekDays.TrimExcess();
            return monthWeekDays;
        }

        public List<RPTCallInfo> GetSearchResultsDayOfWeek(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, Recorder recorder)
        {
            SqlConnection con = DALHelper.GetLocalDBConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", selectKey);
            cmd.Parameters.AddWithValue("@selectYear", selectYear);
            cmd.Parameters.AddWithValue("@selectMonth", selectMonth);
            cmd.Parameters.AddWithValue("@selectDay", selectDay);
            cmd.Parameters.AddWithValue("@FromDate", fromDate);
            cmd.Parameters.AddWithValue("@ToDate", toDate);
            cmd.Parameters.AddWithValue("@TimeRange", timeRange);
            cmd.Parameters.AddWithValue("@Duration", duration);
            cmd.Parameters.AddWithValue("@groupKey", groupKey);
            cmd.Parameters.AddWithValue("@groupYear", groupYear);
            cmd.Parameters.AddWithValue("@groupMonth", groupMonth);
            cmd.Parameters.AddWithValue("@groupDay", groupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", optionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetSearchResultsDayOfWeek", 0));
                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo
                                 {
                                     Key = row["Key"].ToString(),
                                     DayOfWeek = (DayOfWeek)Enum.Parse(typeof(DayOfWeek), (Convert.ToInt32(row["Day"]) - 1).ToString()),
                                     Count = Convert.ToInt64(row["Count"]),
                                     Total = Convert.ToInt64(row["Total"]),
                                     Avg = Convert.ToInt64(row["Avg"]),
                                     RecorderId = recorder.Id,
                                     RecoderName = recorder.Name
                                 }).ToList();

            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }

        public List<RPTCallInfo> GetSearchResultsHour(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, Recorder recorder)
        {
            SqlConnection con = DALHelper.GetLocalDBConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_HOUR_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", selectKey);
            cmd.Parameters.AddWithValue("@selectYear", selectYear);
            cmd.Parameters.AddWithValue("@selectMonth", selectMonth);
            cmd.Parameters.AddWithValue("@selectDay", selectDay);
            cmd.Parameters.AddWithValue("@FromDate", fromDate);
            cmd.Parameters.AddWithValue("@ToDate", toDate);
            cmd.Parameters.AddWithValue("@TimeRange", timeRange);
            cmd.Parameters.AddWithValue("@Duration", duration);
            cmd.Parameters.AddWithValue("@groupKey", groupKey);
            cmd.Parameters.AddWithValue("@groupYear", groupYear);
            cmd.Parameters.AddWithValue("@groupMonth", groupMonth);
            cmd.Parameters.AddWithValue("@groupDay", groupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", optionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetSearchResultsHour", 0));
                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt32(row["CallHour"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"]),
                                     recorder.Id,
                                     recorder.Name
                                     )
                                 { }
                                     ).ToList();
                //).OrderBy(c => c.CallDate).ToList();

            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }

        public static CallInfo GetCallByIdFromRecorder(Recorder recorder, string callId)
        {
            CallInfo call = null;
            try
            {
                using (var conn = DALHelper.GetConnection(0))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALL_GET_BY_ID;
                    cmd.Parameters.AddWithValue("@CallId", callId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetCallByIdFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        call = ORMapper.MapCallInfo(dr);
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return call;
        }

        #endregion

        #region 911 PBX Reports

        public List<RPTCallInfo> GetSearchResults911(string selectKey, string selectYear, string selectMonth, string selectDay, string fromDate, string toDate, string timeRange, string duration, string groupKey, string groupYear, string groupMonth, string groupDay, string optionString, Recorder recorder)
        {
            SqlConnection con = DALHelper.GetLocalDBConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.AdvanceReports.SEARCH_RESULT_EXCEL_911PBX_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@selectKey", selectKey);
            cmd.Parameters.AddWithValue("@selectYear", selectYear);
            cmd.Parameters.AddWithValue("@selectMonth", selectMonth);
            cmd.Parameters.AddWithValue("@selectDay", selectDay);
            cmd.Parameters.AddWithValue("@FromDate", fromDate);
            cmd.Parameters.AddWithValue("@ToDate", toDate);
            cmd.Parameters.AddWithValue("@TimeRange", timeRange);
            cmd.Parameters.AddWithValue("@Duration", duration);
            cmd.Parameters.AddWithValue("@groupKey", groupKey);
            cmd.Parameters.AddWithValue("@groupYear", groupYear);
            cmd.Parameters.AddWithValue("@groupMonth", groupMonth);
            cmd.Parameters.AddWithValue("@groupDay", groupDay);
            cmd.Parameters.AddWithValue("@OptionSTR", optionString);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);

            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;

            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetSearchResults911", 0));
                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     Convert.ToInt64(row["Total"]),
                                     Convert.ToInt64(row["Avg"]),
                                     recorder.Id,
                                     recorder.Name,
                                     Convert.ToInt32(row["NoOfRings"]),
                                     Convert.ToInt32(row["TotalTransferredCount"]),
                                     Convert.ToInt32(row["TotalAbandonedCount"]),
                                     Convert.ToInt64(row["TotalRingTime"]),
                                     Convert.ToInt64(row["TotalTalkTime"]),
                                     Convert.ToInt64(row["TotalHoldTime"]))
                                 { }).ToList();

            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }


        #endregion

        #region Standard Reports, Call Audit/Not Audit Reports
        public static List<RPTCallInfo> GetCallAuditSearchResults(string fromDate, string toDate, string optionStr, Recorder recorder)
        {
            SqlConnection con = DALHelper.GetLocalDBConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.CallAuditReport.CALL_AUDIT_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@FromDate", fromDate);
            cmd.Parameters.AddWithValue("@ToDate", toDate);
            cmd.Parameters.AddWithValue("@OptionSTR", optionStr);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetCallAuditSearchResults", 0));
                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     recorder.Id,
                                     recorder.Name
                                     )
                                 { }
                                     ).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }
        public static List<RPTCallInfo> GetCallsNotAuditedSearchResults(string fromDate, string toDate, string optionStr, Recorder recorder)
        {
            SqlConnection con = DALHelper.GetLocalDBConnection();
            SqlCommand cmd = new SqlCommand(DBConstants.Reports.CallAuditReport.CALL_AUDIT_NOT_AUDITED_CALLS_GETALL, con);
            cmd.CommandTimeout = CMD_TIMEOUT;
            cmd.CommandType = CommandType.StoredProcedure;

            cmd.Parameters.AddWithValue("@FromDate", fromDate);
            cmd.Parameters.AddWithValue("@ToDate", toDate);
            cmd.Parameters.AddWithValue("@OptionSTR", optionStr);

            SqlDataAdapter dataAdapter = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            List<RPTCallInfo> searchResults = null;
            try
            {
                con.Open();
                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetCallsNotAuditedSearchResults", 0));
                dataAdapter.Fill(dt);

                searchResults = (from DataRow row in dt.Rows
                                 select new RPTCallInfo(
                                     row["Key"].ToString(),
                                     Convert.ToInt32(row["Year"]),
                                     Convert.ToInt32(row["Month"]),
                                     Convert.ToInt32(row["Day"]),
                                     Convert.ToInt64(row["Count"]),
                                     recorder.Id,
                                     recorder.Name
                                     )
                                 { }
                                     ).ToList();
            }
            catch (Exception ex) { throw ex; }
            finally { con.Close(); }
            searchResults.TrimExcess();
            return searchResults;
        }
        #endregion


        #region QA Evaluation Reports

        public List<RPTEvaluation> GetRPTEvaluationReportFromRecorder(Recorder recorder, string criteria, int userId)
        {
            List<RPTEvaluation> callEvaluations = new List<RPTEvaluation>();
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.RPT_CALLEVALUATION_GET_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@Criteria", criteria);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetRPTEvaluationReportFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluations = ORMapper.MapRPTEvaluations(dr);
                        return callEvaluations;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Utility Methods

        private static List<Permission> GetPermissions(int uNum, UserGroup uGroup, List<Permission> permissions)
        {
            try
            {
                List<Permission> uPermissions = new List<Permission>();
                Permission uPermission = null;
                if (uGroup == null)
                    return new List<Permission>();
                foreach (var p in permissions)
                {
                    if (Convert.ToInt32(uGroup.AssignAuth.Substring(p.Id, 1)) == 1)
                    {
                        uPermission = (Permission)p.Clone();
                        uPermission.UserNum = uNum;
                        uPermissions.Add(uPermission);
                    }

                }
                return uPermissions;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private static List<Permission> GetEnterprisePermissions(int uNum, UserGroup uGroup, List<Permission> permissions)
        {
            try
            {
                List<Permission> uPermissions = new List<Permission>();
                Permission entPermission = null;
                if (uGroup == null)
                    return new List<Permission>();
                foreach (var p in permissions)
                {
                    if (Convert.ToInt32(uGroup.EnterpriseAssignAuth.Substring(p.Id, 1)) == 1)
                    {
                        entPermission = (Permission)p.Clone();
                        entPermission.UserNum = uNum;
                        uPermissions.Add(entPermission);
                    }
                }
                return uPermissions;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private XElement CreateCallSurveyDetailsXML(CallEvaluation callEvaluation)
        {
            try
            {
                XElement xcallSurveyDetails = new XElement("CallEvaluationAnswers");

                if (callEvaluation.Answers.Count == 0) return xcallSurveyDetails; // in-case of NO Answer

                foreach (var answer in callEvaluation.Answers)
                {
                    if (answer.Options != null)
                    {
                        foreach (var option in answer.Options)
                        {
                            xcallSurveyDetails.Add(new XElement("CallEvaluationAnswer",
                                        new XAttribute("CallEvaluationId", callEvaluation.Id),
                                        new XAttribute("ModifiedDate", callEvaluation.ModifiedDate),
                                        new XAttribute("IsDeleted", false),
                                        new XAttribute("QuestionId", answer.QuestionId),
                                        new XAttribute("QuestionOptionId", option.Id),
                                        new XAttribute("AnswerText", answer.AnswerValue)
                                    ));
                        }
                    }
                    else
                    {
                        xcallSurveyDetails.Add(new XElement("CallEvaluationAnswer",
                                        new XAttribute("CallEvaluationId", callEvaluation.Id),
                                        new XAttribute("ModifiedDate", callEvaluation.ModifiedDate),
                                        new XAttribute("IsDeleted", false),
                                        new XAttribute("QuestionId", answer.QuestionId),
                                        new XAttribute("QuestionOptionId", 0),
                                        new XAttribute("AnswerText", answer.AnswerValue)
                                    ));
                    }
                }
                return xcallSurveyDetails;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            
        }
        private XElement CreateOptionsXML(List<Option> aOptions)
        {
            try
            {
                XElement options = new XElement("Options");
                foreach (var option in aOptions)
                {
                    options.Add(new XElement("Option",
                                    new XAttribute("Id", option.Id),
                                    new XAttribute("QuestionId", option.QuestionId),
                                    new XAttribute("Title", option.Title),
                                    new XAttribute("Ordering", option.Ordering),
                                    new XAttribute("CreatedDate", DateTime.Now),
                                    new XAttribute("IsDeleted", option.IsDeleted),
                                    new XAttribute("Score", option.Score)
                                ));
                }
                return options;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private XElement CreateQuestionsXML(List<Question> eQuestions)
        {
            try
            {
                XElement questions = new XElement("Questions");
                foreach (var qItem in eQuestions)
                {
                    questions.Add(new XElement("Question",
                                    new XAttribute("Id", qItem.Id),
                                    new XAttribute("SurveyId", qItem.SurveyId),
                                    new XAttribute("Ordering", qItem.Ordering),
                                    new XAttribute("IsDeleted", qItem.IsDeleted)
                                ));
                }
                return questions;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion

        #region Private Functions : Added By Arivu previously in Non-EC Mode
        private void EvaluatedCallsDetailBySurveyId(int surveyId, Recorder recorder)
        {
            List<CallEvaluation> callEvaluations = new List<CallEvaluation>();
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_GETALL_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "EvaluatedCallsDetailBySurveyId", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluations = ORMapper.MapCallEvaluationsForPublishedForm(dr);

                        foreach (var callEval in callEvaluations)
                        {
                            if (callEval.StatusId == 3 || callEval.StatusId == 4)
                            {
                                UpdateStatusIdInCallEvaluation(surveyId, recorder);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            { }

        }
        private void UpdateStatusIdInCallEvaluation(int surveyId, Recorder recorder)
        {
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.STATUSID_UPDATE_BY_SURVEYID;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateStatusIdInCallEvaluation", 0));
                    cmd.ExecuteNonQuery();

                }
            }
            catch (Exception ex) { }
        }
        #endregion

        #region Validation/Security
        public bool CheckDBConnection()
        {
            using (var dbConnection = DALHelper.GetLocalDBConnection())
            {
                try
                {
                    dbConnection.Open();
                    return true;
                }
                catch (SqlException)
                {
                    return false;
                }
            }
        }
        #endregion

        #region Channels
        public static List<Channel> GetAudioChannelsFromRecorder(Recorder recorder)
        {
            List<Channel> channels = null;
            Channel channel = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.AUDIO_CHANNELS_GETLIST;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetAudioChannelsFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (channels == null) channels = new List<Channel>();
                        while (dr.Read())
                        {
                            channel = new Channel();
                            channel.Ext = Convert.ToString(dr["Ext"]);
                            channel.RecID = Convert.ToInt32(dr["RecID"]);
                            channel.ChNum = Convert.ToInt32(dr["ChNum"]);
                            channel.ExtIP = Convert.ToString(dr["ExtIP"]);
                            channel.VoIP = Convert.ToString(dr["VoIP"]);
                            channel.Triger = Convert.ToInt32(dr["Triger"]);
                            channel.Min_Du = Convert.ToInt32(dr["Min_Du"]);
                            channel.Hold_Du = Convert.ToInt32(dr["Hold_Du"]);
                            channel.Max_Du = Convert.ToInt32(dr["Max_Du"]);
                            channel.Tr = Convert.ToInt32(dr["Tr"]);
                            channel.Tf = Convert.ToInt32(dr["Tf"]);
                            channel.Gain = Convert.ToInt32(dr["Gain"]);
                            channel.EncType = Convert.ToInt32(dr["EncType"]);
                            channel.Status = Convert.ToInt32(dr["Status"]);
                            //channel.Create_T = Convert.ToString(dr[""]);
                            //channel.Modify_T = Convert.ToString(dr[""]);
                            //channel.Delete_T = Convert.ToString(dr[""]);
                            channel.Descr = Convert.ToString(dr["Descr"]);
                            channel.ExtName = Convert.ToString(dr["ExtName"]);
                            channel.RODMAC = Convert.ToString(dr["RODMAC"]);
                            channel.CallerIDMod = Convert.ToInt32(dr["CallerIDMod"]);
                            channel.HangupDTRMVoltage = Convert.ToInt32(dr["HangupDTRMVoltage"]);
                            channel.LineHoldTime = Convert.ToInt32(dr["LineHoldTime"]);
                            channel.EnableAGC = Convert.ToInt32(dr["EnableAGC"]);
                            channel.OffHook = Convert.ToInt32(dr["OffHook"]);
                            channel.OffHookSub = Convert.ToString(dr["OffHookSub"]);
                            channel.AudioOn = Convert.ToInt32(dr["AudioOn"]);
                            channel.AudioOnSub = Convert.ToString(dr["AudioOnSub"]);
                            channel.FuncBtn = Convert.ToInt32(dr["FuncBtn"]);
                            channel.FuncBtnSub = Convert.ToString(dr["FuncBtnSub"]);
                            channel.OnHook = Convert.ToInt32(dr["OnHook"]);
                            channel.OnHookSub = Convert.ToString(dr["OnHookSub"]);
                            channel.AudioOff = Convert.ToInt32(dr["AudioOff"]);
                            channel.AudioOffSub = Convert.ToString(dr["AudioOffSub"]);
                            channel.ReleaseBtn = Convert.ToInt32(dr["ReleaseBtn"]);
                            channel.ReleaseBtnSub = Convert.ToString(dr["ReleaseBtnSub"]);
                            //channel.Channeltype
                            //channel.IsRevCell
                            //channel.RecName = Convert.ToString(dr["RecName"]);
                            channel.RecName = recorder.Name;

                            channels.Add(channel);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return channels;

        }

        public static Channel GetAudioChannelFromRecorder(Recorder recorder, int channelId)
        {
            Channel channel = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.AUDIO_CHANNEL_GETBYID;
                    cmd.Parameters.AddWithValue("@ChannelId", channelId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetAudioChannelFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            channel = new Channel();
                            channel.Ext = Convert.ToString(dr["Ext"]);
                            channel.RecID = Convert.ToInt32(dr["RecID"]);
                            channel.ChNum = Convert.ToInt32(dr["ChNum"]);
                            channel.ExtIP = Convert.ToString(dr["ExtIP"]);
                            channel.VoIP = Convert.ToString(dr["VoIP"]);
                            channel.Triger = Convert.ToInt32(dr["Triger"]);
                            channel.Min_Du = Convert.ToInt32(dr["Min_Du"]);
                            channel.Hold_Du = Convert.ToInt32(dr["Hold_Du"]);
                            channel.Max_Du = Convert.ToInt32(dr["Max_Du"]);
                            channel.Tr = Convert.ToInt32(dr["Tr"]);
                            channel.Tf = Convert.ToInt32(dr["Tf"]);
                            channel.Gain = Convert.ToInt32(dr["Gain"]);
                            channel.EncType = Convert.ToInt32(dr["EncType"]);
                            channel.Status = Convert.ToInt32(dr["Status"]);
                            //channel.Create_T = Convert.ToString(dr[""]);
                            //channel.Modify_T = Convert.ToString(dr[""]);
                            //channel.Delete_T = Convert.ToString(dr[""]);
                            channel.Descr = Convert.ToString(dr["Descr"]);
                            channel.ExtName = Convert.ToString(dr["ExtName"]);
                            channel.RODMAC = Convert.ToString(dr["RODMAC"]);
                            channel.CallerIDMod = Convert.ToInt32(dr["CallerIDMod"]);
                            channel.HangupDTRMVoltage = Convert.ToInt32(dr["HangupDTRMVoltage"]);
                            channel.LineHoldTime = Convert.ToInt32(dr["LineHoldTime"]);
                            channel.EnableAGC = Convert.ToInt32(dr["EnableAGC"]);
                            channel.OffHook = Convert.ToInt32(dr["OffHook"]);
                            channel.OffHookSub = Convert.ToString(dr["OffHookSub"]);
                            channel.AudioOn = Convert.ToInt32(dr["AudioOn"]);
                            channel.AudioOnSub = Convert.ToString(dr["AudioOnSub"]);
                            channel.FuncBtn = Convert.ToInt32(dr["FuncBtn"]);
                            channel.FuncBtnSub = Convert.ToString(dr["FuncBtnSub"]);
                            channel.OnHook = Convert.ToInt32(dr["OnHook"]);
                            channel.OnHookSub = Convert.ToString(dr["OnHookSub"]);
                            channel.AudioOff = Convert.ToInt32(dr["AudioOff"]);
                            channel.AudioOffSub = Convert.ToString(dr["AudioOffSub"]);
                            channel.ReleaseBtn = Convert.ToInt32(dr["ReleaseBtn"]);
                            channel.ReleaseBtnSub = Convert.ToString(dr["ReleaseBtnSub"]);
                            //channel.Channeltype
                            //channel.IsRevCell
                            //channel.RecName = Convert.ToString(dr["RecName"]);
                            channel.RecName = recorder.Name;

                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return channel;

        }

        public static int DeleteAudioChannels(Recorder recorder, List<int> channelIds)
        {
            //var listIds = new List<int>() { 2154, 2155, 2156, 2157, 2158, 2159, 2160, 2161, 2162, 2164, 2165, 2166 };
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                {
                    conn.Open();

                    using (var tran = conn.BeginTransaction())
                    {
                        using (var cmd = new SqlCommand("DELETE FROM t_ExtInfo WHERE Ext IN (" + String.Join(",", channelIds) + ")", conn, tran))
                        {
                            cmd.CommandTimeout = CMD_TIMEOUT;
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "DeleteAudioChannels", 0));

                            rowsAffected = cmd.ExecuteNonQuery();

                            cmd.CommandText = "DELETE FROM t_Account WHERE Ext IN (" + String.Join(",", channelIds) + ")";
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "DeleteAudioChannels", 0));
                            rowsAffected += cmd.ExecuteNonQuery();

                            cmd.CommandText = "DELETE FROM t_ExtGroup WHERE Ext IN (" + String.Join(",", channelIds) + ")";
                            Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "DeleteAudioChannels", 0));
                            rowsAffected += cmd.ExecuteNonQuery();
                        }
                        tran.Commit();
                    }
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }


        public static int InsertAudioChannels(Recorder recorder, List<Channel> channels, int recorderId)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                {
                    conn.Open();
                    using (var tran = conn.BeginTransaction())
                    {
                        foreach (var channel in channels)
                        {
                            using (SqlCommand cmd = new SqlCommand(DBConstants.VoiceRec.AUDIO_CHANNEL_INSERT, conn, tran))
                            {
                                cmd.CommandTimeout = CMD_TIMEOUT;
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@Ext", channel.Ext);
                                cmd.Parameters.AddWithValue("@RecID", recorderId);
                                cmd.Parameters.AddWithValue("@ChNum", channel.ChNum);
                                cmd.Parameters.AddWithValue("@ExtIP", channel.ExtIP);
                                cmd.Parameters.AddWithValue("@VoIP", channel.VoIP);
                                cmd.Parameters.AddWithValue("@Triger", channel.Triger);
                                cmd.Parameters.AddWithValue("@Min_Du", channel.Min_Du);
                                cmd.Parameters.AddWithValue("@Hold_Du", channel.Hold_Du);
                                cmd.Parameters.AddWithValue("@Max_Du", channel.Max_Du);
                                cmd.Parameters.AddWithValue("@Tr", channel.Tr);
                                cmd.Parameters.AddWithValue("@Tf", channel.Tf);
                                cmd.Parameters.AddWithValue("@Gain", channel.Gain);
                                cmd.Parameters.AddWithValue("@EncType", channel.EncType);
                                cmd.Parameters.AddWithValue("@Status", channel.Status);
                                //cmd.Parameters.AddWithValue("@Create_T", channel.Create_T);
                                //cmd.Parameters.AddWithValue("@Modify_T", channel.Modify_T);
                                cmd.Parameters.AddWithValue("@Descr", channel.Descr);
                                cmd.Parameters.AddWithValue("@ExtName", channel.ExtName);
                                cmd.Parameters.AddWithValue("@RODMAC", channel.RODMAC);
                                cmd.Parameters.AddWithValue("@CallerIDMod", channel.CallerIDMod);
                                cmd.Parameters.AddWithValue("@HangupDTRMVoltage", channel.HangupDTRMVoltage);
                                cmd.Parameters.AddWithValue("@LineHoldTime", channel.LineHoldTime);
                                cmd.Parameters.AddWithValue("@EnableAGC", channel.EnableAGC);
                                cmd.Parameters.AddWithValue("@OffHook", channel.OffHook);
                                cmd.Parameters.AddWithValue("@OffHookSub", channel.OffHookSub);
                                cmd.Parameters.AddWithValue("@AudioOn", channel.AudioOn);
                                cmd.Parameters.AddWithValue("@AudioOnSub", channel.AudioOnSub);
                                cmd.Parameters.AddWithValue("@FuncBtn", channel.FuncBtn);
                                cmd.Parameters.AddWithValue("@FuncBtnSub", channel.FuncBtnSub);
                                cmd.Parameters.AddWithValue("@OnHook", channel.OnHook);
                                cmd.Parameters.AddWithValue("@OnHookSub", channel.OnHookSub);
                                cmd.Parameters.AddWithValue("@AudioOff", channel.AudioOn);
                                cmd.Parameters.AddWithValue("@AudioOffSub", channel.AudioOnSub);
                                cmd.Parameters.AddWithValue("@ReleaseBtn", channel.ReleaseBtn);
                                cmd.Parameters.AddWithValue("@ReleaseBtnSub", channel.ReleaseBtnSub);
                                cmd.Parameters.AddWithValue("@Channeltype", channel.Channeltype);
                                cmd.Parameters.AddWithValue("@IsRevCell", channel.IsRevCell);
                                Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "InsertAudioChannels", 0));

                                rowsAffected += cmd.ExecuteNonQuery();
                            }
                        }
                        tran.Commit();
                    }
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }


        public static int UpdateAudioChannel(Recorder recorder, Channel channel)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                {
                    conn.Open();
                    using (var cmd = conn.CreateCommand())
                    {
                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.VoiceRec.AUDIO_CHANNEL_UPDATE;
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@Ext", channel.Ext);
                        cmd.Parameters.AddWithValue("@ExtIP", channel.ExtIP);
                        cmd.Parameters.AddWithValue("@VoIP", channel.VoIP);
                        cmd.Parameters.AddWithValue("@Triger", channel.Triger);
                        cmd.Parameters.AddWithValue("@Min_Du", channel.Min_Du);
                        cmd.Parameters.AddWithValue("@Hold_Du", channel.Hold_Du);
                        cmd.Parameters.AddWithValue("@Max_Du", channel.Max_Du);
                        cmd.Parameters.AddWithValue("@Descr", channel.Descr);
                        cmd.Parameters.AddWithValue("@ExtName", channel.ExtName);
                        cmd.Parameters.AddWithValue("@RODMAC", channel.RODMAC);
                        //cmd.Parameters.AddWithValue("@CallerIDMod", channel.CallerIDMod);
                        //cmd.Parameters.AddWithValue("@HangupDTRMVoltage", channel.HangupDTRMVoltage);
                        cmd.Parameters.AddWithValue("@LineHoldTime", channel.LineHoldTime);
                        cmd.Parameters.AddWithValue("@EnableAGC", channel.EnableAGC);
                        Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "UpdateAudioChannel", 0));
                        rowsAffected = cmd.ExecuteNonQuery();
                    }
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #region Call Audit
        public static int CallAuditSave(string callId, int userNum, string ipAddress, bool isSaved)
        {
            int rowsAffected = 0;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.CallAudit.CALL_AUDIT_SAVE;
                    cmd.Parameters.AddWithValue("@CallId", callId);
                    cmd.Parameters.AddWithValue("@UserNum", userNum);
                    cmd.Parameters.AddWithValue("@IPAddress", ipAddress);
                    cmd.Parameters.AddWithValue("@IsSaved", isSaved);


                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "CallAuditSave", 0));
                    rowsAffected = cmd.ExecuteNonQuery();
                }
                return rowsAffected;
            }
            catch (Exception ex) { throw ex; }
        }
        public static List<CallAuditReport> GetAuditedCallsByExtension(DateTime startDate, DateTime endDate, int ext)
        {
            List<CallAuditReport> callAuditReports = new List<CallAuditReport>();
            CallAuditReport callAuditReport = new CallAuditReport();
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.CallAuditReport.CALL_AUDIT_GET_BY_EXTENSION;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@ext", ext);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetAuditedCallsByExtension", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            callAuditReports = new List<CallAuditReport>();
                            while (dr.Read())
                            {
                                callAuditReport = new CallAuditReport();
                                callAuditReport.RowNo = Convert.ToInt32(dr["RowNo"]);
                                callAuditReport.CallId = Convert.ToString(dr["CallId"]);
                                callAuditReport.UserNum = (int)dr["UserNum"];
                                callAuditReport.CallType = Convert.ToInt32(dr["CallType"]);
                                callAuditReport.StartTime = DateTime.ParseExact(Convert.ToString(dr["StartTime"]), "yyyyMMddHHmmss", null).ToString();
                                callAuditReport.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                                callAuditReport.Ext = Convert.ToInt32(dr["Ext"]);
                                callAuditReport.ExtName = Convert.ToString(dr["ExtName"]);
                                callAuditReport.FileName = Convert.ToString(dr["FileName"]);
                                callAuditReport.NoOfAudits = Convert.ToInt32(dr["NoOfAudits"]);

                                callAuditReports.Add(callAuditReport);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callAuditReports;
        }
        public static List<CallAuditReport> GetCallAuditTrail(DateTime startDate, DateTime endDate, string callId)
        {
            List<CallAuditReport> callAuditReports = new List<CallAuditReport>();
            CallAuditReport callAuditReport = new CallAuditReport();
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Reports.CallAuditReport.CALL_AUDIT_TRAIL;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@callId", callId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetCallAuditTrail", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            callAuditReports = new List<CallAuditReport>();
                            while (dr.Read())
                            {
                                callAuditReport = new CallAuditReport();
                                callAuditReport.Id = Convert.ToInt32(dr["Id"]);
                                callAuditReport.UserNum = (int)dr["UserNum"];
                                callAuditReport.AuditedBy = Convert.ToString(dr["AuditedBy"]);
                                callAuditReport.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                callAuditReport.IPAddress = Convert.ToString(dr["IPAddress"]);

                                callAuditReport.CallId = Convert.ToString(dr["CallId"]);
                                callAuditReport.CallType = Convert.ToInt32(dr["CallType"]);
                                callAuditReport.StartTime = DateTime.ParseExact(Convert.ToString(dr["StartTime"]), "yyyyMMddHHmmss", null).ToString();
                                callAuditReport.DurationInMilliSeconds = Convert.ToInt32(dr["DurationInMilliSeconds"]);
                                callAuditReport.Ext = Convert.ToInt32(dr["Ext"]);
                                callAuditReport.ExtName = Convert.ToString(dr["ExtName"]);
                                callAuditReport.FileName = Convert.ToString(dr["FileName"]);
                                callAuditReport.AuditDateTime = Convert.ToDateTime(dr["AuditDateTime"]);
                                callAuditReport.IsSaved = Convert.ToBoolean(dr["IsSaved"]);
                                callAuditReport.IsTagged = Convert.ToBoolean(dr["IsTagged"]);

                                callAuditReports.Add(callAuditReport);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return callAuditReports;
        }
        #endregion


        #region Search Calls Revised Functionality

        #region Primary Database Search
        public DALMediaResponse GetDefaultSearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, string startDate, string endDate, string startTime, string endTime, bool isGlobal, string customSpParam, int recId, string recName)
        {
            try
            {
                List<MediaInfo> calls = new List<MediaInfo>();
                int totalPages = 0;
                long totalRecords = 0;

                using (var conn = DALHelper.GetLocalDBConnection())
                {
                    using (var cmd = new SqlCommand())
                    {
                        cmd.Connection = conn;

                        cmd.CommandTimeout = CMD_TIMEOUT;
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_PRIMARY_DB;

                        cmd.Parameters.AddWithValue("@StartDate", startDate);
                        cmd.Parameters.AddWithValue("@EndDate", endDate);
                        cmd.Parameters.AddWithValue("@StartTime", startTime);
                        cmd.Parameters.AddWithValue("@EndTime", endTime);
                        cmd.Parameters.AddWithValue("@DurationSTR", duration);
                        cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                        cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                        cmd.Parameters.AddWithValue("@IsGlobalSearch", isGlobal);

                        cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                        cmd.Parameters.AddWithValue("@PageSize", pageSize);
                        cmd.Parameters.AddWithValue("@TotalPages", 1);
                        cmd.Parameters.AddWithValue("@TotalRecords", 0);

                        cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                        cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;
                        try
                        {
                            conn.Open();
                            long index = 0;
                            using (var dr = cmd.ExecuteReader())
                            {
                                while (dr.Read())
                                {
                                    var call = new MediaInfo
                                    {
                                        RowNo = ++index, //(long)dr["RowNo"],
                                        CallId = Convert.ToString(dr["CallID"]),
                                        CallType = Convert.ToInt32(dr["CallType"]),
                                        ChannelName = Convert.ToString(dr["ExtName"]),
                                        StartTime = Convert.ToString(dr["StartTime"]),
                                        Duration = Convert.ToInt32(dr["Duration"]),
                                        UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                        FileName = Convert.ToString(dr["FileName"]),
                                        GroupName = Convert.ToString(dr["GroupName"]),
                                        RecorderId = recId,
                                        ECRecName = recName,
                                        ChannelNo = Convert.ToInt32(dr["Ext"]),
                                        CustName = Convert.ToString(dr["CustName"]),
                                        ANI = Convert.ToString(dr["ANI"]),
                                        CallerId = Convert.ToString(dr["CallerID"]),
                                        CalledId = Convert.ToString(dr["CalledID"]),
                                        CustInfo1 = Convert.ToString(dr["Tag1"]),
                                        CustInfo2 = Convert.ToString(dr["Tag2"]),
                                        CustInfo3 = Convert.ToString(dr["Tag3"]),
                                        //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                        CustInfo5 = Convert.ToString(dr["Tag4"]),
                                        Tag6 = Convert.ToString(dr["Tag6"]),
                                        Tag7 = Convert.ToString(dr["Tag7"]),
                                        Tag8 = Convert.ToString(dr["Tag8"]),
                                        Tag9 = Convert.ToString(dr["Tag9"]),
                                        Tag10 = Convert.ToString(dr["Tag10"]),
                                        Tag11 = Convert.ToString(dr["Tag11"]),
                                        Tag12 = Convert.ToString(dr["Tag12"]),
                                        Tag13 = Convert.ToString(dr["Tag13"]),
                                        Tag14 = Convert.ToString(dr["Tag14"]),
                                        Tag15 = Convert.ToString(dr["Tag15"]),
                                        Tag16 = Convert.ToString(dr["Tag16"]),
                                        ANINumber = Convert.ToString(dr["ANI_PH"]),
                                        ANIName = Convert.ToString(dr["ANI_NAME"]),
                                        ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                        Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                        UserId = Convert.ToInt32(dr["UserNum"]),
                                        RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                        TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                        Transcription = Convert.ToString(dr["Transcription"]),
                                        BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                                        ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                        IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                        IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                        IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                        IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                        EventName = Convert.ToString(dr["InterviewId"]),
                                        EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                        Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                        Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                        Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                        Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                        //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                        Interview_GPS = Convert.ToString(dr["GPS"]),
                                        Interview_Notes = Convert.ToString(dr["Notes"]),
                                        ErrorInMuxProcess = 0, //Convert.ToInt32(dr["ErrorInMuxProcess"]),

                                    };

                                    calls.Add(call);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }
                        totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                        totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                    }
                }
                return new DALMediaResponse
                {
                    Acknowledge = AcknowledgeType.Success,
                    ListOfMedias = calls,
                    TotalPages = totalPages,
                    TotalRecords = totalRecords,
                    Message = criteria,
                    ProcessingTime = DateTime.Now.ToString(@"hh\:mm\:ss"),
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public DALMediaResponse GetAdvanceSearchResultsDTO(int pageSize, int pageIndex, string duration, string criteria, string startDate, string endDate, string startTime, string endTime, bool isGlobal, string customSpParam, int recId, string recName)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetLocalDBConnection())
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", startDate);
                    cmd.Parameters.AddWithValue("@EndDate", endDate);
                    cmd.Parameters.AddWithValue("@StartTime", startTime);
                    cmd.Parameters.AddWithValue("@EndTime", endTime);
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", isGlobal);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        long index = 0;
                        sw.Start();
                        conn.OpenAsync();
                        using (var dr = cmd.ExecuteReader())
                        {
                            while (dr.Read())
                            {
                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = recId,
                                    ECRecName = recName,
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0,
                                };
                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }
        #endregion

        #region Chained Databases Search
        public DALMediaResponse PerformSearchChainedDBs(int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam, int recId, string recName)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetLocalDBConnection())
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = pageIndex == 1 ? DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_SECONDARY_DBS : DBConstants.VoiceRec.CALLS_SEARCH_MEDIA_SECONDARY_DBS_2N;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsGlobalSearch", callCriteria.SearchType == SearchType.Global ? true : false);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        sw.Start();
                        conn.Open();
                        using (var dr = cmd.ExecuteReader())
                        {
                            while (dr.Read())
                            {
                                var call = new MediaInfo
                                {
                                    RowNo = (long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = recId,
                                    ECRecName = recName,
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0,
                                };
                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }
        #endregion

        #region QA Evaluation Search
        public async Task<DALMediaResponse> GetDefaultQASearchResultsDTO(int tenantId, int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetLocalDBConnection())
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLS_SEARCH_MEDIA_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsRandom", callCriteria.IsRandom);
                    cmd.Parameters.AddWithValue("@IsPercentage", callCriteria.IsPercentage);
                    cmd.Parameters.AddWithValue("@IsAgentBasedSearch", callCriteria.IsAgentBasedSearch);
                    cmd.Parameters.AddWithValue("@NoOfCalls", callCriteria.NoOfCalls);
                    cmd.Parameters.AddWithValue("@AgentName", callCriteria.AgentName);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        long index = 0;
                        await conn.OpenAsync();
                        sw.Start();
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                #region read data from reader

                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag5 = Convert.ToString(dr["Tag5"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0, //Convert.ToInt32(dr["ErrorInMuxProcess"]),

                                };
                                #endregion

                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }
        public async Task<DALMediaResponse> GetAdvanceQASearchResultsDTO(int tenantId, int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetLocalDBConnection())
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLS_SEARCH_MEDIA_PRIMARY_DB;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsRandom", callCriteria.IsRandom);
                    cmd.Parameters.AddWithValue("@IsPercentage", callCriteria.IsPercentage);
                    cmd.Parameters.AddWithValue("@IsAgentBasedSearch", callCriteria.IsAgentBasedSearch);
                    cmd.Parameters.AddWithValue("@NoOfCalls", callCriteria.NoOfCalls);
                    cmd.Parameters.AddWithValue("@AgentName", callCriteria.AgentName);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        long index = 0;
                        sw.Start();
                        await conn.OpenAsync();
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                #region read data from reader

                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag5 = Convert.ToString(dr["Tag5"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0, //Convert.ToInt32(dr["ErrorInMuxProcess"]),

                                };
                                #endregion

                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }
        public async Task<DALMediaResponse> PerformQASearchChainedDBs(int tenantId, int pageSize, int pageIndex, string duration, string criteria, CallCriteria callCriteria, string customSpParam)
        {
            List<MediaInfo> calls = new List<MediaInfo>();
            int totalPages = 0;
            long totalRecords = 0;
            var sw = new Stopwatch();

            using (var conn = DALHelper.GetLocalDBConnection())
            {
                using (var cmd = new SqlCommand())
                {
                    cmd.Connection = conn;

                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = pageIndex == 1 ? DBConstants.Evaluation.CALLS_SEARCH_MEDIA_SECONDARY_DBS : DBConstants.Evaluation.CALLS_SEARCH_MEDIA_SECONDARY_DBS_2N;

                    cmd.Parameters.AddWithValue("@StartDate", callCriteria.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callCriteria.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callCriteria.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callCriteria.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", duration);
                    cmd.Parameters.AddWithValue("@OptionSTR", criteria);
                    cmd.Parameters.AddWithValue("@CustomText", customSpParam);

                    cmd.Parameters.AddWithValue("@IsRandom", callCriteria.IsRandom);
                    cmd.Parameters.AddWithValue("@IsPercentage", callCriteria.IsPercentage);
                    cmd.Parameters.AddWithValue("@IsAgentBasedSearch", callCriteria.IsAgentBasedSearch);
                    cmd.Parameters.AddWithValue("@NoOfCalls", callCriteria.NoOfCalls);
                    cmd.Parameters.AddWithValue("@AgentName", callCriteria.AgentName);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);
                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    try
                    {
                        long index = 0;
                        sw.Start();
                        await conn.OpenAsync();
                        using (var dr = await cmd.ExecuteReaderAsync())
                        {
                            while (await dr.ReadAsync())
                            {
                                #region read data from reader

                                var call = new MediaInfo
                                {
                                    RowNo = ++index, //(long)dr["RowNo"],
                                    CallId = Convert.ToString(dr["CallID"]),
                                    CallType = Convert.ToInt32(dr["CallType"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTime = Convert.ToString(dr["StartTime"]),
                                    Duration = Convert.ToInt32(dr["Duration"]),
                                    UniqueId = Convert.ToInt64(dr["UniqueId"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    GroupName = Convert.ToString(dr["GroupName"]),
                                    RecorderId = Convert.ToInt32(dr["RecID"]),
                                    ECRecName = Convert.ToString(dr["RecName"]),
                                    ChannelNo = Convert.ToInt32(dr["Ext"]),
                                    CustName = Convert.ToString(dr["CustName"]),
                                    ANI = Convert.ToString(dr["ANI"]),
                                    CallerId = Convert.ToString(dr["CallerID"]),
                                    CalledId = Convert.ToString(dr["CalledID"]),
                                    CustInfo1 = Convert.ToString(dr["Tag1"]),
                                    CustInfo2 = Convert.ToString(dr["Tag2"]),
                                    CustInfo3 = Convert.ToString(dr["Tag3"]),
                                    //CustInfo4 = Convert.ToString(dr["Tag4"]),
                                    CustInfo5 = Convert.ToString(dr["Tag4"]),
                                    Tag5 = Convert.ToString(dr["Tag5"]),
                                    Tag6 = Convert.ToString(dr["Tag6"]),
                                    Tag7 = Convert.ToString(dr["Tag7"]),
                                    Tag8 = Convert.ToString(dr["Tag8"]),
                                    Tag9 = Convert.ToString(dr["Tag9"]),
                                    Tag10 = Convert.ToString(dr["Tag10"]),
                                    Tag11 = Convert.ToString(dr["Tag11"]),
                                    Tag12 = Convert.ToString(dr["Tag12"]),
                                    Tag13 = Convert.ToString(dr["Tag13"]),
                                    Tag14 = Convert.ToString(dr["Tag14"]),
                                    Tag15 = Convert.ToString(dr["Tag15"]),
                                    Tag16 = Convert.ToString(dr["Tag16"]),
                                    ANINumber = Convert.ToString(dr["ANI_PH"]),
                                    ANIName = Convert.ToString(dr["ANI_NAME"]),
                                    ANIDetails = Convert.ToString(dr["ANI_DETAILS"]),
                                    Comment = Convert.ToString(dr["CALL_COMMENT"]),
                                    UserId = Convert.ToInt32(dr["UserNum"]),
                                    RetainCall = Convert.ToBoolean(dr["RetainValue"]),
                                    TranscriptId = Convert.ToString(dr["TranscriptionId"]),
                                    Transcription = Convert.ToString(dr["Transcription"]),
                                    BookmarkXML = Convert.ToString(dr["BookMarkXML"]),
                                    ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]),
                                    IsRevCell = Convert.ToBoolean(dr["IsRevCell"]),

                                    IsPictureEvent = Convert.ToBoolean(dr["IsPictureEvent"]),
                                    IsVirtualInspection = Convert.ToBoolean(dr["IsVirtualInspection"]),
                                    IsRevView = Convert.ToBoolean(dr["IsRevView"]),
                                    EventName = Convert.ToString(dr["InterviewId"]),
                                    EventNameIQ3 = Convert.ToString(dr["Interviewee"]),
                                    Interview_DateTime = Convert.ToString(dr["DateTime"]),
                                    Interview_Interviewer = Convert.ToString(dr["Interviewer"]),
                                    Interview_Interviewee = Convert.ToString(dr["Interviewee"]),
                                    Interview_MdInterviewee = Convert.ToString(dr["MDInterviewee"]),
                                    //Interview_InterviewId = Convert.ToString(dr["InterviewId"]),
                                    Interview_GPS = Convert.ToString(dr["GPS"]),
                                    Interview_Notes = Convert.ToString(dr["Notes"]),
                                    ErrorInMuxProcess = 0, //Convert.ToInt32(dr["ErrorInMuxProcess"]),

                                };
                                #endregion

                                calls.Add(call);
                            }
                        }
                        sw.Stop();
                        Debug.WriteLine(sw.Elapsed.ToString(@"hh\:mm\:ss"));
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            return new DALMediaResponse
            {
                Acknowledge = AcknowledgeType.Success,
                ListOfMedias = calls,
                TotalPages = totalPages,
                TotalRecords = totalRecords,
                Message = criteria,
                ProcessingTime = sw.Elapsed.ToString(@"hh\:mm\:ss"),
            };
        }
        #endregion

        #endregion

        public List<User> FetchAllActiveUsers()
        {
            List<User> users = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.Text; ;
                    cmd.CommandText = "SELECT * FROM t_ACCOUNT WHERE STATUS = 1;";

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "FetchAllActiveUsersFromRecorder", 0));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        users = ORMapper.MapAllActiveUsers(dr);
                        dr.NextResult();
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return users;
        }

        public static List<UserActivity> GetUserActivities(int userId, DateTime startDate, DateTime endDate, int pageSize, int pageIndex, out int totalPages, out long totalRecords)
        {
            List<UserActivity> userActivities = null;
            UserActivity userActivity = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_ACTIVITY_SEARCH;
                    cmd.Parameters.AddWithValue("@startDate", startDate);
                    cmd.Parameters.AddWithValue("@endDate", endDate);
                    cmd.Parameters.AddWithValue("@userId", userId);

                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetUserActivities", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            userActivities = new List<UserActivity>();
                            while (dr.Read())
                            {
                                userActivity = new UserActivity();
                                userActivity.Id = Convert.ToInt64(dr["Id"]);
                                userActivity.UserId = (int)dr["UserId"];
                                userActivity.ActivityId = Convert.ToInt32(dr["ActivityId"]);
                                userActivity.ActivityPerformed = Convert.ToString(dr["ActivityPerformed"]);
                                userActivity.CreatedDate = Convert.ToDateTime(dr["CreatedDate"]);
                                userActivity.MessageKey = Convert.ToString(dr["MessageKey"]);
                                userActivity.MessageData = Convert.ToString(dr["MessageData"]);
                                userActivity.Comments = Convert.ToString(dr["Comments"]);
                                userActivity.ClientIP = Convert.ToString(dr["ClientIPAddress"]);

                                userActivities.Add(userActivity);
                            }
                        }
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt64(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return userActivities;
        }


        public static List<CallInfo> GetCallsByLocation(CallInfoSearchCriteriaDTO callInfoSearchCriteriaDTO)
        {
            List<CallInfo> calls = null;
            CallInfo call = null;
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.VoiceRec.CALLS_SEARCH_AND_GET_BY_LOCATION;
                    cmd.CommandTimeout = CMD_TIMEOUT;

                    cmd.Parameters.AddWithValue("@StartDate", callInfoSearchCriteriaDTO.StartDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@EndDate", callInfoSearchCriteriaDTO.EndDate.DateAsStringWithoutSlash());
                    cmd.Parameters.AddWithValue("@StartTime", callInfoSearchCriteriaDTO.StartTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@EndTime", callInfoSearchCriteriaDTO.EndTime.ConvertTimeSpanToString());
                    cmd.Parameters.AddWithValue("@DurationSTR", callInfoSearchCriteriaDTO.DurationStr);
                    cmd.Parameters.AddWithValue("@OptionSTR", callInfoSearchCriteriaDTO.Criteria);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.EnterpriseDAL, "GetCallsByLocation", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (calls == null) calls = new List<CallInfo>();
                        while (dr.Read())
                        {
                            call = new CallInfo();

                            call.CallId = Convert.ToString(dr["CallID"]);
                            call.UniqueId = Convert.ToInt64(dr["UniqueId"]);
                            call.RecorderId = Convert.ToInt32(dr["RecID"]);
                            call.ChannelId = Convert.ToInt32(dr["Ext"]);
                            call.AgentId = Convert.ToInt32(dr["UserNum"]);
                            call.CallType = Convert.ToInt32(dr["CallType"]);

                            call.StartTime = dr["StartTime"].ToString().ConvertStringDateTimeToDateTime();
                            call.StartTimeString = Convert.ToString(dr["StartTime"]);
                            call.DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]);
                            call.FileName = Convert.ToString(dr["FileName"]);

                            if (call.CallType != 7)
                            {
                                call.CallType_inq = call.CallType;
                            }
                            else
                            {
                                if (Regex.IsMatch(call.FileName, "mp4", RegexOptions.IgnoreCase) || Regex.IsMatch(call.FileName, "mov", RegexOptions.IgnoreCase) || Regex.IsMatch(call.FileName, "m4v", RegexOptions.IgnoreCase))
                                {
                                    call.CallType_inq = 8;
                                }
                                else if (Regex.IsMatch(call.FileName, "zip", RegexOptions.IgnoreCase))
                                {
                                    call.CallType_inq = 12;
                                }
                                else
                                {
                                    call.CallType_inq = 7;
                                }
                            }
                            call.Tag3 = Convert.ToString(dr["Tag3"]);
                            call.Tag4 = Convert.ToString(dr["GPS"]);
                            call.IsRevCell = Convert.ToBoolean(dr["IsRevCell"]);
                            if (call.CallType != 7 && call.CallType != 11 && !call.IsRevCell)
                            {
                                call.Latitude = Convert.ToString(dr["Latitude"]);
                                call.Longitude = Convert.ToString(dr["Longitude"]);
                            }
                            else
                            {
                                call.Latitude = Convert.ToString(dr["Longitude"]);
                                call.Longitude = Convert.ToString(dr["Latitude"]);
                            }

                            call.GroupName = Convert.ToString(dr["GroupName"]);
                            call.ChannelName = Convert.ToString(dr["ExtName"]);
                            call.ScreenFileNames = Convert.ToString(dr["ScreenFileNames"]);
                            if (dr.FieldExists("BookMarkXML"))
                            {
                                call.BookmarkXML = Convert.ToString(dr["BookMarkXML"]);
                                if (!String.IsNullOrEmpty(call.BookmarkXML))
                                {
                                    call.BookmarkCSV = call.BookmarkXML.ConvertXmlStringToCsvString();
                                }
                            }
                            call.IsPictureEvent = !DBRecordExtensions.HasColumn(dr, "IsPictureEvent") ? false : Convert.ToBoolean(dr["IsPictureEvent"]);
                            call.IsVirtualInspection = !DBRecordExtensions.HasColumn(dr, "IsVirtualInspection") ? false : Convert.ToBoolean(dr["IsVirtualInspection"]);
                            call.IsRevView = !DBRecordExtensions.HasColumn(dr, "IsRevView") ? false : Convert.ToBoolean(dr["IsRevView"]);
                            call.RevViewFileName = DBRecordExtensions.HasColumn(dr, "RevViewFileName") ? Convert.ToString(dr["RevViewFileName"]) : string.Empty;
                            call.RevViewStartTime = DBRecordExtensions.HasColumn(dr, "RevViewStartTime") ? Convert.ToString(dr["RevViewStartTime"]) : string.Empty;
                            call.STTFileName = DBRecordExtensions.HasColumn(dr, "STTFilePath") ? Convert.ToString(dr["STTFilePath"]) : string.Empty;
                            calls.Add(call);
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }

            return calls;
        }

        public static List<UserExtensionInfo> GetUserExtensionInfos(int recId)
        {
            UserExtensionInfo userExtensionInfo = null;
            List<UserExtensionInfo> userExtensionInfos = new List<UserExtensionInfo>();
            try
            {
                using (var conn = DALHelper.GetLocalDBConnection())
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.UserManagement.USER_EXTENSION_INFO_GET;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.UserManagement, "GetUserExtensionInfos", 0));

                    using (var dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                userExtensionInfo = new UserExtensionInfo();
                                userExtensionInfo.Id = Convert.ToInt32(dr["Id"]);
                                userExtensionInfo.UserNum = Convert.ToInt32(dr["UserNum"]);
                                userExtensionInfo.UserID = Convert.ToString(dr["UserID"]);
                                userExtensionInfo.UserName = Convert.ToString(dr["UserName"]);
                                userExtensionInfo.Ext = Convert.ToInt32(dr["Ext"]);
                                userExtensionInfo.ExtName = Convert.ToString(dr["ExtName"]);
                                userExtensionInfo.ChannelType = Convert.ToInt32(dr["ChannelType"]);
                                userExtensionInfo.IsRevcell = Convert.ToBoolean(dr["IsRevcell"]);
                                userExtensionInfo.RecId = recId;
                                userExtensionInfos.Add(userExtensionInfo);
                            }
                        }
                    }
                }
                return userExtensionInfos;
            }
            catch (Exception ex) { throw ex; }
        }
    }
}
