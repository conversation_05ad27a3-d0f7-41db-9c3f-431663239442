﻿using RevCord.DataContracts.AuditEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.DataContracts.Messages
{
    public class AuditResponse
    {
        public Audit Audit { get; set; }
        public List<Audit> Audits { get; set; }
        /************* Purpose: used for Paging ****************/
        public int TotalPages { get; set; }
        public long TotalRecords { get; set; }
        /************* Purpose: used for Paging ****************/
    }
}