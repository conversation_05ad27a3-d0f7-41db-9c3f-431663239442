﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using RevCord.DataContracts;
using RevCord.DataContracts.DashboardEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.Util;
using System.Threading.Tasks;
using RevCord.DataAccess.Util;

namespace RevCord.DataAccess
{
    public class DashboardDAL
    {
        private int _tenantId;

        public DashboardDAL(int tenantId)
        {
            _tenantId = tenantId;
        }

        private static int CMD_TIMEOUT = AppSettingsUtil.GetInt("dashboardCommandTimeout", 300);

        #region Private Functions
        private const int _maxNoOfRetries = 5;
        #endregion

        #region Main Dashboard

        public DashboardOverallStats GetDashboardStatistics(int userId, DateTime startDate, DateTime endDate, string csvExtenstions, int retries = 1)
        {
            DashboardOverallStats dashBoardStats = new DashboardOverallStats();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_GET_DATA;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@extensions", string.IsNullOrEmpty(csvExtenstions) ? "0" : csvExtenstions);
                    //cmd.Parameters.AddWithValue("@startDate", startDate);
                    //cmd.Parameters.AddWithValue("@endDate", endDate);
                    //cmd.Parameters.AddWithValue("@currentDate", new DateTime());
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Dashboard, "GetDashboardStatistics", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            while (dr.Read())
                            {
                                CallSummary callSummary = new CallSummary();
                                callSummary.Date = Convert.ToInt32(dr["Date"]);
                                callSummary.Ext = Convert.ToInt32(dr["Ext"]);
                                callSummary.ExtName = Convert.ToString(dr["ExtName"]);
                                callSummary.CallDate = dr["CallDate"] == DBNull.Value ? callSummary.Date : Convert.ToInt32(dr["CallDate"]);
                                callSummary.NoOfCalls = Convert.ToInt32(dr["NoOfCalls"]);
                                callSummary.TotalDuration = Convert.ToInt64(dr["TotalDuration"]);

                                dashBoardStats.CallSummary.Add(callSummary);
                            }
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalChannels = Convert.ToInt32(dr["ChannelCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalCalls = Convert.ToInt32(dr["TotalCalls"]);
                            dashBoardStats.AudioCount = Convert.ToInt32(dr["AudioCallCount"]);
                            dashBoardStats.Text911Count = Convert.ToInt32(dr["Text911Count"]);
                            dashBoardStats.ScreenCount = Convert.ToInt32(dr["ScreenCount"]);
                            dr.NextResult();

                            if (dr.HasRows)
                            {
                                dr.Read();
                                dashBoardStats.TotalSurveyForms = Convert.ToInt32(dr["TotalSurveyForms"]);
                                dashBoardStats.PublishedSurveyCount = Convert.ToInt32(dr["PublishedSurveyCount"]);
                                dashBoardStats.UnPublishedSurveyCount = Convert.ToInt32(dr["UnPublishedSurveyCount"]);
                            }
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalActiveUsers = Convert.ToInt32(dr["TotalActiveUsers"]);
                            dashBoardStats.TotalDeletedUsers = Convert.ToInt32(dr["TotalDeletedUsers"]);
                            dashBoardStats.AdminUserCount = Convert.ToInt32(dr["AdminUserCount"]);
                            dashBoardStats.AdditionalUserCount = Convert.ToInt32(dr["AdditionalUserCount"]);
                            dashBoardStats.SimpleUserCount = Convert.ToInt32(dr["SimpleUserCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.SharedEvals = Convert.ToInt32(dr["SharedEvalCount"]);
                            dashBoardStats.UnsharedEvals = Convert.ToInt32(dr["UnsharedEvalCount"]);
                            dr.NextResult();

                            if (dashBoardStats.Playlists == null) dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.Playlists = new PlaylistDAL(_tenantId).GetPlaylists(userId);

                            dr.NextResult();

                            if (dashBoardStats.CallInfoLite == null) dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            int rowNumber = 0;
                            while (dr.Read())
                            {
                                var call = new CallInfoLite
                                {
                                    RowNo = dashBoardStats.AudioCount - (rowNumber++),
                                    CallId = Convert.ToString(dr["CallID"]),
                                    AgentId = Convert.ToInt32(dr["UserNum"]),
                                    ChannelId = Convert.ToInt32(dr["Ext"]),
                                    ChannelName = Convert.ToString(dr["ExtName"]),
                                    StartTimeString = Convert.ToString(dr["StartTime"]),
                                    DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]),
                                    FileName = Convert.ToString(dr["FileName"]),
                                    CallComments = Convert.ToString(dr["CALL_COMMENT"]),
                                };

                                dashBoardStats.CallInfoLite.Add(call);
                            }

                            dr.NextResult();

                            if (dashBoardStats.SevenDayCallCount == null) dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            while (dr.Read())
                            {
                                dashBoardStats.SevenDayCallCount.Add(new Tuple<int, int>(Convert.ToInt32(dr["Date"]), Convert.ToInt32(dr["NoOfCalls"])));
                            }
                        }
                        else
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            dashBoardStats.CallSummaryLite = new List<CallSummaryLite>();
                            dashBoardStats.EvalStatus = new List<int>();
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Number == 1205)   // SqlServer Deadlock Error Code = 1205
                {
                    if (retries <= _maxNoOfRetries)
                    {
                        System.Threading.Thread.Sleep(1500);
                        return GetDashboardStatistics(userId, startDate, endDate, csvExtenstions, ++retries);
                    }
                    else
                        throw sqlEx;
                }
                else
                    throw sqlEx;
            }
            catch (Exception ex) { throw ex; }
            return dashBoardStats;
        }

        public DashboardOverallStats GetDashboardStatisticsLite(int userId, DateTime startDate, DateTime endDate, string csvExtenstions, int retries = 1)
        {
            DashboardOverallStats dashBoardStats = new DashboardOverallStats();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_GETLITEDATA;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@extensions", string.IsNullOrEmpty(csvExtenstions) ? "0" : csvExtenstions);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Dashboard, "GetDashboardStatisticsLite", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            dashBoardStats.TotalChannels = Convert.ToInt32(dr["ChannelCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalCalls = Convert.ToInt32(dr["TotalCalls"]);
                            dashBoardStats.AudioCount = Convert.ToInt32(dr["AudioCallCount"]);
                            dashBoardStats.Text911Count = Convert.ToInt32(dr["Text911Count"]);
                            dashBoardStats.ScreenCount = Convert.ToInt32(dr["ScreenCount"]);
                            dr.NextResult();

                            if (dr.HasRows)
                            {
                                dr.Read();
                                dashBoardStats.TotalSurveyForms = Convert.ToInt32(dr["TotalSurveyForms"]);
                                dashBoardStats.PublishedSurveyCount = Convert.ToInt32(dr["PublishedSurveyCount"]);
                                dashBoardStats.UnPublishedSurveyCount = Convert.ToInt32(dr["UnPublishedSurveyCount"]);
                            }
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalActiveUsers = Convert.ToInt32(dr["TotalActiveUsers"]);
                            dashBoardStats.TotalDeletedUsers = Convert.ToInt32(dr["TotalDeletedUsers"]);
                            dashBoardStats.AdminUserCount = Convert.ToInt32(dr["AdminUserCount"]);
                            dashBoardStats.AdditionalUserCount = Convert.ToInt32(dr["AdditionalUserCount"]);
                            dashBoardStats.SimpleUserCount = Convert.ToInt32(dr["SimpleUserCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.SharedEvals = Convert.ToInt32(dr["SharedEvalCount"]);
                            dashBoardStats.UnsharedEvals = Convert.ToInt32(dr["UnsharedEvalCount"]);
                            dr.NextResult();

                            if (dashBoardStats.Playlists == null) dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.Playlists = new PlaylistDAL(_tenantId).GetPlaylists(userId);

                            //dr.NextResult();

                            //if (dashBoardStats.CallInfoLite == null) dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            //int rowNumber = 0;
                            //while (dr.Read())
                            //{
                            //    var call = new CallInfoLite
                            //    {
                            //        RowNo = ++rowNumber,
                            //        ChannelId = Convert.ToInt32(dr["Ext"]),
                            //        StartTimeString = Convert.ToString(dr["StartTime"]),
                            //        DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]),
                            //    };

                            //    dashBoardStats.CallInfoLite.Add(call);
                            //}

                            dr.NextResult();

                            if (dashBoardStats.SevenDayCallCount == null) dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            while (dr.Read())
                            {
                                dashBoardStats.SevenDayCallCount.Add(new Tuple<int, int>(Convert.ToInt32(dr["Date"]), Convert.ToInt32(dr["NoOfCalls"])));
                            }
                        }
                        else
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            dashBoardStats.CallSummaryLite = new List<CallSummaryLite>();
                            dashBoardStats.EvalStatus = new List<int>();
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Number == 1205)   // SqlServer Deadlock Error Code = 1205
                {
                    if (retries <= _maxNoOfRetries)
                    {
                        System.Threading.Thread.Sleep(1500);
                        return GetDashboardStatisticsLite(userId, startDate, endDate, csvExtenstions, ++retries);
                    }
                    else
                        throw sqlEx;
                }
                else
                    throw sqlEx;
            }
            catch (Exception ex) { throw ex; }
            return dashBoardStats;
        }

        public DashboardOverallStats GetDashboardStatsLite(int userId, DateTime startDate, DateTime endDate, string csvExtenstions, int retries = 1)
        {
            DashboardOverallStats dashBoardStats = new DashboardOverallStats();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_GETLITEDATA;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@extensions", string.IsNullOrEmpty(csvExtenstions) ? "0" : csvExtenstions);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Dashboard, "GetDashboardStatsLite", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            dashBoardStats.TotalChannels = Convert.ToInt32(dr["ChannelCount"]);
                            dr.NextResult();

                            //dr.Read();
                            //dashBoardStats.TotalCalls = Convert.ToInt32(dr["TotalCalls"]);
                            //dr.NextResult();

                            //dr.Read();
                            //dashBoardStats.AudioCount = Convert.ToInt32(dr["AudioCallCount"]);
                            //dr.NextResult();

                            //dr.Read();
                            //dashBoardStats.ScreenCount = Convert.ToInt32(dr["ScreenCount"]);
                            //dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalCalls = Convert.ToInt32(dr["TotalCalls"]);
                            dashBoardStats.AudioCount = Convert.ToInt32(dr["AudioCallCount"]);
                            dashBoardStats.Text911Count = Convert.ToInt32(dr["Text911Count"]);
                            dashBoardStats.ScreenCount = Convert.ToInt32(dr["ScreenCount"]);
                            dr.NextResult();

                            if (dr.HasRows)
                            {
                                dr.Read();
                                dashBoardStats.TotalSurveyForms = Convert.ToInt32(dr["TotalSurveyForms"]);
                                dashBoardStats.PublishedSurveyCount = Convert.ToInt32(dr["PublishedSurveyCount"]);
                                dashBoardStats.UnPublishedSurveyCount = Convert.ToInt32(dr["UnPublishedSurveyCount"]);
                            }
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalActiveUsers = Convert.ToInt32(dr["TotalActiveUsers"]);
                            dashBoardStats.TotalDeletedUsers = Convert.ToInt32(dr["TotalDeletedUsers"]);
                            dashBoardStats.AdminUserCount = Convert.ToInt32(dr["AdminUserCount"]);
                            dashBoardStats.AdditionalUserCount = Convert.ToInt32(dr["AdditionalUserCount"]);
                            dashBoardStats.SimpleUserCount = Convert.ToInt32(dr["SimpleUserCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.SharedEvals = Convert.ToInt32(dr["SharedEvalCount"]);
                            dashBoardStats.UnsharedEvals = Convert.ToInt32(dr["UnsharedEvalCount"]);
                            dr.NextResult();

                            if (dashBoardStats.Playlists == null) dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.Playlists = new PlaylistDAL(_tenantId).GetPlaylists(userId);

                            dr.NextResult();

                            if (dashBoardStats.CallInfoLite == null) dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            int rowNumber = 0;
                            while (dr.Read())
                            {
                                var call = new CallInfoLite
                                {
                                    RowNo = ++rowNumber,
                                    ChannelId = Convert.ToInt32(dr["Ext"]),
                                    StartTimeString = Convert.ToString(dr["StartTime"]),
                                    DurationInMilliSeconds = Convert.ToInt32(dr["Duration"]),
                                };

                                dashBoardStats.CallInfoLite.Add(call);
                            }

                            dr.NextResult();

                            if (dashBoardStats.SevenDayCallCount == null) dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            while (dr.Read())
                            {
                                dashBoardStats.SevenDayCallCount.Add(new Tuple<int, int>(Convert.ToInt32(dr["Date"]), Convert.ToInt32(dr["NoOfCalls"])));
                            }
                        }
                        else
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            dashBoardStats.CallSummaryLite = new List<CallSummaryLite>();
                            dashBoardStats.EvalStatus = new List<int>();
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Number == 1205)   // SqlServer Deadlock Error Code = 1205
                {
                    if (retries <= _maxNoOfRetries)
                    {
                        System.Threading.Thread.Sleep(1500);
                        return GetDashboardStatsLite(userId, startDate, endDate, csvExtenstions, ++retries);
                    }
                    else
                        throw sqlEx;
                }
                else
                    throw sqlEx;
            }
            catch (Exception ex) { throw ex; }
            return dashBoardStats;
        }

        public DashboardOverallStats ReloadDashboardStatistics(int userId, DateTime startDate, DateTime endDate, string csvExtenstions, int retries = 1)
        {
            DashboardOverallStats dashBoardStats = new DashboardOverallStats();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_RELOAD_DATA;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@extensions", string.IsNullOrEmpty(csvExtenstions) ? "0" : csvExtenstions);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Dashboard, "ReloadDashboardStatistics", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            while (dr.Read())
                            {
                                CallSummary callSummary = new CallSummary();
                                callSummary.Date = Convert.ToInt32(dr["Date"]);
                                callSummary.Ext = Convert.ToInt32(dr["Ext"]);
                                callSummary.ExtName = Convert.ToString(dr["ExtName"]);
                                callSummary.CallDate = dr["CallDate"] == DBNull.Value ? callSummary.Date : Convert.ToInt32(dr["CallDate"]);
                                callSummary.NoOfCalls = Convert.ToInt32(dr["NoOfCalls"]);
                                callSummary.TotalDuration = Convert.ToInt64(dr["TotalDuration"]);

                                dashBoardStats.CallSummary.Add(callSummary);
                            }
                        }
                        dr.NextResult();

                        dr.Read();
                        dashBoardStats.TotalChannels = Convert.ToInt32(dr["ChannelCount"]);
                        dr.NextResult();

                        dr.Read();
                        dashBoardStats.TotalCalls = Convert.ToInt32(dr["TotalCalls"]);
                        dashBoardStats.AudioCount = Convert.ToInt32(dr["AudioCallCount"]);
                        dashBoardStats.Text911Count = Convert.ToInt32(dr["Text911Count"]);
                        dashBoardStats.ScreenCount = Convert.ToInt32(dr["ScreenCount"]);
                        dashBoardStats.TotalEvents = Convert.ToInt32(dr["TotalEvents"]);
                        dashBoardStats.IQ3EventsCount = Convert.ToInt32(dr["IQ3EventsCount"]);
                        dashBoardStats.PictureEventsCount = Convert.ToInt32(dr["PictureEventsCount"]);
                        dashBoardStats.VirtualInspectionEventsCount = Convert.ToInt32(dr["VirtualInspectionEventsCount"]);
                        dr.NextResult();

                        if (dr.HasRows)
                        {
                            dr.Read();
                            dashBoardStats.TotalSurveyForms = Convert.ToInt32(dr["TotalSurveyForms"]);
                            dashBoardStats.PublishedSurveyCount = Convert.ToInt32(dr["PublishedSurveyCount"]);
                            dashBoardStats.UnPublishedSurveyCount = Convert.ToInt32(dr["UnPublishedSurveyCount"]);
                        }
                        dr.NextResult();

                        dr.Read();
                        dashBoardStats.TotalActiveUsers = Convert.ToInt32(dr["TotalActiveUsers"]);
                        dashBoardStats.TotalDeletedUsers = Convert.ToInt32(dr["TotalDeletedUsers"]);
                        dashBoardStats.AdminUserCount = Convert.ToInt32(dr["AdminUserCount"]);
                        dashBoardStats.AdditionalUserCount = Convert.ToInt32(dr["AdditionalUserCount"]);
                        dashBoardStats.SimpleUserCount = Convert.ToInt32(dr["SimpleUserCount"]);
                        dashBoardStats.TotalIQ3Users = Convert.ToInt32(dr["TotalIQ3Users"]);
                        dr.NextResult();

                        dr.Read();
                        dashBoardStats.SharedEvals = Convert.ToInt32(dr["SharedEvalCount"]);
                        dashBoardStats.UnsharedEvals = Convert.ToInt32(dr["UnsharedEvalCount"]);
                        dr.NextResult();

                        if (dashBoardStats.Playlists == null) dashBoardStats.Playlists = new List<Playlist>();
                        dashBoardStats.Playlists = new PlaylistDAL(_tenantId).GetPlaylists(userId);
                        dr.NextResult();

                        if (dashBoardStats.SevenDayCallCount == null) dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                        while (dr.Read())
                        {
                            dashBoardStats.SevenDayCallCount.Add(new Tuple<int, int>(Convert.ToInt32(dr["Date"]), Convert.ToInt32(dr["NoOfCalls"])));
                        }

                        /*
                        else
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            dashBoardStats.CallSummaryLite = new List<CallSummaryLite>();
                            dashBoardStats.EvalStatus = new List<int>();
                        }
                        */
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Number == 1205)   // SqlServer Deadlock Error Code = 1205
                {
                    if (retries <= _maxNoOfRetries)
                    {
                        System.Threading.Thread.Sleep(100);
                        return ReloadDashboardStatistics(userId, startDate, endDate, csvExtenstions, ++retries);
                    }
                    else
                        throw sqlEx;
                }
                else
                    throw sqlEx;
            }
            catch (Exception ex) { throw ex; }
            return dashBoardStats;
        }

        public DashboardOverallStats ReloadDashboardStatisticsLite(int userId, DateTime startDate, DateTime endDate, string csvExtenstions, int retries = 1)
        {
            DashboardOverallStats dashBoardStats = new DashboardOverallStats();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_RELOAD_DATA_LITE;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@extensions", string.IsNullOrEmpty(csvExtenstions) ? "0" : csvExtenstions);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Dashboard, "ReloadDashboardStatisticsLite", _tenantId));
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            dashBoardStats.TotalChannels = Convert.ToInt32(dr["ChannelCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalCalls = Convert.ToInt32(dr["TotalCalls"]);
                            dashBoardStats.AudioCount = Convert.ToInt32(dr["AudioCallCount"]);
                            dashBoardStats.Text911Count = Convert.ToInt32(dr["Text911Count"]);
                            dashBoardStats.ScreenCount = Convert.ToInt32(dr["ScreenCount"]);
                            dashBoardStats.TotalEvents = Convert.ToInt32(dr["TotalEvents"]);
                            dashBoardStats.IQ3EventsCount = Convert.ToInt32(dr["IQ3EventsCount"]);
                            dashBoardStats.PictureEventsCount = Convert.ToInt32(dr["PictureEventsCount"]);
                            dashBoardStats.VirtualInspectionEventsCount = Convert.ToInt32(dr["VirtualInspectionEventsCount"]);
                            dr.NextResult();

                            if (dr.HasRows)
                            {
                                dr.Read();
                                dashBoardStats.TotalSurveyForms = Convert.ToInt32(dr["TotalSurveyForms"]);
                                dashBoardStats.PublishedSurveyCount = Convert.ToInt32(dr["PublishedSurveyCount"]);
                                dashBoardStats.UnPublishedSurveyCount = Convert.ToInt32(dr["UnPublishedSurveyCount"]);
                            }
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalActiveUsers = Convert.ToInt32(dr["TotalActiveUsers"]);
                            dashBoardStats.TotalDeletedUsers = Convert.ToInt32(dr["TotalDeletedUsers"]);
                            dashBoardStats.AdminUserCount = Convert.ToInt32(dr["AdminUserCount"]);
                            dashBoardStats.AdditionalUserCount = Convert.ToInt32(dr["AdditionalUserCount"]);
                            dashBoardStats.SimpleUserCount = Convert.ToInt32(dr["SimpleUserCount"]);
                            dashBoardStats.TotalIQ3Users = Convert.ToInt32(dr["TotalIQ3Users"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.SharedEvals = Convert.ToInt32(dr["SharedEvalCount"]);
                            dashBoardStats.UnsharedEvals = Convert.ToInt32(dr["UnsharedEvalCount"]);
                            dr.NextResult();

                            if (dashBoardStats.Playlists == null) dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.Playlists = new PlaylistDAL(_tenantId).GetPlaylists(userId);
                            dr.NextResult();

                            if (dashBoardStats.SevenDayCallCount == null) dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            while (dr.Read())
                            {
                                dashBoardStats.SevenDayCallCount.Add(new Tuple<int, int>(Convert.ToInt32(dr["Date"]), Convert.ToInt32(dr["NoOfCalls"])));
                            }
                        }
                        else
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            dashBoardStats.CallSummaryLite = new List<CallSummaryLite>();
                            dashBoardStats.EvalStatus = new List<int>();
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Number == 1205)   // SqlServer Deadlock Error Code = 1205
                {
                    if (retries <= _maxNoOfRetries)
                    {
                        System.Threading.Thread.Sleep(1500);
                        return ReloadDashboardStatisticsLite(userId, startDate, endDate, csvExtenstions, ++retries);
                    }
                    else
                        throw sqlEx;
                }
                else
                    throw sqlEx;
            }
            catch (Exception ex) { throw ex; }
            return dashBoardStats;
        }

        public DashboardOverallStats GetIRData(int userId, string csvExtenstions, string lastStartTime)
        {
            DashboardOverallStats dashBoardStats = new DashboardOverallStats();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_GET_IR_DATA;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@extensions", string.IsNullOrEmpty(csvExtenstions) ? "0" : csvExtenstions);
                    cmd.Parameters.AddWithValue("@lastStartTime", lastStartTime);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Dashboard, "GetIRData", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            if (dashBoardStats.CallInfoLite == null) dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            int rowNumber = 0;
                            while (dr.Read())
                            {
                                var call = new CallInfoLite
                                {
                                    RowNo = ++rowNumber,
                                    ChannelId = Convert.ToInt32(dr["Ext"]),
                                    StartTimeString = Convert.ToString(dr["StartTime"]),
                                    DurationInMilliSeconds = Convert.ToInt32(dr["Duration"])
                                };

                                dashBoardStats.CallInfoLite.Add(call);
                            }
                        }
                        else
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            dashBoardStats.CallSummaryLite = new List<CallSummaryLite>();
                            dashBoardStats.EvalStatus = new List<int>();
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                throw sqlEx;
            }
            catch (Exception ex) { throw ex; }
            return dashBoardStats;
        }

        public DashboardOverallStats GetRecentRecords(int userId, string csvExtenstions, int noOfRecords, bool isAvrisView = false)
        {
            DashboardOverallStats dashBoardStats = new DashboardOverallStats();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_GET_IR_DATA_RECENT;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@extensions", string.IsNullOrEmpty(csvExtenstions) ? "0" : csvExtenstions);
                    cmd.Parameters.AddWithValue("@noOfRecords", noOfRecords);
                    cmd.Parameters.AddWithValue("@isAvrisView", isAvrisView);
                    conn.Open();

                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Dashboard, "GetRecentRecords", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            if (dashBoardStats.CallInfoLite == null) dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            int rowNumber = 0;
                            while (dr.Read())
                            {
                                var call = new CallInfoLite
                                {
                                    RowNo = ++rowNumber,
                                    CallId = Convert.ToString(dr["CallId"]),
                                    ChannelId = Convert.ToInt32(dr["Ext"]),
                                    StartTimeString = Convert.ToString(dr["StartTime"]),
                                    DurationInMilliSeconds = Convert.ToInt32(dr["Duration"])
                                };

                                dashBoardStats.CallInfoLite.Add(call);
                            }
                        }
                        else
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            dashBoardStats.CallSummaryLite = new List<CallSummaryLite>();
                            dashBoardStats.EvalStatus = new List<int>();
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                throw sqlEx;
            }
            catch (Exception ex) { throw ex; }
            return dashBoardStats;
        }

        public DashboardOverallStats GetCallCountData(int userId, string csvExtenstions, int retries = 1)
        {
            DashboardOverallStats dashBoardStats = new DashboardOverallStats();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_GET_CallCount_DATA;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@extensions", string.IsNullOrEmpty(csvExtenstions) ? "0" : csvExtenstions);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Dashboard, "GetCallCountData", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            dashBoardStats.TotalCalls = Convert.ToInt32(dr["TotalCalls"]);
                            dashBoardStats.AudioCount = Convert.ToInt32(dr["AudioCallCount"]);
                            dashBoardStats.ScreenCount = Convert.ToInt32(dr["ScreenCount"]);
                            dashBoardStats.Text911Count = Convert.ToInt32(dr["Text911Count"]);
                            dashBoardStats.TotalEvents = Convert.ToInt32(dr["TotalEvents"]);
                            dashBoardStats.IQ3EventsCount = Convert.ToInt32(dr["IQ3EventsCount"]);
                            dashBoardStats.PictureEventsCount = Convert.ToInt32(dr["PictureEventsCount"]);
                            dashBoardStats.VirtualInspectionEventsCount = Convert.ToInt32(dr["VirtualInspectionEventsCount"]);
                        }
                        else
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            dashBoardStats.CallSummaryLite = new List<CallSummaryLite>();
                            dashBoardStats.EvalStatus = new List<int>();
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Number == 1205)   // SqlServer Deadlock Error Code = 1205
                {
                    if (retries <= _maxNoOfRetries)
                    {
                        System.Threading.Thread.Sleep(100);
                        return GetCallCountData(userId, csvExtenstions, ++retries);
                    }
                    else
                        throw sqlEx;
                }
                else
                    throw sqlEx;
                throw sqlEx;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return dashBoardStats;
        }

        public DashboardOverallStats LoadDashboardDataLite(int userId, string csvExtenstions, int retries = 1)
        {
            DashboardOverallStats dashBoardStats = new DashboardOverallStats();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_LOAD_DATA_LITE;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@extensions", string.IsNullOrEmpty(csvExtenstions) ? "0" : csvExtenstions);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Dashboard, "LoadDashboardDataLite", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            dr.Read();
                            dashBoardStats.TotalChannels = Convert.ToInt32(dr["ChannelCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalSurveyForms = Convert.ToInt32(dr["TotalSurveyForms"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.PublishedSurveyCount = Convert.ToInt32(dr["PublishedSurveyCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.UnPublishedSurveyCount = Convert.ToInt32(dr["UnPublishedSurveyCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalActiveUsers = Convert.ToInt32(dr["TotalActiveUsers"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.TotalDeletedUsers = Convert.ToInt32(dr["TotalDeletedUsers"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.AdminUserCount = Convert.ToInt32(dr["AdminUserCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.AdditionalUserCount = Convert.ToInt32(dr["AdditionalUserCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.SimpleUserCount = Convert.ToInt32(dr["SimpleUserCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.SharedEvals = Convert.ToInt32(dr["SharedEvalCount"]);
                            dr.NextResult();

                            dr.Read();
                            dashBoardStats.UnsharedEvals = Convert.ToInt32(dr["UnsharedEvalCount"]);
                            dr.NextResult();

                            dashBoardStats.EvalStatus = new List<int>();
                            while (dr.Read())
                            {
                                dashBoardStats.EvalStatus.Add(Convert.ToInt32(dr["EvalCount"]));
                            }
                            dr.NextResult();

                            if (dashBoardStats.Playlists == null) dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.Playlists = new PlaylistDAL(_tenantId).GetPlaylists(userId);
                            dr.NextResult();
                        }
                        else
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            dashBoardStats.CallSummaryLite = new List<CallSummaryLite>();
                            dashBoardStats.EvalStatus = new List<int>();
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Number == 1205)   // SqlServer Deadlock Error Code = 1205
                {
                    if (retries <= _maxNoOfRetries)
                    {
                        System.Threading.Thread.Sleep(100);
                        return LoadDashboardDataLite(userId, csvExtenstions, ++retries);
                    }
                    else
                        throw sqlEx;
                }
                else
                    throw sqlEx;
            }
            catch (Exception ex) { throw ex; }
            return dashBoardStats;
        }
        public DashboardOverallStats LoadSevenDayCallData(int userId, string csvExtenstions, int retries = 1)
        {
            DashboardOverallStats dashBoardStats = new DashboardOverallStats();
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_LOAD_SEVENDAY_CALLDATA;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@extensions", string.IsNullOrEmpty(csvExtenstions) ? "0" : csvExtenstions);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Dashboard, "LoadSevenDayCallData", _tenantId));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (dr.HasRows)
                        {
                            if (dashBoardStats.SevenDayCallCount == null) dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            while (dr.Read())
                            {
                                dashBoardStats.SevenDayCallCount.Add(new Tuple<int, int>(Convert.ToInt32(dr["Date"]), Convert.ToInt32(dr["NoOfCalls"])));
                            }
                        }
                        else
                        {
                            if (dashBoardStats.CallSummary == null) dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallSummary = new List<CallSummary>();
                            dashBoardStats.CallInfoLite = new List<CallInfoLite>();
                            dashBoardStats.Playlists = new List<Playlist>();
                            dashBoardStats.SevenDayCallCount = new List<Tuple<int, int>>();
                            dashBoardStats.CallSummaryLite = new List<CallSummaryLite>();
                            dashBoardStats.EvalStatus = new List<int>();
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Number == 1205)   // SqlServer Deadlock Error Code = 1205
                {
                    if (retries <= _maxNoOfRetries)
                    {
                        System.Threading.Thread.Sleep(100);
                        return LoadSevenDayCallData(userId, csvExtenstions, ++retries);
                    }
                    else
                        throw sqlEx;
                }
                else
                    throw sqlEx;
            }
            catch (Exception ex) { throw ex; }
            return dashBoardStats;
        }


        public List<string> GetLiveEvents(int userId, string LastStartTime, int retries = 1)
        {
            List<string> LiveEventsList = new List<string>();

            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Common.DASHBOARD_GET_LiveEvents;
                    cmd.CommandTimeout = CMD_TIMEOUT;
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@startTime", LastStartTime);
                    conn.Open();

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            LiveEventsList.Add(Convert.ToString(dr["InterviewId"]));
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Number == 1205)   // SqlServer Deadlock Error Code = 1205
                {
                    if (retries <= _maxNoOfRetries)
                    {
                        System.Threading.Thread.Sleep(100);
                        return GetLiveEvents(userId, LastStartTime, ++retries);
                    }
                    else
                        throw sqlEx;
                }
                else
                    throw sqlEx;
            }
            catch (Exception ex) { throw ex; }

            return LiveEventsList;
        }

        #endregion

    }
}
