﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RevCord.DataContracts.Response;
using RevCord.DataContracts.Request;
using RevCord.DataAccess;
using RevCord.DataContracts.ReportEntities;
using RevCord.Util;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataContracts.Criteria;
using RevCord.DataContracts.SurveyEntities;
using System.Web.Script.Serialization;
using System.Data.SqlClient;
using System.Threading.Tasks;
using RevCord.DataContracts;
using RevCord.DataAccess.Util;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.IQ3;

namespace RevCord.BusinessLogic
{
    public class ReportManager
    {
        private readonly static string _serverIP = SiteConfig.HelperServiceAddress; //"127.0.0.1";

        public int IQ3TenantUsage { get; private set; }
        #region Advance Reports

        public ReportResponse GetCallReport(ReportRequest reportRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetCallReport", reportRequest.TenantId, "GetCallReport function has been called successfully."));
                List<RPTCallInfo> totalCalls = null;
                List<RPTCallInfo> totalRecorderCalls = new List<RPTCallInfo>();
                List<RPTCallInfoDetail> totalRecorderCallDetails = new List<RPTCallInfoDetail>();

                ReportResponse reportResponse = null;

                List<Recorder> recorders = reportRequest.Recorders;
                foreach (var rec in recorders)
                {
                    totalCalls = null;
                    switch (reportRequest.ReportSubType)
                    {

                        #region Excel

                        case ReportSubType.Excel:
                            switch (reportRequest.XAxisData)
                            {
                                #region Day Wise

                                case XAxisData.Day:
                                    totalCalls = performSearchDayWise(reportRequest, totalCalls, rec);
                                    PreformGroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                #region Month WeekDay Wise

                                case XAxisData.MonthWeekDay:
                                    totalCalls = performSearchMonthWeekDayWise(reportRequest, totalCalls, rec);
                                    PreformGroupingMonthWeekDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                #region Month Wise

                                case XAxisData.Month:
                                    totalCalls = performSearchMonthWise(reportRequest, totalCalls, rec);
                                    PreformGroupingMonthWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                #region WeekDay Wise

                                case XAxisData.WeekDay:
                                    totalCalls = performSearchWeekWise(reportRequest, totalCalls, rec);
                                    PreformGroupingWeekDaysWise(totalCalls, rec);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;

                                #endregion


                                #region Hour Wise

                                case XAxisData.Hour:
                                    totalCalls = performSearchDayWise(reportRequest, totalCalls, rec, true);
                                    PreformGroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                default:
                                    break;
                            }
                            //reportResponse = new ReportResponse { CallInfos = totalCalls };
                            break;

                        #endregion



                        #region BarChart

                        case ReportSubType.BarChart:
                            switch (reportRequest.XAxisData)
                            {
                                #region Day Wise

                                case XAxisData.Day:
                                    totalCalls = performSearchDayWise(reportRequest, totalCalls, rec);
                                    PreformGroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                //case XAxisData.MonthWeekDay:
                                //    break;

                                #region Month Wise

                                case XAxisData.Month:
                                    totalCalls = performSearchMonthWise(reportRequest, totalCalls, rec);
                                    PreformGroupingMonthWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                #region WeekDay Wise

                                case XAxisData.WeekDay:
                                    totalCalls = performSearchWeekWise(reportRequest, totalCalls, rec);
                                    totalCalls.ForEach(c => c.DayOfWeekString = c.DayOfWeek.ToString());
                                    PreformGroupingWeekDaysWise(totalCalls, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;

                                #endregion


                                default:
                                    break;
                            }
                            totalRecorderCalls.OrderBy(c => c.RecorderId).ThenBy(c => c.CallDate).ToList();
                            if (reportRequest.GetDetailReportData == true)
                            {
                                if (rec.IsPrimary)
                                {
                                    var callDetails = new RPTCallInfoDALEC().GetDetailSearchResults(reportRequest, rec);
                                    totalRecorderCallDetails.AddRange(callDetails);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string selectKey = reportRequest.QueryParameter.SelectKey;
                                    string selectYear = reportRequest.QueryParameter.SelectYear;
                                    string selectMonth = reportRequest.QueryParameter.SelectMonth;
                                    string selectDay = reportRequest.QueryParameter.SelectDay;
                                    string fromDate = reportRequest.QueryParameter.FromDate;
                                    string toDate = reportRequest.QueryParameter.ToDate;
                                    string timeRange = reportRequest.QueryParameter.TimeRange;
                                    string duration = reportRequest.QueryParameter.Duration;
                                    string groupKey = reportRequest.QueryParameter.GroupKey;
                                    string groupYear = reportRequest.QueryParameter.GroupYear;
                                    string groupMonth = reportRequest.QueryParameter.GroupMonth;
                                    string groupDay = reportRequest.QueryParameter.GroupDay;
                                    string optionSTR = reportRequest.QueryParameter.OptionString;

                                    var callDetails = entClient.GetDetailSearchResults(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, rec).ToList();
                                    totalRecorderCallDetails.AddRange(callDetails);
                                }
                            }
                            else
                                totalRecorderCallDetails = null;
                            //reportResponse = new ReportResponse { CallInfos = totalCalls.OrderBy(c => c.CallDate).ToList(), CallInfoDetails = reportRequest.GetDetailReportData == true ? new RPTCallInfoDAL().GetDetailSearchResults(reportRequest) : null };
                            break;

                        #endregion


                        #region PieChart

                        case ReportSubType.PieChart:
                            switch (reportRequest.XAxisData)
                            {
                                #region Day Wise

                                case XAxisData.Day:
                                    totalCalls = performSearchDayWise(reportRequest, totalCalls, rec);
                                    PreformGroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                //case XAxisData.MonthWeekDay:
                                //    break;

                                #region Month Wise

                                case XAxisData.Month:
                                    totalCalls = performSearchMonthWise(reportRequest, totalCalls, rec);
                                    PreformGroupingMonthWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                #region WeekDay Wise

                                case XAxisData.WeekDay:
                                    totalCalls = performSearchWeekWise(reportRequest, totalCalls, rec);
                                    totalCalls.ForEach(c => c.DayOfWeekString = c.DayOfWeek.ToString());
                                    PreformGroupingWeekDaysWise(totalCalls, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);

                                    break;

                                #endregion


                                default:
                                    break;
                            }
                            totalRecorderCalls.OrderBy(c => c.RecorderId).ThenBy(c => c.CallDate).ToList();
                            if (reportRequest.GetDetailReportData == true)
                            {
                                if (rec.IsPrimary)
                                {
                                    var callDetails = new RPTCallInfoDALEC().GetDetailSearchResults(reportRequest, rec);
                                    totalRecorderCallDetails.AddRange(callDetails);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string selectKey = reportRequest.QueryParameter.SelectKey;
                                    string selectYear = reportRequest.QueryParameter.SelectYear;
                                    string selectMonth = reportRequest.QueryParameter.SelectMonth;
                                    string selectDay = reportRequest.QueryParameter.SelectDay;
                                    string fromDate = reportRequest.QueryParameter.FromDate;
                                    string toDate = reportRequest.QueryParameter.ToDate;
                                    string timeRange = reportRequest.QueryParameter.TimeRange;
                                    string duration = reportRequest.QueryParameter.Duration;
                                    string groupKey = reportRequest.QueryParameter.GroupKey;
                                    string groupYear = reportRequest.QueryParameter.GroupYear;
                                    string groupMonth = reportRequest.QueryParameter.GroupMonth;
                                    string groupDay = reportRequest.QueryParameter.GroupDay;
                                    string optionSTR = reportRequest.QueryParameter.OptionString;

                                    var callDetails = entClient.GetDetailSearchResults(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, rec).ToList();
                                    totalRecorderCallDetails.AddRange(callDetails);
                                }
                            }
                            else
                                totalRecorderCallDetails = null;
                            //reportResponse = new ReportResponse { CallInfos = totalCalls.OrderBy(c => c.CallDate).ToList(), CallInfoDetails = reportRequest.GetDetailReportData == true ? new RPTCallInfoDAL().GetDetailSearchResults(reportRequest) : null };
                            break;

                            #endregion

                    }
                }

                return reportResponse = new ReportResponse { CallInfos = totalRecorderCalls, CallInfoDetails = totalRecorderCallDetails };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetCallReport", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetCallReport", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse GetCallReportFromRecorder(ReportRequest reportRequest)
        {
            List<RPTCallInfo> totalCalls = null;
            List<RPTCallInfo> totalRecorderCalls = new List<RPTCallInfo>();
            List<RPTCallInfoDetail> totalRecorderCallDetails = new List<RPTCallInfoDetail>();

            ReportResponse reportResponse = null;

            List<Recorder> recorders = reportRequest.Recorders;
            Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetCallReportFromRecorder", reportRequest.TenantId, "GetCallReportFromRecorder function has been called successfully."));

            try
            {
                foreach (var rec in recorders)
                {
                    totalCalls = null;
                    switch (reportRequest.ReportSubType)
                    {

                        #region Excel

                        case ReportSubType.Excel:
                            switch (reportRequest.XAxisData)
                            {
                                #region Day Wise

                                case XAxisData.Day:
                                    totalCalls = performSearchDayWise(reportRequest, totalCalls, rec);
                                    PreformGroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                #region Month WeekDay Wise

                                case XAxisData.MonthWeekDay:
                                    totalCalls = performSearchMonthWeekDayWise(reportRequest, totalCalls, rec);
                                    PreformGroupingMonthWeekDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                #region Month Wise

                                case XAxisData.Month:
                                    totalCalls = performSearchMonthWise(reportRequest, totalCalls, rec);
                                    PreformGroupingMonthWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                #region WeekDay Wise

                                case XAxisData.WeekDay:
                                    totalCalls = performSearchWeekWise(reportRequest, totalCalls, rec);
                                    PreformGroupingWeekDaysWise(totalCalls, rec);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;

                                #endregion


                                #region Hour Wise

                                case XAxisData.Hour:
                                    totalCalls = performSearchDayWise(reportRequest, totalCalls, rec, true);
                                    PreformGroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                default:
                                    break;
                            }
                            if (reportRequest.GetDetailReportData == true)
                            {
                                if (rec.IsPrimary)
                                {
                                    var callDetails = new RPTCallInfoDALEC().GetDetailSearchResults(reportRequest, rec);
                                    totalRecorderCallDetails.AddRange(callDetails);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string selectKey = reportRequest.QueryParameter.SelectKey;
                                    string selectYear = reportRequest.QueryParameter.SelectYear;
                                    string selectMonth = reportRequest.QueryParameter.SelectMonth;
                                    string selectDay = reportRequest.QueryParameter.SelectDay;
                                    string fromDate = reportRequest.QueryParameter.FromDate;
                                    string toDate = reportRequest.QueryParameter.ToDate;
                                    string timeRange = reportRequest.QueryParameter.TimeRange;
                                    string duration = reportRequest.QueryParameter.Duration;
                                    string groupKey = reportRequest.QueryParameter.GroupKey;
                                    string groupYear = reportRequest.QueryParameter.GroupYear;
                                    string groupMonth = reportRequest.QueryParameter.GroupMonth;
                                    string groupDay = reportRequest.QueryParameter.GroupDay;
                                    string optionSTR = reportRequest.QueryParameter.OptionString;

                                    var callDetails = entClient.GetDetailSearchResults(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, rec).ToList();

                                    totalRecorderCallDetails.AddRange(callDetails);
                                }
                            }
                            else
                                totalRecorderCallDetails = null;
                            //reportResponse = new ReportResponse { CallInfos = totalCalls };
                            break;

                        #endregion



                        #region BarChart

                        case ReportSubType.BarChart:
                            switch (reportRequest.XAxisData)
                            {
                                #region Day Wise

                                case XAxisData.Day:
                                    totalCalls = performSearchDayWise(reportRequest, totalCalls, rec);
                                    PreformGroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                //case XAxisData.MonthWeekDay:
                                //    break;

                                #region Month Wise

                                case XAxisData.Month:
                                    totalCalls = performSearchMonthWise(reportRequest, totalCalls, rec);
                                    PreformGroupingMonthWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                #region WeekDay Wise

                                case XAxisData.WeekDay:
                                    totalCalls = performSearchWeekWise(reportRequest, totalCalls, rec);
                                    totalCalls.ForEach(c => c.DayOfWeekString = c.DayOfWeek.ToString());
                                    PreformGroupingWeekDaysWise(totalCalls, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;

                                #endregion


                                default:
                                    break;
                            }
                            totalRecorderCalls.OrderBy(c => c.RecorderId).ThenBy(c => c.CallDate).ToList();
                            if (reportRequest.GetDetailReportData == true)
                            {
                                if (rec.IsPrimary)
                                {
                                    var callDetails = new RPTCallInfoDALEC().GetDetailSearchResults(reportRequest, rec);
                                    totalRecorderCallDetails.AddRange(callDetails);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string selectKey = reportRequest.QueryParameter.SelectKey;
                                    string selectYear = reportRequest.QueryParameter.SelectYear;
                                    string selectMonth = reportRequest.QueryParameter.SelectMonth;
                                    string selectDay = reportRequest.QueryParameter.SelectDay;
                                    string fromDate = reportRequest.QueryParameter.FromDate;
                                    string toDate = reportRequest.QueryParameter.ToDate;
                                    string timeRange = reportRequest.QueryParameter.TimeRange;
                                    string duration = reportRequest.QueryParameter.Duration;
                                    string groupKey = reportRequest.QueryParameter.GroupKey;
                                    string groupYear = reportRequest.QueryParameter.GroupYear;
                                    string groupMonth = reportRequest.QueryParameter.GroupMonth;
                                    string groupDay = reportRequest.QueryParameter.GroupDay;
                                    string optionSTR = reportRequest.QueryParameter.OptionString;

                                    var callDetails = entClient.GetDetailSearchResults(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, rec).ToList();

                                    totalRecorderCallDetails.AddRange(callDetails);
                                }
                            }
                            //else
                            //    totalRecorderCallDetails = null;
                            //reportResponse = new ReportResponse { CallInfos = totalCalls.OrderBy(c => c.CallDate).ToList(), CallInfoDetails = reportRequest.GetDetailReportData == true ? new RPTCallInfoDAL().GetDetailSearchResults(reportRequest) : null };
                            break;

                        #endregion


                        #region PieChart

                        case ReportSubType.PieChart:
                            switch (reportRequest.XAxisData)
                            {
                                #region Day Wise

                                case XAxisData.Day:
                                    totalCalls = performSearchDayWise(reportRequest, totalCalls, rec);
                                    PreformGroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                //case XAxisData.MonthWeekDay:
                                //    break;

                                #region Month Wise

                                case XAxisData.Month:
                                    totalCalls = performSearchMonthWise(reportRequest, totalCalls, rec);
                                    PreformGroupingMonthWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion


                                #region WeekDay Wise

                                case XAxisData.WeekDay:
                                    totalCalls = performSearchWeekWise(reportRequest, totalCalls, rec);
                                    totalCalls.ForEach(c => c.DayOfWeekString = c.DayOfWeek.ToString());
                                    PreformGroupingWeekDaysWise(totalCalls, rec, false);
                                    totalRecorderCalls.AddRange(totalCalls);

                                    break;

                                #endregion


                                default:
                                    break;
                            }
                            totalRecorderCalls.OrderBy(c => c.RecorderId).ThenBy(c => c.CallDate).ToList();
                            if (reportRequest.GetDetailReportData == true)
                            {
                                if (rec.IsPrimary)
                                {
                                    var callDetails = new RPTCallInfoDALEC().GetDetailSearchResults(reportRequest, rec);
                                    totalRecorderCallDetails.AddRange(callDetails);
                                }
                                else
                                {
                                    // Alternative approach
                                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                                    string selectKey = reportRequest.QueryParameter.SelectKey;
                                    string selectYear = reportRequest.QueryParameter.SelectYear;
                                    string selectMonth = reportRequest.QueryParameter.SelectMonth;
                                    string selectDay = reportRequest.QueryParameter.SelectDay;
                                    string fromDate = reportRequest.QueryParameter.FromDate;
                                    string toDate = reportRequest.QueryParameter.ToDate;
                                    string timeRange = reportRequest.QueryParameter.TimeRange;
                                    string duration = reportRequest.QueryParameter.Duration;
                                    string groupKey = reportRequest.QueryParameter.GroupKey;
                                    string groupYear = reportRequest.QueryParameter.GroupYear;
                                    string groupMonth = reportRequest.QueryParameter.GroupMonth;
                                    string groupDay = reportRequest.QueryParameter.GroupDay;
                                    string optionSTR = reportRequest.QueryParameter.OptionString;

                                    var callDetails = entClient.GetDetailSearchResults(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, rec).ToList();
                                    totalRecorderCallDetails.AddRange(callDetails);
                                }

                            }
                            else
                                totalRecorderCallDetails = null;
                            //reportResponse = new ReportResponse { CallInfos = totalCalls.OrderBy(c => c.CallDate).ToList(), CallInfoDetails = reportRequest.GetDetailReportData == true ? new RPTCallInfoDAL().GetDetailSearchResults(reportRequest) : null };
                            break;

                            #endregion

                    }
                }
                return reportResponse = new ReportResponse { CallInfos = totalRecorderCalls, CallInfoDetails = totalRecorderCallDetails };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetCallReportFromRecorder", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetCallReportFromRecorder", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse GetCallAuditReportFromRecorder(ReportRequest reportRequest)
        {
            List<RPTCallInfo> totalCalls = null;
            List<RPTCallInfo> totalRecorderCalls = new List<RPTCallInfo>();
            List<RPTCallInfoDetail> totalRecorderCallDetails = new List<RPTCallInfoDetail>();
            ReportResponse reportResponse = null;
            List<Recorder> recorders = reportRequest.Recorders;
            try
            {
                foreach (var rec in recorders)
                {
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetCallAuditReportFromRecorder", reportRequest.TenantId, "GetCallAuditReportFromRecorder function has been called successfully. recId = " + rec.Id + " RecName = " + rec.Name));

                    totalCalls = null;
                    totalCalls = performCallAuditSearch(reportRequest, totalCalls, rec);
                    PreformGroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                    if (totalCalls != null)
                        totalRecorderCalls.AddRange(totalCalls);
                }
                return reportResponse = new ReportResponse { CallInfos = totalRecorderCalls, CallInfoDetails = totalRecorderCallDetails };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetCallAuditReportFromRecorder", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetCallAuditReportFromRecorder", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse GetCallsNotAuditedReportFromRecorder(ReportRequest reportRequest)
        {
            List<RPTCallInfo> totalCalls = null;
            List<RPTCallInfo> totalRecorderCalls = new List<RPTCallInfo>();
            List<RPTCallInfoDetail> totalRecorderCallDetails = new List<RPTCallInfoDetail>();
            ReportResponse reportResponse = null;
            List<Recorder> recorders = reportRequest.Recorders;
            try
            {
                foreach (var rec in recorders)
                {
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetCallsNotAuditedReportFromRecorder", reportRequest.TenantId, "GetCallsNotAuditedReportFromRecorder function has been called successfully. recId = " + rec.Id + " RecName = " + rec.Name));

                    totalCalls = null;
                    totalCalls = performCallsNotAuditedSearch(reportRequest, totalCalls, rec);
                    PreformGroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                    if (totalCalls != null)
                        totalRecorderCalls.AddRange(totalCalls);
                }
                return reportResponse = new ReportResponse { CallInfos = totalRecorderCalls, CallInfoDetails = totalRecorderCallDetails };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetCallsNotAuditedReportFromRecorder", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetCallsNotAuditedReportFromRecorder", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }



        #endregion

        #region 911 PBXn

        public ReportResponse Get911PbxReportFromRecorder(ReportRequest reportRequest)
        {
            List<RPTCallInfo> totalCalls = null;
            List<RPTCallInfo> totalRecorderCalls = new List<RPTCallInfo>();
            List<RPTCallInfoDetail> totalRecorderCallDetails = new List<RPTCallInfoDetail>();

            ReportResponse reportResponse = null;

            List<Recorder> recorders = reportRequest.Recorders;
            try
            {
                foreach (var rec in recorders)
                {
                    Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "Get911PbxReportFromRecorder", reportRequest.TenantId, "Get911PbxReportFromRecorder function has been called successfully. recId = " + rec.Id + " RecName = " + rec.Name));

                    totalCalls = null;

                    switch (reportRequest.ReportSubType)
                    {
                        case ReportSubType.Excel:
                            switch (reportRequest.GroupData)
                            {
                                #region ByChannelName

                                case GroupData.ByChannelName:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);

                                    break;
                                #endregion


                                #region ByAgent

                                case GroupData.ByAgent:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                #region ByChannel

                                case GroupData.ByChannel:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                #region ByGroup

                                case GroupData.ByGroup:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                #region ByTotal

                                case GroupData.ByTotal:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                    #endregion

                            }
                            break;
                        case ReportSubType.BarChart:
                            switch (reportRequest.GroupData)
                            {
                                #region ByChannelName

                                case GroupData.ByChannelName:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);

                                    break;
                                #endregion


                                #region ByAgent

                                case GroupData.ByAgent:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                #region ByChannel

                                case GroupData.ByChannel:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                #region ByGroup

                                case GroupData.ByGroup:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                #region ByTotal

                                case GroupData.ByTotal:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                    #endregion

                            }
                            break;
                        case ReportSubType.PieChart:
                            switch (reportRequest.GroupData)
                            {
                                #region ByChannelName

                                case GroupData.ByChannelName:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);

                                    break;
                                #endregion


                                #region ByAgent

                                case GroupData.ByAgent:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                #region ByChannel

                                case GroupData.ByChannel:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                #region ByGroup

                                case GroupData.ByGroup:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                #endregion

                                #region ByTotal

                                case GroupData.ByTotal:
                                    totalCalls = perform911SearchDayWise(reportRequest, totalCalls, rec);
                                    perform911GroupingDayWise(totalCalls, reportRequest.QueryParameter.FromDate, reportRequest.QueryParameter.ToDate, rec);
                                    if (totalCalls != null)
                                        totalRecorderCalls.AddRange(totalCalls);
                                    break;
                                    #endregion

                            }
                            break;
                        case ReportSubType.StackedBarChart:
                            break;
                        case ReportSubType.CallsAudited:
                            break;
                        case ReportSubType.CallsNotAudited:
                            break;
                        default:
                            break;
                    }
                }
                return reportResponse = new ReportResponse { CallInfos = totalRecorderCalls, CallInfoDetails = totalRecorderCallDetails };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "Get911PbxReportFromRecorder", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "Get911PbxReportFromRecorder", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        //private static void setQueryOptions(ReportRequest reportRequest)
        //{
        //    reportRequest.QueryParameter.OptionString = RPTQueryHelper.BuildOptionString(reportRequest.QueryParameter.OptionGroupString, reportRequest.QueryParameter.OptionGroupExtString, reportRequest.QueryParameter.OptionExtString);
        //}

        //private static string createGroupClause(List<string> groupExtKeys)
        //{
        //    return QueryHelper.BuildGroupExtWhereClause(groupExtKeys, "CI", "CI");
        //}

        private static string createGroupClause(ReportCriteria criteria, int recId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "createGroupClause", 0, "createGroupClause function has been called successfully. recId = " + recId));

                StringBuilder sbWhereClause = new StringBuilder();
                //sbWhereClause.Append(" 1 = 1 ");

                #region ------- Group Extensions -------
                bool Isaudioavailable = false;
                bool IsIq3Available = false;
                if (criteria.RecorderAudioGroupExtensions != null && criteria.RecorderAudioGroupExtensions.Count() > 0)
                {
                    var recResult = criteria.RecorderAudioGroupExtensions.FirstOrDefault(r => r.RecorderId == recId);
                    if (recResult != null)
                    {
                        var recCatGrpExt = recResult.AudioGroupExtension;
                        //sbWhereClause.AppendLine();
                        if (recResult.AudioGroupExtension != null)
                        {
                            if (recResult.AudioGroupExtension.GroupExtensions.Count > 0)
                            {
                                sbWhereClause.AppendLine();
                                sbWhereClause.Append(" ( ");//1
                                foreach (var grpExt in recResult.AudioGroupExtension.GroupExtensions)
                                {
                                    Isaudioavailable = true;
                                    // Commented By KM on 20160810
                                    // sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} AND {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                    sbWhereClause.AppendFormat(@"({0}.Ext IN ({1}))", "CI", grpExt.ExtensionIdsCSV);
                                    sbWhereClause.Append(" OR ");
                                    sbWhereClause.AppendLine();
                                }
                                sbWhereClause.RemoveLast(" OR ");
                                sbWhereClause.Append(" ) ");//1
                                sbWhereClause.AppendLine();
                                //sbWhereClause.AppendFormat(" AND CI.CallType = {0} ", (int)recResult.AudioGroupExtension.GroupType);//Call Type
                                sbWhereClause.AppendLine();
                                sbWhereClause.Append(" AND CI.CallType = 1 ");
                            }
                        }

                        //                    else
                        //                        sbWhereClause.Append(" 1 = 1 ");
                    }
                }
                #region Inquire Arivu
                if (criteria.RecorderInquireGroupExtensions != null && criteria.RecorderInquireGroupExtensions.Count() > 0)
                {
                    var recResult = criteria.RecorderInquireGroupExtensions.FirstOrDefault(r => r.RecorderId == recId);
                    if (recResult != null)
                    {
                        var recCatGrpExt = recResult.InquireGroupExtension;

                        //sbWhereClause.AppendLine();
                        if (recResult.InquireGroupExtension != null)
                        {
                            if (recResult.InquireGroupExtension.GroupExtensions.Count > 0)
                            {
                                sbWhereClause.AppendLine();
                                if (Isaudioavailable)
                                {
                                    sbWhereClause.Append(" OR ");//1
                                }
                                sbWhereClause.Append(" ( ");//1
                                foreach (var grpExt in recResult.InquireGroupExtension.GroupExtensions)
                                {
                                    IsIq3Available = true;
                                    // Commented By KM on 20160810
                                    // sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} AND {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                                    sbWhereClause.AppendFormat(@"({0}.Ext IN ({1}))", "CI", grpExt.ExtensionIdsCSV);
                                    sbWhereClause.Append(" OR ");
                                    sbWhereClause.AppendLine();
                                }
                                sbWhereClause.RemoveLast(" OR ");
                                sbWhereClause.Append(" ) ");//1
                                sbWhereClause.AppendLine();
                                sbWhereClause.AppendLine();
                                sbWhereClause.Append(" AND CI.CallType = 7 ");
                            }
                        }
                        else if (Isaudioavailable == false)
                        {
                            if (string.IsNullOrEmpty(sbWhereClause.ToString()))
                                sbWhereClause.Append(" 1 = 1 ");
                        }
                    }
                }
                #endregion

                #region Inquire Arivu
                //if (criteria.RecorderMdGroupExtensions != null && criteria.RecorderMdGroupExtensions.Count() > 0)
                //{
                //    var recResult = criteria.RecorderMdGroupExtensions.FirstOrDefault(r => r.RecorderId == recId);
                //    if (recResult != null)
                //    {
                //        var recCatGrpExt = recResult.MdGroupExtension;

                //        //sbWhereClause.AppendLine();
                //        if (recResult.MdGroupExtension != null)
                //        {
                //            if (recResult.MdGroupExtension.GroupExtensions.Count > 0)
                //            {
                //                if (sbWhereClause.ToString() == " 1 = 1 ")
                //                    sbWhereClause.Remove(sbWhereClause.ToString().LastIndexOf(" 1 = 1 "), " 1 = 1 ".Length);
                //                sbWhereClause.AppendLine();
                //                if (Isaudioavailable || IsIq3Available)
                //                {
                //                    sbWhereClause.Append(" OR ");//1
                //                }
                //                sbWhereClause.Append(" ( ");//1
                //                foreach (var grpExt in recResult.MdGroupExtension.GroupExtensions)
                //                {
                //                    // Commented By KM on 20160810
                //                    // sbWhereClause.AppendFormat(@"({0}.GroupNum = {1} AND {0}.Ext IN ({2}))", "CI", grpExt.GroupId, grpExt.ExtensionIdsCSV);
                //                    sbWhereClause.AppendFormat(@"({0}.Ext IN ({1}))", "CI", grpExt.ExtensionIdsCSV);
                //                    sbWhereClause.Append(" OR ");
                //                    sbWhereClause.AppendLine();
                //                }
                //                sbWhereClause.RemoveLast(" OR ");
                //                sbWhereClause.Append(" ) ");//1
                //                sbWhereClause.AppendLine();
                //                sbWhereClause.AppendLine();
                //            }
                //        }
                //        else if (Isaudioavailable == false)
                //        {
                //            if (string.IsNullOrEmpty(sbWhereClause.ToString()))
                //                sbWhereClause.Append(" 1 = 1 ");
                //        }
                //    }
                //}
                #endregion

                #endregion

                System.Diagnostics.Debug.WriteLine(sbWhereClause.ToString());
                if (recId > 1)      // For Secondary Recorder, filter only Audio.
                    sbWhereClause.Append(" AND CI.CallType = 1 ");
                return sbWhereClause.ToString();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        #region Private Method's

        #region Perform Search

        private static List<RPTCallInfo> performSearchDayWise(ReportRequest reportRequest, List<RPTCallInfo> totalCalls, Recorder recorder, bool getByHour = false)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "performSearchDayWise", reportRequest.TenantId, "performSearchDayWise function has been called successfully."));

                if (reportRequest.GroupData == GroupData.ByChannelName)
                {
                    reportRequest.QueryParameter.SelectKey = "CI.Ext + ' : ' + ISNULL(G.GroupName, 'unknown') + ' : ' + ISNULL(EI.ExtName, 'NULL')";
                    reportRequest.QueryParameter.GroupKey = "CI.Ext, G.GroupName,EI.ExtName,";
                }
                else if (reportRequest.GroupData == GroupData.ByAgent)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(A.UserName, CI.UserNum) + ' - ' + ISNULL(A.UserID, 'unknown') + ' : ' + ISNULL(G.GroupName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CI.UserNum, CI.GroupNum, G.GroupName, A.UserName, A.UserID,";
                }
                else if (reportRequest.GroupData == GroupData.ByChannel)
                {
                    reportRequest.QueryParameter.SelectKey = "CI.Ext + ' : ' + ISNULL(G.GroupName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CI.Ext, G.GroupName,";
                }
                else if (reportRequest.GroupData == GroupData.ByGroup)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(G.GroupName, 'unknown') + ' - ' + CONVERT(VARCHAR, CI.GroupNum)";
                    reportRequest.QueryParameter.GroupKey = "CI.GroupNum, G.GroupName,";
                }
                else if (reportRequest.GroupData == GroupData.ByTotal)
                {
                    reportRequest.QueryParameter.SelectKey = "'[TOTAL]'";
                    reportRequest.QueryParameter.GroupKey = string.Empty;
                }
                else if (reportRequest.GroupData == GroupData.ByCallTag)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(CT.TagName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CT.TagName,";
                }
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectYear))
                    reportRequest.QueryParameter.SelectYear = QueryConstants.SELECT_YEAR;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectMonth))
                    reportRequest.QueryParameter.SelectMonth = QueryConstants.SELECT_MONTH;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectDay))
                    reportRequest.QueryParameter.SelectDay = QueryConstants.SELECT_DAY;


                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupYear))
                    reportRequest.QueryParameter.GroupYear = QueryConstants.GROUP_YEAR;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupMonth))
                    reportRequest.QueryParameter.GroupMonth = QueryConstants.GROUP_MONTH;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupDay))
                    reportRequest.QueryParameter.GroupDay = QueryConstants.GROUP_DAY;

                reportRequest.QueryParameter.TimeRange = string.Empty;
                if (reportRequest.QueryParameter.TimeStart != "00:00:00" || reportRequest.QueryParameter.TimeEnd != "00:00:00")
                    reportRequest.QueryParameter.TimeRange = QueryConstants.TIME_START + reportRequest.QueryParameter.TimeStart + "' AND '" + reportRequest.QueryParameter.TimeEnd + "'";

                reportRequest.QueryParameter.Duration = QueryConstants.DURATION_DEFAULT;
                if (reportRequest.QueryParameter.DurationStart != "00:00:00" || reportRequest.QueryParameter.DurationEnd != "00:00:00")
                    reportRequest.QueryParameter.Duration = QueryConstants.DURATION_BETWEEN + Convert.ToDateTime(reportRequest.QueryParameter.DurationStart).TimeOfDay.TotalMilliseconds.ToString() + "' AND '" + Convert.ToDateTime(reportRequest.QueryParameter.DurationEnd).TimeOfDay.TotalMilliseconds.ToString() + "'";

                if (reportRequest.Criteria != null)
                    reportRequest.QueryParameter.OptionString = " AND (" + createGroupClause(reportRequest.Criteria, recorder.Id) + " ) ";
                else
                    reportRequest.QueryParameter.OptionString = " AND (1 = 1) ";
                //setQueryOptions(reportRequest);
                if (reportRequest.QueryParameter.IsAniAli)
                    reportRequest.QueryParameter.OptionString += QueryConstants.WHERE_CLAUSE_ANIALI;
                //Incoming and Outgoing Calls
                if (reportRequest.QueryParameter.CallBarring == 1)
                    reportRequest.QueryParameter.OptionString += QueryConstants.INCOMING_CALLS;
                else if (reportRequest.QueryParameter.CallBarring == 2)
                    reportRequest.QueryParameter.OptionString += QueryConstants.OUTGOING_CALLS;

                if (reportRequest.IsCustomDataSearch)
                {
                    switch (reportRequest.CustomSearchType)
                    {
                        case CustomSearchType.InspectionTemplate:
                            reportRequest.QueryParameter.OptionString += " AND ( insp.InspectionTemplateId = " + reportRequest.QueryParameter.InspectionTemplateId + " ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3Inspection insp on insp.EventId = CI.CallId ";
                            break;
                        case CustomSearchType.BookmarkText:
                            //reportRequest.QueryParameter.OptionString += " AND ( bm.ID = " + reportRequest.QueryParameter.BookmarkId + " ) ";
                            reportRequest.QueryParameter.OptionString += " AND ( bm.MarkerAnswer LIKE N'%" + reportRequest.QueryParameter.BookmarkText + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN  iq3InspectionMarker bm ON bm.EventId =  CI.CallId ";
                            break;
                        case CustomSearchType.BookmarkNote:
                            reportRequest.QueryParameter.OptionString += " AND ( bm.Note LIKE N'%" + reportRequest.QueryParameter.BookmarkNotes + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN  iq3InspectionMarker bm ON bm.EventId =  CI.CallId ";
                            break;
                        case CustomSearchType.PreInspectionTitle:
                            reportRequest.QueryParameter.OptionString += " AND ( pid.PreInspectionTitle LIKE N'%" + reportRequest.QueryParameter.PreInspectionTitle + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3PreInspectionData pid ON pid.EventId = CI.CallId ";
                            break;
                        case CustomSearchType.PreInspectionData:
                            reportRequest.QueryParameter.OptionString += " AND ( pid.PreInspectionText LIKE N'%" + reportRequest.QueryParameter.PreInspectionData + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3PreInspectionData pid ON pid.EventId = CI.CallId ";
                            break;
                        default:
                            break;
                    }
                }

                #region ------- Custom Search -------

                if (!string.IsNullOrEmpty(reportRequest.QueryParameter.CustomText))
                {
                    StringBuilder sbCustomClause = new StringBuilder();
                    sbCustomClause.Append(" AND ");
                    sbCustomClause.Append(" ( ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" (CI.Ext LIKE N'%" + reportRequest.QueryParameter.CustomText + "%')");
                    sbCustomClause.Append(" OR ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" (CI.CustName LIKE N'%" + reportRequest.QueryParameter.CustomText + "%')");
                    sbCustomClause.Append(" OR ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" (CI.ANI LIKE N'%" + reportRequest.QueryParameter.CustomText + "%')");
                    sbCustomClause.Append(" OR ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" (CI.ANI_PH LIKE N'%" + reportRequest.QueryParameter.CustomText + "%')");
                    sbCustomClause.Append(" OR ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" (CI.ANI_NAME LIKE N'%" + reportRequest.QueryParameter.CustomText + "%')");
                    sbCustomClause.Append(" OR ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" (CI.Tag2 LIKE N'%" + reportRequest.QueryParameter.CustomText + "%')");
                    sbCustomClause.Append(" OR ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" (CI.Tag3 LIKE N'%" + reportRequest.QueryParameter.CustomText + "%')");
                    sbCustomClause.Append(" OR ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" (CI.Tag4 LIKE N'%" + reportRequest.QueryParameter.CustomText + "%')");
                    sbCustomClause.Append(" OR ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" (CI.CalledID LIKE N'%" + reportRequest.QueryParameter.CustomText + "%')");
                    sbCustomClause.Append(" OR ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" (CI.CALL_COMMENT LIKE N'%" + reportRequest.QueryParameter.CustomText + "%')");
                    sbCustomClause.Append(" OR ");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" ((select count(*) from t_BookMark BM where CI.CallID = BM.CallID and BM.bookmarktext LIKE N'%" + reportRequest.QueryParameter.CustomText + "%' ) > 0)");
                    sbCustomClause.AppendLine();
                    sbCustomClause.Append(" ) ");

                    reportRequest.QueryParameter.OptionString += sbCustomClause.ToString();
                }

                #endregion

                var recnodes = reportRequest.Criteria.RecorderAudioGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                var recnodesInquire = reportRequest.Criteria.RecorderInquireGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                //var recnodesMd = reportRequest.Criteria.RecorderMdGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                if ((recnodes != null && recnodes.AudioGroupExtension != null) || (recnodesInquire != null && recnodesInquire.InquireGroupExtension != null))
                {
                    if (recorder.IsPrimary)
                    {
                        if (getByHour)
                        {
                            totalCalls = new RPTCallInfoDALEC().GetSearchResultsHour(reportRequest, recorder);
                            return totalCalls;
                        }
                        totalCalls = new RPTCallInfoDALEC().GetSearchResults(reportRequest, recorder);
                    }
                    else
                    {
                        // Alternative approach
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        string selectKey = reportRequest.QueryParameter.SelectKey;
                        string selectYear = reportRequest.QueryParameter.SelectYear;
                        string selectMonth = reportRequest.QueryParameter.SelectMonth;
                        string selectDay = reportRequest.QueryParameter.SelectDay;
                        string fromDate = reportRequest.QueryParameter.FromDate;
                        string toDate = reportRequest.QueryParameter.ToDate;
                        string timeRange = reportRequest.QueryParameter.TimeRange;
                        string duration = reportRequest.QueryParameter.Duration;
                        string groupKey = reportRequest.QueryParameter.GroupKey;
                        string groupYear = reportRequest.QueryParameter.GroupYear;
                        string groupMonth = reportRequest.QueryParameter.GroupMonth;
                        string groupDay = reportRequest.QueryParameter.GroupDay;
                        string optionSTR = reportRequest.QueryParameter.OptionString;

                        if (getByHour)
                        {
                            //totalCalls = new RPTCallInfoDALEC().GetSearchResultsHour(reportRequest, recorder);
                            totalCalls = entClient.GetSearchResultsHour(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, recorder).ToList();
                            return totalCalls;
                        }
                        totalCalls = entClient.GetSearchResults(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, recorder).ToList();
                    }
                }
                //else+
                //{
                //    totalCalls = new RPTCallInfoDALEC().GetSearchResults(reportRequest, recorder);
                //}


                #region Inquire : Arivu
                //var recnodesInquire = reportRequest.Criteria.RecorderInquireGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                //if (recnodesInquire.InquireGroupExtension != null)
                //{
                //    if (getByHour)
                //    {
                //        totalCalls = new RPTCallInfoDALEC().GetSearchResultsHour(reportRequest, recorder);
                //        return totalCalls;
                //    }
                //    totalCalls = new RPTCallInfoDALEC().GetSearchResults(reportRequest, recorder);
                //}
                //else
                //{
                //    totalCalls = new RPTCallInfoDALEC().GetSearchResults(reportRequest, recorder);
                //}
                #endregion
                return totalCalls;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "performSearchDayWise", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "performSearchDayWise", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        private static List<RPTCallInfo> performSearchMonthWeekDayWise(ReportRequest reportRequest, List<RPTCallInfo> totalCalls, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "performSearchMonthWeekDayWise", reportRequest.TenantId, "performSearchMonthWeekDayWise function has been called successfully."));

                if (reportRequest.GroupData == GroupData.ByChannelName)
                {
                    reportRequest.QueryParameter.SelectKey = "CI.Ext + ' : ' + ISNULL(G.GroupName, 'unknown') + ' : ' + ISNULL(EI.ExtName, 'NULL')";
                    reportRequest.QueryParameter.GroupKey = "CI.Ext, G.GroupName,EI.ExtName,";
                }
                else if (reportRequest.GroupData == GroupData.ByAgent)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(A.UserName, CI.UserNum) + ' - ' + ISNULL(A.UserID, 'unknown') + ' : ' + ISNULL(G.GroupName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CI.UserNum, CI.GroupNum, G.GroupName, A.UserName, A.UserID,";
                }
                else if (reportRequest.GroupData == GroupData.ByChannel)
                {
                    reportRequest.QueryParameter.SelectKey = "CI.Ext + ' : ' + ISNULL(G.GroupName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CI.Ext, G.GroupName,";
                }
                else if (reportRequest.GroupData == GroupData.ByGroup)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(G.GroupName, 'unknown') + ' - ' + CONVERT(VARCHAR, CI.GroupNum)";
                    reportRequest.QueryParameter.GroupKey = "CI.GroupNum, G.GroupName,";
                }
                else if (reportRequest.GroupData == GroupData.ByTotal)
                {
                    reportRequest.QueryParameter.SelectKey = "'[TOTAL]'";
                    reportRequest.QueryParameter.GroupKey = string.Empty;
                }
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectYear))
                    reportRequest.QueryParameter.SelectYear = QueryConstants.SELECT_YEAR;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectMonth))
                    reportRequest.QueryParameter.SelectMonth = QueryConstants.SELECT_MONTH;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectDay))
                    reportRequest.QueryParameter.SelectDay = "dbo.fn_getWeekDay(CI.StartTime)";

                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupYear))
                    reportRequest.QueryParameter.GroupYear = QueryConstants.GROUP_YEAR;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupMonth))
                    reportRequest.QueryParameter.GroupMonth = QueryConstants.GROUP_MONTH;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupDay))
                    reportRequest.QueryParameter.GroupDay = "dbo.fn_getWeekDay(CI.StartTime)";

                reportRequest.QueryParameter.TimeRange = string.Empty;
                if (reportRequest.QueryParameter.TimeStart != "00:00:00" || reportRequest.QueryParameter.TimeEnd != "00:00:00")
                    reportRequest.QueryParameter.TimeRange = QueryConstants.TIME_START + reportRequest.QueryParameter.TimeStart + "' AND '" + reportRequest.QueryParameter.TimeEnd + "'";

                reportRequest.QueryParameter.Duration = QueryConstants.DURATION_DEFAULT;
                if (reportRequest.QueryParameter.DurationStart != "00:00:00" || reportRequest.QueryParameter.DurationEnd != "00:00:00")
                    reportRequest.QueryParameter.Duration = QueryConstants.DURATION_BETWEEN + Convert.ToDateTime(reportRequest.QueryParameter.DurationStart).TimeOfDay.TotalMilliseconds.ToString() + "' AND '" + Convert.ToDateTime(reportRequest.QueryParameter.DurationEnd).TimeOfDay.TotalMilliseconds.ToString() + "'";

                if (reportRequest.Criteria != null)
                    reportRequest.QueryParameter.OptionString = " AND ( " + createGroupClause(reportRequest.Criteria, recorder.Id) + " ) ";
                else
                    reportRequest.QueryParameter.OptionString = " AND (1 = 1) ";
                //setQueryOptions(reportRequest);
                if (reportRequest.QueryParameter.IsAniAli)
                    reportRequest.QueryParameter.OptionString += QueryConstants.WHERE_CLAUSE_ANIALI;
                //Incoming and Outgoing Calls
                if (reportRequest.QueryParameter.CallBarring == 1)
                    reportRequest.QueryParameter.OptionString += QueryConstants.INCOMING_CALLS;
                else if (reportRequest.QueryParameter.CallBarring == 2)
                    reportRequest.QueryParameter.OptionString += QueryConstants.OUTGOING_CALLS;

                if (reportRequest.IsCustomDataSearch)
                {
                    switch (reportRequest.CustomSearchType)
                    {
                        case CustomSearchType.InspectionTemplate:
                            reportRequest.QueryParameter.OptionString += " AND ( insp.InspectionTemplateId = " + reportRequest.QueryParameter.InspectionTemplateId + " ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3Inspection insp on insp.EventId = CI.CallId ";
                            break;
                        case CustomSearchType.BookmarkText:
                            //reportRequest.QueryParameter.OptionString += " AND ( bm.ID = " + reportRequest.QueryParameter.BookmarkId + " ) ";
                            reportRequest.QueryParameter.OptionString += " AND ( bm.MarkerAnswer LIKE N'%" + reportRequest.QueryParameter.BookmarkText + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN  iq3InspectionMarker bm ON bm.EventId =  CI.CallId ";
                            break;
                        case CustomSearchType.BookmarkNote:
                            reportRequest.QueryParameter.OptionString += " AND ( bm.Note LIKE N'%" + reportRequest.QueryParameter.BookmarkNotes + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN  iq3InspectionMarker bm ON bm.EventId =  CI.CallId ";
                            break;
                        case CustomSearchType.PreInspectionTitle:
                            reportRequest.QueryParameter.OptionString += " AND ( pid.PreInspectionTitle LIKE N'%" + reportRequest.QueryParameter.PreInspectionTitle + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3PreInspectionData pid ON pid.EventId = CI.CallId ";
                            break;
                        case CustomSearchType.PreInspectionData:
                            reportRequest.QueryParameter.OptionString += " AND ( pid.PreInspectionText LIKE N'%" + reportRequest.QueryParameter.PreInspectionData + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3PreInspectionData pid ON pid.EventId = CI.CallId ";
                            break;
                        default:
                            break;
                    }
                }

                //totalCalls = new RPTCallInfoDALEC().GetSearchResultsMonthDayOfWeek(reportRequest, recorder);
                var recnodes = reportRequest.IsOnlyIQ3ModeEnabled ? reportRequest.Criteria.RecorderInquireGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id) : reportRequest.Criteria.RecorderAudioGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                var recnodesInquire = reportRequest.Criteria.RecorderInquireGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                if ((recnodes != null && recnodes.AudioGroupExtension != null) || (recnodesInquire != null && recnodesInquire.InquireGroupExtension != null))
                {
                    //totalCalls = new RPTCallInfoDALEC().GetSearchResultsMonthDayOfWeek(reportRequest, recorder);
                    if (recorder.IsPrimary)
                    {
                        totalCalls = new RPTCallInfoDALEC().GetSearchResultsMonthDayOfWeek(reportRequest, recorder);
                    }
                    else
                    {
                        // Alternative approach
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        string selectKey = reportRequest.QueryParameter.SelectKey;
                        string selectYear = reportRequest.QueryParameter.SelectYear;
                        string selectMonth = reportRequest.QueryParameter.SelectMonth;
                        string selectDay = reportRequest.QueryParameter.SelectDay;
                        string fromDate = reportRequest.QueryParameter.FromDate;
                        string toDate = reportRequest.QueryParameter.ToDate;
                        string timeRange = reportRequest.QueryParameter.TimeRange;
                        string duration = reportRequest.QueryParameter.Duration;
                        string groupKey = reportRequest.QueryParameter.GroupKey;
                        string groupYear = reportRequest.QueryParameter.GroupYear;
                        string groupMonth = reportRequest.QueryParameter.GroupMonth;
                        string groupDay = reportRequest.QueryParameter.GroupDay;
                        string optionSTR = reportRequest.QueryParameter.OptionString;

                        totalCalls = entClient.GetSearchResultsMonthDayOfWeek(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, recorder).ToList();
                    }
                }

                return totalCalls;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "performSearchMonthWeekDayWise", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "performSearchMonthWeekDayWise", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        private static List<RPTCallInfo> performSearchMonthWise(ReportRequest reportRequest, List<RPTCallInfo> totalCalls, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "performSearchMonthWise", reportRequest.TenantId, "performSearchMonthWise function has been called successfully."));

                if (reportRequest.GroupData == GroupData.ByChannelName)
                {
                    reportRequest.QueryParameter.SelectKey = "CI.Ext + ' : ' + ISNULL(G.GroupName, 'unknown') + ' : ' + ISNULL(EI.ExtName, 'NULL')";
                    reportRequest.QueryParameter.GroupKey = "CI.Ext, G.GroupName,EI.ExtName,";
                }
                else if (reportRequest.GroupData == GroupData.ByAgent)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(A.UserName, CI.UserNum) + ' - ' + ISNULL(A.UserID, 'unknown') + ' : ' + ISNULL(G.GroupName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CI.UserNum, CI.GroupNum, G.GroupName, A.UserName, A.UserID,";
                }
                else if (reportRequest.GroupData == GroupData.ByChannel)
                {
                    reportRequest.QueryParameter.SelectKey = "CI.Ext + ' : ' + ISNULL(G.GroupName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CI.Ext, G.GroupName,";
                }
                else if (reportRequest.GroupData == GroupData.ByGroup)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(G.GroupName, 'unknown') + ' - ' + CONVERT(VARCHAR, CI.GroupNum)";
                    reportRequest.QueryParameter.GroupKey = "CI.GroupNum, G.GroupName,";
                }
                else if (reportRequest.GroupData == GroupData.ByTotal)
                {
                    reportRequest.QueryParameter.SelectKey = "'[TOTAL]'";
                    reportRequest.QueryParameter.GroupKey = string.Empty;
                }
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectYear))
                    reportRequest.QueryParameter.SelectYear = QueryConstants.SELECT_YEAR;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectMonth))
                    reportRequest.QueryParameter.SelectMonth = QueryConstants.SELECT_MONTH;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectDay))
                    reportRequest.QueryParameter.SelectDay = "'0'";

                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupYear))
                    reportRequest.QueryParameter.GroupYear = QueryConstants.GROUP_YEAR;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupMonth))
                    //reportRequest.QueryParameter.GroupMonth = QueryConstants.GROUP_MONTH;
                    reportRequest.QueryParameter.GroupMonth = QueryConstants.GROUP_MONTH.Remove(QueryConstants.GROUP_MONTH.Length - 1);


                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupDay))
                    reportRequest.QueryParameter.GroupDay = string.Empty;

                reportRequest.QueryParameter.TimeRange = string.Empty;
                if (reportRequest.QueryParameter.TimeStart != "00:00:00" || reportRequest.QueryParameter.TimeEnd != "00:00:00")
                    reportRequest.QueryParameter.TimeRange = QueryConstants.TIME_START + reportRequest.QueryParameter.TimeStart + "' AND '" + reportRequest.QueryParameter.TimeEnd + "'";

                reportRequest.QueryParameter.Duration = QueryConstants.DURATION_DEFAULT;
                if (reportRequest.QueryParameter.DurationStart != "00:00:00" || reportRequest.QueryParameter.DurationEnd != "00:00:00")
                    reportRequest.QueryParameter.Duration = QueryConstants.DURATION_BETWEEN + Convert.ToDateTime(reportRequest.QueryParameter.DurationStart).TimeOfDay.TotalMilliseconds.ToString() + "' AND '" + Convert.ToDateTime(reportRequest.QueryParameter.DurationEnd).TimeOfDay.TotalMilliseconds.ToString() + "'";

                if (reportRequest.Criteria != null)
                    reportRequest.QueryParameter.OptionString = " AND ( " + createGroupClause(reportRequest.Criteria, recorder.Id) + " ) ";
                else
                    reportRequest.QueryParameter.OptionString = " AND (1 = 1) ";
                //setQueryOptions(reportRequest);
                if (reportRequest.QueryParameter.IsAniAli)
                    reportRequest.QueryParameter.OptionString += QueryConstants.WHERE_CLAUSE_ANIALI;
                //Incoming and Outgoing Calls
                if (reportRequest.QueryParameter.CallBarring == 1)
                    reportRequest.QueryParameter.OptionString += QueryConstants.INCOMING_CALLS;
                else if (reportRequest.QueryParameter.CallBarring == 2)
                    reportRequest.QueryParameter.OptionString += QueryConstants.OUTGOING_CALLS;

                if (reportRequest.IsCustomDataSearch) {
                    switch (reportRequest.CustomSearchType)
                    {
                        case CustomSearchType.InspectionTemplate:
                            reportRequest.QueryParameter.OptionString += " AND ( insp.InspectionTemplateId = " + reportRequest.QueryParameter.InspectionTemplateId + " ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3Inspection insp on insp.EventId = CI.CallId ";
                            break;
                        case CustomSearchType.BookmarkText:
                            //reportRequest.QueryParameter.OptionString += " AND ( bm.ID = " + reportRequest.QueryParameter.BookmarkId + " ) ";
                            reportRequest.QueryParameter.OptionString += " AND ( bm.MarkerAnswer LIKE N'%" + reportRequest.QueryParameter.BookmarkText + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN  iq3InspectionMarker bm ON bm.EventId =  CI.CallId ";
                            break;
                        case CustomSearchType.BookmarkNote:
                            reportRequest.QueryParameter.OptionString += " AND ( bm.Note LIKE N'%" + reportRequest.QueryParameter.BookmarkNotes + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN  iq3InspectionMarker bm ON bm.EventId =  CI.CallId ";
                            break;
                        case CustomSearchType.PreInspectionTitle:
                            reportRequest.QueryParameter.OptionString += " AND ( pid.PreInspectionTitle LIKE N'%" + reportRequest.QueryParameter.PreInspectionTitle + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3PreInspectionData pid ON pid.EventId = CI.CallId ";
                            break;
                        case CustomSearchType.PreInspectionData:
                            reportRequest.QueryParameter.OptionString += " AND ( pid.PreInspectionText LIKE N'%" + reportRequest.QueryParameter.PreInspectionData + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3PreInspectionData pid ON pid.EventId = CI.CallId ";
                            break;
                        default:
                            break;
                    }
                }

                //totalCalls = new RPTCallInfoDALEC().GetSearchResults(reportRequest, recorder);
                var recnodes = reportRequest.Criteria.RecorderAudioGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                var recnodesInquire = reportRequest.Criteria.RecorderInquireGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                if ((recnodes != null && recnodes.AudioGroupExtension != null) || (recnodesInquire != null && recnodesInquire.InquireGroupExtension != null))
                {
                    //totalCalls = new RPTCallInfoDALEC().GetSearchResults(reportRequest, recorder);
                    if (recorder.IsPrimary)
                    {
                        totalCalls = new RPTCallInfoDALEC().GetSearchResults(reportRequest, recorder);
                    }
                    else
                    {
                        // Alternative approach
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        string selectKey = reportRequest.QueryParameter.SelectKey;
                        string selectYear = reportRequest.QueryParameter.SelectYear;
                        string selectMonth = reportRequest.QueryParameter.SelectMonth;
                        string selectDay = reportRequest.QueryParameter.SelectDay;
                        string fromDate = reportRequest.QueryParameter.FromDate;
                        string toDate = reportRequest.QueryParameter.ToDate;
                        string timeRange = reportRequest.QueryParameter.TimeRange;
                        string duration = reportRequest.QueryParameter.Duration;
                        string groupKey = reportRequest.QueryParameter.GroupKey;
                        string groupYear = reportRequest.QueryParameter.GroupYear;
                        string groupMonth = reportRequest.QueryParameter.GroupMonth;
                        string groupDay = reportRequest.QueryParameter.GroupDay;
                        string optionSTR = reportRequest.QueryParameter.OptionString;

                        totalCalls = entClient.GetSearchResults(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, recorder).ToList();
                    }

                }
                return totalCalls;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "performSearchMonthWise", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "performSearchMonthWise", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        private static List<RPTCallInfo> performSearchWeekWise(ReportRequest reportRequest, List<RPTCallInfo> totalCalls, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "performSearchWeekWise", reportRequest.TenantId, "performSearchWeekWise function has been called successfully."));

                if (reportRequest.GroupData == GroupData.ByChannelName)
                {
                    reportRequest.QueryParameter.SelectKey = "CI.Ext + ' : ' + ISNULL(G.GroupName, 'unknown') + ' : ' + ISNULL(EI.ExtName, 'NULL')";
                    reportRequest.QueryParameter.GroupKey = "CI.Ext, G.GroupName,EI.ExtName,";
                }
                else if (reportRequest.GroupData == GroupData.ByAgent)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(A.UserName, CI.UserNum) + ' - ' + ISNULL(A.UserID, 'unknown') + ' : ' + ISNULL(G.GroupName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CI.UserNum, CI.GroupNum, G.GroupName, A.UserName, A.UserID,";
                }
                else if (reportRequest.GroupData == GroupData.ByChannel)
                {
                    reportRequest.QueryParameter.SelectKey = "CI.Ext + ' : ' + ISNULL(G.GroupName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CI.Ext, G.GroupName,";
                }
                else if (reportRequest.GroupData == GroupData.ByGroup)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(G.GroupName, 'unknown') + ' - ' + CONVERT(VARCHAR, CI.GroupNum)";
                    reportRequest.QueryParameter.GroupKey = "CI.GroupNum, G.GroupName,";
                }
                else if (reportRequest.GroupData == GroupData.ByTotal)
                {
                    reportRequest.QueryParameter.SelectKey = "'[TOTAL]'";
                    reportRequest.QueryParameter.GroupKey = string.Empty;
                }

                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectYear))
                    reportRequest.QueryParameter.SelectYear = "0";
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectMonth))
                    reportRequest.QueryParameter.SelectMonth = "0";
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectDay))
                    reportRequest.QueryParameter.SelectDay = "dbo.fn_getWeekDay(CI.StartTime)";

                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupYear))
                    reportRequest.QueryParameter.GroupYear = "";
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupMonth))
                    reportRequest.QueryParameter.GroupMonth = "";
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupDay))
                    reportRequest.QueryParameter.GroupDay = "dbo.fn_getWeekDay(CI.StartTime)";

                reportRequest.QueryParameter.TimeRange = string.Empty;
                if (reportRequest.QueryParameter.TimeStart != "00:00:00" || reportRequest.QueryParameter.TimeEnd != "00:00:00")
                    reportRequest.QueryParameter.TimeRange = QueryConstants.TIME_START + reportRequest.QueryParameter.TimeStart + "' AND '" + reportRequest.QueryParameter.TimeEnd + "'";

                reportRequest.QueryParameter.Duration = QueryConstants.DURATION_DEFAULT;
                if (reportRequest.QueryParameter.DurationStart != "00:00:00" || reportRequest.QueryParameter.DurationEnd != "00:00:00")
                    reportRequest.QueryParameter.Duration = QueryConstants.DURATION_BETWEEN + Convert.ToDateTime(reportRequest.QueryParameter.DurationStart).TimeOfDay.TotalMilliseconds.ToString() + "' AND '" + Convert.ToDateTime(reportRequest.QueryParameter.DurationEnd).TimeOfDay.TotalMilliseconds.ToString() + "'";

                if (reportRequest.Criteria != null)
                    reportRequest.QueryParameter.OptionString = " AND ( " + createGroupClause(reportRequest.Criteria, recorder.Id) + " ) ";
                else
                    reportRequest.QueryParameter.OptionString = " AND (1 = 1) ";
                //setQueryOptions(reportRequest);
                if (reportRequest.QueryParameter.IsAniAli)
                    reportRequest.QueryParameter.OptionString += QueryConstants.WHERE_CLAUSE_ANIALI;
                //Incoming and Outgoing Calls
                if (reportRequest.QueryParameter.CallBarring == 1)
                    reportRequest.QueryParameter.OptionString += QueryConstants.INCOMING_CALLS;
                else if (reportRequest.QueryParameter.CallBarring == 2)
                    reportRequest.QueryParameter.OptionString += QueryConstants.OUTGOING_CALLS;

                if (reportRequest.IsCustomDataSearch)
                {
                    switch (reportRequest.CustomSearchType)
                    {
                        case CustomSearchType.InspectionTemplate:
                            reportRequest.QueryParameter.OptionString += " AND ( insp.InspectionTemplateId = " + reportRequest.QueryParameter.InspectionTemplateId + " ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3Inspection insp on insp.EventId = CI.CallId ";
                            break;
                        case CustomSearchType.BookmarkText:
                            //reportRequest.QueryParameter.OptionString += " AND ( bm.ID = " + reportRequest.QueryParameter.BookmarkId + " ) ";
                            reportRequest.QueryParameter.OptionString += " AND ( bm.MarkerAnswer LIKE N'%" + reportRequest.QueryParameter.BookmarkText + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN  iq3InspectionMarker bm ON bm.EventId =  CI.CallId ";
                            break;
                        case CustomSearchType.BookmarkNote:
                            reportRequest.QueryParameter.OptionString += " AND ( bm.Note LIKE N'%" + reportRequest.QueryParameter.BookmarkNotes + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN  iq3InspectionMarker bm ON bm.EventId =  CI.CallId ";
                            break;
                        case CustomSearchType.PreInspectionTitle:
                            reportRequest.QueryParameter.OptionString += " AND ( pid.PreInspectionTitle LIKE N'%" + reportRequest.QueryParameter.PreInspectionTitle + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3PreInspectionData pid ON pid.EventId = CI.CallId ";
                            break;
                        case CustomSearchType.PreInspectionData:
                            reportRequest.QueryParameter.OptionString += " AND ( pid.PreInspectionText LIKE N'%" + reportRequest.QueryParameter.PreInspectionData + "%' ) ";
                            reportRequest.JOINString = @" INNER JOIN iq3PreInspectionData pid ON pid.EventId = CI.CallId ";
                            break;
                        default:
                            break;
                    }
                }

                //totalCalls = new RPTCallInfoDALEC().GetSearchResultsDayOfWeek(reportRequest, recorder);
                var recnodes = reportRequest.Criteria.RecorderAudioGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                var recnodesInquire = reportRequest.Criteria.RecorderInquireGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                if ((recnodes != null && recnodes.AudioGroupExtension != null) || (recnodesInquire != null && recnodesInquire.InquireGroupExtension != null))
                {
                    //totalCalls = new RPTCallInfoDALEC().GetSearchResultsDayOfWeek(reportRequest, recorder);
                    if (recorder.IsPrimary)
                    {
                        totalCalls = new RPTCallInfoDALEC().GetSearchResultsDayOfWeek(reportRequest, recorder);
                    }
                    else
                    {
                        // Alternative approach
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        string selectKey = reportRequest.QueryParameter.SelectKey;
                        string selectYear = reportRequest.QueryParameter.SelectYear;
                        string selectMonth = reportRequest.QueryParameter.SelectMonth;
                        string selectDay = reportRequest.QueryParameter.SelectDay;
                        string fromDate = reportRequest.QueryParameter.FromDate;
                        string toDate = reportRequest.QueryParameter.ToDate;
                        string timeRange = reportRequest.QueryParameter.TimeRange;
                        string duration = reportRequest.QueryParameter.Duration;
                        string groupKey = reportRequest.QueryParameter.GroupKey;
                        string groupYear = reportRequest.QueryParameter.GroupYear;
                        string groupMonth = reportRequest.QueryParameter.GroupMonth;
                        string groupDay = reportRequest.QueryParameter.GroupDay;
                        string optionSTR = reportRequest.QueryParameter.OptionString;

                        totalCalls = entClient.GetSearchResultsDayOfWeek(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, recorder).ToList();
                    }

                }
                return totalCalls;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "performSearchWeekWise", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "performSearchWeekWise", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        #region Perform Search Audit Report
        private static List<RPTCallInfo> performCallAuditSearch(ReportRequest reportRequest, List<RPTCallInfo> totalCalls, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "performCallAuditSearch", reportRequest.TenantId, "performCallAuditSearch function has been called successfully."));

                if (reportRequest.Criteria != null)
                    reportRequest.QueryParameter.OptionString = " AND (" + createGroupClause(reportRequest.Criteria, recorder.Id) + " ) ";
                else
                    reportRequest.QueryParameter.OptionString = " AND (1 = 1) ";
                var recnodes = reportRequest.Criteria.RecorderAudioGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                var recnodesInquire = reportRequest.Criteria.RecorderInquireGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                //var recnodesMd = reportRequest.Criteria.RecorderMdGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                if (recnodes.AudioGroupExtension != null || recnodesInquire.InquireGroupExtension != null)
                {
                    if (recorder.IsPrimary)
                    {
                        totalCalls = new RPTCallInfoDALEC().GetCallAuditSearchResults(reportRequest, recorder);
                    }
                    else
                    {
                        // Alternative approach
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        string fromDate = reportRequest.QueryParameter.FromDate;
                        string toDate = reportRequest.QueryParameter.ToDate;
                        string optionSTR = reportRequest.QueryParameter.OptionString;
                        totalCalls = entClient.GetCallAuditSearchResults(fromDate, toDate, optionSTR, recorder).ToList();
                    }
                }
                return totalCalls;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "performCallAuditSearch", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "performCallAuditSearch", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        private static List<RPTCallInfo> performCallsNotAuditedSearch(ReportRequest reportRequest, List<RPTCallInfo> totalCalls, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "performCallsNotAuditedSearch", reportRequest.TenantId, "performCallsNotAuditedSearch function has been called successfully."));

                if (reportRequest.Criteria != null)
                    reportRequest.QueryParameter.OptionString = " AND (" + createGroupClause(reportRequest.Criteria, recorder.Id) + " ) ";
                else
                    reportRequest.QueryParameter.OptionString = " AND (1 = 1) ";
                var recnodes = reportRequest.Criteria.RecorderAudioGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                var recnodesInquire = reportRequest.Criteria.RecorderInquireGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                //var recnodesMd = reportRequest.Criteria.RecorderMdGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                if (recnodes.AudioGroupExtension != null || recnodesInquire.InquireGroupExtension != null)
                {
                    if (recorder.IsPrimary)
                    {
                        totalCalls = new RPTCallInfoDALEC().GetCallsNotAuditedSearchResults(reportRequest, recorder);
                    }
                    else
                    {
                        // Alternative approach
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        string fromDate = reportRequest.QueryParameter.FromDate;
                        string toDate = reportRequest.QueryParameter.ToDate;
                        string optionSTR = reportRequest.QueryParameter.OptionString;
                        totalCalls = entClient.GetCallsNotAuditedSearchResults(fromDate, toDate, optionSTR, recorder).ToList();
                    }
                }
                return totalCalls;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "performCallsNotAuditedSearch", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "performCallsNotAuditedSearch", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Call Results Grouping

        private static void PreformGroupingDayWise(List<RPTCallInfo> totalCalls, string fromDate, string toDate, Recorder rec, bool populateEmptyDate = true)
        {
            try
            {
                List<RPTCallInfo> calls = totalCalls;
                IEnumerable<DateTime> daysBetween = RPTDateTimeHelper.GetDaysBetweenTwoDates(DateTime.ParseExact(fromDate, "yyyyMMdd", null), DateTime.ParseExact(toDate, "yyyyMMdd", null));//null;
                if (populateEmptyDate)
                {
                    if (calls != null && calls.Count > 0)
                    {
                        List<RPTCallInfo> emptyCallInfos = new List<RPTCallInfo>();
                        //daysBetween = DateTimeHelper.GetDaysBetweenTwoDates(DateTime.ParseExact(fromDate, "yyyyMMdd", null), DateTime.ParseExact(toDate, "yyyyMMdd", null));
                        foreach (var day in daysBetween)
                        {
                            if (calls.FirstOrDefault(c => c.CallDate == day.Date) == null)
                                emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = day.Date, RecorderId = rec.Id, RecoderName = rec.Name }); //TODO Add a Proper Key
                        }
                        calls.AddRange(emptyCallInfos);
                    }
                }
                //if (!totalCalls.Any())yield return null;
            }
            catch (Exception ex) { throw ex; }
        }

        private static void PreformGroupingMonthWise(List<RPTCallInfo> totalCalls, string fromDate, string toDate, Recorder rec, bool populateEmptyDate = true)
        {
            try
            {
                List<RPTCallInfo> calls = totalCalls;
                //IEnumerable<DateTime> monthsBetween = DateTimeHelper.GetMonthsBetweenTwoDates(DateTime.ParseExact(fromDate, "yyyyMMdd", null), DateTime.ParseExact(toDate, "yyyyMMdd", null));//= null;
                if (populateEmptyDate)
                {
                    if (calls != null && calls.Count > 0)
                    {
                        List<RPTCallInfo> emptyCallInfos = new List<RPTCallInfo>();
                        var callMonths = from c in calls
                                         select c.CallDate;
                        var missingMonths = RPTDateTimeHelper.GetMissingMonths(DateTime.ParseExact(fromDate, "yyyyMMdd", null), DateTime.ParseExact(toDate, "yyyyMMdd", null), callMonths);
                        foreach (var mon in missingMonths)
                            emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = mon.Date, RecorderId = rec.Id, RecoderName = rec.Name }); //TODO Add a Proper Key

                        calls.AddRange(emptyCallInfos);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private static void PreformGroupingMonthWeekDayWise(List<RPTCallInfo> totalCalls, string fromDate, string toDate, Recorder rec, bool populateEmptyMonth = true)
        {
            try
            {
                List<RPTCallInfo> calls = totalCalls;

                if (populateEmptyMonth)
                {
                    if (calls != null && calls.Count > 0)
                    {
                        List<RPTCallInfo> emptyCallInfos = new List<RPTCallInfo>();
                        var callMonths = from c in calls
                                         select c.CallDate;

                        var callMonthsGrouped = (from c in calls
                                                 group c by c.CallDate);
                        //).ToList();

                        foreach (var month in callMonthsGrouped)
                        {
                            var sun = calls.FirstOrDefault(d => d.DayOfWeek == DayOfWeek.Sunday && d.CallDate.Month == month.Key.Month);
                            var mon = calls.FirstOrDefault(d => d.DayOfWeek == DayOfWeek.Monday && d.CallDate.Month == month.Key.Month);
                            var tue = calls.FirstOrDefault(d => d.DayOfWeek == DayOfWeek.Tuesday && d.CallDate.Month == month.Key.Month);
                            var wed = calls.FirstOrDefault(d => d.DayOfWeek == DayOfWeek.Wednesday && d.CallDate.Month == month.Key.Month);
                            var thu = calls.FirstOrDefault(d => d.DayOfWeek == DayOfWeek.Thursday && d.CallDate.Month == month.Key.Month);
                            var fri = calls.FirstOrDefault(d => d.DayOfWeek == DayOfWeek.Friday && d.CallDate.Month == month.Key.Month);
                            var sat = calls.FirstOrDefault(d => d.DayOfWeek == DayOfWeek.Saturday && d.CallDate.Month == month.Key.Month);

                            if (sun == null)
                                emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Key.Date, DayOfWeek = DayOfWeek.Sunday, RecorderId = rec.Id, RecoderName = rec.Name });
                            if (mon == null)
                                emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Key.Date, DayOfWeek = DayOfWeek.Monday, RecorderId = rec.Id, RecoderName = rec.Name });
                            if (tue == null)
                                emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Key.Date, DayOfWeek = DayOfWeek.Tuesday, RecorderId = rec.Id, RecoderName = rec.Name });
                            if (wed == null)
                                emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Key.Date, DayOfWeek = DayOfWeek.Wednesday, RecorderId = rec.Id, RecoderName = rec.Name });
                            if (thu == null)
                                emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Key.Date, DayOfWeek = DayOfWeek.Thursday, RecorderId = rec.Id, RecoderName = rec.Name });
                            if (fri == null)
                                emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Key.Date, DayOfWeek = DayOfWeek.Friday, RecorderId = rec.Id, RecoderName = rec.Name });
                            if (sat == null)
                                emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Key.Date, DayOfWeek = DayOfWeek.Saturday, RecorderId = rec.Id, RecoderName = rec.Name });
                        }
                        ///add calling months missing weekdays
                        calls.AddRange(emptyCallInfos);
                        emptyCallInfos = new List<RPTCallInfo>();

                        var missingMonths = RPTDateTimeHelper.GetMissingMonths(DateTime.ParseExact(fromDate, "yyyyMMdd", null), DateTime.ParseExact(toDate, "yyyyMMdd", null), callMonths);
                        foreach (var month in missingMonths)
                        {
                            emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Date, DayOfWeek = DayOfWeek.Sunday }); //TODO Add a Proper Key
                            emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Date, DayOfWeek = DayOfWeek.Saturday }); //TODO Add a Proper Key
                            emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Date, DayOfWeek = DayOfWeek.Monday }); //TODO Add a Proper Key
                            emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Date, DayOfWeek = DayOfWeek.Tuesday }); //TODO Add a Proper Key
                            emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Date, DayOfWeek = DayOfWeek.Wednesday }); //TODO Add a Proper Key
                            emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Date, DayOfWeek = DayOfWeek.Thursday }); //TODO Add a Proper Key
                            emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = month.Date, DayOfWeek = DayOfWeek.Friday }); //TODO Add a Proper Key
                        }
                        ///add missing months
                        if (emptyCallInfos.Count > 0)
                            calls.AddRange(emptyCallInfos);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private static void PreformGroupingWeekDaysWise(List<RPTCallInfo> totalCalls, Recorder rec, bool populateWeekDays = true)
        {
            try
            {
                List<RPTCallInfo> calls = totalCalls;
                IEnumerable<DayOfWeek> weekDays = null;
                if (populateWeekDays)
                {
                    if (calls != null && calls.Count > 0)
                    {
                        List<RPTCallInfo> emptyCallInfos = new List<RPTCallInfo>();
                        weekDays = RPTDateTimeHelper.EachDayOfTheWeek;
                        foreach (var day in weekDays)
                        {
                            if (calls.FirstOrDefault(c => c.DayOfWeek == day) == null)
                                emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, DayOfWeek = day, DayOfWeekString = day.ToString(), RecorderId = rec.Id, RecoderName = rec.Name }); //TODO Add a Proper Key
                                                                                                                                                                                            //emptyCallInfos.Add(new CallInfo { Key = calls[0].Key, DayOfWeek = day }); //TODO Add a Proper Key
                        }
                        calls.AddRange(emptyCallInfos);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #endregion

        #region 911 PBX Perform Search

        private static List<RPTCallInfo> perform911SearchDayWise(ReportRequest reportRequest, List<RPTCallInfo> totalCalls, Recorder recorder)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "perform911SearchDayWise", reportRequest.TenantId, "perform911SearchDayWise function has been called successfully."));

                if (reportRequest.GroupData == GroupData.ByChannelName)
                {
                    reportRequest.QueryParameter.SelectKey = "CI.Ext + ' : ' + ISNULL(G.GroupName, 'unknown') + ' : ' + ISNULL(EI.ExtName, 'NULL')";
                    reportRequest.QueryParameter.GroupKey = "CI.Ext, G.GroupName,EI.ExtName,";
                }
                else if (reportRequest.GroupData == GroupData.ByAgent)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(A.UserName, CI.UserNum) + ' - ' + ISNULL(A.UserID, 'unknown') + ' : ' + ISNULL(G.GroupName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CI.UserNum, CI.GroupNum, G.GroupName, A.UserName, A.UserID,";
                }
                else if (reportRequest.GroupData == GroupData.ByChannel)
                {
                    reportRequest.QueryParameter.SelectKey = "CI.Ext + ' : ' + ISNULL(G.GroupName, 'unknown')";
                    reportRequest.QueryParameter.GroupKey = "CI.Ext, G.GroupName,";
                }
                else if (reportRequest.GroupData == GroupData.ByGroup)
                {
                    reportRequest.QueryParameter.SelectKey = "ISNULL(G.GroupName, 'unknown') + ' - ' + CONVERT(VARCHAR, CI.GroupNum)";
                    reportRequest.QueryParameter.GroupKey = "CI.GroupNum, G.GroupName,";
                }
                else if (reportRequest.GroupData == GroupData.ByTotal)
                {
                    reportRequest.QueryParameter.SelectKey = "'[TOTAL]'";
                    reportRequest.QueryParameter.GroupKey = string.Empty;
                }
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectYear))
                    reportRequest.QueryParameter.SelectYear = QueryConstants.SELECT_YEAR;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectMonth))
                    reportRequest.QueryParameter.SelectMonth = QueryConstants.SELECT_MONTH;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.SelectDay))
                    reportRequest.QueryParameter.SelectDay = QueryConstants.SELECT_DAY;


                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupYear))
                    reportRequest.QueryParameter.GroupYear = QueryConstants.GROUP_YEAR;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupMonth))
                    reportRequest.QueryParameter.GroupMonth = QueryConstants.GROUP_MONTH;
                if (string.IsNullOrEmpty(reportRequest.QueryParameter.GroupDay))
                    reportRequest.QueryParameter.GroupDay = QueryConstants.GROUP_DAY;

                reportRequest.QueryParameter.TimeRange = string.Empty;
                if (reportRequest.QueryParameter.TimeStart != "00:00:00" || reportRequest.QueryParameter.TimeEnd != "00:00:00")
                    reportRequest.QueryParameter.TimeRange = QueryConstants.TIME_START + reportRequest.QueryParameter.TimeStart + "' AND '" + reportRequest.QueryParameter.TimeEnd + "'";

                reportRequest.QueryParameter.Duration = QueryConstants.DURATION_DEFAULT;
                if (reportRequest.QueryParameter.DurationStart != "00:00:00" || reportRequest.QueryParameter.DurationEnd != "00:00:00")
                    reportRequest.QueryParameter.Duration = QueryConstants.DURATION_BETWEEN + Convert.ToDateTime(reportRequest.QueryParameter.DurationStart).TimeOfDay.TotalMilliseconds.ToString() + "' AND '" + Convert.ToDateTime(reportRequest.QueryParameter.DurationEnd).TimeOfDay.TotalMilliseconds.ToString() + "'";

                if (reportRequest.Criteria != null)
                    reportRequest.QueryParameter.OptionString = " AND (" + createGroupClause(reportRequest.Criteria, recorder.Id) + " ) ";
                else
                    reportRequest.QueryParameter.OptionString = " AND (1 = 1) ";
                //setQueryOptions(reportRequest);
                if (reportRequest.QueryParameter.IsAniAli)
                    reportRequest.QueryParameter.OptionString += QueryConstants.WHERE_CLAUSE_ANIALI;
                //Incoming and Outgoing Calls
                if (reportRequest.QueryParameter.CallBarring == 1)
                    reportRequest.QueryParameter.OptionString += QueryConstants.INCOMING_CALLS;
                else if (reportRequest.QueryParameter.CallBarring == 2)
                    reportRequest.QueryParameter.OptionString += QueryConstants.OUTGOING_CALLS;

                var recnodes = reportRequest.Criteria.RecorderAudioGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                //var recnodesInquire = reportRequest.Criteria.RecorderInquireGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                //var recnodesMd = reportRequest.Criteria.RecorderMdGroupExtensions.FirstOrDefault(r => r.RecorderId == recorder.Id);
                if (recnodes != null && recnodes.AudioGroupExtension != null)
                {
                    if (recorder.IsPrimary)
                    {
                        totalCalls = new RPTCallInfoDALEC().GetSearchResults911(reportRequest, recorder);
                    }
                    else
                    {
                        // Alternative approach
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        string selectKey = reportRequest.QueryParameter.SelectKey;
                        string selectYear = reportRequest.QueryParameter.SelectYear;
                        string selectMonth = reportRequest.QueryParameter.SelectMonth;
                        string selectDay = reportRequest.QueryParameter.SelectDay;
                        string fromDate = reportRequest.QueryParameter.FromDate;
                        string toDate = reportRequest.QueryParameter.ToDate;
                        string timeRange = reportRequest.QueryParameter.TimeRange;
                        string duration = reportRequest.QueryParameter.Duration;
                        string groupKey = reportRequest.QueryParameter.GroupKey;
                        string groupYear = reportRequest.QueryParameter.GroupYear;
                        string groupMonth = reportRequest.QueryParameter.GroupMonth;
                        string groupDay = reportRequest.QueryParameter.GroupDay;
                        string optionSTR = reportRequest.QueryParameter.OptionString;

                        totalCalls = entClient.GetSearchResults911(selectKey, selectYear, selectMonth, selectDay, fromDate, toDate, timeRange, duration, groupKey, groupYear, groupMonth, groupDay, optionSTR, recorder).ToList();
                    }
                }
                return totalCalls;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "perform911SearchDayWise", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "perform911SearchDayWise", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }

        }

        private static void perform911GroupingDayWise(List<RPTCallInfo> totalCalls, string fromDate, string toDate, Recorder rec, bool populateEmptyDate = true)
        {
            try
            {
                List<RPTCallInfo> calls = totalCalls;
                IEnumerable<DateTime> daysBetween = RPTDateTimeHelper.GetDaysBetweenTwoDates(DateTime.ParseExact(fromDate, "yyyyMMdd", null), DateTime.ParseExact(toDate, "yyyyMMdd", null));//null;
                if (populateEmptyDate)
                {
                    if (calls != null && calls.Count > 0)
                    {
                        List<RPTCallInfo> emptyCallInfos = new List<RPTCallInfo>();
                        //daysBetween = DateTimeHelper.GetDaysBetweenTwoDates(DateTime.ParseExact(fromDate, "yyyyMMdd", null), DateTime.ParseExact(toDate, "yyyyMMdd", null));
                        foreach (var day in daysBetween)
                        {
                            if (calls.FirstOrDefault(c => c.CallDate == day.Date) == null)
                                emptyCallInfos.Add(new RPTCallInfo { Key = calls[0].Key, CallDate = day.Date, RecorderId = rec.Id, RecoderName = rec.Name }); //TODO Add a Proper Key
                        }
                        calls.AddRange(emptyCallInfos);
                    }
                }
                //if (!totalCalls.Any())yield return null;
            }
            catch (Exception ex) { throw ex; }
        }

        #endregion

        #endregion


        #region Bindings

        public List<RPTCallInfo> GetSearchResultsExcel()
        {
            return null;
        }
        public List<RPTCallInfoDetail> GetSearchResultsDetails()
        {
            return null;
        }

        #endregion


        #region Evaluation Reports

        public ReportResponse GetRPTEvaluations(ReportRequest rRequest)
        {
            try
            {
                rRequest.CustomReportSearchCriteria = this.PopulateEvaluationSearchCriteria(rRequest.QueryParameter);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetRPTEvaluations", rRequest.TenantId, "GetRPTEvaluations function has been called successfully. CustomReportSearchCriteria = " + rRequest.CustomReportSearchCriteria));

                List<RPTEvaluation> RPTEvaluations = new ReportDAL(rRequest.TenantId).GetRPTEvaluationReport(rRequest.CustomReportSearchCriteria, 0);
                if (rRequest.IsEvalReportGroup)
                    PreformEvalGroupingMonthWise(RPTEvaluations, rRequest.QueryParameter.FromDate, rRequest.QueryParameter.ToDate);
                return new ReportResponse { RPTEvaluations = RPTEvaluations };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetRPTEvaluations", rRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetRPTEvaluations", rRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse GetRPTEvaluationsMain(ReportRequest rRequest)
        {
            try
            {
                rRequest.CustomReportSearchCriteria = this.PopulateEvaluationSearchCriteria(rRequest.QueryParameter);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetRPTEvaluationsMain", rRequest.TenantId, "GetRPTEvaluationsMain function has been called successfully. CustomReportSearchCriteria = " + rRequest.CustomReportSearchCriteria));

                return new ReportResponse
                {
                    RPTEvaluationsMain = new ReportDAL(rRequest.TenantId).GetRPTEvaluationMainReport(rRequest.CustomReportSearchCriteria, 0),
                    RPTEvaluations = new ReportDAL(rRequest.TenantId).GetRPTEvaluationReport(rRequest.CustomReportSearchCriteria, 0)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetRPTEvaluationsMain", rRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetRPTEvaluationsMain", rRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        private static void PreformEvalGroupingMonthWise(List<RPTEvaluation> totalEvals, string fromDate, string toDate, bool populateEmptyDate = true)
        {
            try
            {
                List<RPTEvaluation> calls = totalEvals;
                /*Delete this:added this for Monthly report*/
                calls.ForEach(c =>
                {
                    c.CompletedDate = new DateTime(c.CompletedDate.Year, c.CompletedDate.Month, 1);
                });
                /*Delete this:added this for Monthly report*/
                if (populateEmptyDate)
                {
                    if (calls != null && calls.Count > 0)
                    {
                        List<RPTEvaluation> emptyCallInfos = new List<RPTEvaluation>();
                        var callMonths = from c in calls
                                         select c.CompletedDate;
                        var missingMonths = RPTDateTimeHelper.GetMissingMonths(DateTime.ParseExact(fromDate, "yyyyMMdd", null), DateTime.ParseExact(toDate, "yyyyMMdd", null), callMonths);
                        foreach (var mon in missingMonths)
                            emptyCallInfos.Add(new RPTEvaluation { Id = 0, AgentId = calls[0].AgentId, CompletedDate = mon.Date, QScore = 0, EvaluatedScore = 0 }); //TODO Add a Proper Key

                        calls.AddRange(emptyCallInfos);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        private string PopulateEvaluationSearchCriteria(RPTQueryParameter rPTQueryParameter)
        {
            try
            {
                StringBuilder sbCriteria = new StringBuilder();
                if (rPTQueryParameter.OptionSurveyString != "0")
                {
                    sbCriteria.Append(String.Format(" AND ceo.SurveyId = {0} ", rPTQueryParameter.OptionSurveyString));
                }
                if (rPTQueryParameter.IsDateForEvaluation)
                {
                    //AND CompletedDate  BETWEEN '2012-11-01 00:00:00' AND '2013-01-31 00:00:00'";
                    sbCriteria.Append(String.Format(" AND CompletedDate  BETWEEN '{0}' AND '{1}' "
                        , (rPTQueryParameter.FromDate + "000000").ConvertStringDateTimeToDateTime().ToString("yyyy-MM-dd HH:mm:ss")
                        , (rPTQueryParameter.ToDate + "235959").ConvertStringDateTimeToDateTime().ToString("yyyy-MM-dd HH:mm:ss")));
                }
                else
                {
                    sbCriteria.Append(String.Format(" AND StartTime  BETWEEN '{0}000000' AND '{1}235959' "
                        , rPTQueryParameter.FromDate, rPTQueryParameter.ToDate));
                }

                if (rPTQueryParameter.GroupExtensions[0].Length > 0)
                {
                    sbCriteria.Append(QueryHelper.BuildGroupUserWhereClause(rPTQueryParameter.GroupExtensions,
                        rPTQueryParameter.XAxisData == 3 ? "AppUserId" : "AppUserId")); // Ext
                                                                                        //rPTQueryParameter.XAxisData == 3 ? "UserId" : "AppUserId"));

                }
                return sbCriteria.ToString();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public ReportResponse GetSurveysOnly(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetSurveysOnly", tenantId, "GetSurveysOnly function has been called successfully."));

                return new ReportResponse { Surveys = new SurveyDAL(tenantId).GetSurveysOnly() };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetSurveysOnly", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetSurveysOnly", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        #region Evaluation Reports EC
        public ReportResponse GetRPTEvaluationsFromRecorder(Recorder recorder, ReportRequest rRequest)
        {
            List<RPTEvaluation> RPTEvaluations = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "GetRPTEvaluationsFromRecorder", rRequest.TenantId, "GetRPTEvaluationsFromRecorder function has been called successfully. recorder.Id = " + recorder.Id + " recorder.Name = " + recorder.Name));
                rRequest.CustomReportSearchCriteria = this.PopulateEvaluationSearchCriteria(rRequest.QueryParameter);
                if (recorder.IsPrimary)
                    RPTEvaluations = new ReportDALEC().GetRPTEvaluationReportFromRecorder(recorder, rRequest.CustomReportSearchCriteria, 0);
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    RPTEvaluations = entClient.GetRPTEvaluationReportFromRecorder(recorder, rRequest.CustomReportSearchCriteria, 0).ToList();
                }
                if (rRequest.IsEvalReportGroup)
                    PreformEvalGroupingMonthWise(RPTEvaluations, rRequest.QueryParameter.FromDate, rRequest.QueryParameter.ToDate);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetRPTEvaluationsFromRecorder", rRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetRPTEvaluationsFromRecorder", rRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return new ReportResponse { RPTEvaluations = RPTEvaluations };
        }
        public ReportResponse GetRPTEvaluationsMainFromRecorder(Recorder recorder, ReportRequest rRequest)
        {
            List<RPTEvaluationMain> mainRPTEvaluations = null;
            List<RPTEvaluation> rptEvaluation = null;
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "GetRPTEvaluationsMainFromRecorder", rRequest.TenantId, "GetRPTEvaluationsMainFromRecorder function has been called successfully. recorder.Id = " + recorder.Id + " Recorder Name = " + recorder.Name));
                rRequest.CustomReportSearchCriteria = this.PopulateEvaluationSearchCriteria(rRequest.QueryParameter);
                mainRPTEvaluations = new ReportDALEC().GetRPTEvaluationMainReportFromRecorder(recorder, rRequest.CustomReportSearchCriteria, 0);
                rptEvaluation = new ReportDALEC().GetRPTEvaluationReportFromRecorder(recorder, rRequest.CustomReportSearchCriteria, 0);
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetRPTEvaluationsMainFromRecorder", rRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetRPTEvaluationsMainFromRecorder", rRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return new ReportResponse
            {
                RPTEvaluationsMain = mainRPTEvaluations,
                RPTEvaluations = rptEvaluation
            };
        }
        public ReportResponse GetSurveysFromRecorder(Recorder recorder)
        {
            List<Survey> surveys = null;
            try
            {

                if (recorder.IsPrimary)
                {
                    surveys = new SurveyDALEC().GetPublishedSurveysFromRecorder(recorder);
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    surveys = entClient.GetSurveysFromRecorder(recorder).ToList();
                }
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "GetSurveysFromRecorder", 0, "GetSurveysFromRecorder function has been called successfully. recorder.Id = " + recorder.Id + "Recorder Name = " + recorder.Name));
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetSurveysFromRecorder", 0, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetSurveysFromRecorder", 0, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
            return new ReportResponse { Surveys = surveys };
        }
        #endregion

        #region Call Audit Report
        public ReportResponse GetCallAuditReport(ReportRequest reportRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetCallAuditReport", reportRequest.TenantId, "GetCallAuditReport function has been called successfully."));

                var criteria = reportRequest.CallAuditCriteria;
                int totalPages = 0;
                int totalRecords = 0;
                return new ReportResponse { CallAuditReports = new ReportDAL(reportRequest.TenantId).GetCallAuditReport(criteria.UserNum, criteria.StartDate, criteria.EndDate, criteria.PageSize, criteria.PageNumber, out totalPages, out totalRecords), TotalPages = totalPages, TotalRecords = totalRecords };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetCallAuditReport", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetCallAuditReport", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public ReportResponse GetAuditedCallsByExtension(ReportRequest reportRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetAuditedCallsByExtension", reportRequest.TenantId, "GetAuditedCallsByExtension function has been called successfully."));
                var recorders = reportRequest.Recorders;
                var criteria = reportRequest.CallAuditCriteria;
                var reportResponse = new ReportResponse();

                foreach (var rec in recorders)
                {
                    if (rec.IsPrimary)
                    {
                        reportResponse.CallAuditReports = new ReportDAL(reportRequest.TenantId).GetAuditedCallsByExtension(criteria.StartDate, criteria.EndDate, criteria.Ext);
                    }
                    else
                    {
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        reportResponse.CallAuditReports = entClient.GetAuditedCallsByExtension(criteria.StartDate, criteria.EndDate, criteria.Ext).ToList();
                    }
                }
                return reportResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetAuditedCallsByExtension", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetAuditedCallsByExtension", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public ReportResponse GetCallAuditTrail(ReportRequest reportRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetCallAuditTrail", reportRequest.TenantId, "GetCallAuditTrail function has been called successfully."));

                var recorders = reportRequest.Recorders;
                var criteria = reportRequest.CallAuditCriteria;
                var reportResponse = new ReportResponse();
                foreach (var rec in recorders)
                {
                    if (rec.IsPrimary)
                        reportResponse.CallAuditReports = new ReportDAL(reportRequest.TenantId).GetCallAuditTrail(criteria.StartDate, criteria.EndDate, criteria.CallId);
                    else
                    {
                        RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                        entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + rec.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                        reportResponse.CallAuditReports = entClient.GetCallAuditTrail(criteria.StartDate, criteria.EndDate, criteria.CallId).ToList();
                    }
                }
                return reportResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetCallAuditTrail", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetCallAuditTrail", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Saved Report
        public ReportResponse AddSavedReport(ReportRequest reportRequest)
        {
            try
            {
                var savedReport = reportRequest.SavedReport;
                savedReport.Id = new ReportDAL(reportRequest.TenantId).AddSavedReport(savedReport);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "AddSavedReport", reportRequest.TenantId, "AddSavedReport function has been called successfully. savedReport.Id = " + savedReport.Id));
                return new ReportResponse
                {
                    Acknowledge = savedReport.Id > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = savedReport.Id > 0 ? 1 : 0,
                    Message = string.Format("{0} has been saved successfully.", savedReport.ReportName),
                    SavedReport = savedReport,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "AddSavedReport", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "AddSavedReport", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse CreateDuplicateReport(int tenantId, int UserId, int ReportId, string ReportName, string ReportDescription)
        {
            try
            {
                SavedReport objSavedReport = new SavedReport();
                objSavedReport = new ReportDAL(tenantId).CreateDuplicateReport(UserId, ReportId, ReportName, ReportDescription);

                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "AddSavedReport", tenantId, "CreateDuplicateReport function has been called successfully. objSavedReport.Id = " + objSavedReport.Id));
                return new ReportResponse
                {
                    Acknowledge = objSavedReport != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = objSavedReport.Id > 0 ? 1 : 0,
                    Message = string.Format("{0} has been duplicated successfully.", objSavedReport.ReportName),
                    SavedReport = objSavedReport,
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "CreateDuplicateReport", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "CreateDuplicateReport", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse UpdateSavedReport(ReportRequest reportRequest)
        {
            try
            {
                var savedReport = reportRequest.SavedReport;
                savedReport.Id = new ReportDAL(reportRequest.TenantId).UpdateSavedReport(savedReport.Id, savedReport.Criteria);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "UpdateSavedReport", reportRequest.TenantId, "UpdateSavedReport function has been called successfully. savedReport.Id = " + savedReport.Id));
                return new ReportResponse { SavedReport = savedReport };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "UpdateSavedReport", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "UpdateSavedReport", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public ReportResponse GetAllSavedReports(ReportRequest reportRequest)
        {
            try
            {
                var savedReport = reportRequest.SavedReport;
                List<SavedReport> savedReports = new ReportDAL(reportRequest.TenantId).GetAllSavedReports(savedReport.UserId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "GetAllSavedReports", reportRequest.TenantId, "GetAllSavedReports function has been called successfully. SavedBy = " + reportRequest.SavedReport.UserId));
                return new ReportResponse { SavedReports = savedReports };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetAllSavedReports", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetAllSavedReports", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        public ReportResponse GetSavedReportsPaged(ReportRequest reportRequest)
        {
            try
            {
                var criteria = reportRequest.SavedReportCriteria;

                StringBuilder sbWhereClause = new StringBuilder();
                //sbWhereClause.Append(" 1 = 1 ");
                #region ------- Where Criteria -------

                //TODO:: Move this if statement inside switch statement below.
                if (criteria != null)
                {
                    /*if (criteria.UserId > 0)
                    {
                        sbWhereClause.Append(" AND (UserId = " + criteria.UserId + ")");
                        sbWhereClause.AppendLine();
                    }*/
                    if (!string.IsNullOrEmpty(criteria.ReportName))
                    {
                        sbWhereClause.Append(" AND (ReportName = " + criteria.ReportName + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.IsFavorite.HasValue)
                    {
                        //sbWhereClause.Append(" AND (IsFavorite = " + criteria.IsFavorite.Value.ToString().ToLower() + ")");
                        //sbWhereClause.Append(" AND (IsFavorite = " + Convert.ToInt32(criteria.IsFavorite.Value) + ")");
                        sbWhereClause.Append(" AND (FavoriteOf LIKE '%" + Convert.ToInt32(criteria.UserId) + "%')");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.IsShared.HasValue)
                    {
                        sbWhereClause.Append(" AND (IsShared = " + criteria.IsShared.Value + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.IsScheduled.HasValue)
                    {
                        sbWhereClause.Append(" AND (IsScheduled= " + criteria.IsScheduled.Value + ")");
                        sbWhereClause.AppendLine();
                    }
                    sbWhereClause.AppendLine();
                }
                switch (criteria.SavedReportMode)
                {
                    case SavedReportMode.None:
                        break;
                    case SavedReportMode.All:
                        criteria.LoadGroupBasedReports = true;
                        break;
                    case SavedReportMode.Recent:
                        DateTime startDate = DateTime.Now.AddDays(-7); 
                        DateTime endDate = DateTime.Now;
                        sbWhereClause.AppendFormat(@" AND (LastExecutedOn >= '{0}' AND LastExecutedOn < '{1}')", startDate, endDate);
                        sbWhereClause.AppendLine();
                        break;
                    case SavedReportMode.MyReports:
                        criteria.LoadGroupBasedReports = false;
                        break;
                    case SavedReportMode.Scheduled:
                        break;
                    case SavedReportMode.Shared:
                        break;
                    case SavedReportMode.Favorite:
                        break;
                    default:
                        break;
                }
                #endregion





                int totalPages = 0;
                long totalRecords = 0;

                var savedReports = new ReportDAL(reportRequest.TenantId).GetSavedReportsPaged(criteria.UserId, criteria.PageSize, criteria.PageIndex, sbWhereClause.ToString(), criteria.LoadGroupBasedReports, out totalPages, out totalRecords);
                return new ReportResponse
                {
                    SavedReports = savedReports,
                    TotalPages = totalPages,
                    TotalRecords = Convert.ToInt32(totalRecords),
                };
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetSavedReportsPaged", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse GetSavedReportById(ReportRequest reportRequest)
        {
            try
            {
                var savedRpt = reportRequest.SavedReport;
                SavedReport savedReport = new ReportDAL(reportRequest.TenantId).GetSavedReportById(savedRpt.Id);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetSavedReportById", reportRequest.TenantId, "GetSavedReportById function has been called successfully. SavedBy = " + reportRequest.SavedReport.UserId));
                return new ReportResponse { SavedReport = savedReport };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetSavedReportById", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetSavedReportById", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportRequest GetSavedReportCriteriaById(ReportRequest reportRequest)
        {
            try
            {
                var savedRpt = reportRequest.SavedReport;
                string savedReportCritera = new ReportDAL(reportRequest.TenantId).GetSavedReportCriteriaById(savedRpt.Id);
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetSavedReportCriteriaById", reportRequest.TenantId, "GetSavedReportCriteriaById function has been called successfully. SavedBy = " + reportRequest.SavedReport.UserId));
                ReportRequest request = new JavaScriptSerializer().Deserialize<ReportRequest>(savedReportCritera);
                return request;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetSavedReportCriteriaById", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetSavedReportCriteriaById", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse DeleteSavedReport(int tenantId, int savedReportId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "DeleteSavedReport", tenantId, "DeleteSavedReport function has been called successfully."));

                bool isSavedReportDeleted = new ReportDAL(tenantId).DeleteSavedReport(savedReportId);
                return new ReportResponse { SavedReport = new SavedReport { Id = savedReportId, IsDeleted = isSavedReportDeleted } };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "DeleteSavedReport", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "DeleteSavedReport", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse UpdateReportExecutionTime(int tenantId, int savedReportId, DateTime executionTime)
        {
            try
            {
                int rowsAffected = new ReportDAL(tenantId).UpdateReportExecutionTime(savedReportId, executionTime);
                return new ReportResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    Message = "Report execution time has been successfully updated."
                };
            }
            catch (Exception ex) { throw ex; }
        }


        public ReportResponse DeleteSavedReports(ReportRequest reportRequest)
        {
            try
            {
                int rowsAffected = new ReportDAL(reportRequest.TenantId).DeleteSavedReports(reportRequest.Ids);
                return new ReportResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    Message = string.Format("Saved Reports has been deleted successfully against Id:{0}.", string.Join(", ", reportRequest.Ids))
                };
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion

        public ReportResponse MarkUnmarkFavorite(int tenantId, int userNum, int savedReportId, bool isFavorite)
        {
            try
            {
                int rowsAffected = new ReportDAL(tenantId).MarkUnmarkFavorite(userNum, savedReportId, isFavorite);
                return new ReportResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    RowsAffected = rowsAffected,
                    Message = string.Format("Saved Reports has been successfully marked as {0}.", isFavorite ? "Favorite" : "Un-Favorite")
                };
            }
            catch (Exception ex) { throw ex; }
        }

        #region Activity Report
        public ReportResponse GetUserActivityReportFromRecorder(ReportRequest reportRequest)
        {
            try
            {
                var criteria = reportRequest.Criteria;
                var recorder = reportRequest.Recorder;
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.Report, "GetUserActivityReportFromRecorder", reportRequest.TenantId, "GetUserActivityReportFromRecorder function has been called successfully."));
                int totalPages = 0;
                long totalRecords = 0;
                if (recorder.IsPrimary)
                {
                    return new ReportResponse
                    {
                        UserActivities = new ReportDAL(reportRequest.TenantId).GetUserActivities(criteria.UserId, criteria.StartDate, criteria.EndDate, criteria.PageSize, criteria.PageIndex, out totalPages, out totalRecords),
                        TotalPages = totalPages,
                        TotalRecords = Convert.ToInt32(totalRecords),
                    };
                }
                else
                {
                    // Alternative approach
                    RevcordEnterpriseSvc.RevEnterpriseSvcClient entClient = new RevcordEnterpriseSvc.RevEnterpriseSvcClient();
                    entClient.Endpoint.Address = new System.ServiceModel.EndpointAddress(new Uri(@"http://" + recorder.IP + @"/RevEnterpriseSvc/RevEnterpriseSvc.svc"), new[] { System.ServiceModel.Channels.AddressHeader.CreateAddressHeader("ServerIP", "", _serverIP) });
                    return new ReportResponse
                    {
                        UserActivities = entClient.GetUserActivities(criteria.UserId, criteria.StartDate, criteria.EndDate, criteria.PageSize, criteria.PageIndex, out totalPages, out totalRecords).ToList(),
                        TotalPages = totalPages,
                        TotalRecords = Convert.ToInt32(totalRecords),
                    };
                }
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetUserActivityReportFromRecorder", reportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetUserActivityReportFromRecorder", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
        #endregion

        #region Shared Report Info

        public ReportResponse GetSharedSavedReportsPaged(ReportRequest reportRequest)
        {
            try
            {
                var criteria = reportRequest.SavedReportCriteria;

                StringBuilder sbWhereClause = new StringBuilder();

                if (criteria != null)
                {
                    if (!string.IsNullOrEmpty(criteria.ReportName))
                    {
                        sbWhereClause.Append(" AND (ReportName = " + criteria.ReportName + ")");
                        sbWhereClause.AppendLine();
                    }
                    if (criteria.IsFavorite.HasValue)
                    {
                        sbWhereClause.Append(" AND (IsFavorite = " + Convert.ToInt32(criteria.IsFavorite.Value) + ")");
                        sbWhereClause.AppendLine();
                    }
                    sbWhereClause.AppendLine();
                }

                int totalPages = 0;
                long totalRecords = 0;

                var savedReports = new ReportDAL(reportRequest.TenantId).GetSharedSavedReportsPaged(criteria.UserId, criteria.PageSize, criteria.PageIndex, sbWhereClause.ToString(), out totalPages, out totalRecords);
                return new ReportResponse
                {
                    SavedReports = savedReports,
                    TotalPages = totalPages,
                    TotalRecords = Convert.ToInt32(totalRecords),
                };
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetSharedSavedReportsPaged", reportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse GetSharedReportInfo(ReportRequest objReportRequest)
        {
            try
            {
                SharedReportInfo objSharedReportInfo = null;

                objSharedReportInfo = new ReportDAL(objReportRequest.TenantId).GetSharedReportInfo(objReportRequest.SharedReportInfoId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "GetSharedReportInfo", objReportRequest.TenantId, new JavaScriptSerializer().Serialize(objSharedReportInfo)));

                return new ReportResponse
                {
                    Acknowledge = objSharedReportInfo != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    SharedReportInfo = objSharedReportInfo
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetSharedReportInfo", objReportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetSharedReportInfo", objReportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse GetSharedReportInfoByReport(ReportRequest objReportRequest)
        {
            try
            {
                SharedReportInfo objSharedReportInfo = null;

                objSharedReportInfo = new ReportDAL(objReportRequest.TenantId).GetSharedReportInfoByReport(objReportRequest.ReportId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "GetSharedReportInfoByReport", objReportRequest.TenantId, new JavaScriptSerializer().Serialize(objSharedReportInfo)));

                return new ReportResponse
                {
                    Acknowledge = objSharedReportInfo != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    SharedReportInfo = objSharedReportInfo
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetSharedReportInfoByReport", objReportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetSharedReportInfoByReport", objReportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse SaveSharedReportInfo(ReportRequest objReportRequest)
        {
            try
            {
                SharedReportInfo objSharedReportInfo = objReportRequest.SharedReportInfo;
                objSharedReportInfo.Id = new ReportDAL(objReportRequest.TenantId).SaveSharedReportInfo(objReportRequest.SharedReportInfo);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "SaveSharedReportInfo", objReportRequest.TenantId, " SharedReportInfoId = " + objSharedReportInfo.Id));
                return new ReportResponse
                {
                    Acknowledge = objSharedReportInfo.Id > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    SharedReportInfo = objSharedReportInfo
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "SaveSharedReportInfo", objReportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "SaveSharedReportInfo", objReportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse UpdateSharedReportInfo(ReportRequest objReportRequest)
        {
            try
            {
                SharedReportInfo objSharedReportInfo = objReportRequest.SharedReportInfo;
                objSharedReportInfo.Id = new ReportDAL(objReportRequest.TenantId).UpdateSharedReportInfo(objReportRequest.SharedReportInfo);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "UpdateSharedReportInfo", objReportRequest.TenantId, " SharedReportInfoId = " + objSharedReportInfo.Id));
                return new ReportResponse
                {
                    Acknowledge = objSharedReportInfo.Id > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    SharedReportInfo = objSharedReportInfo
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "UpdateSharedReportInfo", objReportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "UpdateSharedReportInfo", objReportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public ReportResponse RemoveSharedReportInfo(ReportRequest objReportRequest)
        {
            try
            {
                int SharedReportInfoId = new ReportDAL(objReportRequest.TenantId).RemoveSharedReportInfo(objReportRequest.SharedReportInfoId);
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "RemoveSharedReportInfo", objReportRequest.TenantId, " SharedReportInfoId = " + SharedReportInfoId));

                return new ReportResponse
                {
                    Acknowledge = SharedReportInfoId > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    SharedReportInfo = new SharedReportInfo { Id = SharedReportInfoId }
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "RemoveSharedReportInfo", objReportRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "RemoveSharedReportInfo", objReportRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        public string GetCompanyNameForVesselReport(int tenantId)
        {
            try
            {
                string companyName = new ReportDAL(tenantId).GetCompanyNameForVesselReport();
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.Report, "GetCompanyNameForVesselReport", tenantId, " Company Name = " + companyName));
                return companyName;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.Report, "GetCompanyNameForVesselReport", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.Report, "GetCompanyNameForVesselReport", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region Tenant IQ3 Usage Report
        public ReportResponse FetchTenantIQ3UsageReport(ReportRequest reportRequest)
        {
            try
            {
                List<TenantIQ3Usage> tenantIQ3Usage = new List<TenantIQ3Usage>();
                switch (reportRequest.Criteria.TenantAppType)
                {
                    case TenantAppType.All:
                        tenantIQ3Usage = new ReportDAL(reportRequest.TenantId).FetchTenantIQ3UsageReport(reportRequest.Criteria);
                        var mgoTenantIQ3UsageList = new ReportDAL(reportRequest.TenantId).FetchMGOTenantIQ3UsageReport(reportRequest.Criteria);
                        tenantIQ3Usage.AddRange(mgoTenantIQ3UsageList);
                        break;
                    case TenantAppType.MT:
                        tenantIQ3Usage = new ReportDAL(reportRequest.TenantId).FetchTenantIQ3UsageReport(reportRequest.Criteria);
                        break;
                    case TenantAppType.MGO:
                        tenantIQ3Usage = new ReportDAL(reportRequest.TenantId).FetchMGOTenantIQ3UsageReport(reportRequest.Criteria);
                        break;
                    default:
                        break;
                }
                

                return new ReportResponse
                {
                    Acknowledge = tenantIQ3Usage != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    TenantIQ3UsageList = tenantIQ3Usage
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public ReportResponse FetchIQ3UsageDetailsByTenant(ReportRequest reportRequest)
        {
            try
            {
                List<MediaInfo> mediaInfos = new ReportDAL(reportRequest.Criteria.TenantId).FetchIQ3UsageDetailsByTenant(reportRequest.Criteria);

                return new ReportResponse
                {
                    Acknowledge = mediaInfos != null ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    MediaInfos = mediaInfos
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public int GetActiveTenantsCount() {
            try
            {
                return new ReportDAL(0).GetActiveTenantsCount();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }
}