<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:tns="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Criteria" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts.Criteria" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd9" namespace="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" />
  <xs:import schemaLocation="http://localhost:62195/RevEnterpriseSvc.svc?xsd=xsd8" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:complexType name="CallCriteria">
    <xs:sequence>
      <xs:element minOccurs="0" name="AgentName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ArchiveGroup" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CategoryGroupExtensions" nillable="true" type="tns:ArrayOfCategoryGroupExtension" />
      <xs:element minOccurs="0" name="CustomSearchOperator" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CustomText" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="EndDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="EndDuration" type="ser:duration" />
      <xs:element minOccurs="0" name="EndTime" type="ser:duration" />
      <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="EvalSearchType" type="q1:EvalSearchType" />
      <xs:element minOccurs="0" name="IsAgentBasedSearch" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsAvrisView" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsCustomSearch" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsGroupBasedSearchOnReport" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsNotAuditCallsRequest" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsOnlyDemoSearch" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsPercentage" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRandom" type="xs:boolean" />
      <xs:element minOccurs="0" name="NoOfCalls" type="xs:int" />
      <xs:element minOccurs="0" name="RadioTalkGroup" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecId" type="xs:int" />
      <xs:element minOccurs="0" name="RecorderCategoryGroupExtensions" nillable="true" type="tns:ArrayOfRecorderCategoryGroupExtension" />
      <xs:element minOccurs="0" name="RecorderName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ReportGroupId" type="xs:int" />
      <xs:element minOccurs="0" name="RestrictionInHours" type="xs:int" />
      <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="SearchType" type="q2:SearchType" />
      <xs:element minOccurs="0" name="SelectedDemoExtensions" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="StartDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="StartDuration" type="ser:duration" />
      <xs:element minOccurs="0" name="StartTime" type="ser:duration" />
      <xs:element minOccurs="0" name="TimeFormat" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CallCriteria" nillable="true" type="tns:CallCriteria" />
  <xs:complexType name="ArrayOfCategoryGroupExtension">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="CategoryGroupExtension" nillable="true" type="tns:CategoryGroupExtension" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfCategoryGroupExtension" nillable="true" type="tns:ArrayOfCategoryGroupExtension" />
  <xs:complexType name="CategoryGroupExtension">
    <xs:sequence>
      <xs:element minOccurs="0" name="GroupExtensions" nillable="true" type="tns:ArrayOfGroupExtension" />
      <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="GroupType" type="q3:GroupType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CategoryGroupExtension" nillable="true" type="tns:CategoryGroupExtension" />
  <xs:complexType name="ArrayOfGroupExtension">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="GroupExtension" nillable="true" type="tns:GroupExtension" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfGroupExtension" nillable="true" type="tns:ArrayOfGroupExtension" />
  <xs:complexType name="GroupExtension">
    <xs:sequence>
      <xs:element xmlns:q4="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="ExtensionIds" nillable="true" type="q4:ArrayOfstring" />
      <xs:element minOccurs="0" name="ExtensionIdsCSV" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GroupId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GroupExtension" nillable="true" type="tns:GroupExtension" />
  <xs:complexType name="ArrayOfRecorderCategoryGroupExtension">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RecorderCategoryGroupExtension" nillable="true" type="tns:RecorderCategoryGroupExtension" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRecorderCategoryGroupExtension" nillable="true" type="tns:ArrayOfRecorderCategoryGroupExtension" />
  <xs:complexType name="RecorderCategoryGroupExtension">
    <xs:sequence>
      <xs:element minOccurs="0" name="CategoryGroupExtensions" nillable="true" type="tns:ArrayOfCategoryGroupExtension" />
      <xs:element minOccurs="0" name="RadioTalkGroup" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecorderId" type="xs:int" />
      <xs:element minOccurs="0" name="RecorderIp" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RecorderName" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RecorderCategoryGroupExtension" nillable="true" type="tns:RecorderCategoryGroupExtension" />
  <xs:complexType name="GroupTreeCriteria">
    <xs:sequence>
      <xs:element minOccurs="0" name="AuthNum" type="xs:int" />
      <xs:element minOccurs="0" name="AuthType" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IsAgentNodesRequired" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsArchiveTalkGroup" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsAudioOnly" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsEnterpriseTree" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRadioTalkGroup" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRevCellRequired" type="xs:boolean" />
      <xs:element minOccurs="0" name="SelectType" type="xs:int" />
      <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/RevCord.DataContracts" minOccurs="0" name="TreeType" type="q5:GroupNodeType" />
      <xs:element minOccurs="0" name="Type" type="xs:int" />
      <xs:element minOccurs="0" name="UserId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UserNum" type="xs:int" />
      <xs:element minOccurs="0" name="UserType" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GroupTreeCriteria" nillable="true" type="tns:GroupTreeCriteria" />
</xs:schema>