﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using RevCord.DataContracts;
using RevCord.DataContracts.DTO;
using RevCord.DataContracts.EvaluationEntities;
using RevCord.DataContracts.SurveyEntities;
using RevCord.DataContracts.UserManagement;
using RevCord.DataContracts.ViewModelEntities;
using RevCord.DataContracts.VoiceRecEntities;
using RevCord.DataAccess.Util;
using RevCord.Util;

namespace RevCord.DataAccess
{
    public class EvaluationDALEC
    {
        private int _tenantId;
        public EvaluationDALEC(int tenantId)
        {
            this._tenantId = tenantId;
        }

        #region QA Dashboard drilldown graphs
        public static RecorderEvaluation GetAllDrilldownChartsFromAllRecorders(Recorder recorder)
        {
            List<Tuple<int, string, int, string, string>> evalsByStatus = null;
            List<Tuple<int, string, int, string, string>> evalsByCampaign = null;
            List<Tuple<int, string, int, string, string>> evalsByEvaluator = null;
            List<Tuple<int, string, int, string, string>> evalsByTopScorer = null;
            List<Tuple<int, string, int, string, string>> multiCallEvalsByStatus = null;
            Tuple<int, string, int, string, string> eval = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Charts.DASHBOARD_GETALL;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetAllDrilldownChartsFromAllRecorders", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        if (evalsByStatus == null) evalsByStatus = new List<Tuple<int, string, int, string, string>>();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                eval = new Tuple<int, string, int, string, string>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Description"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));
                                evalsByStatus.Add(eval);
                            }
                        }
                        dr.NextResult();

                        if (evalsByCampaign == null) evalsByCampaign = new List<Tuple<int, string, int, string, string>>();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                eval = new Tuple<int, string, int, string, string>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Title"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));
                                evalsByCampaign.Add(eval);
                            }
                        }
                        dr.NextResult();

                        if (evalsByEvaluator == null) evalsByEvaluator = new List<Tuple<int, string, int, string, string>>();
                        int num = 0;
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                eval = new Tuple<int, string, int, string, string>(++num, Convert.ToString(dr["Evaluator"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));
                                evalsByEvaluator.Add(eval);
                            }
                        }
                        dr.NextResult();

                        num = 0;
                        if (evalsByTopScorer == null) evalsByTopScorer = new List<Tuple<int, string, int, string, string>>();
                        if (dr.HasRows)
                        {
                            while (dr.Read())
                            {
                                eval = new Tuple<int, string, int, string, string>(++num, Convert.ToString(dr["TopScorer"]), Convert.ToInt32(dr["TotalScore"]), dr["EvalIds"] == DBNull.Value ? string.Empty : Convert.ToString(dr["EvalIds"]), dr["EvalScores"] == DBNull.Value ? string.Empty : Convert.ToString(dr["EvalScores"]));
                                evalsByTopScorer.Add(eval);
                            }
                        }

                        dr.NextResult();

                        if (multiCallEvalsByStatus == null) multiCallEvalsByStatus = new List<Tuple<int, string, int, string, string>>();
                        while (dr.Read())
                        {
                            if (dr["Id"] != null)
                            {
                                eval = new Tuple<int, string, int, string, string>(Convert.ToInt32(dr["Id"]), Convert.ToString(dr["Description"]), Convert.ToInt32(dr["EvalCount"]), Convert.ToString(dr["EvalIds"]), Convert.ToString(dr["EvalScores"]));
                                multiCallEvalsByStatus.Add(eval);
                            }
                        }
                    }
                }
            }
            catch (Exception ex) { throw ex; }
            return new RecorderEvaluation { EvalsByStatus = evalsByStatus, EvalsByCampaigns = evalsByCampaign, EvalsByEvaluators = evalsByEvaluator, EvalsByTopScorers = evalsByTopScorer, MultiCallEvalsByStatus = multiCallEvalsByStatus, RecorderId = recorder.Id, RecorderName = recorder.Name };
        }
        #endregion
        
        #region Agents Evaluation
        public List<CallEvaluationDTO> GetEvaluationsFromAllRecorders(Recorder recorder,string whereClause, int userId, int pageIndex, int pageSize, out int totalPages, out int totalRecords, bool SharedRequired = false, bool IsEvaluatorSearch = false)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_GETALL_BY_WHERE_CLAUSE_EC;
                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    //cmd.Parameters.AddWithValue("@SharedRequired", SharedRequired);
                    //cmd.Parameters.AddWithValue("@SharedRequired", SharedRequired == true ? 1 : 0);
                    //cmd.Parameters.AddWithValue("@IsEvaluatorSearch", recorder.Id == 1 ? IsEvaluatorSearch : true);
                    cmd.Parameters.AddWithValue("@SharedRequired", SharedRequired == true ? 1 : 0);
                    cmd.Parameters.AddWithValue("@IsEvaluatorSearch", IsEvaluatorSearch == true ? 1 : 0);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetEvaluationsFromAllRecorders", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr, recorder);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }
        public List<CallEvaluationDTO> PerformActionAndGetEvaluationDTOFromAllRecorders(Recorder recorder, int actionToPerformOnRecorder, string callSurveyIds, string whereClause, int action, int actionValue, int userId, DateTime? ActionDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_DO_ACTION_EC_N_GETALL_BY_WHERE_CLAUSE;
                    cmd.Parameters.AddWithValue("@CallEvaluationIds", callSurveyIds);
                    cmd.Parameters.AddWithValue("@Action", action);
                    cmd.Parameters.AddWithValue("@Value", actionValue);
                    cmd.Parameters.AddWithValue("@ActionDate", ActionDate);
                    cmd.Parameters.AddWithValue("@bActionRequired", actionToPerformOnRecorder == recorder.Id ? true : false);
                    cmd.Parameters.AddWithValue("@IsEvaluatorSearch", recorder.Id == 1 ? false : true);

                    cmd.Parameters.AddWithValue("@WhereClause", whereClause);

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "PerformActionAndGetEvaluationDTOFromAllRecorders", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr, recorder);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }
        public bool PerformActionOnRecorder(Recorder recorder, string callSurveyIds, int action, int actionValue, int userId, DateTime? ActionDate)
        {
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_DO_ACTION_EC;
                    cmd.Parameters.AddWithValue("@CallEvaluationIds", callSurveyIds);
                    cmd.Parameters.AddWithValue("@Action", action);
                    cmd.Parameters.AddWithValue("@Value", actionValue);
                    cmd.Parameters.AddWithValue("@ActionDate", ActionDate);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "PerformActionOnRecorder", 0));

                    return Convert.ToBoolean(cmd.ExecuteNonQuery());
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public List<CallEvaluationDTO> shareUnshareAndGetEvaluationDTOFromAllRecorders(Recorder recorder, int actionToPerformOnRecorder, string callSurveyIds, string whereClause, int action, int actionValue, int userId, string shareWith, bool isSharedEvaluatorRetains, DateTime? ActionDate, int pageIndex, int pageSize, out int totalPages, out int totalRecords)
        {
            List<CallEvaluationDTO> callEvaluationDTOs = null;
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATIONDTO_DO_ACTION_EC_N_GETALL_BY_WHERE_CLAUSE;
                    cmd.Parameters.AddWithValue("@CallEvaluationIds", callSurveyIds);
                    cmd.Parameters.AddWithValue("@Action", action);
                    cmd.Parameters.AddWithValue("@Value", actionValue);
                    cmd.Parameters.AddWithValue("@ActionDate", ActionDate);
                    cmd.Parameters.AddWithValue("@bActionRequired", actionToPerformOnRecorder == recorder.Id ? true : false);
                    cmd.Parameters.AddWithValue("@IsEvaluatorSearch", recorder.Id == 1 ? false : true);

                    cmd.Parameters.AddWithValue("@WhereClause", whereClause.Replace("OR (  )  OR", " "));

                    cmd.Parameters.AddWithValue("@UserId", userId);
                    cmd.Parameters.AddWithValue("@ShareWith", shareWith);
                    cmd.Parameters.AddWithValue("@IsSharedEvaluatorRetains", isSharedEvaluatorRetains);
                    cmd.Parameters.AddWithValue("@PageIndex", pageIndex);
                    cmd.Parameters.AddWithValue("@PageSize", pageSize);
                    cmd.Parameters.AddWithValue("@TotalPages", 1);
                    cmd.Parameters.AddWithValue("@TotalRecords", 0);

                    cmd.Parameters["@TotalPages"].Direction = ParameterDirection.Output;
                    cmd.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "shareUnshareAndGetEvaluationDTOFromAllRecorders", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        callEvaluationDTOs = ORMapper.CallEvaluationDTOs(dr, recorder);
                    }
                    totalPages = Convert.ToInt32(cmd.Parameters["@TotalPages"].Value.ToString());
                    totalRecords = Convert.ToInt32(cmd.Parameters["@TotalRecords"].Value.ToString());
                }
            }
            catch (Exception ex) { throw ex; }
            return callEvaluationDTOs;
        }
        public CallEvaluation GetRecorderCallEvaluationDetailsById(Recorder recorder, long callEvaluationId, int userId)
        {
            CallEvaluation callEvaluation = new CallEvaluation();
            Survey survey = null;
            List<Answer> answers = null;
            List<Option> options = null;
            
            try
            {
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_GET_DETAILS_BY_ID;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", callEvaluationId);
                    cmd.Parameters.AddWithValue("@UserId", userId);

                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "GetRecorderCallEvaluationDetailsById", 0));

                    //Total 8 Tables return.
                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        //1. Survey ResultSet
                        survey = ORMapper.MapSurvey(dr);//SurveyTable
                        if (survey == null) return callEvaluation;
                        dr.NextResult();
                        //2. Sections
                        survey.Sections = ORMapper.MapSections(dr);//Section Table
                        dr.NextResult();
                        //3. Questions
                        survey.Questions = ORMapper.MapQuestions(dr); //Question Table
                        dr.NextResult();
                        //4. Question Options
                        options = ORMapper.MapOptions(dr); //Option Table
                        if (survey.Questions != null)
                        {
                            survey.Questions.ForEach(q => q.Options = options.FindAll(o => o.QuestionId == q.Id));
                        }


                        dr.NextResult();
                        //5. CallEvaluation Resultset
                        callEvaluation = ORMapper.MapCallEvaluation(dr);
                        dr.NextResult();
                        //6. Answer ResultSet
                        answers = ORMapper.MapCallEvauationAnswerMaster(dr);
                        callEvaluation.Answers = answers;
                        dr.NextResult();
                        //7. Answer Options ResultSet
                        options = ORMapper.MapCallEvauationAnswerChild(dr);
                        callEvaluation.Answers.ForEach(a => a.Options = options.FindAll(o => o.QuestionId == a.QuestionId));

                        dr.NextResult();
                        //8. CallInfo ResultSet
                        callEvaluation.CallInfo = ORMapper.MapCallInfo(dr);
                        callEvaluation.Survey = survey;

                        dr.NextResult();
                        //9. Agent ResultSet
                        callEvaluation.Agent = ORMapper.MapUserWithoutChannel(dr);

                        return callEvaluation;
                    }
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public CallEvaluation RecorderUpdateCallEvaluation(Recorder recorder, CallEvaluation callEvaluation)
        {
            try
            {
                XElement xEvalCallSurveyDetails = this.CreateCallSurveyDetailsXML(callEvaluation);

                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_UPDATE;

                    cmd.Parameters.AddWithValue("@CallEvaluationId", callEvaluation.Id);
                    cmd.Parameters.AddWithValue("@StatusId", callEvaluation.StatusId);
                    //cmd.Parameters.AddWithValue("@UserId", evalCallSurvey.UserId);
                    //cmd.Parameters.AddWithValue("@IsShared", callEvaluation.IsShared);
                    cmd.Parameters.AddWithValue("@ModifiedDate", callEvaluation.ModifiedDate);
                    //cmd.Parameters.AddWithValue("@IsDeleted", callEvaluation.IsDeleted);
                    cmd.Parameters.AddWithValue("@CallEvaluationAnswers", xEvalCallSurveyDetails.ToString());

                    cmd.Parameters.AddWithValue("@ScorerComments", callEvaluation.SupervisorComments);
                    cmd.Parameters.AddWithValue("@EvaluatedScore", callEvaluation.EvaluatedScore);

                    //cmd.Parameters["@ScorerComments"].Direction = ParameterDirection.InputOutput;
                    cmd.Parameters["@EvaluatedScore"].Direction = ParameterDirection.Output;
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "RecorderUpdateCallEvaluation", 0));

                    cmd.ExecuteNonQuery();

                    //callEvaluation.SupervisorComments = Convert.ToString(cmd.Parameters["@ScorerComments"].Value.ToString());
                    callEvaluation.EvaluatedScore = Convert.ToSingle(cmd.Parameters["@EvaluatedScore"].Value.ToString());

                    return callEvaluation;
                }
            }
            catch (Exception ex) { throw ex; }
            //return false;
        }
        public User UpdateAndGetAssociatedUserFromRecorder(Recorder recorder, int evalId, int userNum)
        {
            try
            {
                User user = null;
                using (var conn = new SqlConnection(recorder.ConnectionString))
                using (var cmd = conn.CreateCommand())
                {
                    conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_UPDATE_ASSOCIATED_AGENT;
                    cmd.Parameters.AddWithValue("@CallEvaluationId", evalId);
                    cmd.Parameters.AddWithValue("@AgentId", userNum);
                    int count = (int)cmd.ExecuteNonQuery();

                    cmd.CommandType = CommandType.Text;
                    cmd.CommandText = "SELECT * FROM t_Account where UserNum = @userNum";
                    cmd.Parameters.AddWithValue("@userNum", userNum);
                    
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "UpdateAndGetAssociatedUserFromRecorder", 0));

                    using (SqlDataReader dr = cmd.ExecuteReader())
                    {
                        user = ORMapper.MapUserAsAgent(dr);
                    }
                    return user;
                }
            }
            catch (Exception ex) { throw ex; }
        }
        public short InsertCallEvaluationOnRecorder(Recorder recorder, int surveyId, string callId, int agentId, EvaluationStatus status, int userId)
        {
            try
            {
                using (var conn = DALHelper.GetConnection(_tenantId))
                using (var cmd = conn.CreateCommand())
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandText = DBConstants.Evaluation.CALLEVALUATION_INSERT_SINGLE;
                    cmd.Parameters.AddWithValue("@SurveyId", surveyId);
                    cmd.Parameters.AddWithValue("@CallId", callId);
                    cmd.Parameters.AddWithValue("@AppUserId", agentId);
                    cmd.Parameters.AddWithValue("@StatusId", (int)status);
                    cmd.Parameters.AddWithValue("@UserId", userId);
                    conn.Open();
                    Task.Run(() => RevAuditLogger.WriteSQL(QueryWriterUtil.GetCommandSQL(cmd), Originator.Evaluation, "InsertCallEvaluationOnRecorder", 0));

                    return Convert.ToInt16(cmd.ExecuteScalar());
                }
            }
            catch (Exception ex) { throw ex; }
        }
        #endregion

        #region Utility
        private XElement CreateCallSurveyDetailsXML(CallEvaluation callEvaluation)
        {
            XElement xcallSurveyDetails = new XElement("CallEvaluationAnswers");

            if (callEvaluation.Answers.Count == 0) return xcallSurveyDetails; // in-case of NO Answer

            foreach (var answer in callEvaluation.Answers)
            {
                if (answer.Options != null)
                {
                    foreach (var option in answer.Options)
                    {
                        xcallSurveyDetails.Add(new XElement("CallEvaluationAnswer",
                                    new XAttribute("CallEvaluationId", callEvaluation.Id),
                                    new XAttribute("ModifiedDate", callEvaluation.ModifiedDate),
                                    new XAttribute("IsDeleted", false),
                                    new XAttribute("QuestionId", answer.QuestionId),
                                    new XAttribute("QuestionOptionId", option.Id),
                                    new XAttribute("AnswerText", answer.AnswerValue)
                                ));
                    }
                }
                else
                {
                    xcallSurveyDetails.Add(new XElement("CallEvaluationAnswer",
                                    new XAttribute("CallEvaluationId", callEvaluation.Id),
                                    new XAttribute("ModifiedDate", callEvaluation.ModifiedDate),
                                    new XAttribute("IsDeleted", false),
                                    new XAttribute("QuestionId", answer.QuestionId),
                                    new XAttribute("QuestionOptionId", 0),
                                    new XAttribute("AnswerText", answer.AnswerValue)
                                ));
                }
            }
            return xcallSurveyDetails;
        }
        private XElement CreateUserSurveyDetailsXML(UserEvaluation userEval)
        {
            XElement xcallSurveyDetails = new XElement("UserEvaluationAnswers");

            if (userEval.Answers.Count == 0) return xcallSurveyDetails; // in-case of NO Answer

            foreach (var answer in userEval.Answers)
            {
                if (answer.Options != null)
                {
                    foreach (var option in answer.Options)
                    {
                        xcallSurveyDetails.Add(new XElement("UserEvaluationAnswer",
                                    new XAttribute("UserEvaluationId", userEval.Id),
                                    new XAttribute("ModifiedDate", userEval.ModifiedDate),
                                    new XAttribute("IsDeleted", false),
                                    new XAttribute("QuestionId", answer.QuestionId),
                                    new XAttribute("QuestionOptionId", option.Id),
                                    new XAttribute("AnswerText", answer.AnswerValue)
                                ));
                    }
                }
                else
                {
                    xcallSurveyDetails.Add(new XElement("CallEvaluationAnswer",
                                    new XAttribute("CallEvaluationId", userEval.Id),
                                    new XAttribute("ModifiedDate", userEval.ModifiedDate),
                                    new XAttribute("IsDeleted", false),
                                    new XAttribute("QuestionId", answer.QuestionId),
                                    new XAttribute("QuestionOptionId", 0),
                                    new XAttribute("AnswerText", answer.AnswerValue)
                                ));
                }
            }
            return xcallSurveyDetails;
        }
        #endregion

    }
}
