﻿using RevCord.DataAccess;
using RevCord.DataAccess.Util;
using RevCord.DataContracts;
using RevCord.DataContracts.MessageBase;
using RevCord.DataContracts.Messages;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RevCord.BusinessLogic
{
    public class CustomerDBManager
    {
        public CustomerDBResponse GetIQ3AssetsModel(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.CustomerDB, "GetIQ3AssetsModel", tenantId, "GetIQ3AssetsModel function has been called. tenantId = " + tenantId));
                return new CustomerDBResponse
                {
                    IQ3AssetsModel = new CustomerDBDAL(tenantId).GetIQ3AssetsModel()
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "GetIQ3AssetsModel", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "GetIQ3AssetsModel", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse GetIQ3AssetsModelForDelete(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.CustomerDB, "GetIQ3AssetsModelForDelete", tenantId, "GetIQ3AssetsModel function has been called. tenantId = " + tenantId));
                return new CustomerDBResponse
                {
                    IQ3AssetsModel = new CustomerDBDAL(tenantId).GetIQ3AssetsModelForDelete()
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "GetIQ3AssetsModelForDelete", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "GetIQ3AssetsModelForDelete", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse GetIQ3AssetsModelForRecover(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.CustomerDB, "GetIQ3AssetsModelForRecover", tenantId, "GetIQ3AssetsModelForRecover function has been called. tenantId = " + tenantId));
                return new CustomerDBResponse
                {
                    IQ3AssetsModel = new CustomerDBDAL(tenantId).GetIQ3AssetsModelForRecover()
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "GetIQ3AssetsModelForRecover", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "GetIQ3AssetsModelForRecover", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse UpdateIQ3AssetModel(CustomerDBRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.CustomerDB, "UpdateIQ3AssetModel", request.TenantId, "UpdateIQ3AssetModel function has been called. request.TenantId = " + request.TenantId));
                int rowsAffected = new CustomerDBDAL(request.TenantId).UpdateIQ3AssetModel(request.IQ3AssetsModel);
                return new CustomerDBResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    Message = string.Format("IQ3Asset Model has been updated successfully.")
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "UpdateIQ3AssetModel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "UpdateIQ3AssetModel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse AddFieldIQ3AssetModel(CustomerDBRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.CustomerDB, "AddFieldIQ3AssetModel", request.TenantId, "AddFieldIQ3AssetModel function has been called. request.TenantId = " + request.TenantId));
                return new CustomerDBResponse
                {
                    FlagStatus = new CustomerDBDAL(request.TenantId).AddFieldIQ3AssetModel(request.IQ3AssetModel)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "AddFieldIQ3AssetModel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "AddFieldIQ3AssetModel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse DeleteIQ3AssetModel(CustomerDBRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.CustomerDB, "DeleteIQ3AssetModel", request.TenantId, "DeleteIQ3AssetModel function has been called. request.TenantId = " + request.TenantId));
                return new CustomerDBResponse
                {
                    FlagStatus = new CustomerDBDAL(request.TenantId).DeleteIQ3AssetModel(request.IQ3AssetModelIds)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "DeleteIQ3AssetModel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "DeleteIQ3AssetModel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse UnDeleteIQ3AssetModel(CustomerDBRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.CustomerDB, "UnDeleteIQ3AssetModel", request.TenantId, "UnDeleteIQ3AssetModel function has been called. request.TenantId = " + request.TenantId));
                return new CustomerDBResponse
                {
                    FlagStatus = new CustomerDBDAL(request.TenantId).UnDeleteIQ3AssetModel(request.IQ3AssetModelIds)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "UnDeleteIQ3AssetModel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "UnDeleteIQ3AssetModel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse ReorderModel(CustomerDBRequest request)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.CustomerDB, "ReorderModel", request.TenantId, "ReorderModel function has been called. request.TenantId = " + request.TenantId));
                int rowsAffected = new CustomerDBDAL(request.TenantId).ReorderModel(request.IQ3AssetsModel);

                return new CustomerDBResponse
                {
                    Acknowledge = rowsAffected > 0 ? AcknowledgeType.Success : AcknowledgeType.Failure,
                    Message = string.Format("IQ3Assetl Model has been reordered successfully.")
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "ReorderModel", request.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "ReorderModel", request.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #region IQ3 Asset


        public CustomerDBResponse GetAllIQ3Assets(CustomerDBRequest customerDBRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.CustomerDB, "GetAllIQ3Assets", customerDBRequest.TenantId, " GetAllIQ3Assets function has been called successfully. tenantId = " + customerDBRequest.TenantId));
                return new CustomerDBResponse { IQ3Assets = new CustomerDBDAL(customerDBRequest.TenantId).GetAllIQ3Assets() };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "GetAllIQ3Assets", customerDBRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "GetAllIQ3Assets", customerDBRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse SearchAllIQ3Assets(CustomerDBRequest customerDBRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.CustomerDB, "SearchAllIQ3Assets", customerDBRequest.TenantId, " SearchAllIQ3Assets function has been called successfully. tenantId = " + customerDBRequest.TenantId));
                return new CustomerDBResponse { IQ3Assets = new CustomerDBDAL(customerDBRequest.TenantId).SearchAllIQ3Assets(customerDBRequest.SearchText) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "SearchAllIQ3Assets", customerDBRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "SearchAllIQ3Assets", customerDBRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse GetIQ3AssetById(CustomerDBRequest customerDBRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.CustomerDB, "GetIQ3AssetById", customerDBRequest.TenantId, " GetIQ3AssetById function has been called successfully. tenantId = " + customerDBRequest.TenantId + " - assetId = " + customerDBRequest.AssetId));
                return new CustomerDBResponse { IQ3Asset = new CustomerDBDAL(customerDBRequest.TenantId).GetIQ3AssetById(customerDBRequest.AssetId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "GetIQ3AssetById", customerDBRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "GetIQ3AssetById", customerDBRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse AddIQ3Asset(CustomerDBRequest customerDBRequest)
        {
            try
            {
                CustomerDBResponse inspectionResponse = new CustomerDBResponse { FlagStatus = new CustomerDBDAL(customerDBRequest.TenantId).AddIQ3Asset(customerDBRequest.IQ3AssetAdd, customerDBRequest.UserNum) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.CustomerDB, "AddIQ3Asset", customerDBRequest.TenantId, "IQ3Asset has been added successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "AddIQ3Asset", customerDBRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "AddIQ3Asset", customerDBRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse UpdateIQ3Asset(CustomerDBRequest customerDBRequest)
        {
            try
            {
                CustomerDBResponse inspectionResponse = new CustomerDBResponse { FlagStatus = new CustomerDBDAL(customerDBRequest.TenantId).UpdateIQ3Asset(customerDBRequest.IQ3AssetUpdate) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.CustomerDB, "UpdateIQ3Asset", customerDBRequest.TenantId, "IQ3Asset data has been updated successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "UpdateIQ3Asset", customerDBRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "UpdateIQ3Asset", customerDBRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse UpdateStatusIQ3Asset(CustomerDBRequest customerDBRequest)
        {
            try
            {
                CustomerDBResponse inspectionResponse = new CustomerDBResponse { FlagStatus = new CustomerDBDAL(customerDBRequest.TenantId).UpdateStatusIQ3Asset(customerDBRequest.AssetId, (int)customerDBRequest.AssetStatus) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.CustomerDB, "UpdateStatusIQ3Asset", customerDBRequest.TenantId, "IQ3Asset Status has been updated successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "UpdateStatusIQ3Asset", customerDBRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "UpdateStatusIQ3Asset", customerDBRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse UpdateAssetPhoto(CustomerDBRequest customerDBRequest)
        {
            try
            {
                CustomerDBResponse inspectionResponse = new CustomerDBResponse { FlagStatus = new CustomerDBDAL(customerDBRequest.TenantId).UpdateAssetPhoto(customerDBRequest.Id, customerDBRequest.AssetPhoto) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.CustomerDB, "UpdateAssetPhoto", customerDBRequest.TenantId, "Asset Photo has been updated successfully.  Id = " + customerDBRequest.Id));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "UpdateAssetPhoto", customerDBRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "UpdateAssetPhoto", customerDBRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse GetIQ3AssetHistoryByAssetId(CustomerDBRequest customerDBRequest)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.CustomerDB, "GetIQ3AssetHistoryByAssetId", customerDBRequest.TenantId, " GetIQ3AssetHistoryByAssetId function has been called successfully. tenantId = " + customerDBRequest.TenantId));
                return new CustomerDBResponse { IQ3AssetHistory = new CustomerDBDAL(customerDBRequest.TenantId).GetIQ3AssetHistoryByAssetId(customerDBRequest.AssetId) };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "GetIQ3AssetHistoryByAssetId", customerDBRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "GetIQ3AssetHistoryByAssetId", customerDBRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse LogIQ3AssetHistory(CustomerDBRequest customerDBRequest)
        {
            try
            {
                CustomerDBResponse inspectionResponse = new CustomerDBResponse { FlagStatus = new CustomerDBDAL(customerDBRequest.TenantId).LogIQ3AssetHistory(customerDBRequest.IQ3AssetHistory) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.CustomerDB, "LogIQ3AssetHistory", customerDBRequest.TenantId, "IQ3Asset History has been logged successfully.  "));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "LogIQ3AssetHistory", customerDBRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "LogIQ3AssetHistory", customerDBRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        #endregion

        public CustomerDBResponse FetchAllAssets(int tenantId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.CustomerDB, "FetchAllAssets", tenantId, "FetchAllAssets function has been called. tenantId = " + tenantId));
                return new CustomerDBResponse
                {
                    Assets = new CustomerDBDAL(tenantId).FetchAllAssets()
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "FetchAllAssets", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "FetchAllAssets", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse FetchAssetDetailsById(int tenantId, int assetId)
        {
            try
            {
                Task.Run(() => RevAuditLogger.WriteInformation(Originator.CustomerDB, "FetchAssetDetailsById", tenantId, "FetchAssetDetailsById function has been called. tenantId = " + tenantId));
                return new CustomerDBResponse
                {
                    AssetDetails = new CustomerDBDAL(tenantId).FetchAssetDetailsById(assetId)
                };
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "FetchAssetDetailsById", tenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "FetchAssetDetailsById", tenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }

        public CustomerDBResponse UpdateVisibleStatus(CustomerDBRequest customerDBRequest)
        {
            try
            {
                CustomerDBResponse inspectionResponse = new CustomerDBResponse { FlagStatus = new CustomerDBDAL(customerDBRequest.TenantId).UpdateVisibleStatus(customerDBRequest.Id, customerDBRequest.VisibleStatus) };
                Task.Run(() => RevAuditLogger.WriteSuccess(Originator.CustomerDB, "UpdateVisibleStatus", customerDBRequest.TenantId, "Visible Status has been updated successfully.  Id = " + customerDBRequest.Id));
                return inspectionResponse;
            }
            catch (SqlException sqle)
            {
                Task.Run(() => RevAuditLogger.WriteSQLException(Originator.CustomerDB, "UpdateVisibleStatus", customerDBRequest.TenantId, sqle.Message, sqle.StackTrace, sqle.ErrorCode, string.Empty, 0));
                throw sqle;
            }
            catch (Exception ex)
            {
                Task.Run(() => RevAuditLogger.WriteException(Originator.CustomerDB, "UpdateVisibleStatus", customerDBRequest.TenantId, ex.Message, ex.StackTrace, 0, string.Empty, 0));
                throw ex;
            }
        }
    }
}
